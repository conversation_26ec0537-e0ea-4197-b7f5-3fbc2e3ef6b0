package com.appnomic.appsone.controlcenter.beans.autodiscovery;

import com.appnomic.appsone.common.beans.discovery.Attribute;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class Component {
    private int componentId;
    private int componentTypeId;
    private String componentName;
    private String discoveryPattern;
    private List<String> relativePathList;
    private List<Attribute> attributes;
    private LdPreloadDetails ldPreloadDetails;
    private String jvmArgs;
}
