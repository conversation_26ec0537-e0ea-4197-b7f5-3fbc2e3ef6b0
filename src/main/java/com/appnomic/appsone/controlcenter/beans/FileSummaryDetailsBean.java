package com.appnomic.appsone.controlcenter.beans;

import lombok.Data;

/**
 * <AUTHOR> : 09/04/2019
 */
@Data
public class FileSummaryDetailsBean {
    private int fileProcessedId;
    private String key;
    private String value;
    private int accountId;
    private int isDebugLogs;

    public FileSummaryDetailsBean(){}

    public FileSummaryDetailsBean(int fileProcessedId, int accountId, String key, String value,
            int isDebugLogs){
        this.fileProcessedId = fileProcessedId;
        this.accountId = accountId;
        this.key = key;
        this.value = value;
        this.isDebugLogs = isDebugLogs;
    }
}
