package com.appnomic.appsone.controlcenter.beans;

import lombok.Data;

@Data
public class CompInstAttributesBean {
    private int attributeId;
    private String attributeValue;
    private String attributeName;
    private String attributeKey;
    private int required;
    private int min;
    private int max;
    private String pattern;
    private String type;
    private String errorMessage;
    private String options;
    private String createdTime;
    private String updatedTime;
    private int compInstanceId;
    private int mstComponentAttributeMappingId;
    private int mstCommonAttributesId;
    private String userDetailsId;
    private int isCustom;
    private int status;
    private String accountIdentifier;
    private String instanceIdentifier;
}