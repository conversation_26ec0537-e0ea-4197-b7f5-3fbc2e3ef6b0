package com.appnomic.appsone.controlcenter.beans;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.Set;

@Data
public class DiscoveryTagDetailsBean {
    Integer mappingId;
    String tagName;
    Integer maxTags;
    Integer tagCount;
    Integer status;


    public Set<String> getTagNameList() {
        Set<String> tagNameList = new HashSet<>();
        if (StringUtils.isNotEmpty(this.tagName)) {
            String[] tags = this.tagName.split(",");
            for (String tag : tags) {
                tagNameList.add(tag.trim());
            }
        }
        return tagNameList;
    }
}
