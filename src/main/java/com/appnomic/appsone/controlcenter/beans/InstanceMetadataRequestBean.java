package com.appnomic.appsone.controlcenter.beans;

import com.heal.configuration.pojos.InstanceMetadata;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InstanceMetadataRequestBean {
    private Integer instanceId;
    private Integer memorySizeMB;
    private Integer cpuCores;
    private Integer diskSizeMB;
    private String instanceIdentifier;
    private String environmentName;
    private String relatedInstanceIdentifier;
    private String instanceName;
    private Integer environmentId;
    private Integer relatedInstanceId;
    private String createdTime;
    private String updatedTime;
    private String userDetailsId;
    private Integer status;
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        InstanceMetadataRequestBean that = (InstanceMetadataRequestBean) obj;

        return Objects.equals(instanceIdentifier, that.instanceIdentifier);
    }

    @Override
    public int hashCode() {
        return Objects.hash(instanceIdentifier);
    }
    public InstanceMetadata mapToInstanceMetadata() {
        return InstanceMetadata.builder()
                .instanceId(instanceId)
                .instanceName(instanceName)
                .instanceIdentifier(instanceIdentifier)
                .environmentId(environmentId)
                .environmentName(environmentName)
                .relatedInstanceId(relatedInstanceId)
                .memorySizeMB(memorySizeMB)
                .cpuCores(cpuCores)
                .diskSizeMB(diskSizeMB)
                .createdTime(createdTime)
                .updatedTime(updatedTime)
                .userDetailsId(userDetailsId)
                .status(status)
                .build();
    }
}

