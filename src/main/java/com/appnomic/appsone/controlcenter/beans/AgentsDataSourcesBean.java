package com.appnomic.appsone.controlcenter.beans;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.TagMappingBean;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> on 28/04/2022
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentsDataSourcesBean {
    List<TagMappingBean> addedAgentDataSources;
    List<TagMappingBean> deletedAgentDataSources;
}