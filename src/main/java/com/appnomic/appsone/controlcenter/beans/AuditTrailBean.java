package com.appnomic.appsone.controlcenter.beans;

import lombok.Builder;
import lombok.Data;
import java.util.List;

@Data
@Builder
public class AuditTrailBean {
    private List<Integer> bigFeatureIds;
    private List<Integer> appIds;
    private List<Integer> serviceIds;
    private Integer accountId;
    private String userId;
    private long fromTime;
    private long toTime;
    private  String timeZone;
    private  String defaultTimeZone;

}