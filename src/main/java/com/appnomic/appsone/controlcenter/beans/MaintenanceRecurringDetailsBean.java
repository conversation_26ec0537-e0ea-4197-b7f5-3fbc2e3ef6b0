package com.appnomic.appsone.controlcenter.beans;

import com.appnomic.appsone.common.beans.MaintenanceWindowBean;
import com.appnomic.appsone.common.beans.RecurringDetailsBean;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MaintenanceRecurringDetailsBean {

    List<MaintenanceWindowBean> maintenanceWindowBeanList;
    Map<Integer, RecurringDetailsBean> recurringDetailsMap;
}
