package com.appnomic.appsone.controlcenter.beans;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.ComponentAttributesBean;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> on 16/7/20
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InstanceMappingDetails {
    private List<CompInstanceKpiDetailsBean> nonGroupKpi;
    private List<CompInstanceKpiGroupDetailsBean> groupKpi;
    private List<ComponentAttributesBean> attributes;
}
