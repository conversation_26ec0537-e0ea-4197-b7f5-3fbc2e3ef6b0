package com.appnomic.appsone.controlcenter.beans;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ComponentFilesDetailsBean {
    private int id;
    private int mstComponentId;
    private int mstComponentVersionId;
    private int mstKpiDetailsId;
    private String fileName;
    private String relativePath;
    private String userDetailsId;
    private String createdTime;
    private String updatedTime;
    private int kpiTypeId;
}