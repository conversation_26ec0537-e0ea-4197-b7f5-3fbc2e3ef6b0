package com.appnomic.appsone.controlcenter.beans;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ServiceTransactionSettingBean {

    private int id;
    private int accountId;
    private int serviceId;
    private String lastCommitedTime;
    private int lastCommitedTransactions;
    private int maxVolumeCount;
    private int maxTransactionsLimit;
    private String createdTime;
    private String updatedTime;
    private String userDetailsId;
    private int autoCommitDuration;
    private int schedulerDetailsId;
}
