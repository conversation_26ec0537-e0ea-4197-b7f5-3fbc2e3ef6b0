package com.appnomic.appsone.controlcenter.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
public class ActionsCategoryBean {
    private int id;
    private String name;
    private String categoryId;
    private int timeWindowInSecs;
    private String commandExecType;
    private String actionExecType;
    private String downloadType;
    private int retries;
    private int ttlInSecs;
    private ActionScriptsBean scriptDetails;
    @JsonIgnore
    private int actionExecTypeId;
    @JsonIgnore  private int objectId;
    @JsonIgnore  private String objectRefTable;
    private int status;
}
