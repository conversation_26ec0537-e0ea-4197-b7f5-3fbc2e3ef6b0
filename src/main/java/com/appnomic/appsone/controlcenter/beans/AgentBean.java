package com.appnomic.appsone.controlcenter.beans;

import com.appnomic.appsone.controlcenter.pojo.CommandDetailsPojo;
import com.appnomic.appsone.controlcenter.pojo.ComponentAgent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentBean {
    private int id;
    private int status;
    private int commandId;
    private int agentTypeId;
    private int compInstanceId;
    private int physicalAgentId;
    private int supervisorId;
    private String mode;
    private String name;
    private String description;
    private String commandName;
    private String uniqueToken;
    private String physicalAgentIdentifier;
    private String userDetailsId;
    private String hostAddress;
    private String version;
    private String installationPath;
    private int communicationInterval;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private List<CommandDetailsPojo> commands;
    private String subType;
    private int serviceId;
    private String serviceIdentifier;
    private String serviceName;
    private ComponentAgent agentMappingDetails;
    private String updatedBy;
    private int accountId;
    private String accountIdentifier;
    private String userDetailId;
    private List<String> addedDataSources;
    private List<String> deletedDataSources;
    private boolean forensicsEnabled;
    private List<Integer> serviceIds;

    @Override
    public int hashCode() {
        return physicalAgentId;
    }

    @Override
    public boolean equals(Object bean) {
        if(bean instanceof AgentBean) {
            AgentBean agentBean = (AgentBean) bean;
            return (agentBean.getPhysicalAgentId() == this.physicalAgentId);
        }
        return false;
    }
}
