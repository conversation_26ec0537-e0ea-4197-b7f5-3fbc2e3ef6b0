package com.appnomic.appsone.controlcenter.beans;

import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> : 17/1/19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CompInstanceKpiDetailsBean {
    private int id;
    private int compInstanceId;
    private int mstProducerKpiMappingId;
    private int collectionInterval;
    private int status;
    private String createdTime;
    private String updatedTime;
    private String userDetailsId;
    private int mstKpiDetailsId;
    private int mstProducerId;
    private int notification;

    public void populate(Integer compInstanceId, InstanceMappingDetails mappingDetails, int producerKpiMappingId, int producerId, int kpiId,
                         String userDetailsId) {
        String timestampString = DateTimeUtil.getTimeInGMT(new Date().getTime());
        this.setCompInstanceId(compInstanceId);
        this.setMstProducerKpiMappingId(producerKpiMappingId);
        this.setCollectionInterval(getCollectionInterval(mappingDetails, kpiId));
        this.setCreatedTime(timestampString);
        this.setUpdatedTime(timestampString);
        this.setUserDetailsId(userDetailsId);
        this.setMstKpiDetailsId(kpiId);
        this.setMstProducerId(producerId);
    }

    private int getCollectionInterval(InstanceMappingDetails mappingDetails, int kpiId) {
        Integer collInterval;

        collInterval = mappingDetails.getNonGroupKpi().stream()
                .filter( it -> ( it.getMstKpiDetailsId() == kpiId))
                .map(CompInstanceKpiDetailsBean::getCollectionInterval)
                .findAny()
                .orElse(1);

        return collInterval;
    }
}
