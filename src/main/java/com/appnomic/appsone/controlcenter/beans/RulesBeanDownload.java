package com.appnomic.appsone.controlcenter.beans;

import lombok.Data;

import java.util.Map;

@Data
public class RulesBeanDownload {
 private String ruleName;
 private String ruleType;
 private String segmentURIType;
 private String segmentValue;
 private String serviceId;
 private String payloadType = "Form Data";
 private Map<String, String> queryParameters;
 private Map<String, String>  payloadParameters;
 private Map<String, String>  httpHeaderParameters;

}