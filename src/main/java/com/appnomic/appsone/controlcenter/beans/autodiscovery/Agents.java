package com.appnomic.appsone.controlcenter.beans.autodiscovery;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class Agents {
    private String componentAgentUid;
    private String logForwarderUid;
    private String supervisorUid;
    @JsonIgnore
    private String psAgentUid;
    private String jimAgentUid;
    private String forensicAgentUid;
    @JsonIgnore
    private String hostIdentifier;
    private List<JIMAgent> jimAgents;
}
