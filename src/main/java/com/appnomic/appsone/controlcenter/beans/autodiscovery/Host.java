package com.appnomic.appsone.controlcenter.beans.autodiscovery;

import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.enums.DiscoveryStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class Host {
    private String hostIdentifier;
    private String operatingSystem;
    private String operatingSystemVersion;
    private String hostname;
    private String platform;
    @JsonIgnore
    private long lastUpdatedTime;
    @JsonIgnore
    private int environment;
    private long lastDiscoveryRunTime;
    private DiscoveryStatus discoveryStatus;
    @JsonIgnore
    private int isIgnored;
    @JsonIgnore
    private String ignoredBy;
    private String currentUser;
    private List<Endpoint> endpoints;
    private List<Connection> connections;
    private List<Process> runningProcesses;
    private List<NetworkInterface> networkInterfaces;
    private List<MountPoint> mountPoints;
    private List<DiscoveryError> discoveryErrors;
    private Agents agents;
    @JsonIgnore
    private String addToSystemErrorMessage;
    @JsonIgnore
    private String hostInstanceSDMIdentifier;
    @JsonIgnore
    private List<String> availableCommandsInPath;
    private Map<String, String> hostAttributes;
    private Prerequisite preRequisitesFound;
    @JsonIgnore
    private String scriptPath;
    private boolean enableScriptExecRequested;
    private boolean validationScriptExecRequested;

    public void addDiscoveryError(DiscoveryError discoveryError) {
        if (discoveryErrors == null) {
            discoveryErrors = new ArrayList<DiscoveryError>();
        }
        discoveryErrors.add(discoveryError);
    }
}
