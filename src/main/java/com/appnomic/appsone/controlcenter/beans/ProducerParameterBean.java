package com.appnomic.appsone.controlcenter.beans;

import com.appnomic.appsone.controlcenter.pojo.ProducerParameterDetails;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import lombok.Data;

import java.util.Date;

@Data
public class ProducerParameterBean {

    private Integer id;
    private int producerId;
    private String parameterType;
    private String name;
    private String value;
    //additional fields for getting extra fields, so that it can be used for insert as well
    private String createdTime;
    private String updatedTime;
    private String userDetailsId;
    private int parameterOrder;

    public void populate(int producerId, String userId, int order, ProducerParameterDetails producerParameterDetails) {

        Date date = new Date();
        this.setProducerId(producerId);
        this.setParameterType(producerParameterDetails.getParameterType());
        this.setName(producerParameterDetails.getParameterName());
        this.setValue(producerParameterDetails.getParameterValue());
        this.setCreatedTime(DateTimeUtil.getTimeInGMT(date.getTime()));
        this.setUpdatedTime(DateTimeUtil.getTimeInGMT(date.getTime()));
        this.setUserDetailsId(userId);
        this.setParameterOrder(order);

    }
}
