package com.appnomic.appsone.controlcenter.beans;

import lombok.Data;

import java.sql.Timestamp;
import java.util.Objects;

/**
 * <AUTHOR> : 6/3/19
 */
@Data
public class CompInstKpiThresholdDetailsBean {
    private int id;
    private int compInstanceId;
    private int kpiId;
    private String kpiAttribute;
    private int operationId;
    private Double minThreshold;
    private Double maxThreshold;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private String userDetailsId;
    private int kpiGroupId;
    private Timestamp startTime;
    private Timestamp endTime;
    private int accountId;
    private int persistence;
    private int suppression;
    private int status;
    private int severity;
    private int isMaintenanceExcluded;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CompInstKpiThresholdDetailsBean that = (CompInstKpiThresholdDetailsBean) o;
        return id == that.id;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
