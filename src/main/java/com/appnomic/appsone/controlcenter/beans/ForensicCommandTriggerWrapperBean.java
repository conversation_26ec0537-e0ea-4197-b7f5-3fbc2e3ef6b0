package com.appnomic.appsone.controlcenter.beans;

import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import com.appnomic.appsone.controlcenter.pojo.ForensicCmdTriggeredStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ForensicCommandTriggerWrapperBean {
    ForensicCmdTriggeredStatus forensicCmdTriggeredStatus;
    CommandRequestProtos.CommandRequest commandRequest;
}
