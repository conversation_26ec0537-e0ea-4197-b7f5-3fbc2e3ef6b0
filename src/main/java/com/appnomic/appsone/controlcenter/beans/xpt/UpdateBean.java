package com.appnomic.appsone.controlcenter.beans.xpt;

import lombok.Data;

import java.util.HashSet;
import java.util.Set;

@Data
public class UpdateBean {
    private int id;
    private String name;
    private Boolean status;
    private Integer monitorEnabled;
    private Integer discoveryEnabled;
    private int auditEnabled;
    private String updateTime;
    private String userDetailsId;
    Set<String> addDiscoveryTags = new HashSet<>();
    Set<String> removeDiscoveryTags = new HashSet<>();
}
