package com.appnomic.appsone.controlcenter.health;

import com.appnomic.appsone.controlcenter.cache.CCCache;

import java.util.Map;

public class ApplicationInfo implements ApplicationInfoMBean {
    @Override
    public int getHttpRequests() {
        return CCCache.INSTANCE.getRequests();
    }

    @Override
    public int getUnauthorizedRequests() {
        return CCCache.INSTANCE.getUnauthorizedRequests();
    }

    @Override
    public int getAccessDeniedRequests() {
        return CCCache.INSTANCE.getAccessDeniedRequests();
    }

    @Override
    public int getSkipValidationRequests() {
        return CCCache.INSTANCE.getSkipValidationRequests();
    }

    @Override
    public Map<String, Double> getSlowRequestDetails() {
        return CCCache.INSTANCE.getSlowRequestDetails();
    }

    @Override
    public Map<Integer, Integer> getStatusCodes() {
        return CCCache.INSTANCE.getStatusCodes();
    }

    @Override
    public double getMaxResponseTime() {
        return CCCache.INSTANCE.getMaxRespTimeInMillSecs();
    }

    @Override
    public int getSlowRequests() {
        return CCCache.INSTANCE.getSlowRequests();
    }

    @Override
    public int getCCErrors() {
        return CCCache.INSTANCE.getCCErrors();
    }

    @Override
    public int getRequestThreshold() {
        return CCCache.INSTANCE.getRequestThreshold();
    }

    @Override
    public void setRequestThreshold(int thresholdInSecs) {
        CCCache.INSTANCE.setRequestThreshold(thresholdInSecs);
    }
}
