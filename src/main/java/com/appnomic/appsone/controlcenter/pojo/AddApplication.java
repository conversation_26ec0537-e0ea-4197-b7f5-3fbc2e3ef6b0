package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.heal.configuration.pojos.ParentApplication;
import com.heal.configuration.pojos.Tags;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddApplication {
    private static final Logger LOGGER = LoggerFactory.getLogger(AddApplication.class);
    private static final String DEFAULT_TIMEZONE = "(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi";
    private String name;
    private String identifier;
    private List<String> services;
    private String timezone = DEFAULT_TIMEZONE;
    private String accountIdentifier;
    private ParentApplication parentApplication;
    private String userId;
    private List<ApplicationTags> tags;

    public boolean validate() {

        if (this.accountIdentifier == null || this.accountIdentifier.trim().length() == 0) {
            LOGGER.error(UIMessages.ACCOUNT_EMPTY);
            return false;
        }
        if (this.name == null || this.name.length() > 128 || this.name.length() < 1) {
            LOGGER.error(UIMessages.ACTION_INVALID_NAME);
            return false;
        }
        if (this.identifier != null && this.identifier.length() > 128) {
            LOGGER.error(UIMessages.INVALID_IDENTIFIER);
            return false;
        }

        if (!this.name.matches(Constants.ALLOWED_NAME_CHARACTERS)) {
            LOGGER.error(UIMessages.ALLOWED_NAME_CHARACTERS, "Application name");
            return false;
        }

        if (this.identifier != null && !this.identifier.matches(Constants.ALLOWED_IDENTIFIER_CHARACTERS)) {
            LOGGER.error(UIMessages.ALLOWED_IDENTIFIER_CHARACTERS, "Application identifier");
            return false;
        }
        if(this.parentApplication == null || this.parentApplication.getName().trim().length() == 0) {
            LOGGER.warn("Parent Application name is Empty. Adding Application name as Parent Application");
        }

        if (this.services != null && !this.services.isEmpty()) {
            for (String s : services) {
                if (s.trim().length() == 0) {
                    LOGGER.error("One or more service is empty.");
                    return false;
                }
            }
        }

        if(this.tags != null && !this.tags.isEmpty()) {
            for(ApplicationTags tag : tags) {
                if(tag == null) {
                    LOGGER.error("No tags are provided");
                    return false;
                }
            }
        }
        return true;
    }

}
