package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandArgumentBean;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CategoryForensicDetails {

    private int categoryId;
    private String categoryName;
    private int forensicId;
    private String forensicName;
    private int commandId;
    private String commandName;
    private List<CommandArgumentBean> commandArguments;
    private int suppressionInterval;
    private int triggerForensicStatus;

    private static final Logger LOGGER = LoggerFactory.getLogger(CategoryForensicDetails.class);

    public boolean validate() {
        boolean val = true;

        if (categoryId < 1) {
            LOGGER.error("categoryId should not be less than 1.");
            val = false;
        }

        if (forensicId < 1) {
            LOGGER.error("forensicId should not be less than 1.");
            val = false;
        }

        if (suppressionInterval < 0) {
            LOGGER.error("suppressionInterval should not be negative.");
            val = false;
        }

        if (triggerForensicStatus != 0 && triggerForensicStatus != 1) {
            LOGGER.error("triggerForensicStatus should 0 or 1.");
            val = false;
        }

        return val;
    }
}
