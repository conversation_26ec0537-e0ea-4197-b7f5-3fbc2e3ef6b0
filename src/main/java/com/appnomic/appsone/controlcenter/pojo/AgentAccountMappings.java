package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> : 25/4/19
 */
@Data
public class AgentAccountMappings {
    private String accountIdentifier;
    private List<Tags> tags;

    public void validate() throws ControlCenterException {
        if (StringUtils.isEmpty(this.accountIdentifier)) throw new ControlCenterException("account Identifier can not be null or empty.");
        if (this.tags == null || this.tags.isEmpty()) throw new ControlCenterException("tags can not be null or empty.");
        for (Tags tag : this.tags) {
            tag.validate();
        }
    }
}
