package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActionDetails {
    private static final Logger LOGGER = LoggerFactory.getLogger(ActionDetails.class);

    private String name;
    private String identifier;
    private String standardType = Constants.ACTION_STANDARD_TYPE_CUSTOM;
    private String agentType = Constants.ACTION_AGENT_TYPE;
    private List<String> categories;
    private String executionType = Constants.ACTION_EXECUTION_TYPE_SCRIPT;
    private String downloadType = Constants.ACTION_DOWNLOAD_TYPE_PDF;
    private String commandExecutionType = Constants.ACTION_COMMAND_EXECUTION_TYPE_LONGPOLLING;
    private CommandDetails commandDetails;

    public boolean validate() {
        if (this.name == null || this.name.length() > 128 || this.name.length() < 1) {
            LOGGER.error(UIMessages.ACTION_INVALID_NAME);
            return false;
        }

        if (this.identifier != null && this.identifier.length() > 128) {
            LOGGER.error(UIMessages.INVALID_IDENTIFIER);
            return false;
        }

        if (!this.name.matches(Constants.ALLOWED_NAME_CHARACTERS)) {
            LOGGER.error(UIMessages.ALLOWED_NAME_CHARACTERS, "Action name");
            return false;
        }

        if (this.identifier != null && !this.identifier.matches(Constants.ALLOWED_IDENTIFIER_CHARACTERS)) {
            LOGGER.error(UIMessages.ALLOWED_IDENTIFIER_CHARACTERS, "Action identifier");
            return false;
        }

        if (this.commandDetails == null) {
            LOGGER.error(UIMessages.COMMAND_DETAILS_NULL);
            return false;
        }

        if (this.categories.isEmpty()) {
            LOGGER.error(UIMessages.ACTION_NO_CATEGORIES);
            return false;
        }

        return this.commandDetails.validate();
    }
}
