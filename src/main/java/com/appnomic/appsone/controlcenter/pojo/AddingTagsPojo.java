package com.appnomic.appsone.controlcenter.pojo;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AddingTagsPojo {

    private static final Logger LOGGER = LoggerFactory.getLogger(AddingTagsPojo.class);

    private String whomToTag;
    private String referenceId;
    private String tagValue;

    public boolean validate() {
        boolean retVal = true;

        if(this.whomToTag == null || this.whomToTag.trim().isEmpty()) {
            LOGGER.error("whomToTag to be tagged is invalid. It should be service");
            retVal = false;
        }

        if(this.referenceId == null || this.referenceId.trim().isEmpty()) {
            LOGGER.error("referenceId is invalid. Valid value is either the service identifier or service name");
            retVal = false;
        }

        if(this.tagValue == null || this.tagValue.trim().isEmpty()) {
            LOGGER.error("tagValue is invalid. tagValue should be 0 or 1");
            retVal = false;
        } else if (!this.tagValue.matches(Constants.ALLOWED_NAME_CHARACTERS)) {
            LOGGER.error(UIMessages.ALLOWED_NAME_CHARACTERS, "Tag value");
            return false;
        }

        return retVal;
    }
}
