package com.appnomic.appsone.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RequestedTransactionTypeEntity {
    private int id;
    private String name;
    private String identifier;
    private String rule;
    private int ruleId;
    private long requestCount;
    private long createdTime;
    private long lastModifiedOn;
    private String lastModifiedBy;
    private int status;

}
