package com.appnomic.appsone.controlcenter.pojo;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class TransactionConfig {

    private int id;
    private String txnId;
    private String txnName;
    private int monitorEnabled;
    private int auditEnabled;
    private int percentileEnabled;
    private String serviceIdentifier;
    private String serviceName;
    private List<String> appIdentifiers;
    private List<Map<String, String>> tags;
    private List<TxnKPIViolationConfig> txnKpiViolationConfig = new ArrayList<>();
    private Map<String, String> auditConfigurations = new HashMap<>();

}
