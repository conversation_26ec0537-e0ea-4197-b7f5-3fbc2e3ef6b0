package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> on 27/7/20
 */
@Data
public class RecurringBean {

    private int id;
    private String recurringType;
    private int recurringTypeId;
    private int day;
    private int week;
    private int month;
    private String weeknames;
    private String startHour;
    private String endHour;
    private long duration;
    private String recurringData;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private String userDetails;
    private DailyRecurringBean daily;
    private WeeklyRecurringBean weekly;
    private MonthlyRecurringBean monthly;


    private Map<String, String> error = new HashMap<>();

    private static final Logger logger = LoggerFactory.getLogger(MaintenanceWindowPojo.class);

    public void validateRecurring() {
        if (StringUtils.isEmpty(this.recurringType)) {
            logger.error(UIMessages.RECURRING_TYPE_EMPTY_ERROR);
            this.error.put("Maintenance Type", UIMessages.RECURRING_TYPE_EMPTY_ERROR);
        }
        if (StringUtils.isEmpty(this.startHour)) {
            logger.error(UIMessages.START_HOUR_EMPTY_ERROR);
            this.error.put("Maintenance Type", UIMessages.START_HOUR_EMPTY_ERROR);
        }
        if (StringUtils.isEmpty(this.endHour)) {
            logger.error(UIMessages.END_HOUR_EMPTY_ERROR);
            this.error.put("Maintenance Type", UIMessages.END_HOUR_EMPTY_ERROR);
        }
        if (!(this.day > -1)) {
            logger.error(UIMessages.DAY_EMPTY_ERROR);
            this.error.put("Maintenance Type", UIMessages.DAY_EMPTY_ERROR);
        }
        if (!(this.month > -1)) {
            logger.error(UIMessages.MONTH_EMPTY_ERROR);
            this.error.put("Maintenance Type", UIMessages.MONTH_EMPTY_ERROR);
        }
        if (!(this.week > -1)) {
            logger.error(UIMessages.WEEK_EMPTY_ERROR);
            this.error.put("Maintenance Type", UIMessages.WEEK_EMPTY_ERROR);
        }
    }
}