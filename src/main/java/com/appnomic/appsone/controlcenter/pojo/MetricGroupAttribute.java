package com.appnomic.appsone.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MetricGroupAttribute {

    private String value;
    private String aliasName;
    private int status;
    private int dataCollected;
    private int common;
    private int commonAliasName;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof MetricGroupAttribute)) {
            return false;
        }
        MetricGroupAttribute attribute = (MetricGroupAttribute) o;
        return status == attribute.getStatus() && dataCollected == attribute.getDataCollected() &&
                value.equals(attribute.getValue());
    }

    @Override
    public int hashCode() {
        return Objects.hash(value, status, dataCollected);
    }
}
