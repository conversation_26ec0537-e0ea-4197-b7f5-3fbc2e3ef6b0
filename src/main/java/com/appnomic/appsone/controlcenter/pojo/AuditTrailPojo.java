package com.appnomic.appsone.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuditTrailPojo {
    String applicationName ;
    String ServiceName;
    String activityType;
    String subActivityType;
    String operationType;
    String updatedBy;
    Long auditTime;
    Map<String, Map<String, String>> value;

    public AuditTrailPojo(AuditTrailPojo auditTrailPojo) {
        this.applicationName = auditTrailPojo.getApplicationName();
        ServiceName = auditTrailPojo.getServiceName();
        this.activityType = auditTrailPojo.getActivityType();
        this.subActivityType = auditTrailPojo.getSubActivityType();
        this.operationType = auditTrailPojo.getOperationType();
        this.updatedBy = auditTrailPojo.getUpdatedBy();
        this.auditTime = auditTrailPojo.getAuditTime();
        this.value = auditTrailPojo.getValue();
    }
}