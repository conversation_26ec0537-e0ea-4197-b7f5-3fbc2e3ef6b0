package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.ConstraintViolation;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Set;

@Data
public class AddComponentRequest {

    private static final Logger logger = LoggerFactory.getLogger(AddComponentRequest.class);

    @Size(min = 1, max = 32, message = "component name length must be less than or equal to 32")
    @NotNull(message = "componentName cannot be null")
    private String componentName;
    @NotNull(message = "type cannot be null")
    private String type;
    @Size(min = 1, max = 32, message = "component commonVersion length must be less than or equal to 32")
    @NotNull(message = "commonVersion cannot be null")
    private String commonVersion;
    @Size(min = 1, max = 128, message = "component version length must be less than or equal to 128")
    @NotNull(message = "version cannot be null")
    private String version;
    @Size(min = 1, max = 256, message = "component description length must be less than or equal to 256")
    @NotNull(message = "description cannot be null")
    private String description;
    private boolean custom = true;
    private List<ComponentAttribute> attributes;

    public boolean validate(){
        Set<ConstraintViolation<Object>> violations = ValidationUtils.validateFields(this);
        for (ConstraintViolation<Object> violation : violations) {
            logger.error(violation.getMessage());
        }
        if(!violations.isEmpty()){
            return false;
        }

        if (!this.componentName.matches(Constants.ALLOWED_NAME_CHARACTERS)) {
            logger.error(UIMessages.ALLOWED_NAME_CHARACTERS, "Component name");
            return false;
        }

        return !(attributes != null && attributes.stream()
                                                .anyMatch(attribute -> !attribute.validate()));
    }
}