package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.beans.ConnectionDetailsBean;
import lombok.Data;

import java.util.*;

@Data
public class ImportConnectionFile {
    private String fromServices;
    private String toService;
    private List<Tags> tags = new ArrayList<>();
    private List<Tags> compTags = new ArrayList<>();
    private List txnViolationConfigs = Collections.emptyList();
    private List<ConnectionDetailsBean> connections = new ArrayList<>();
    private Map<String, String> errorMessage = new HashMap<>();
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ImportConnectionFile)) return false;
        ImportConnectionFile that = (ImportConnectionFile) o;
        return Objects.equals(getFromServices(), that.getFromServices()) &&
                Objects.equals(getToService(), that.getToService());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getFromServices(), getToService());
    }

    public ImportConnectionFile(String fromServices, String toService) {
        this.fromServices=fromServices;
        this.toService=toService;

            if (toService != null && !toService.isEmpty()) {
                ConnectionDetailsBean connectionDetailsBean = new ConnectionDetailsBean();
                connectionDetailsBean.setSourceRefObject(fromServices);
                connectionDetailsBean.setDestinationRefObject(toService);
                connectionDetailsBean.setIsDiscovery(0);
                this.connections.add(connectionDetailsBean);
            }
    }


}