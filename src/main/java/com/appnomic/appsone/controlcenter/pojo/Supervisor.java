package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Supervisor {
    private final static Logger LOGGER = LoggerFactory.getLogger(Supervisor.class);

    private int id;
    private String supervisorId;
    private String name;
    private String supervisorTypeName;
    private String hostAddress;
    private String hostBoxName;
    private String version;
    private String mode;
    private boolean status;

    public String validate() {
        StringBuilder strBuilder = new StringBuilder();

        if(name == null || name.trim().isEmpty() || name.trim().length() > 128) {
            strBuilder.append("supervisorName");
            LOGGER.error("Supervisor name is invalid. Reason: It is either NULL, empty or has length greater than 128 characters");
        }

        if (this.name != null && !this.name.trim().isEmpty() && !this.name.matches(Constants.ALLOWED_NAME_CHARACTERS)) {
            strBuilder.append(strBuilder.toString().isEmpty() ? "supervisorNameCharacters" : ", supervisorNameCharacters");
            LOGGER.error(UIMessages.ALLOWED_NAME_CHARACTERS, "Supervisor name");
        }

        if(hostBoxName == null || hostBoxName.trim().isEmpty() || hostBoxName.trim().length() > 64) {
            strBuilder.append(strBuilder.toString().isEmpty() ? "hostBoxName" : ", hostBoxName");
            LOGGER.error("Supervisor host box name is invalid. Reason : It is either NULL or empty or has length greater than 64 characters");
        }

        if(supervisorId != null && (supervisorId.trim().isEmpty() || supervisorId.trim().length() > 128)) {
            strBuilder.append(strBuilder.toString().isEmpty() ? "supervisorIdentifier" : ", supervisorIdentifier");
            LOGGER.error("Supervisor identifier is invalid. Reason: It is either empty or has length greater than 128 characters");
        }

        if (this.supervisorId != null && !this.supervisorId.trim().isEmpty() && !this.supervisorId.matches(Constants.ALLOWED_IDENTIFIER_CHARACTERS)) {
            strBuilder.append(strBuilder.toString().isEmpty() ? "Supervisor identifier characters" : ", Supervisor identifier characters");
            LOGGER.error(UIMessages.ALLOWED_NAME_CHARACTERS, "Supervisor identifier");
        }

        if(supervisorTypeName == null || supervisorTypeName.trim().isEmpty()) {
            strBuilder.append(strBuilder.toString().isEmpty() ? "supervisorTypeName" : ", supervisorTypeName");
            LOGGER.error("Supervisor type is invalid. Reason: It is either NULL or empty");
        }

        if(version == null || version.trim().isEmpty() || version.trim().length() > 32) {
            strBuilder.append(strBuilder.toString().isEmpty() ? "version" : ", version");
            LOGGER.error("Supervisor version is invalid. Reason: It is either NULL or empty or has length greater than 32 characters");
        }

        if(hostAddress == null || hostAddress.trim().isEmpty() || hostAddress.trim().length() > 64) {
            strBuilder.append(strBuilder.toString().isEmpty() ? "hostAddress" : ", hostAddress");
            LOGGER.error("Supervisor host address is invalid. Reason: It is either NULL or empty or has length greater than 64 characters");
        }

        if (mode != null
                && ((mode.trim().isEmpty() || mode.trim().length() > 64)
                || (!Constants.SUPERVISOR_REMOTE_MODE.equalsIgnoreCase(mode)
                && !Constants.SUPERVISOR_LOCAL_MODE.equalsIgnoreCase(mode)))) {
            strBuilder.append(strBuilder.toString().isEmpty() ? "mode" : ", mode");
            LOGGER.error("Supervisor mode of execution if invalid. Reason: If defined, it should be either LOCAL or REMOTE");
        }

        if(mode == null) {
            mode = Constants.SUPERVISOR_LOCAL_MODE;
        }

        return strBuilder.toString();
    }

    public String validateForUpdate() {
        StringBuilder strBuilder = new StringBuilder();

        if(supervisorId != null) {
            LOGGER.warn("Supervisor identifier provided in update request will be ignored.");
        }

        if(name != null && (name.trim().isEmpty() || name.trim().length() > 128)) {
            LOGGER.error("Supervisor name is invalid. Reason: It is either NULL, empty or has length greater than 128 characters");
            strBuilder.append("name");
        }

        if(hostBoxName != null && (hostBoxName.trim().isEmpty() || hostBoxName.trim().length() > 64)) {
            LOGGER.error("Supervisor host box name is invalid. Reason : It is either NULL or empty or has length greater than 64 characters");
            strBuilder.append(strBuilder.toString().isEmpty() ? "hostBoxName" : ", hostBoxName");
        }

        if(hostAddress != null && (hostAddress.trim().isEmpty() || hostAddress.trim().length() > 64)) {
            LOGGER.error("Supervisor host address is invalid. Reason: It is either NULL or empty or has length greater than 64 characters");
            strBuilder.append(strBuilder.toString().isEmpty() ? "hostAddress" : ", hostAddress");
        }

        if (mode != null
                && ((mode.trim().isEmpty() || mode.trim().length() > 64)
                || (!Constants.SUPERVISOR_REMOTE_MODE.equalsIgnoreCase(mode)
                && !Constants.SUPERVISOR_LOCAL_MODE.equalsIgnoreCase(mode)))) {
            LOGGER.error("Supervisor mode of execution if invalid. Reason: If defined, it should be either LOCAL or REMOTE");
            strBuilder.append(strBuilder.toString().isEmpty() ? "mode" : ", mode");
        }

        if(supervisorTypeName != null && supervisorTypeName.trim().isEmpty()) {
            LOGGER.error("Supervisor type is invalid. Reason: It is either NULL or empty");
            strBuilder.append(strBuilder.toString().isEmpty() ? "supervisorTypeName" : ", supervisorTypeName");
        }

        if(version != null && version.trim().isEmpty()) {
            LOGGER.error("Supervisor version is invalid");
            strBuilder.append(strBuilder.toString().isEmpty() ? "version" : ", version");
        }

        return strBuilder.toString();
    }
}
