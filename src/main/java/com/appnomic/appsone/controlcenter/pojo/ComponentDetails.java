package com.appnomic.appsone.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ComponentDetails {

    private int id;
    private String name;
    private Set<IdPojo> componentTypes;
    private Set<Version> commonVersions;


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Version {
        private int id;
        private String version;
    }

}
