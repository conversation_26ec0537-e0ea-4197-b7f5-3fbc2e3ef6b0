package com.appnomic.appsone.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.utils.CollectionUtils;
import java.util.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AgentCompInstanceMaping {
    private int instanceId;
    private List<Integer> addedAgentIds;
    private List<Integer> deletedAgentIds;
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentCompInstanceMaping.class);

    public boolean validate() {
        boolean retVal = true;

        if (instanceId < 1) {
            LOGGER.error("Instance Id cannot be less than 1.");
            retVal = false;
        }

        if(!CollectionUtils.isNotEmpty(addedAgentIds) && !CollectionUtils.isNotEmpty(deletedAgentIds)) {
            LOGGER.error("Agent mapping details does not exist.");
            retVal = false;
        }

        if(CollectionUtils.isNotEmpty(addedAgentIds) && addedAgentIds.size() != new HashSet<>(addedAgentIds).size()) {
            LOGGER.error("Agent mapping details contain duplicate agent Ids.");
            retVal = false;
        }

        if(CollectionUtils.isNotEmpty(deletedAgentIds) && deletedAgentIds.size() != new HashSet<>(deletedAgentIds).size()) {
            LOGGER.error("Agent mapping details contain duplicate agent Ids.");
            retVal = false;
        }

        return retVal;
    }
}
