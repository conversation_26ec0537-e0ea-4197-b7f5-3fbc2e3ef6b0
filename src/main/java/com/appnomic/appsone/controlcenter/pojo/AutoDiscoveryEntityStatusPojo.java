package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.common.autodiscovery.Entity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor

public class AutoDiscoveryEntityStatusPojo {
    private String[] identifiers;
    private Integer isIgnored;
    private String ignoredBy;
    private Entity entityType;
    private Map<String, String> errorMessages = new HashMap<>();

    public boolean validateMandatoryFields() {
        boolean pass = true;

        if (identifiers == null || identifiers.length == 0) {
            log.error("Identifiers cannot be null or empty.");
            pass = false;
        }
        if (isIgnored == null) {
            log.error("isIgnored value cannot be null or empty.");
            pass = false;
        } else if (isIgnored != 0 && isIgnored != 1) {
            log.error("Invalid is_ignored input. Provided- {}", isIgnored);
            pass = false;
        }
        if (entityType != Entity.Host && entityType != Entity.CompInstance && entityType != null) {
            log.error("Entity type incompatible. Provided- {}", entityType);
            pass = false;
        }
        return pass;
    }
}
