package com.appnomic.appsone.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ObjPojo
{
    private static final Logger LOGGER = LoggerFactory.getLogger(ObjPojo.class);

    private int id;
    private String name;

}
