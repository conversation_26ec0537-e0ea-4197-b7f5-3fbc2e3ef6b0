package com.appnomic.appsone.controlcenter.pojo.agentconfig;

import com.appnomic.appsone.controlcenter.pojo.CollectionAgentParam;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class AgentConfig {

    private int id;
    private int physicalAgentId;
    private int supervisorId;
    private String agentId;
    private String agentName;
    private String physicalAgentIdentifier;
    private String agentMode;
    private int status;
    private int agentTypeId;
    private String agentTypeName;
    private String configOperationMode;
    private String dataOperationMode;
    private CollectionAgentParam collectionAgentParam;
    private List<Map<String, String>> tags;
    private List<Integer> compInstanceIds;
    private String hostNameOrIp;
    private String serviceName;
    private String version;
    private int communicationInterval;
    private int forensicEnabled;    //TODO: Add the forensic enabled column forensicEnabled
}
