package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.exceptions.ServiceConfigDetailsException;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ServiceConfigurationBean;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceConfigDetails {
    private int serviceConfigId;
    private int sorPersistence;
    private int sorSuppression;
    private int norPersistence;
    private int norSuppression;

    public void checkIfServiceConfigExists(List<ServiceConfigurationBean> list) throws ServiceConfigDetailsException {
        if (list.stream().noneMatch(s -> s.getId() == this.getServiceConfigId())) {
            throw new ServiceConfigDetailsException("Invalid ServiceConfigId : " + this.getServiceConfigId());
        }
    }

    public void validate() throws ServiceConfigDetailsException {
        if (this.getSorPersistence() < 1 || this.getSorPersistence() > 120) {
            throw new ServiceConfigDetailsException("Persistence should be in range 1-120. Invalid value : " + this.getSorPersistence());
        }
        if (this.getSorSuppression() < 1 || this.getSorSuppression() > 120) {
            throw new ServiceConfigDetailsException("Suppression should be in range 1-120. Invalid value : " + this.getSorSuppression());
        }
    }
}



