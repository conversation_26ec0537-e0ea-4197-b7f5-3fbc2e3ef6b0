package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import lombok.Data;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Data
public class AddRulesPojo {
    private String createdTime;
    private String updatedTime;
    private String userDetails;
    private String ruleName;
    private String ruleType;
    private String httpMethodType = "GET";
    private String segmentURIType;
    private String segmentValue;
    private int firstUriSegments;
    private int lastUriSegments;
    private boolean completeURI;
    private String payloadType = "Form Data";
    private String completePattern;
    private String customSegments;
    private int id;
    private int isDefault;
    private int order;
    private String serviceId;
    private int accountId;

    private Integer monitorEnabled;

    private Integer discoveryEnabled;
    private Map<String, String> queryParameters;
    private Map<String, String> payloadParameters;
    private Map<String, String> httpHeaderParameters;
    private Map<String, String> error = new HashMap<>();
    private static final String RULE_NAME_STRING = "ruleName";
    Set<String> addDiscoveryTags = new HashSet<>();
    Set<String> removeDiscoveryTags = new HashSet<>();
    private String userDetailsId;

    public void validate() {
        if (StringUtils.isEmpty(this.ruleName)) {
            error.put(RULE_NAME_STRING, "Rule Name can not be empty or null");
        }

        if (!this.ruleName.matches(Constants.ALLOWED_NAME_CHARACTERS)) {
            error.put(RULE_NAME_STRING, "Rule name");
        }

        if(ruleName.trim().length() < 2) {
            error.put(RULE_NAME_STRING, "Rule name should be minimum 2 characters.");
        } else if(ruleName.trim().length() > 64) {
            error.put(RULE_NAME_STRING, "Rule name should not exceed more than 64 characters.");
        }
        if (StringUtils.isEmpty(this.ruleType)) {
            error.put("ruleType", "Rule Type can not be empty or null");
        }
        if (StringUtils.isEmpty(this.payloadType)) {
            error.put("payloadType", "payload Type can not be empty or null");
        }
        if (StringUtils.isEmpty(this.httpMethodType)) {
            error.put("httpMethodType", "http method Type can not be empty or null");
        }
        if (this.segmentURIType  == null || this.segmentURIType.length() < 1) {
                error.put("segmentURIType", "segment uri type can not be empty or null");
        }

        if (this.segmentValue == null || this.segmentValue.length() < 1) {
                error.put("segmentValue", "segment value can not be empty or null");
        }
        if (this.queryParameters != null && this.queryParameters.size() < 1)
            error.put("queryParameters", "query param  can not be empty or null");
        if (this.payloadParameters != null && this.payloadParameters.size() < 1)
            error.put("payloadParameters", "payload param  can not be empty or null");
        if (this.httpHeaderParameters != null && this.httpHeaderParameters.size() < 1)
            error.put("httpHeaderParameters", "http header param can not be empty or null");
    }

    public String getUniquePatter() {

        StringBuilder completePatternObject = new StringBuilder();
        completePatternObject.append(getRuleType());
        completePatternObject.append(",");
        completePatternObject.append(getSegmentURIType());
        completePatternObject.append(",");
        completePatternObject.append(getSegmentValue());
        completePatternObject.append(",");
        completePatternObject.append(getQueryParameters());
        completePatternObject.append(",");
        completePatternObject.append(getPayloadType());
        completePatternObject.append(",");
        completePatternObject.append(getPayloadParameters());
        completePatternObject.append(",");
        completePatternObject.append(getHttpMethodType());
        completePatternObject.append(",");
        completePatternObject.append(getHttpHeaderParameters());

        return completePatternObject.toString();
    }
}
