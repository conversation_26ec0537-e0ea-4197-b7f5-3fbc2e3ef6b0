package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.util.StringUtils;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Data
public class AgentTriggerStatusPojo {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentTriggerStatusPojo.class);

    private int physicalAgentId;
    private String commandJobId;

    public boolean validate() {
        boolean retVal = true;
        if (this.physicalAgentId <= 0) {
            LOGGER.error("Invalid Physical AgentId [{}] found.", this.physicalAgentId);
            retVal = false;
        }
        if (this.commandJobId == null || StringUtils.isEmpty(this.commandJobId)) {
            LOGGER.error("Invalid commandJobId [{}] found.", this.commandJobId);
            retVal = false;
        }
        return retVal;
    }
}
