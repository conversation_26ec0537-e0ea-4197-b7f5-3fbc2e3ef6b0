package com.appnomic.appsone.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class InstKpiAttrPersistSuppressDetails {

    private int kpiId;
    private int groupKpiId;
    private String attributeValue;
    private Integer persistence;
    private Integer suppression;
    private ActionsEnum action;

    public boolean validate(boolean addingConfig) {
        boolean retVal = true;

        if(kpiId <= 0) {
            log.error("kpiId validation failure. It should be a positive integer");
            retVal = false;
        }

        if(addingConfig && persistence < 0) {
            log.error("sorPersistence validation failure. It should be a positive integer");
            retVal = false;
        }

        if(!addingConfig && persistence != null && persistence < 0) {
            log.error("sorPersistence validation failure. It should be a positive integer");
            retVal = false;
        }

        if(addingConfig && suppression < 0) {
            log.error("sorSuppression validation failure. It should be a positive integer");
            retVal = false;
        }

        if(!addingConfig && suppression != null && suppression < 0) {
            log.error("sorSuppression validation failure. It should be a positive integer");
            retVal = false;
        }

        if(attributeValue == null || attributeValue.trim().isEmpty() || attributeValue.trim().length() > 512) {
            log.error("attributeValue validation failure. Reason: It should be a non-NULL, non-empty string with length lesser than or equal to 512 characters");
            retVal = false;
        }

        if(groupKpiId == 0 && (attributeValue != null && !attributeValue.equals("ALL"))) {
            log.error("attributeValue validation failure. Reason: It should be 'ALL' for non-group KPI");
            retVal = false;
        }

        if(!addingConfig && action == null) {
            log.error("Update action validation failure. Reason: It should be either ADD, MODIFY or DELETE.");
            retVal = false;
        }

        return retVal;
    }
}
