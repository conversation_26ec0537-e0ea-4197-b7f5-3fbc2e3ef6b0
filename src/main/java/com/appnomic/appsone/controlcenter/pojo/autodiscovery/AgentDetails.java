package com.appnomic.appsone.controlcenter.pojo.autodiscovery;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentDetails {

    private int agentId;
    private String agentName;
    private String agentIdentifier;
    private int physicalAgentId;
    private String physicalAgentIdentifier;
    private int agentTypeId;
    private String agentTypeName;
    private int status;

}
