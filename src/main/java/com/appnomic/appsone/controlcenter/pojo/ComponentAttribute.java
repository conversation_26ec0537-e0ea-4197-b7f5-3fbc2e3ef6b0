package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.ConstraintViolation;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComponentAttribute {

    private static final Logger logger = LoggerFactory.getLogger(ComponentAttribute.class);

    @Size(min = 1, max = 64, message = "attribute name length must be less than or equal to 64")
    @NotNull(message = "attribute name cannot be null")
    private String name;
    @Size(min = 1, max = 64, message = "attribute type length must be less than or equal to 64")
    @NotNull(message = "attribute type cannot be null")
    private String type;
    @Size(max = 64, message = "attribute defaultValue length must be less than or equal to 64")
    private String defaultValue;
    private boolean mandatory;
    @Max(Integer.MAX_VALUE)
    private int maxLength;
    @Max(Integer.MAX_VALUE)
    private int minLength;
    @Size(max = 512, message = "attribute regex length must be less than 512")
    private String regex;

    public boolean validate(){
        Set<ConstraintViolation<Object>> violations = ValidationUtils.validateFields(this);
        for (ConstraintViolation<Object> violation : violations) {
            logger.error(violation.getMessage());
        }
        if(!violations.isEmpty()){
            return false;
        }
        if (!this.getName().matches(Constants.ALLOWED_NAME_CHARACTERS)) {
            logger.error(UIMessages.ALLOWED_NAME_CHARACTERS, "Attribute name");
            return false;
        }

        if(maxLength < 0 || minLength < 0){
            logger.error("maxLength or minLength cannot be negative");
            return false;
        }
        return true;
    }

}
