package com.appnomic.appsone.controlcenter.pojo;

import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class KpiAttributeThresholdInfo {

    private int kpiId;
    private int groupKpiId;
    private String attributeValue;
    private String dataType;
    private String unit;
    private ThresholdDetails operation;
    private Severity severity;
    private Status status;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ThresholdDetails {
        private int common;
        private String value;
        private Double minThreshold;
        private Double maxThreshold;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Severity {
        private int common;
        private int value;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Status {
        private int common;
        private int value;
    }
}
