package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.common.UIMessages;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;

@Data
public class RequestObject {

    private String body;
    private Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
    private Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
    private Map<String,String[]> queryParams = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
    private String httpType;

    private static final Logger LOGGER = LoggerFactory.getLogger(RequestObject.class);

    public RequestObject(Request request) {
        if (request == null) {
            LOGGER.error(UIMessages.REQUEST_NULL);
            return;
        }

        try {

            this.httpType = request.requestMethod();
            byte[] bodyBytes = request.bodyAsBytes();
            if (bodyBytes == null || bodyBytes.length == 0) {
                LOGGER.trace("Request body is empty. for Request method: [{}], API: [{}]", this.httpType,request.pathInfo());
            } else {
                LOGGER.trace("Request method: {}", this.httpType);
                int length = request.bodyAsBytes().length;
                LOGGER.trace("Request body size: {} bytes", length);
                this.body = request.body();
                LOGGER.trace("Request body: {}, for API: [{}]", this.body, request.pathInfo());
            }

            this.params.putAll(request.params());
            if(request.queryMap() != null && request.queryMap().toMap() != null) {
                this.queryParams = new HashMap<>(request.queryMap().toMap());
            }
            Objects.requireNonNull(request.headers()).forEach(header -> this.headers.put(header, request.headers(header)));
        } catch (Exception e) {
            LOGGER.error("Error occurred while reading body from request [{}]", request.pathInfo(), e);
        }
    }
}