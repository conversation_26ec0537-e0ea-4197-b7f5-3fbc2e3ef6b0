package com.appnomic.appsone.controlcenter.pojo;

public enum TransactionDiscoveryStatus {
    DISCARDED_ALWAYS(0),
    ACCEPTED(1),
    DISCOVERED(2),
    DISCARDED(3);

    private final int status;

    TransactionDiscoveryStatus(int status) {
        this.status = status;
    }

    public static int getValueForStatus(String status) {
        switch (status.toUpperCase()) {
            case "DISCARD":
                return DISCARDED.getStatus();
            case "ACCEPT":
                return ACCEPTED.getStatus();
            case "DISCOVER":
                return DISCOVERED.getStatus();
            case "DISCARD-ALWAYS":
                return DISCARDED_ALWAYS.getStatus();
            case "DISCOVERED":
                return DISCOVERED.getStatus();
            case "DISCARDED":
                return DISCARDED.getStatus();
            default:
                return -1;
        }
    }

    public int getStatus() {
        return this.status;
    }
}
