package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.quartz.CronExpression;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddKpiRequest {

    private static final Logger LOGGER = LoggerFactory.getLogger(AddKpiRequest.class);

    private int custom = 1;
    private int enableAnalytics;
    private int collectionIntervalSeconds;
    private String kpiName;
    private String kpiIdentifier;
    private String description;
    private String dataType;
    private String kpiType;
    private String clusterOperation;
    private String rollupOperation;
    private String clusterAggregation;
    private String instanceAggregation;
    private String measureUnits;
    private String groupKpiIdentifier;
    private String componentName;
    private String componentVersion;
    private String componentType;
    private String categoryName;
    private String cronExpression;
    private int deltaPerSec=1;
    private int resetDeltaValue=1;

    public boolean isValid() {
        boolean retVal = true;

        if (!validateIntValues()) {
            retVal = false;
        }

        if (!validateClusterInstOpsAgg()) {
            retVal = false;
        }

        if (!isKpiTypeDataTypeValid()) {
            retVal = false;
        }

        if (!validateComponentAndCategory()) {
            retVal = false;
        }

        if(!validateStringParameters()) {
            retVal = false;
        }

        if(cronExpression != null && !cronExpression.trim().isEmpty()) {
            try {
                new CronExpression(cronExpression);
            } catch (Exception e) {
                LOGGER.error("Cron expression is invalid. Details: ", e);
                retVal = false;
            }
        }

        return retVal;
    }

    private boolean validateStringParameters() {
        boolean retVal = true;

        if (StringUtils.isEmpty(kpiName) || kpiName.trim().length() > 128) {
            LOGGER.error("KpiName validation failure. Reason: kpiName is NULL or empty or its length is greater than 128 characters.");
            retVal = false;
        }


        if (!this.kpiName.matches(Constants.ALLOWED_NAME_CHARACTERS)) {
            LOGGER.error(UIMessages.ALLOWED_NAME_CHARACTERS, "KPI");
            return false;
        }

        if(null != kpiIdentifier) {
            kpiIdentifier = kpiIdentifier.trim();
            if (kpiIdentifier.isEmpty() || kpiIdentifier.length() > 128) {
                LOGGER.error("Alias name validation failure. Reason: aliasName is  or its length is greater than 128 characters.");
                retVal = false;
            }

            if (this.kpiIdentifier != null && !this.kpiIdentifier.matches(Constants.ALLOWED_IDENTIFIER_CHARACTERS)) {
                LOGGER.error(UIMessages.ALLOWED_IDENTIFIER_CHARACTERS, "KPI");
                return false;
            }
        } else {
            LOGGER.info("kpiIdentifier not provided in the request. It will be auto-generated.");
        }

        if (StringUtils.isEmpty(groupKpiIdentifier) || groupKpiIdentifier.trim().length() > 64) {
            LOGGER.error("groupKpiIdentifier validation failure. Reason: groupKpiIdentifier is NULL or empty or its length is greater than 64 characters.");
            retVal = false;
        }

        if(StringUtils.isEmpty(description) || description.length() > 256) {
            LOGGER.error("Description validation failure. Reason: description is NULL or empty or its length is greater than 256 characters.");
            retVal = false;
        }

        if (!this.description.matches(Constants.ALLOWED_NAME_CHARACTERS)) {
            LOGGER.error(UIMessages.ALLOWED_NAME_CHARACTERS, "KPI description");
            return false;
        }

        if(StringUtils.isEmpty(measureUnits) || measureUnits.trim().length() > 16) {
            LOGGER.error("measurementUnits validation failure. " +
                    "Reason: measurementUnits is either NULL, empty or its length is greater than 16 characters.");
            retVal = false;
        }
        return retVal;
    }

    private boolean validateIntValues() {
        boolean retVal = true;

        if(enableAnalytics != 1 && enableAnalytics != 0) {
            LOGGER.error("enableAnalytics validation failure. Reason: enableAnalytics provided is [{}]. " +
                    "It should be either 0 r 1.", enableAnalytics);
            retVal = false;
        }

        if(collectionIntervalSeconds <= 0 || collectionIntervalSeconds%60 != 0) {
            LOGGER.error("collectionIntervalSeconds validation failure. Reason: collectionIntervalSeconds " +
                    "provided is [{}]. It should be a multiple of 60.", collectionIntervalSeconds);
            retVal = false;
        }

        if(custom != 0 && custom != 1) {
            LOGGER.error("custom validation failure. Reason: custom provided is [{}]. It should be either 0 or 1.", custom);
            retVal = false;
        }

        return retVal;
    }

    private boolean isKpiTypeDataTypeValid() {
        boolean retVal = true;

        if (StringUtils.isEmpty(kpiType)) {
            LOGGER.error("KpiType validation failure. Reason: kpiType is either NULL or empty. " +
                    "It should be one of Availability, Core, FileWatch or ConfigWatch.");
            retVal = false;
        }

        if(StringUtils.isEmpty(dataType)) {
            LOGGER.error("dataType validation failure. Reason: dataType is either NULL or empty. " +
                    "It should be one of Integer, Float or Text.");
            retVal = false;
        }
        return retVal;
    }

    private boolean validateClusterInstOpsAgg() {
        boolean retVal = true;

        if (StringUtils.isEmpty(clusterOperation)) {
            LOGGER.error("clusterOperation validation failure. Reason: clusterOperation is either NULL or empty. " +
                    "It should be one of Sum, Average or None.");
            retVal = false;
        }

        if (StringUtils.isEmpty(rollupOperation)) {
            LOGGER.error("rollupOperation validation failure. Reason: rollupOperation is either NULL or empty. " +
                    "It should be one of Sum, Average or None.");
            retVal = false;
        }

        if (StringUtils.isEmpty(clusterAggregation)) {
            LOGGER.error("clusterAggregation validation failure. Reason: clusterAggregation is either NULL or empty." +
                    "It should be one of SingleValue, MultiValue or None.");
            retVal = false;
        }

        if (StringUtils.isEmpty(instanceAggregation)) {
            LOGGER.error("instanceAggregation validation failure. Reason: instanceAggregation is either NULL or empty." +
                    "It should be one of SingleValue, MultiValue or None.");
            retVal = false;
        }
        return retVal;
    }

    private boolean validateComponentAndCategory() {
        boolean retVal = true;

        if(StringUtils.isEmpty(componentName) || componentName.trim().length() > 45) {
            LOGGER.error("componentName validation failure. " +
                    "Reason: componentName is either NULL, empty or its length is greater than 45 characters.");
            retVal = false;
        }

        if(StringUtils.isEmpty(componentVersion) || componentVersion.trim().length() > 45) {
            LOGGER.error("componentVersion validation failure. " +
                    "Reason: componentVersion is either NULL, empty or its length is greater than 45 characters.");
            retVal = false;
        }

        if(StringUtils.isEmpty(categoryName) || categoryName.trim().length() > 128) {
            LOGGER.error("categoryName validation failure. " +
                    "Reason: categoryName is either NULL, empty or its length is greater than 128 characters.");
            retVal = false;
        }

        if(StringUtils.isEmpty(componentType) || componentType.trim().length() > 45) {
            LOGGER.error("componentType validation failure. " +
                    "Reason: componentType is either NULL, empty or its length is greater than 45 characters.");
            retVal = false;
        }

        return retVal;
    }
}
