package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommandDetails {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommandDetails.class);

    private String name;
    private String identifier;
    private String commandName;
    private int timeOutInSecs = Constants.DEFAULT_TIMEOUT_IN_SECS;
    private String producerType = Constants.SCRIPT;
    private List<CommandDetailArgument> commandArguments;
    private String outputType = Constants.MST_SUB_TYPE_BLOB;


    public boolean validate(){
        if(this.name == null || this.name.length() < 1 || this.name.length()>128) {
            LOGGER.error(UIMessages.COMMAND_NAME_INVALID,this.name);
            return false;
        }
        if (this.identifier != null && this.identifier.length() > 128) {
            LOGGER.error("Command {} {}",UIMessages.INVALID_IDENTIFIER ,this.identifier);
            return false;
        }
        if (!this.name.matches(Constants.ALLOWED_NAME_CHARACTERS)) {
            LOGGER.error(UIMessages.ALLOWED_NAME_CHARACTERS, "Command name");
            return false;
        }

        if (this.identifier != null && !this.identifier.matches(Constants.ALLOWED_IDENTIFIER_CHARACTERS)) {
            LOGGER.error(UIMessages.ALLOWED_IDENTIFIER_CHARACTERS, "Command identifier");
            return false;
        }

        if(this.commandName==null || this.commandName.length()<1 || this.commandName.length()>128) {
            LOGGER.error("Invalid commandName : {} ",this.commandName);
            return false;
        }
        if(!(this.commandArguments==null || this.commandArguments.isEmpty())) {
            for (CommandDetailArgument a : this.commandArguments
            ) {
                if (!a.validate())
                    return false;
            }
        }
        return true;
    }
}