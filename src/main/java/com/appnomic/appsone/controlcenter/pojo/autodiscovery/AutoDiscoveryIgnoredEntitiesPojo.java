package com.appnomic.appsone.controlcenter.pojo.autodiscovery;

import com.appnomic.appsone.controlcenter.common.autodiscovery.Entity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

@Data

public class AutoDiscoveryIgnoredEntitiesPojo {
    private String name;
    private String identifier;
    private List<String> hostAddress;
    private List<String> discoveredEntities;
    private Entity entityType;
    private long lastDiscoveryRunTime;
    private long lastUpdatedTime;
    private String ignoredBy;
}
