package com.appnomic.appsone.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceInstanceAttributeBean {
    private int attributeId;
    private String attributeValue;
    private int serviceInstanceId;
    private String collectionInterval;
    private int componentAttributeMappingId;
    private String createdTime;
    private String updatedTime;
    private String userDetailsId;
    private int mstCommonAttributesId;
    private String attributeName;
}
