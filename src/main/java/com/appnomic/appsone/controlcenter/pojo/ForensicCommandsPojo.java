package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.beans.CommandArgumentsBean;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ForensicCommandsPojo {
    private int commandId;
    private String agentType;
    private String agentIdentifier;
    private String serviceIdentifier;
    private String commandIdentifier;
    private String compInstanceIdentifier;
    private Map<String, String> argumentsMap;
    // name, value, placeholder -true/false, default value
    private List<CommandArgumentsBean> commandArguments;
    private Map<String, String> metaData;
}
