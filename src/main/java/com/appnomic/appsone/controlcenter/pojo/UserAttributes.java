package com.appnomic.appsone.controlcenter.pojo;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class UserAttributes {
    private int userId;
    private String userIdentifier;
    private String contactNumber;
    private String emailAddress;
    private String userDetailsId;
    private String profileName;
    private String roleName;
    private boolean status;
    private Timestamp updatedTime;
}
