package com.appnomic.appsone.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> on 09/06/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TransactionStaticThresholds {

    private Integer dataId;
    private String responseTimeType;
    private String kpiId;
    private String kpiName;
    private int categoryId;
    private String categoryName;
    private String kpiDataType;
    private String kpiUnit;
    private String kpiLevel;
    private String kpiAttribute;
    private String userDefinedOperationType;
    private String systemOperationType;
    private boolean generateAnomaly;
    private boolean userDefinedSOR;
    private Map<String, Double> systemThresholds;
    private Map<String, Double> userThresholds;
    private boolean severe;
    private Integer persistence;
    private Integer suppression;
    private boolean excludeMaintenance;

}
