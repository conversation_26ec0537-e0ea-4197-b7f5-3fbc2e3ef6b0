package com.appnomic.appsone.controlcenter.pojo.connectors;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Comparator;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkerParameter {
    private int id;
    private int chainWorkerId;
    private String name;
    private String value;

    public WorkerParameter(int chainWorkerId, String name, String value) {
        this.chainWorkerId = chainWorkerId;
        this.name = name;
        this.value = value;
    }

    public static final Comparator<WorkerParameter> comparator = new Comparator<WorkerParameter>() {
        @Override
        public int compare(WorkerParameter workerParameter, WorkerParameter workerParameter2) {
            return Integer.valueOf(workerParameter.getChainWorkerId()).compareTo(Integer.valueOf(workerParameter2.getChainWorkerId()));
        }
    };
}
