package com.appnomic.appsone.controlcenter.pojo;

import java.util.HashMap;
import java.util.Map;

public enum InstanceType {
    INSTANCES("instances"),
    CLUSTER("clusters");

    private String configDBName;
    private static final Map<String, InstanceType> reverseLookupMap = new HashMap<>();
    static {
        for (InstanceType vio : InstanceType.values()) {
            reverseLookupMap.put(vio.getConfigDBName(), vio);
        }
    }
    InstanceType(String configDBName) {
        this.configDBName = configDBName;

    }

    public String getConfigDBName() {
        return configDBName;
    }
}
