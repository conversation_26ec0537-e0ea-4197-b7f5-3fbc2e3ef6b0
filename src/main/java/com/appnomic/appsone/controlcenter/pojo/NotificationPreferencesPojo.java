package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

@Data
public class NotificationPreferencesPojo {
    private int applicationId;
    private String applicationName;
    private int signalTypeId;
    private String signalType;
    private int severityTypeId;
    private int notificationTypeId;
    private String severityType;

    @JsonIgnore
    private Map<String, String> error = new HashMap<>();

    private static final Logger logger = LoggerFactory.getLogger(NotificationPreferencesPojo.class);


    public void validate() {
        if (applicationId == 0)
            error.put("applicationId", "applicationId is empty");
        if (StringUtils.isEmpty(signalType))
            error.put("signalType", "signalType is empty");
        if (signalTypeId == 0)
            error.put("signalTypeId", "signalTypeId is empty");
        if (severityTypeId == 0)
            error.put("severityTypeId", "severityTypeId is empty");
        if (StringUtils.isEmpty(severityType))
            error.put("severityType", "severityType is empty");
        if (notificationTypeId == 0)
            error.put("notificationTypeId", "notificationTypeId is empty");
    }

    @Override
    public int hashCode() {
        return 1;
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof NotificationPreferencesPojo))
            return false;

        NotificationPreferencesPojo npp = (NotificationPreferencesPojo) obj;
        return (npp.applicationId == applicationId) && (npp.signalTypeId == signalTypeId) && (npp.severityTypeId == severityTypeId);
    }
}
