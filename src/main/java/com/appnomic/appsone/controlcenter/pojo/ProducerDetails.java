package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.ConstraintViolation;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

@Data
public class ProducerDetails {
    private static final Logger logger = LoggerFactory.getLogger(ProducerDetails.class);

    @Size(min = 1, max = 45, message = "producer name length must be less than or equal to 45")
    @Pattern(regexp = "^[a-zA-Z0-9_.\\-, ]+$", message = "Invalid producer naming convention used.")
    private String name;

    @Size(min = 1, max = 256, message = "producer desc length must be less than or equal to 256")
    private String description;
    private String kpiType;
    private int isGroupKpi;
    private String producerType;
    private Map<String, String> producerAttributes = new HashMap<>();
    private int isDeprecated;  //default is 0 hence un-initialized
    private String version = "1.0";
    private int isCustom = 1;  //default is 1
    private int status = 1; //default is 1
    private List<ProducerKpiMappingDetails> kpiMapping = new ArrayList<>();
    private List<ProducerParameterDetails> parameters = new ArrayList<>();

    public boolean isValid() {
        if (!validateMandatoryFields() || !validateAcceptableInputLength()) {
            return false;
        }

        AtomicBoolean validity = new AtomicBoolean(true);

        this.getKpiMapping().forEach(kpi -> {
            if (!kpi.isValid()) {
                validity.set(false);
            }
        });

        try {
            this.getParameters().forEach(param -> {
                if (!param.isValid()) {
                    validity.set(false);
                }
            });
        } catch (Exception e) {
            logger.error("Exception in parameters key:- ", Throwables.getRootCause(e));
            validity.set(false);
        }


        return validity.get();
    }

    private boolean validateMandatoryFields() {
        return (this.getName() != null && !this.getName().isEmpty() &&
                this.getDescription() != null && this.getKpiType() != null &&
                !this.getKpiType().isEmpty() && this.getIsGroupKpi() >= 0 &&
                this.getIsGroupKpi() <= 1 && this.getProducerType() != null &&
                !this.getProducerType().isEmpty() && this.getKpiMapping() != null &&
                !this.getKpiMapping().isEmpty());
    }

    private boolean validateAcceptableInputLength() {
        Set<ConstraintViolation<Object>> violations = ValidationUtils.validateFields(this);
        if (!violations.isEmpty()) {
            violations.forEach(it -> logger.error(it.getMessage()));
            return false;
        }

        return true;
    }
}
