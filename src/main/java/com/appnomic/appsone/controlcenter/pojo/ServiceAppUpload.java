package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.beans.ConnectionDetailsBean;
import lombok.Data;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Data
public class ServiceAppUpload {
    private String services;
    private String application;
    private String connection;
    private List<Tags> tags = new ArrayList<>();
    private List txnViolationConfigs = Collections.emptyList();
    private List<ConnectionDetailsBean> connections = new ArrayList<>();
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ImportFile)) return false;
        ImportFile that = (ImportFile) o;
        return Objects.equals(getServices(), that.getServices()) &&
                Objects.equals(getApplication(), that.getApplication()) &&
                Objects.equals(getConnection(), that.getConnection());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getServices(), getApplication(), getConnection());
    }
    public ServiceAppUpload(String services, String application, String connection) {
        this.application = application;
        this.services = services;
        this.connection=connection;

        if (connection != null) {
            ConnectionDetailsBean connectionDetailsBean = new ConnectionDetailsBean();
            connectionDetailsBean.setSourceRefObject(services);
            connectionDetailsBean.setDestinationRefObject(connection);
            connectionDetailsBean.setIsDiscovery(0);
            this.connections.add(connectionDetailsBean);
        }
    }


}