package com.appnomic.appsone.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserPojo {
    private String userDetailsId;
    private String userName;
    private String firstName;
    private String lastName;
    private String emailId;
    private String toEmails;
    private String ccEmails;
    private String contactNumber;
    private int status;
    private int roleId;
    private int profileId;
    private int profileChange;
    private int emailEnabled;
    private int smsEnabled;
    private int recipientsEnabled;
    private List<ApplicationDetailsForConfigData> applicationDetails;
    private UserTimezoneResponse timezoneDetail;
    private boolean isTimezoneMychoice;
    private boolean isNotificationsTimezoneMychoice;
    private NotificationChoice notificationChoice;

    private static final Logger LOGGER = LoggerFactory.getLogger(UserPojo.class);
}
