package com.appnomic.appsone.controlcenter.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.heal.configuration.pojos.ParentApplication;
import com.heal.configuration.pojos.Tags;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetApplication {

    private int id;
    private String name;
    private String identifier;
    private long lastModifiedOn;
    private String lastModifiedBy;
    private ParentApplication parentApplication;
    private List<ServiceClusterDetails> services;
    private List<Tags> tags;

    @Data
    @Builder
    public static class ServiceClusterDetails{

        private int id;
        private String name;
        private String identifier;
        private List<ClusterComponentDetails> hostCluster;
        private List<ClusterComponentDetails> componentCluster;

    }

    @Data
    public static class ClusterComponentDetails{

        private int id;
        private String name;
        private String identifier;
        private int componentId;
        private String componentName;
        private int componentVersionId;
        private String componentVersionName;
        private int commonVersionId;
        private String commonVersionName;
        private int componentTypeId;
        private String componentTypeName;
        @JsonIgnore
        private int serviceId;
        @JsonIgnore
        private String serviceName;
        @JsonIgnore
        private String serviceIdentifier;

    }

}
