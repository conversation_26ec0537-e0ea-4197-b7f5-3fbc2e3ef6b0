package com.appnomic.appsone.controlcenter.pojo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.heal.configuration.pojos.MaintenanceDetails;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ClusterInstancePojo {
    private int clusterId;
    private String clusterIdentifier;
    private String clusterName;
    private int status;
    private int hostId;
    private int isDR;
    private int isCluster;
    private int componentVersionId;
    private String createdTime;
    private String updatedTime;
    private String lastModifiedBy;
    private int componentId;
    private int componentTypeId;
    private int discovery;
    private String hostAddress;
    private int commonVersionId;
    private int supervisorId;
    private int parentInstanceId;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer accountId;
}
