package com.appnomic.appsone.controlcenter.pojo;

import java.util.HashMap;
import java.util.Map;

public enum HostComInstanceType {
    HOST_CLUSTER("hostCluster"),
    COMP_CLUSTER("compCluster");

    private String configDBName;
    private static final Map<String, HostComInstanceType> reverseLookupMap = new HashMap<>();
    static {
        for (HostComInstanceType vio : HostComInstanceType.values()) {
            reverseLookupMap.put(vio.getConfigDBName(), vio);
        }
    }
    HostComInstanceType(String configDBName) {
        this.configDBName = configDBName;

    }

    public String getConfigDBName() {
        return configDBName;
    }
}
