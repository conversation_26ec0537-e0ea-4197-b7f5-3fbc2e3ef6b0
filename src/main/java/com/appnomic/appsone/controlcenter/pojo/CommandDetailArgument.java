package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.common.Constants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommandDetailArgument {
    private static final Logger LOGGER = LoggerFactory.getLogger(CommandDetailArgument.class);

    private String argumentKey;
    private String argumentValue;
    private String defaultValue;
    private String argumentType = Constants.MST_SUB_TYPE_COMMAND_LINE;
    private String argumentValueType = Constants.MST_SUB_TYPE_TEXT_BOX;

    public boolean validate() {
        if(this.getArgumentKey()==null || this.getArgumentKey().length()<1 || this.getArgumentKey().length()>64) {
            LOGGER.error("argumentKey should not be null and it should contain 1-64 characters only. {} ",this.argumentKey);
            return false;
        }
        if(this.getArgumentValue()==null || this.getArgumentValue().length()<1 || this.getArgumentValue().length()>128) {
            LOGGER.error("argumentValue should not be null and it should contain 1-128 characters only. {} ",this.argumentValue);
            return false;
        }
        if(this.getDefaultValue()==null || this.getDefaultValue().length()<1|| this.getDefaultValue().length()>128) {
            LOGGER.error("defaultValue should not be null and it should contain 1-128 characters only. {} ",this.defaultValue);
            return false;
        }
        return true;
    }

}
