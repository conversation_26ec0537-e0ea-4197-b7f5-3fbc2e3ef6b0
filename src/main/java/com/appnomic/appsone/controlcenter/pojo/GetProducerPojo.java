package com.appnomic.appsone.controlcenter.pojo;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class GetProducerPojo {
    private int id;
    private String name;
    private String description;
    private String producerType;
    private boolean kpiMapped;
    private int isGroupKPI;
    private int isCustom;
    private int status;
    private int producerTimeOut;
    private int producerThreshold;
    private long createdOn;
    private String lastModifiedBy;
    private long lastModifiedOn;

}
