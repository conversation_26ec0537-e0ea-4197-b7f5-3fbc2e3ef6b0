package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.beans.AgentBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.PhysicalAgentBean;
import lombok.Data;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;
@Data
public class AgentDetailsBeansPojo {
    private int physicalAgentId;
    private String name;
    private String currentStatus="";
    private String desiredStatus="";
    private String lastDesiredStatus="";
    private String lastCommandName;
    private long lastCommandTime;
    private int isCommandComplete;
    private String commandJobId="";
    private Map<String, String> metaData = new HashMap<>();
    private String identifier;
    private String agentType;
    private String version;
    private Timestamp installedOn;
    private String hostAddress;
    private int noOfCmds;

    public AgentDetailsBeansPojo(AgentBean agentBean, PhysicalAgentBean physicalAgentBean) {
        this.physicalAgentId = physicalAgentBean.getId();
        this.name = agentBean.getName();

        if (agentBean.getCommandName() != null) {
            this.lastCommandName = agentBean.getCommandName();
        } else {
            this.lastCommandName = "";
        }

        this.isCommandComplete = physicalAgentBean.getLastCommandExecuted();

        if (physicalAgentBean.getLastJobId() != null) {
            this.commandJobId = physicalAgentBean.getLastJobId();
        }
    }

}
