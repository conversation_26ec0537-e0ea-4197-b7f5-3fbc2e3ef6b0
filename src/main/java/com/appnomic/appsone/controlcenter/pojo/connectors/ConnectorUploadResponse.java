package com.appnomic.appsone.controlcenter.pojo.connectors;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConnectorUploadResponse {
    private String name;
    private String features;
    private int status;
    private Timestamp createdOn;
    private String createdBy;
}
