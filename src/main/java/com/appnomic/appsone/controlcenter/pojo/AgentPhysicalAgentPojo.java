package com.appnomic.appsone.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgentPhysicalAgentPojo {

    private int agentId;
    private int physicalAgentId;
    private int lastCommandExecuted;
    private Integer lastStatusId;
    private String physicalAgentIdentifier;
    private String userDetailsId;
    private String lastJobId;
    private Timestamp createdTime;
    private Timestamp updatedTime;

}
