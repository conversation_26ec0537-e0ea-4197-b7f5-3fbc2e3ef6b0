package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.ConstraintViolation;
import javax.validation.constraints.Size;
import java.util.Set;

@Data
public class ProducerParameterDetails {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProducerParameterDetails.class);
    private String parameterType;

    @Size(min = 1, max = 45, message = "parameter name length must be less than or equal to 45")
    private String parameterName;

    @Size(min = 1, max = 512, message = "parameter value length must be less than or equal to 512")
    private String parameterValue;

    public boolean isValid() {
        return (this.getParameterType() != null && this.getParameterType().length() > 0 &&
                this.getParameterName() != null && this.getParameterName().length() > 0 &&
                this.getParameterValue() != null && this.getParameterValue().length() > 0 &&
                validateAcceptableInputLength());
    }

    private boolean validateAcceptableInputLength() {

        Set<ConstraintViolation<Object>> violations = ValidationUtils.validateFields(this);
        if( ! violations.isEmpty() ) {
            violations.forEach(it -> LOGGER.error(it.getMessage()));
            return false;
        }

        return true;
    }

}
