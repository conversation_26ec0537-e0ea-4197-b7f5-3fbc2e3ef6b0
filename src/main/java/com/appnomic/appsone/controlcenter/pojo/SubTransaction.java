package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.beans.SubTransactionBean;
import com.appnomic.appsone.controlcenter.beans.TransactionMatcherDetailsBean;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR> Parekaden : 23/1/19
 */
@Data
public class SubTransaction {
    HTTPTxnConfiguration httpTxnConfig;
    TCPTxnConfiguration tcpTxnConfig;

    public SubTransactionBean getSubTransactionBean(String userId, String txnType) {
        SubTransactionBean subTransactionBean = new SubTransactionBean();
        subTransactionBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        subTransactionBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        subTransactionBean.setUserDetailsId(userId);

        if(txnType.equalsIgnoreCase(TransactionType.HTTP.name()))   {
            HTTPTxnConfiguration httpTxnConfiguration = this.getHttpTxnConfig();
            String type = httpTxnConfiguration.getType();
            String urlPattern = httpTxnConfiguration.getUrlPattern();
            subTransactionBean.setHttpMethod(type != null ? type.trim() : "");
            subTransactionBean.setHttpUrl(urlPattern != null ? urlPattern.trim() : "");

            if(httpTxnConfiguration.getHeaderPattern() != null && !httpTxnConfiguration.getHeaderPattern().isEmpty())   {
                Map<String,String> headerPatters = httpTxnConfiguration.getHeaderPattern();
                headerPatters.forEach((key, value) -> {
                    TransactionMatcherDetailsBean transactionMatcherDetailsBean = httpTxnConfiguration.getHTTPTransactionMatcherDetailsBean("Header",
                            key+","+value, userId);
                    if(transactionMatcherDetailsBean != null)
                        subTransactionBean.getTransactionMatcherDetailsList().add(transactionMatcherDetailsBean);
                    return;
                });
            }

            if(httpTxnConfiguration.getBodyPattern() != null && !httpTxnConfiguration.getBodyPattern().isEmpty())  {
                Map<String,String> bodyPatters = httpTxnConfiguration.getBodyPattern();
                bodyPatters.forEach((key, value) -> {
                    TransactionMatcherDetailsBean transactionMatcherDetailsBean = httpTxnConfiguration.getHTTPTransactionMatcherDetailsBean("Body",
                            key+","+value, userId);
                    if(transactionMatcherDetailsBean != null)
                        subTransactionBean.getTransactionMatcherDetailsList().add(transactionMatcherDetailsBean);
                    return;
                });
            }

            if(httpTxnConfiguration.getQueryParam() != null && !httpTxnConfiguration.getQueryParam().isEmpty()) {
                Map<String,String> queryParameters = httpTxnConfiguration.getQueryParam();
                queryParameters.forEach((key, value) -> {
                    TransactionMatcherDetailsBean transactionMatcherDetailsBean = httpTxnConfiguration.getHTTPTransactionMatcherDetailsBean("QueryParams",
                            key+","+value, userId);
                    if(transactionMatcherDetailsBean != null)
                        subTransactionBean.getTransactionMatcherDetailsList().add(transactionMatcherDetailsBean);
                    return;
                });

            }

        }   else if(txnType.equalsIgnoreCase(TransactionType.TCP.name()))   {

            TCPTxnConfiguration tcpTxnConfiguration = this.getTcpTxnConfig();
            TransactionMatcherDetailsBean transactionMatcherDetailsBean = tcpTxnConfiguration.getTCPTransactionMatcherDetailsBean(
                    tcpTxnConfiguration.getTcpStartPattern(), tcpTxnConfiguration.getTcpEndPattern(),
                    tcpTxnConfiguration.getLength(), userId);
            subTransactionBean.getTransactionMatcherDetailsList().add(transactionMatcherDetailsBean);
        }
        return subTransactionBean;
    }
}
