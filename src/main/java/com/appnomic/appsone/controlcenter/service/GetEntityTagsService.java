package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.GetEntityTagsBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.heal.configuration.pojos.EntityTags;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;

@Slf4j
public class GetEntityTagsService {
    public GenericResponse<List<EntityTags>> getEntityTags(Request request, Response response) {
        GenericResponse<List<EntityTags>> responseObject = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);
            GetEntityTagsBL getEntityTagsBL = new GetEntityTagsBL();
            UtilityBean<Object> validEntityName = getEntityTagsBL.clientValidation(requestObject);
            String entityName = getEntityTagsBL.serverValidation(validEntityName);
            responseObject.setData(getEntityTagsBL.process(entityName));

            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage("Entity Tags for the given entity name is successfully fetched");
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Invalid Request.", e);
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            log.error("Error occurred while Entity Tags for the given entity name.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        }
        return responseObject;
    }
}
