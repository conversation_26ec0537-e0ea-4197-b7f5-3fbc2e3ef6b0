package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.ComponentDetailBean;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.exceptions.FileUploadException;
import com.appnomic.appsone.controlcenter.pojo.CsvRow;
import com.appnomic.appsone.controlcenter.pojo.FileUploadStatusDetail;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.Reader;
import java.nio.file.Files;
import java.util.*;
import java.util.regex.Pattern;

public class UploadValidationService {

    public static final String HEADER_HOST_NAME = "Host Instance Name";
    public static final String HEADER_HOST_ADDRESS = "Host Address";
    public static final String HEADER_HOST_OS_TYPE = "Host OS";
    public static final String HEADER_HOST_OS_VERSION = "Host OS Version";
    public static final String HEADER_COMPONENT_INSTANCE_NAME = "Component Instance Name";
    public static final String HEADER_COMPONENT_TYPE = "Component Name";
    public static final String HEADER_COMPONENT_VERSION = "Component Version";
    public static final String HEADER_SERVICE_NAME = "Service Name";
    public static final String HEADER_APPLICATION_NAME = "Application Name";

    public static final String REGEX_NAME_VALIDATION = "[\\w ~!@#$%^*()\\-+/=\\\\,.\\[\\]{}}|?]{3,40}";
    public static final String REGEX_IPV4_VALIDATION = "^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1" +
                                                       "-9]?[0-9])\\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])$";
    public static final String REGEX_IPV6_VALIDATION = "(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1," +
                                                       "4}|([0-9a-fA-F]{1,4}:){1,7}:|" +
                                                       "([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))";
    public static final String REGEX_DOMAIN_VALIDATION = "(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])" +
                                                         "?\\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]";

    public static final Pattern PATTERN_NAME = Pattern.compile(REGEX_NAME_VALIDATION);
    public static final Pattern PATTERN_IPV4 = Pattern.compile(REGEX_IPV4_VALIDATION);
    public static final Pattern PATTERN_IPV6 = Pattern.compile(REGEX_IPV6_VALIDATION);
    public static final Pattern PATTERN_DOMAIN = Pattern.compile(REGEX_DOMAIN_VALIDATION);

    public static final String ERROR_TYPE_INVALID_FORMAT = "Invalid format";
    public static final String ERROR_TYPE_INVALID_COLUMN_DATA = "Invalid Column data";

    public static final String ERROR_DESC_INVALID_FORMAT = "%s %s is not defined as per the " +
                                                           "schema.";

    public static final String ERROR_DESC_INVALID_COLUMN_DATA = "%s %s is not valid as per the " +
                                                               "schema.";

    private static final Logger logger = LoggerFactory.getLogger(UploadValidationService.class);


    public List<CsvRow> parseCSV(String fileName, FileUploadStatusDetail response){
        List<CsvRow> rows = new ArrayList<>();
        File fileObj = new File(fileName);
        List<Map<String, String>> list = new ArrayList<>();
        try (Reader reader = Files.newBufferedReader(fileObj.toPath());
             CSVParser csvParser = new CSVParser(
                     reader, CSVFormat.DEFAULT.withFirstRecordAsHeader()
                                              .withIgnoreHeaderCase()
                                              .withTrim()
                                              .withIgnoreEmptyLines())) {
            Iterable<CSVRecord> csvRecords = csvParser.getRecords();
            Map<String, Integer> headerMap = csvParser.getHeaderMap();
            Set<String> headers = headerMap.keySet();
            for (CSVRecord csvRecord : csvRecords) {
                Map<String, String> map = new HashMap<>();
                int valuesPresent = 0;
                for (String header : headers) {
                    if (StringUtils.isEmpty(header)) {
                        continue;
                    }
                    String temp = csvRecord.get(header);
                    map.put(header, temp);
                    if (StringUtils.isNotEmpty(temp)) {
                        valuesPresent++;
                    }
                }
                if (map.size() > 0 && valuesPresent > 0) {
                    list.add(map);
                }
            }

        }catch (Exception e){
            logger.error("Error in parsing", e);
            if (e.getMessage().contains("The header contains a duplicate name")) {
                throw new FileUploadException("The header has a duplicate entry. Please check and" +
                                              " correct");
            }
        }

        rows=addCsvRowsToMap(list,response,rows);
        return rows;
    }

private List<CsvRow> addCsvRowsToMap(List<Map<String, String>> list, FileUploadStatusDetail response, List<CsvRow> rows){

    for(int i = 0; i<list.size(); i++){
        CsvRow row = toImportFileObj(list.get(i), response, i+1);
        if(row != null){
            rows.add(row);
        }
    }
    return rows;
    }

    private CsvRow toImportFileObj(Map<String, String> map, FileUploadStatusDetail response,
                                   int lineNumber){

        if(map.isEmpty()){
            response.addWarningStatus(lineNumber, "Empty Line",
                                      "No data present for this line");
        }

        String hostName = map.get(HEADER_HOST_NAME);
        String hostAddress = map.get(HEADER_HOST_ADDRESS);
        String hostOsType = map.get(HEADER_HOST_OS_TYPE);
        String hostOsVersion = map.get(HEADER_HOST_OS_VERSION);
        String compInstanceName = map.get(HEADER_COMPONENT_INSTANCE_NAME);
        String componentType = map.get(HEADER_COMPONENT_TYPE);
        String componentVersion = map.get(HEADER_COMPONENT_VERSION);
        String serviceName = map.get(HEADER_SERVICE_NAME);
        String applicationName = map.get(HEADER_APPLICATION_NAME);
        if(map.get(serviceName) == null){
            map.put(HEADER_SERVICE_NAME, "NoService");
        }

        validateType1Error(map, response, lineNumber);
        if(response.skipLine(lineNumber)){
            return null;
        }

        validateType2Error(map, response, lineNumber);
        if(response.skipLine(lineNumber)){
            return null;
        }


        return new CsvRow(hostName, hostAddress, hostOsType, hostOsVersion, compInstanceName,
                              componentType, componentVersion, serviceName, applicationName);

    }

    private void validateType2Error(Map<String, String> map, FileUploadStatusDetail response,
            int lineNumber){


        String hostOsType = map.get(HEADER_HOST_OS_TYPE);
        String hostOsVersion = map.get(HEADER_HOST_OS_VERSION);
        String componentType = map.get(HEADER_COMPONENT_TYPE);
        String componentVersion = map.get(HEADER_COMPONENT_VERSION);


        List<ComponentDetailBean> hostComponents =
                MasterCache.getComponentDetail(hostOsType);
        if(hostComponents == null || hostComponents.isEmpty()){
            response.addErrorStatus(lineNumber, ERROR_TYPE_INVALID_COLUMN_DATA,
                                    String.format(ERROR_DESC_INVALID_COLUMN_DATA,
                                                  HEADER_HOST_OS_TYPE, hostOsType));
        }else{
            ComponentDetailBean hostComponent = hostComponents.stream().filter(
                    componentDetailBean -> componentDetailBean.getComponentVersionName()
                    .equals(hostOsVersion)).findAny().orElse(null);
            if(hostComponent == null){
                response.addErrorStatus(lineNumber, ERROR_TYPE_INVALID_COLUMN_DATA,
                                        String.format(ERROR_DESC_INVALID_COLUMN_DATA,
                                                      HEADER_HOST_OS_VERSION, hostOsVersion));
            }
        }

        List<ComponentDetailBean> components =
                MasterCache.getComponentDetail(componentType);
        if(components == null){
            response.addErrorStatus(lineNumber, ERROR_TYPE_INVALID_COLUMN_DATA,
                                    String.format(ERROR_DESC_INVALID_COLUMN_DATA,
                                                  HEADER_COMPONENT_TYPE, componentType));
        }else{
            ComponentDetailBean component = components.stream().filter(
                    componentDetailBean -> componentDetailBean.getComponentVersionName()
                                                              .equals(componentVersion)).findAny().orElse(null);
            if(component == null){
                response.addErrorStatus(lineNumber, ERROR_TYPE_INVALID_COLUMN_DATA,
                                        String.format(ERROR_DESC_INVALID_COLUMN_DATA,
                                                      HEADER_COMPONENT_VERSION, componentVersion));
            }
        }

    }

    private void validateType1Error(Map<String, String> map, FileUploadStatusDetail response,
            int lineNumber){
        for(Map.Entry<String, String> entry : map.entrySet()){

            String header = entry.getKey();
            String value = entry.getValue();

            if(HEADER_HOST_NAME.equalsIgnoreCase(header)
               || HEADER_COMPONENT_INSTANCE_NAME.equalsIgnoreCase(header)
               || HEADER_SERVICE_NAME.equalsIgnoreCase(header)
               || HEADER_APPLICATION_NAME.equalsIgnoreCase(header)){
                if(!validateNameString(value)){
                    response.addErrorStatus(lineNumber, ERROR_TYPE_INVALID_FORMAT,
                                            String.format(ERROR_DESC_INVALID_FORMAT, header, value));
                }
            }
            else if(HEADER_HOST_ADDRESS.equalsIgnoreCase(header) && !validateAddress(value)){
                response.addErrorStatus(lineNumber, ERROR_TYPE_INVALID_FORMAT,
                                        String.format(ERROR_DESC_INVALID_FORMAT, header, value));
            }
        }
    }

    private boolean validateNameString(String name){
        return PATTERN_NAME.matcher(name).matches();
    }

    private boolean validateAddress(String address){
        return validateIP(address) || validateDomain(address);
    }

    private boolean validateIP(String ip){
        return validateIPV4(ip) || validateIPV6(ip);
    }

    private boolean validateIPV4(String ip){
        return PATTERN_IPV4.matcher(ip).matches();
    }

    private boolean validateIPV6(String ip){
        return PATTERN_IPV6.matcher(ip).matches();
    }

    private boolean validateDomain(String domain){
        return PATTERN_DOMAIN.matcher(domain).matches();
    }

}
