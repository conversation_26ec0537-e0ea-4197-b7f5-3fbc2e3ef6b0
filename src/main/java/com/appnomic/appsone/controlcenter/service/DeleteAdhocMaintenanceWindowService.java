package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.DeleteAdhocMaintenanceWindowBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.MaintenanceDetails;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.MaintenanceWindowPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;

@Slf4j
public class DeleteAdhocMaintenanceWindowService {

    public GenericResponse<Integer> deleteMaintenanceWindow(Request request, Response response) {
        GenericResponse<Integer> responseObject = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);

            DeleteAdhocMaintenanceWindowBL deleteAdhocMaintenanceWindowBL = new DeleteAdhocMaintenanceWindowBL();

            UtilityBean<MaintenanceWindowPojo> utilityBean = deleteAdhocMaintenanceWindowBL.clientValidation(requestObject);
            List<MaintenanceDetails> maintenanceDetailBeans = deleteAdhocMaintenanceWindowBL.serverValidation(utilityBean);
            deleteAdhocMaintenanceWindowBL.process(maintenanceDetailBeans);

            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage("Maintenance Detail Deleted Successfully");
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Invalid Request.", e);
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            log.error("Error occurred while Deleting Maintenance Window.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        return responseObject;
    }
}
