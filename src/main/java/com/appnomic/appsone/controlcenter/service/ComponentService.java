package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.dao.mysql.ComponentDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.CustomComponentException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.appnomic.appsone.model.JWTData;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.io.IOException;
import java.util.List;

import static com.appnomic.appsone.controlcenter.common.UIMessages.*;

public class ComponentService {

    private static final Logger logger = LoggerFactory.getLogger(ComponentService.class);
    private static ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    public GenericResponse<IdPojo> addComponent(Request request, Response response) {

        try {
            String accountIdString = request.params(Constants.ACCOUNT_IDENTIFIER);

            AccountBean account = ValidationUtils.validAndGetAccount(accountIdString);

            if (account == null) {
                logger.error("Invalid account id: {}", accountIdString);
                return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                        Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                        ACCOUNT_IDENTIFIER_INVALID, null, true);
            }

            if (request.body() == null || request.body().trim().length() == 0) {
                logger.error("Request body can not be null/empty.");
                return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                        Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                        "Request body can not be null/empty.", null, true);
            }
            String userId;
            try {
                userId = getUserId(request);
            } catch (ControlCenterException e) {
                logger.error(e.getSimpleMessage());
                return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                        Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                        e.getSimpleMessage(), null, true);
            }

            AddComponentRequest componentRequest;
            try {
                componentRequest = objectMapper.readValue(request.body(), new TypeReference<AddComponentRequest>() {
                });
            } catch (IOException e) {
                logger.error(Constants.JSON_PARSE_ERROR + " : {}", e.getMessage());
                return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                        Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                        Constants.JSON_PARSE_ERROR, null, true);
            }

            if (componentRequest == null || !componentRequest.validate()
                    || !validateAddComponentRequest(componentRequest, account.getId())) {
                return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                        Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                        "Invalid request", null, true);
            }

            MasterComponentBean componentBean = addComponentDetails(componentRequest, account,
                    userId);

            GenericResponse<IdPojo> genericResponse = CommonUtils.getGenericResponse(response,
                    StatusResponse.SUCCESS.name(),
                    Constants.SUCCESS_STATUS_CODE, CUSTOM_COMPONENT_ADD_SUCCESS, null, false);

            if (componentBean != null) {
                genericResponse.setData(IdPojo.builder()
                        .id(componentBean.getId())
                        .identifier(componentBean.getIdentifier())
                        .name(componentBean.getName())
                        .build());
            }

            return genericResponse;
        } catch (CustomComponentException e) {
            logger.error(CUSTOM_COMPONENT_ADD_FAILURE, e);

            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, e.getMessage(), e, true);

        } catch (Exception e) {
            logger.error(CUSTOM_COMPONENT_ADD_FAILURE, e);

            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, CUSTOM_COMPONENT_ADD_FAILURE, e, true);
        }
    }

    private MasterComponentBean addComponentDetails(AddComponentRequest componentRequest, AccountBean account,
                                                    String userId) {
        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        try {
            return dbi.inTransaction((conn, status) ->
                    addComponentDetails(componentRequest, account.getId(), userId, conn)
            );

        } catch (Exception e) {
            if (Throwables.getRootCause(e) instanceof CustomComponentException) {
                throw (CustomComponentException) Throwables.getRootCause(e);
            } else {
                throw e;
            }
        }
    }

    public boolean validateAddComponentRequest(AddComponentRequest componentRequest,
                                               int accountId) {

        MasterComponentTypeBean typeBean = MasterCache.getMasterComponentTypeUsingName(componentRequest.getType(),
                        accountId + "");
        if (typeBean == null) {
            logger.error("Invalid component type");
            return false;
        }

        MasterComponentBean componentBean =
                ComponentDataService.getComponent(componentRequest.getComponentName(), null);

        if (componentBean != null) {
            int componentId = componentBean.getId();

            ViewComponentBean viewComponentBean = ComponentDataService.getComponentDetail(
                    componentId, typeBean.getId(),
                    componentRequest.getVersion(), null);

            if (viewComponentBean != null) {
                throw new CustomComponentException(ERROR_CUSTOM_COMPONENT_ALREADY_EXISTS_WITH_NAME);
            }
        }
        return true;
    }


    private MasterComponentBean addComponentDetails(AddComponentRequest componentRequest, int accountId,
                                                    String userId, Handle conn) {
        MasterComponentBean componentBean =
                ComponentDataService.getComponent(componentRequest.getComponentName(), conn);

        ComponentParameters params = new ComponentParameters();

        int componentId;
        int componentTypeId = MasterCache.getMasterComponentTypeUsingName(
                componentRequest.getType(), accountId + "").getId();
        if (componentBean != null) {
            componentId = componentBean.getId();
            List<MasterComponentTypeBean> componentTypeBeans =
                    ComponentDataService.getComponentType(componentId, null);
            if (componentTypeBeans.stream().noneMatch(
                    type -> type.getName().equalsIgnoreCase(componentRequest.getType()))) {
                addComponentTypeMapping(componentId, componentRequest.getType(), accountId, userId, conn);
            }
        } else {
            componentId = addComponentToDB(componentRequest, accountId, userId, conn);

            addComponentTypeMapping(componentId, componentRequest.getType(), accountId, userId,
                    conn);
            componentBean =
                    ComponentDataService.getComponent(componentRequest.getComponentName(), conn);
        }

        int commonVersionId =
                getOrAddCommonVersion(componentRequest.getCommonVersion(), componentId,
                        componentRequest.isCustom(), accountId, userId, conn);


        params.setComponentId(componentId);
        params.setComponentTypeId(componentTypeId);
        params.setCommonVersionId(commonVersionId);
        params.setCustom(componentRequest.isCustom());
        addComponentVersion(componentRequest.getVersion(), commonVersionId, componentId,
                componentRequest.isCustom(), userId, accountId, conn);

        addComponentAttributes(componentRequest.getAttributes(), accountId, params, userId, conn);

        return componentBean;


    }

    public int addComponentToDB(AddComponentRequest componentRequest, int accountId,
                                String userId, Handle conn) {
        return ComponentDataService.addComponent(
                AddComponentBean.getInstance(componentRequest, accountId,
                        userId), conn);
    }

    public void addComponentTypeMapping(int componentId, String componentType, int accountId,
                                        String userId, Handle conn) {
        MasterComponentTypeBean componentTypeBean = MasterCache.getMasterComponentTypeUsingName(componentType, accountId + "");

        AddComponentMappingBean addComponentMappingBean = AddComponentMappingBean
                .builder().componentId(componentId).componentTypeId(componentTypeBean.getId())
                .accountId(accountId).userId(userId)
                .createdAt(DateTimeUtil.getCurrentTimestampInGMT()).build();

        int componentMappingId = ComponentDataService.addComponentMapping(addComponentMappingBean, conn);

        if (componentMappingId <= 0) {
            throw new CustomComponentException("Error while adding component type mapping");
        }
    }

    public int getOrAddCommonVersion(String commonVersion, int componentId, boolean isCustom,
                                     int accountId, String userId, Handle conn) {
        CommonVersionBean commonVersionBean = ComponentDataService
                .getCommonVersion(componentId, commonVersion, accountId, conn);

        if (commonVersionBean == null) {
            CommonVersionBean addCommonVersionBean = CommonVersionBean
                    .builder().name(commonVersion).componentId(componentId)
                    .isCustom(isCustom ? 1 : 0).status(1).createdAt(DateTimeUtil.getCurrentTimestampInGMT())
                    .updatedAt(DateTimeUtil.getCurrentTimestampInGMT()).userId(userId)
                    .accountId(accountId).build();
            int commonVersionId = ComponentDataService.addCommonVersion(addCommonVersionBean, conn);
            if (commonVersionId <= 0) {
                throw new CustomComponentException("Error while adding common version");
            }
            return commonVersionId;
        } else {
            return commonVersionBean.getId();
        }
    }

    public void addComponentVersion(String version, int commonVersionId, int componentId,
                                    boolean isCustom, String userId, int accountId, Handle conn) {
        AddComponentVersionBean addComponentVersion = AddComponentVersionBean
                .builder().name(version).isCustom(isCustom ? 1 : 0).status(1)
                .commonVersionId(commonVersionId).componentId(componentId)
                .createdAt(DateTimeUtil.getCurrentTimestampInGMT())
                .updatedAt(DateTimeUtil.getCurrentTimestampInGMT()).userId(userId)
                .accountId(accountId).build();

        int componentVersionId = ComponentDataService.addComponentVersion(addComponentVersion,
                conn);
        if (componentVersionId <= 0) {
            throw new CustomComponentException("Error while adding component version mapping");
        }
    }

    public void addComponentAttributes(List<ComponentAttribute> attributes, int accountId,
                                       ComponentParameters params, String userId, Handle conn) {
        boolean isCustom = params.isCustom();

        if (attributes == null) {
            return;
        }

        for (ComponentAttribute attributeDto : attributes) {

            MasterCommonAttributeBean masterCommonAttributeBean = ComponentDataService
                    .getCommonAttribute(attributeDto.getName(), accountId, conn);

            int masterCommonAttributeId;

            if (masterCommonAttributeBean == null) {
                masterCommonAttributeId = addMasterCommonAttribute(attributeDto, userId,
                        accountId, isCustom, conn);
                if (masterCommonAttributeId <= 0) {
                    throw new CustomComponentException("Error while adding common attribute");
                }
            } else {
                masterCommonAttributeId = masterCommonAttributeBean.getId();
            }

            int componentAttributeId = addComponentAttributeMapping(attributeDto,
                    masterCommonAttributeId,
                    params, userId, conn);

            if (componentAttributeId <= 0) {
                throw new CustomComponentException("Error while adding component attribute");
            }
        }
    }

    private int addComponentAttributeMapping(ComponentAttribute attributeDto,
                                             int masterCommonAttributeId, ComponentParameters params, String userId, Handle conn) {
        AddComponentAttributeMappingBean addCompAttributeBean = AddComponentAttributeMappingBean
                .builder().commonAttributesId(masterCommonAttributeId)
                .isCustom(params.isCustom() ? 1 : 0).isMandatory(attributeDto.isMandatory() ? 1 : 0)
                .defaultValue(attributeDto.getDefaultValue())
                .commonVersionId(params.getCommonVersionId())
                .createdAt(DateTimeUtil.getCurrentTimestampInGMT())
                .updatedAt(DateTimeUtil.getCurrentTimestampInGMT()).userId(userId)
                .minLength(attributeDto.getMinLength()).maxLength(attributeDto.getMaxLength())
                .regex(attributeDto.getRegex()).componentId(params.getComponentId())
                .componentTypeId(params.getComponentTypeId()).build();

        return ComponentDataService.addComponentAttribute(addCompAttributeBean, conn);
    }

    private int addMasterCommonAttribute(ComponentAttribute attributeDto,
                                         String userId, int accountId, boolean isCustom, Handle conn) {
        MasterCommonAttributeBean addCommonAttributeBean = MasterCommonAttributeBean
                .builder().attributeName(attributeDto.getName().replace(" ", ""))
                .attributeType(attributeDto.getType()).isCustom(isCustom ? 1 : 0).status(1)
                .createdAt(DateTimeUtil.getCurrentTimestampInGMT())
                .updatedAt(DateTimeUtil.getCurrentTimestampInGMT()).userId(userId)
                .accountId(accountId).name(attributeDto.getName()).build();

        return ComponentDataService.addCommonAttribute(addCommonAttributeBean, conn);
    }

    public String getUserId(Request request) throws ControlCenterException {
        String authKey = request.headers(Constants.AUTHORIZATION);
        JWTData jwtData = KeyCloakAuthService.extractUserDetails(authKey);
        return jwtData.getSub();
    }
}