package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.businesslogic.AutoDiscoveryMapEntityBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.*;

import com.appnomic.appsone.controlcenter.pojo.autodiscovery.AutoDiscoveryMapEntityPojo;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;

@Slf4j
public class AutoDiscoveryMapEntityToService {

    public GenericResponse mapEntity(Request request, Response response) {
        GenericResponse responseObject = new GenericResponse();
        AutoDiscoveryMapEntityBL adMapEntityBL = new AutoDiscoveryMapEntityBL();
        try {
            RequestObject requestObject = new RequestObject(request);
            UtilityBean<AutoDiscoveryMapEntityPojo> utilityBean = adMapEntityBL.clientValidation(requestObject);
            AutoDiscoveryMapEntityPojo adMapEntityBean = adMapEntityBL.serverValidation(utilityBean);
            List<String> invalidJsonObjects = adMapEntityBL.process(adMapEntityBean);

            if (invalidJsonObjects.contains("UNMAPPING")) {
                invalidJsonObjects.remove("UNMAPPING");
                responseObject.setData("Service(s) unmapped successfully.");
                responseObject.setMessage("Invalid Json Objects: " + invalidJsonObjects);
                responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            } else if (invalidJsonObjects.contains("WRONG-EDIT")) {
                invalidJsonObjects.remove("WRONG-EDIT");
                responseObject.setData("Invalid Json Objects: " + invalidJsonObjects);
                responseObject.setMessage("Cannot edit component instance mappings here!");
                responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            } else if (invalidJsonObjects.contains("PASS")){
                invalidJsonObjects.remove("PASS");
                if (invalidJsonObjects.size() == 0) {
                    responseObject.setData("Mapping successful and related connections are discovered.");
                    responseObject.setMessage("Invalid Json Objects: " + invalidJsonObjects);
                    responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
                }
            } else if (!invalidJsonObjects.stream().anyMatch(i -> i.equals("UNMAPPING") || i.equals("WRONG-EDIT"))) {
                if (invalidJsonObjects.size() > 0) {
                    responseObject.setData("Invalid entities found.");
                    responseObject.setMessage("Invalid Json Objects: " + invalidJsonObjects);
                    responseObject.setResponseStatus(StatusResponse.PARTIAL_FAILURE.name());
                }
            }
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Error details: {}", e);
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            log.error("Error while processing the request. {}.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        return responseObject;
    }
}
