package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.businesslogic.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.cache.keys.CommVersionKPIs;
import com.appnomic.appsone.controlcenter.cache.keys.Host;
import com.appnomic.appsone.controlcenter.cache.keys.MstKpi;
import com.appnomic.appsone.controlcenter.cache.keys.ProducerKpis;
import com.appnomic.appsone.controlcenter.common.*;
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.MasterDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ServiceKpiThreshold;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.ComponentUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.appnomic.appsone.model.JWTData;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Kumar : 16/1/19
 */
public class ComponentInstanceService {
    private static final Logger logger = LoggerFactory.getLogger(ComponentInstanceService.class);
    private final ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    private static final String IDENTIFIER = ":identifier";
    private static final String REQUEST_BODY_NULL_LOGGER_STRING = "Request body can not be null.";
    private static final String AUTHORIZATION = "Authorization";
    private static final String INVALID_ACC_ID_STRING = "Invalid account id provided";
    private static final String NO_COMPONENT_INSTANCE_STRING = "Received empty list of component instance objects";
    private Map<Integer, Map<String, IdPojo>> controllerMap = null;
    private static final Set<ServiceKpiThreshold> serviceKpiThresholdSet = new HashSet<>();
    private static final int serviceTypeId = MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT,
            Constants.SERVICES_CONTROLLER_TYPE).getSubTypeId();
    private static final int applicationTypeId = MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT,
            Constants.APPLICATION_CONTROLLER_TYPE).getSubTypeId();

    /**
     * Add component instance API will call this method
     */
    public ResponseObject<Object> addComponentInstance(Request request, Response response) {
        ResponseObject<Object> responseObject = new ResponseObject<>();
        List<ComponentInstance> invalidJsonObjects;
        try {
            String accountIdString = request.params(IDENTIFIER);
            int accountId = ValidationUtils.validAndGetIdentifier(accountIdString);
            /*
             * Check the request body before doing processing
             */
            if (request.body() == null) {
                responseObject.setResponseStatus(StatusResponse.FAILURE.name());
                responseObject.setMessage(REQUEST_BODY_NULL_LOGGER_STRING);
                response.status(403);
                logger.error(REQUEST_BODY_NULL_LOGGER_STRING);
                return responseObject;
            }
            String authKey = request.headers(AUTHORIZATION);
            JWTData jwtData = KeyCloakAuthService.extractUserDetails(authKey);
            String userId = jwtData.getSub();

            invalidJsonObjects = addComponentInstancesDetails(request.body(), userId, accountId);
            if (!invalidJsonObjects.isEmpty()) {
                responseObject.setMessage("Invalid component instance details.");
                responseObject.setInvalidJsonObjects(invalidJsonObjects);
                responseObject.setResponseStatus(StatusResponse.FAILURE.name());
                response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            } else {
                responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            }
        } catch (RequestException e) {
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            logger.error("Error while processing the request. Details: ", e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        return responseObject;
    }

    public List<ComponentInstance> addComponentInstancesDetails(String jsonString, String userId, int accountId) throws Exception {
        List<ComponentInstance> errorMessages = new ArrayList<>();
        List<ComponentInstance> componentInstanceList;
        try {
            componentInstanceList = new ObjectMapper().readValue(jsonString, new TypeReference<List<ComponentInstance>>() {
            });
        } catch (IOException e) {
            logger.error(Constants.JSON_PARSE_ERROR);
            throw new RequestException(Constants.JSON_PARSE_ERROR);
        }
        /*
         * Is given account id is valid
         */
        if (accountId == -1) {
            logger.error(INVALID_ACC_ID_STRING);
            throw new ControlCenterException(INVALID_ACC_ID_STRING);
        }
        /*
         * If component instance object is null or empty
         */
        if (componentInstanceList == null || componentInstanceList.isEmpty()) {
            logger.warn(NO_COMPONENT_INSTANCE_STRING);
            throw new ControlCenterException(NO_COMPONENT_INSTANCE_STRING);
        }

        List<ComponentInstance> validComponentInstanceList = new ArrayList<>();
        /*
         * Validate json data
         */
        for (ComponentInstance componentInstance : componentInstanceList) {
            componentInstance.validateMandatoryFields();
            if (!componentInstance.getErrorMessage().isEmpty()) {
                for (String message : componentInstance.getErrorMessage().values()) {
                    logger.error("Invalid JSON due to: {}", message);
                }
                continue;
            }
            validComponentInstanceList.add(componentInstance);
        }

        controllerMap = MasterDataService.getAllControllerList().parallelStream()
                .filter(c -> c.getControllerTypeId() == serviceTypeId || c.getControllerTypeId() == applicationTypeId)
                .collect(Collectors.groupingBy(Controller::getAccountId,
                        Collectors.toMap(Controller::getIdentifier, c -> IdPojo.builder()
                                .id(Integer.parseInt(c.getAppId()))
                                .identifier(c.getIdentifier())
                                .name(c.getName())
                                .build())));

        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        dbi.inTransaction((conn, status) -> {
            for (ComponentInstance componentInstance : validComponentInstanceList) {
                try {
                    if (componentInstance.getIsCluster() == 1) {
                        logger.info("componentInstance-{} identifier-{} is not a component instance",
                                componentInstance.getName(), componentInstance.getIdentifier());
                        continue;
                    }
                    errorMessages.add(componentInstance);
                    addComponentInstance(componentInstance, accountId, userId, controllerMap, conn);
                } catch (ControlCenterException e) {
                    throw new RuntimeException(e.getMessage());
                }
            }
            return "Nothing to return";
        });
        List<ComponentInstance> result = new ArrayList<>();
        for (ComponentInstance componentInstance : errorMessages) {
            if (!componentInstance.getErrorMessage().isEmpty()) result.add(componentInstance);
        }
        return result;
    }


    /**
     * Add host API will call this method
     */
    public ResponseObject<String> addCluster(Request request, Response response) {
        ResponseObject<String> responseObject = new ResponseObject<>();
        try {
            String accountIdString = request.params(IDENTIFIER);
            int accountId = ValidationUtils.validAndGetIdentifier(accountIdString);
            /*
             * Check the request body before doing processing
             */
            if (request.body() == null) {
                responseObject.setResponseStatus(StatusResponse.FAILURE.name());
                responseObject.setMessage(REQUEST_BODY_NULL_LOGGER_STRING);
                response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
                logger.error(REQUEST_BODY_NULL_LOGGER_STRING);
                return responseObject;
            }
            String authKey = request.headers(AUTHORIZATION);
            JWTData jwtData = KeyCloakAuthService.extractUserDetails(authKey);
            String userId = jwtData.getSub();
            addClusterDetails(request.body(), userId, accountId);
        } catch (Exception e) {
            logger.error("Error while converting request object into cluster json object. Details: ", e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        }
        responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
        return responseObject;
    }

    public void addClusterDetails(String jsonString, String userId, int accountId) throws Exception {
        ResponseObject<Object> responseObject = new ResponseObject<>();
        List<ComponentInstance> invalidJsonObjects = new ArrayList<>();
        List<ComponentInstance> errorMessages = new ArrayList<>();
        List<ComponentInstance> componentInstanceList;
        try {
            componentInstanceList = objectMapper.readValue(jsonString, new TypeReference<List<ComponentInstance>>() {
            });
        } catch (IOException e) {
            logger.error(Constants.JSON_PARSE_ERROR + " : {}", e.getMessage());
            throw new RequestException(Constants.JSON_PARSE_ERROR);
        }
        /*
         * Is given account id is valid
         */
        if (accountId == -1) {
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(INVALID_ACC_ID_STRING);
            logger.error(INVALID_ACC_ID_STRING);
            throw new ControlCenterException(INVALID_ACC_ID_STRING);
        }
        /*
         * If component instance object is null or empty
         */
        if (componentInstanceList == null || componentInstanceList.isEmpty()) {
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(NO_COMPONENT_INSTANCE_STRING);
            logger.warn(NO_COMPONENT_INSTANCE_STRING);
            throw new ControlCenterException(NO_COMPONENT_INSTANCE_STRING);
        }

        List<ComponentInstance> validComponentInstanceList = new ArrayList<>();
        /*
         * Validate json data
         */
        for (ComponentInstance componentInstance : componentInstanceList) {
            componentInstance.validateMandatoryFields();
            if (!componentInstance.getErrorMessage().isEmpty()) {
                invalidJsonObjects.add(componentInstance);
                continue;
            }
            validComponentInstanceList.add(componentInstance);
        }

        if (invalidJsonObjects.size() != 0) {
            responseObject.setMessage("Invalid cluster details.");
            responseObject.setInvalidJsonObjects(invalidJsonObjects);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            logger.warn("Invalid/Null/Empty Values found in Request body");
            throw new ControlCenterException("Invalid/Null/Empty Values found in Request body");
        }

        controllerMap = MasterDataService.getAllControllerList().parallelStream()
                .filter(c -> c.getControllerTypeId() == serviceTypeId || c.getControllerTypeId() == applicationTypeId)
                .collect(Collectors.groupingBy(Controller::getAccountId,
                        Collectors.toMap(Controller::getIdentifier, c -> IdPojo.builder()
                                .id(Integer.parseInt(c.getAppId()))
                                .identifier(c.getIdentifier())
                                .name(c.getName())
                                .build())));

        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        dbi.inTransaction((conn, status) -> {
            for (ComponentInstance componentInstance : validComponentInstanceList) {
                try {
                    if (componentInstance.getIsCluster() == 1) {
                        logger.info("componentInstance-" + componentInstance.getName() + " identifier-"
                                + componentInstance.getIdentifier() + " is not a component instance");
                        continue;
                    }
                    errorMessages.add(componentInstance);
                    addCluster(componentInstance, accountId, userId, controllerMap, conn);
                } catch (ControlCenterException e) {
                    throw new RuntimeException(e.getMessage());
                }
            }
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            return responseObject;
        });

        List<ComponentInstance> result = new ArrayList<>();
        for (ComponentInstance componentInstance : errorMessages) {
            if (!componentInstance.getErrorMessage().isEmpty()) result.add(componentInstance);
        }
        responseObject.setMessage(result);
    }

    private void addComponentInstance(ComponentInstance componentInstance, int accountId, String userId, Map<Integer, Map<String, IdPojo>> controllerMapData, Handle handle) throws Exception {
        addInstance(componentInstance, false, false, accountId, userId, controllerMapData, handle);
    }

    private void addCluster(ComponentInstance componentInstance, int accountId, String userId, Map<Integer, Map<String, IdPojo>> controllerMapData, Handle handle) throws Exception {
        addInstance(componentInstance, true, false, accountId, userId, controllerMapData, handle);
    }

    private void addHost(ComponentInstance componentInstance, int accountId, String userId, Map<Integer, Map<String, IdPojo>> controllerMapData, Handle handle) throws Exception {
        addInstance(componentInstance, false, true, accountId, userId, controllerMapData, handle);
    }

    /**
     * Add component/cluster/host
     */
    private void addInstance(ComponentInstance componentInstance, boolean isCluster, boolean isHost, int accountId, String userId,
                             Map<Integer, Map<String, IdPojo>> controllerMapData, Handle handle) throws Exception {
        ComponentInstanceBean componentInstanceBean = createComponentInstanceBean(componentInstance, accountId, userId, handle);
        if (componentInstanceBean == null) return;
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
        BindInDataService bindInDataService = new BindInDataService();

        List<String> mappingIdentifiers = new ArrayList<>(componentInstance.getMappingIdentifiers());

        List<ComponentInstanceBean> mappingIdentifiersDetails = null;

        if (mappingIdentifiers.size() > 0) {

            if (!isHost && mappingIdentifiers.size() > 1) {
                throw new ControlCenterException("more than 1 mapping identifiers found, " +
                        "maximum one is allowed");
            }

            mappingIdentifiersDetails = bindInDataService.getComponentInstanceBeans(mappingIdentifiers, accountId, handle);

            if (mappingIdentifiersDetails.size() != mappingIdentifiers.size()) {
                throw new ControlCenterException("Invalid mapping identifiers");
            }

            MasterComponentTypeBean hostComponentTypeBean = MasterCache.getMasterComponentTypeUsingName(Constants.HOST, accountId + "");

            if(Objects.isNull(hostComponentTypeBean)) {
                logger.error("Host component type bean unavailable for account [{}]", accountId);
                throw new ControlCenterException("Invalid mapping identifiers");
            }

            boolean errorFound = false;

            for (ComponentInstanceBean detail : mappingIdentifiersDetails) {
                if (isCluster) {
                    if (detail.getIsCluster() == 0) {
                        errorFound = true;
                        break;
                    }
                } else {
                    if (detail.getIsCluster() == 1) {
                        errorFound = true;
                        break;
                    }
                }
                if (isHost) {
                    if (detail.getMstComponentTypeId() == hostComponentTypeBean.getId()) {
                        errorFound = true;
                        break;
                    }
                } else {
                    if (detail.getMstComponentTypeId() != hostComponentTypeBean.getId()) {
                        errorFound = true;
                        break;
                    }
                }
            }

            if (errorFound) {
                throw new ControlCenterException("Invalid mapping identifiers");
            }

            if (!isHost) {
                componentInstanceBean.setHostId(mappingIdentifiersDetails.get(0).getId());
            }

        }

        String mstComponentName = componentInstance.getMstComponentName();
        String mstComponentType = componentInstance.getMstComponentType();
        String mstComponentVersion = componentInstance.getMstComponentVersion();
        MasterComponentVersionBean masterComponentVersionBean = ComponentUtils.validateMasterComponentVersion(mstComponentVersion, componentInstanceBean.getMstComponentId(),
                componentInstance.getErrorMessage(), String.valueOf(accountId));
        /*
         * Check host in component instance table
         */
        if (!isCluster) {
            List<Attributes> attributesList = componentInstance.getAttributes();
            Attributes hostAddress = ComponentUtils.getHostAddress(attributesList);
            componentInstanceBean.setHostAddress(hostAddress.getValue());
            Host key = new Host();
            key.setAccountId(accountId);
            key.setHostAddress(componentInstanceBean.getHostAddress());
            ComponentInstanceBean host = MasterDataService.getHostsData(key.getHostAddress(), key.getAccountId());
            if (isHost && host != null) {
                String log = "Host address-" + componentInstanceBean.getHostAddress() + " is already registered in db.";
                logger.warn(log);
                logger.warn("No any host is found in DB for - {} mstComponentType-{} mstComponentVersion-{}", mstComponentName, mstComponentType, mstComponentVersion);
                componentInstance.putErrorMessage("Host", log);
                return;
            }
            if (host == null) {
                logger.warn("No any host is found in DB for - {} mstComponentType-{} mstComponentVersion-{}", mstComponentName, mstComponentType, mstComponentVersion);
            } else {
                componentInstanceBean.setHostId(host.getId());
            }
            componentInstanceBean.setIsCluster(0);
        } else {
            componentInstanceBean.setIsCluster(1);
            componentInstanceBean.setHostAddress(null);
        }

        int id = compInstanceDataService.addComponentInstance(componentInstanceBean, handle);
        if (id == -1) {
            String log = "Unable to create component instance for -" + mstComponentName + ", mstComponentType-" + mstComponentType + ", mstComponentVersion-" + mstComponentVersion;
            logger.warn(log);
            componentInstance.putErrorMessage("componenetInstance", log);
            throw new ControlCenterException(log);
        }

        if (mappingIdentifiersDetails != null && isHost) {
            mappingIdentifiersDetails.forEach(detail -> detail.setHostId(id));
            compInstanceDataService.updateInstanceHostIds(mappingIdentifiersDetails, handle);
        }

        //update the cache
        MasterCache.getCompInstUsingAccountIdAndInstName(componentInstanceBean.getName(), accountId);
        logger.info("{} is created successfully for component for -{} mstComponentType-{} mstComponentVersion-{}", componentInstance.getName(), mstComponentName, mstComponentType, mstComponentVersion);


        String clusterName = componentInstance.getClusterName();
        if (!isCluster && clusterName != null) {
            addCompClusterMapping(accountId, id, userId, clusterName, componentInstance.getErrorMessage(), handle);
        }

        /*
         * Map agent identifier with component instance
         */
        List<String> agentIdentifiers = componentInstance.getAgentIdentifiers();
        if (agentIdentifiers != null) {
            for (String agentIdentifier : agentIdentifiers) {
                AgentBean agentBean = MasterCache.getAgentBean(agentIdentifier);
                if (agentBean == null) {
                    String log = "No any agent is found for given agent uid-" + agentIdentifier;
                    logger.warn(log);
                    componentInstance.putErrorMessage("agentIdentifier", log);
                    throw new ControlCenterException(log);
                }
                AddAgent agentService = new AddAgent();
                agentService.addAgentCompInstMapping(id, agentBean.getId(), handle);
            }
        }

        List<Tags> tagsList = componentInstance.getTags();
        if (tagsList != null && !tagsList.isEmpty()) {
            tagsList.forEach(tag -> {
                try {
                    addController(tag, id, accountId, userId, controllerMapData, handle);
                } catch (ControlCenterException e) {
                    componentInstance.getErrorMessage().put("tag-mapping-comp-instance-exception", e.getSimpleMessage());
                    logger.error("Exception occurred while adding tag mapping for comp instance to controller.", e);
                }
            });
        }
        addAllNonGroupKPIs(componentInstance.getKpi(), masterComponentVersionBean.getMstCommonVersionId(), masterComponentVersionBean.getId(), mstComponentVersion,
                componentInstanceBean.getMstComponentId(), mstComponentType, componentInstanceBean.getMstComponentTypeId(), id, userId, handle, componentInstance.getErrorMessage(), accountId);
        List<GroupKPIs> groupKPIsList = componentInstance.getGroupKpi();
        if (groupKPIsList != null && !groupKPIsList.isEmpty()) {
            addAllGroupKPIs(masterComponentVersionBean.getId(),
                    masterComponentVersionBean.getMstCommonVersionId(), mstComponentVersion,
                    componentInstanceBean.getMstComponentId(), mstComponentType,
                    componentInstanceBean.getMstComponentTypeId(), id, groupKPIsList, userId,
                    handle, componentInstance.getErrorMessage(), accountId, false, isCluster);
        }
        addAllGroupKPIs(masterComponentVersionBean.getId(),
                masterComponentVersionBean.getMstCommonVersionId(), mstComponentVersion,
                componentInstanceBean.getMstComponentId(), mstComponentType,
                componentInstanceBean.getMstComponentTypeId(), id, componentInstance.getGroupKpiDiscovery(),
                userId, handle, componentInstance.getErrorMessage(), accountId, true, isCluster);
        /*
         * Check attributes for given component
         */
        if (!isCluster) {
            List<AttributesViewBean> attributesViewBeanList = MasterCache.getAttributesViewBeanList(masterComponentVersionBean.getMstCommonVersionId());
            if (attributesViewBeanList != null && !attributesViewBeanList.isEmpty()) {
                addAttributesForCompInstance(attributesViewBeanList, componentInstance.getAttributes(), id, userId, componentInstance.getErrorMessage(), handle);
            }
            //Add Config/Watch KPIs
            ComponentInstanceBL.addConfigWatchKPIs(componentInstanceBean, id, handle);
        }
        StaticThreshold.createKpiThresholdForAvailabilityKpi(componentInstance, accountId, userId, handle, controllerMap.getOrDefault(accountId, new HashMap<>()), serviceKpiThresholdSet);
    }


    /**
     * Create component instance bean object
     */
    private ComponentInstanceBean createComponentInstanceBean(ComponentInstance componentInstance, int accountId, String userId, Handle handle) throws Exception {
        String mstComponentName = componentInstance.getMstComponentName();
        MasterComponentBean masterComponentBean = ComponentUtils.validateMasterComponent(mstComponentName, componentInstance.getErrorMessage(), String.valueOf(accountId));
        String mstComponentType = componentInstance.getMstComponentType();
        MasterComponentTypeBean masterComponentTypeBean = ComponentUtils.validateMasterComponentType(mstComponentType, componentInstance.getErrorMessage(), String.valueOf(accountId));
        String mstComponentVersion = componentInstance.getMstComponentVersion();
        MasterComponentVersionBean masterComponentVersionBean = ComponentUtils.validateMasterComponentVersion(mstComponentVersion, masterComponentBean.getId(), componentInstance.getErrorMessage(), String.valueOf(accountId));

        /*
         * Check attributes for given component
         */
        List<AttributesViewBean> attributesViewBeanList = MasterCache.getAttributesViewBeanList(masterComponentVersionBean.getMstCommonVersionId());
        if (attributesViewBeanList == null) {
            logger.warn("No any attributes are found in AttributesView table for -{} mstComponentType-{} mstComponentVersion-{}", mstComponentName, mstComponentType, mstComponentVersion);
        }

        boolean isCompInstanceIsAvailable = isCompInstanceAlreadyAvailable(componentInstance, masterComponentBean.getId(), accountId, attributesViewBeanList, handle);
        if (isCompInstanceIsAvailable) {
            return null;
        }
        /*
         * Create component instance bean object to insert data into component instance table
         */
        ComponentInstanceBean componentInstanceBean = new ComponentInstanceBean();
        String instanceName = null;
        String identifier = null;
        /*
         * If name and identifier both are available then set values for respective field
         */
        if (componentInstance.getIdentifier() != null && componentInstance.getName() != null) {
            instanceName = componentInstance.getName();
            identifier = componentInstance.getIdentifier();
        } else if (componentInstance.getIdentifier() != null) {
            /*
             * If only identifier is available then values for name and identifier would be same
             */
            identifier = componentInstance.getIdentifier();
            instanceName = identifier;
        } else if (componentInstance.getName() != null) {
            /*
             * If only name is available then value for both would be same
             */
            instanceName = componentInstance.getName();
            identifier = instanceName;
        }
        componentInstanceBean.setName(instanceName);
        componentInstanceBean.setStatus(1);
        componentInstanceBean.setIsDR(0);
        componentInstanceBean.setIsCluster(0);
        componentInstanceBean.setMstComponentVersionId(masterComponentVersionBean.getId());
        componentInstanceBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        componentInstanceBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        componentInstanceBean.setAccountId(accountId);
        componentInstanceBean.setMstComponentId(masterComponentBean.getId());
        componentInstanceBean.setMstComponentTypeId(masterComponentTypeBean.getId());
        componentInstanceBean.setUserDetailsId(userId);
        componentInstanceBean.setIdentifier(identifier);
        componentInstanceBean.setMstCommonVersionId(masterComponentVersionBean.getMstCommonVersionId());
        componentInstanceBean.setDiscovery(0);
        return componentInstanceBean;
    }

    /**
     * Add component instance cluster mapping
     */
    public static void addCompClusterMapping(int accountId, int id, String userId, String clusterName, Map<String, String> message, Handle handle) {
        ComponentInstanceBean compInstClusterDetails = MasterCache.getCompInstUsingAccountIdAndInstName(clusterName, accountId);
        if (compInstClusterDetails == null) {
            String log = "No any cluster is found for given cluster name -" + clusterName + " and account id-" + accountId;
            logger.warn(log);
            message.put("cluster", log);
            return;
        }
        CompClusterMappingBean compClusterMappingBean = new CompClusterMappingBean();
        compClusterMappingBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        compClusterMappingBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        compClusterMappingBean.setUserDetailsId(userId);
        compClusterMappingBean.setAccountId(accountId);
        compClusterMappingBean.setCompInstanceId(id);
        compClusterMappingBean.setClusterId(compInstClusterDetails.getId());
        int mappingId = new CompInstanceDataService().addCompClusterMapping(compClusterMappingBean, handle);
        if (mappingId == -1) {
            String log = "Unable to do component instance cluster mapping for cluster name-" + clusterName + " accountId-" + accountId;
            logger.warn(log);
            message.put("cluster_instance_mapping", log);
            return;
        }
        logger.info("component instance cluster mapping for cluster name-{} accountId-{} and component instance id-{} is created successfully.", clusterName, accountId, id);
    }

    /**
     * Add attributes for created component instance
     */
    private void addAttributesForCompInstance(List<AttributesViewBean> attributesViewBeanList, List<Attributes> attributesList, int cmpInstId,
                                              String userDetails, Map<String, String> message, Handle handle) {
        //Get attribute bean object for a given attribute name
        for (AttributesViewBean attributesViewBean : attributesViewBeanList) {
            Attributes attribute = ComponentUtils.getComponentAttribute(attributesList, attributesViewBean.getAttributeName());
            CompInstanceAttributesBean compInstanceAttributesBean = new CompInstanceAttributesBean();
            compInstanceAttributesBean.setAttributeName(attributesViewBean.getAttributeName());
            if (attribute != null) compInstanceAttributesBean.setAttributeValue(attribute.getValue());
            else compInstanceAttributesBean.setAttributeValue(attributesViewBean.getDefaultValue());
            compInstanceAttributesBean.setCompInstanceId(cmpInstId);
            compInstanceAttributesBean.setMstComponentAttributeMappingId(attributesViewBean.getMstComponentAttributeMappingId());
            compInstanceAttributesBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
            compInstanceAttributesBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
            compInstanceAttributesBean.setUserDetailsId(userDetails);
            compInstanceAttributesBean.setMstCommonAttributesId(attributesViewBean.getAttributeId());
            int id = new CompInstanceDataService().addComponentInstanceAttributes(compInstanceAttributesBean, handle);
            if (id == -1) {
                String log = "Unable to add attribute-" + attributesViewBean.getAttributeName() + " for component instance id-" + cmpInstId;
                logger.info(log);
                message.put("attributes", log);
                return;
            }
            logger.info("Attribute-{} is added for component instance id-{}", attributesViewBean.getAttributeName(), cmpInstId);
        }
    }

    /**
     * Add non group kpi for created component instance
     */
    private void addAllNonGroupKPIs(List<KPI> kpiList, int mstCommonVersionId, int mstCompVersionId, String mstCompVersionName, int mstCompId,
                                    String mstCompTypeName, int mastCompTypeId, int compInstanceId, String userDetails,
                                    Handle handle, Map<String, String> message, int accountId) throws Exception {
        CommVersionKPIs commVersionKPIs = new CommVersionKPIs();
        commVersionKPIs.setAccountId(accountId);
        commVersionKPIs.setMstCommonVersionId(mstCommonVersionId);
        List<ViewCommonVersionKPIsBean> viewCommonVersionKPIsBeansNonGroup = MasterCache.getNonGroupKPIUsingCommonVersionId(commVersionKPIs);
        if (viewCommonVersionKPIsBeansNonGroup == null) {
            String log = "No any non group kpi is available for given master component version -" + mstCompVersionName + ", mstComponentType-" + mstCompTypeName;
            logger.warn(log);
            message.put("nonGroupKPI", log);
            throw new ControlCenterException(log);
        }

        List<KPI> finalKpiList = new ArrayList<>();
        if (kpiList != null && !kpiList.isEmpty())
            viewCommonVersionKPIsBeansNonGroup = ComponentUtils.filterRequiredKPI(viewCommonVersionKPIsBeansNonGroup, kpiList, finalKpiList);
        for (ViewCommonVersionKPIsBean viewCommonVersionKPIsBean : viewCommonVersionKPIsBeansNonGroup) {
            //We consider only status = 1 kpi
            if (viewCommonVersionKPIsBean.getStatus() == 0) continue;
            ProducerKpis producerKpis = new ProducerKpis();
            producerKpis.setMstKpiDetailsId(viewCommonVersionKPIsBean.getKpiId());
            producerKpis.setMstCompVersionId(mstCompVersionId);
            producerKpis.setMstCompId(mstCompId);
            producerKpis.setMstCompTypeId(mastCompTypeId);
            producerKpis.setAccountId(accountId);
            ViewProducerKPIsBean viewProducerKPIsBean = MasterCache.getViewProducerKPIsNonGroup(producerKpis);
            if (viewProducerKPIsBean == null) {
                String log = "No any producer is found for kpi - " + viewCommonVersionKPIsBean.getKpiName();
                logger.warn(log);
                message.put("nonGroupKPI", log);
                throw new ControlCenterException(log);
            }
            if (viewProducerKPIsBean.getStatus() == 0) continue;

            CompInstanceKpiDetailsBean compInstanceKpiDetailsBean = new CompInstanceKpiDetailsBean();
            compInstanceKpiDetailsBean.setCompInstanceId(compInstanceId);
            compInstanceKpiDetailsBean.setMstProducerKpiMappingId(viewProducerKPIsBean.getMstProducerKpiMappingId());
            compInstanceKpiDetailsBean.setCollectionInterval(viewCommonVersionKPIsBean.getDefaultCollectionInterval());
            compInstanceKpiDetailsBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
            compInstanceKpiDetailsBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
            compInstanceKpiDetailsBean.setStatus(1);
            compInstanceKpiDetailsBean.setUserDetailsId(userDetails);
            compInstanceKpiDetailsBean.setMstKpiDetailsId(viewCommonVersionKPIsBean.getKpiId());
            compInstanceKpiDetailsBean.setMstProducerId(viewProducerKPIsBean.getProducerId());
            int id = new CompInstanceDataService().addNonGroupComponentInstanceKPI(compInstanceKpiDetailsBean, handle);
            if (id == -1) {
                String log = "Unable to add non group kpi for component instance-" + compInstanceKpiDetailsBean.getCompInstanceId();
                logger.warn(log);
                message.put("nonGroupKPI", log);
                throw new ControlCenterException(log);
            }
            logger.info("{} kpi is added for component instance-{}", viewCommonVersionKPIsBean.getKpiName(), compInstanceKpiDetailsBean.getCompInstanceId());
        }

        //Add threshold for each kpi
        ThresholdService thresholdService = new ThresholdService();
        logger.info("Started adding component instance kpi threshold for instance id-{} accountId-{} user details-{}", compInstanceId, accountId, userDetails);
        for (KPI kpi : finalKpiList) {
            thresholdService.addCompInstKpiThreshold(kpi, compInstanceId, userDetails, accountId, handle);
        }
    }

    /**
     * Add group kpi data for created component instance
     */
    private void addAllGroupKPIs(int mstCompVersionId, int mstCommonVersionId, String mstCompVersionName,
                                 int mstCompId, String mstCompTypeName, int mastCompTypeId,
                                 int compInstanceId, List<GroupKPIs> groupKPIList, String userDetails,
                                 Handle handle, Map<String, String> message, int accountId,
                                 boolean isDiscovery, boolean isCluster) throws Exception {
        ThresholdService thresholdService = new ThresholdService();
        CommVersionKPIs commVersionKPIs = new CommVersionKPIs();
        commVersionKPIs.setAccountId(accountId);
        commVersionKPIs.setMstCommonVersionId(mstCommonVersionId);
        List<ViewCommonVersionKPIsBean> viewCommonVersionKPIsBeansGroup = MasterCache.getGroupKPIUsingCommonVersionId(commVersionKPIs);
        if (viewCommonVersionKPIsBeansGroup == null) {
            String log = "No any group kpi is available for given master component version -" + mstCompVersionName + ", mstComponentType-" + mstCompTypeName;
            logger.warn(log);
            message.put("groupKpi", log);
            return;
        }

        if (isCluster) {
            final Set<Integer> clusterKpiIds = MasterDataService.getAllKpisList().stream().filter(
                    kpi -> !kpi.getClusterOperation().equalsIgnoreCase(Constants.NONE))
                    .map(AllKpiList::getKpiId).collect(Collectors.toSet());

            viewCommonVersionKPIsBeansGroup = viewCommonVersionKPIsBeansGroup.stream().filter(
                    group -> clusterKpiIds.contains(group.getKpiId())).collect(Collectors.toList());
        }
        for (int index = 0; index < viewCommonVersionKPIsBeansGroup.size(); index++) {
            ViewCommonVersionKPIsBean viewCommonVersionKPIsBean = viewCommonVersionKPIsBeansGroup.get(index);
            if (viewCommonVersionKPIsBean.getStatus() == 0) {
                continue;
            }
            if (groupKPIList != null) {
                for (GroupKPIs groupKPIs : groupKPIList) {
                    if (groupKPIs.getKpiId() == viewCommonVersionKPIsBean.getKpiId()) {
                        addGroup(viewCommonVersionKPIsBean, mstCompVersionId, mstCompId, mastCompTypeId,
                                accountId, message, userDetails, compInstanceId, groupKPIs, index,
                                thresholdService, handle, isDiscovery);
                    }
                }
            } else {
                addGroup(viewCommonVersionKPIsBean, mstCompVersionId, mstCompId, mastCompTypeId,
                        accountId, message, userDetails, compInstanceId, null, index,
                        thresholdService, handle, isDiscovery);
            }
        }
    }

    private void addGroup(ViewCommonVersionKPIsBean viewCommonVersionKPIsBean, int mstCompVersionId,
                          int mstCompId, int mastCompTypeId, int accountId, Map<String, String> message,
                          String userDetails, int compInstanceId, GroupKPIs groupKPIs, int index,
                          ThresholdService thresholdService, Handle handle, boolean isDiscovery) throws Exception {

        int collectionInterval = viewCommonVersionKPIsBean.getDefaultCollectionInterval();
        if (groupKPIs != null && groupKPIs.getCollectionInterval() > 0) {
            collectionInterval = groupKPIs.getCollectionInterval();
        }
        int groupId = viewCommonVersionKPIsBean.getKpiGroupId();
        MasterKpiGroupBean groupBean = MasterCache.getGroupKpiDetailList(accountId, groupId);
        if (groupBean == null) {
            String log = "No any group kpi is found in master kpi group details table for group-" + groupId;
            logger.warn(log);
            message.put("groupKpi", log);
            throw new ControlCenterException(log);
        }

        if (isDiscovery != (groupBean.getDiscovery() == 1)) {
            return;
        }

        ProducerKpis producerKpis = new ProducerKpis();
        producerKpis.setMstKpiDetailsId(viewCommonVersionKPIsBean.getKpiId());
        producerKpis.setMstCompVersionId(mstCompVersionId);
        producerKpis.setMstCompId(mstCompId);
        producerKpis.setMstCompTypeId(mastCompTypeId);
        producerKpis.setAccountId(accountId);
        ViewProducerKPIsBean viewProducerKPIsBean = MasterCache.getViewProducerKPIsGroup(producerKpis);
        if (viewProducerKPIsBean == null) {
            String log = "No any producer is found for group kpi - " + viewCommonVersionKPIsBean.getKpiName();
            logger.warn(log);
            message.put("groupKpi", log);
            return;
        }

        MstKpi mstKpi = new MstKpi();
        mstKpi.setAccountId(accountId);
        mstKpi.setKpiId(viewProducerKPIsBean.getMstKpiDetailsId());
        MasterKPIDetailsBean kpiBean = MasterCache.getMasterKPIDetailsBean(mstKpi);
        if (kpiBean == null) {
            String log = "No any group kpi is found in master kpi details table for producer-" +
                    viewProducerKPIsBean.getProducerName();
            logger.warn(log);
            message.put("groupKpi", log);
            throw new ControlCenterException(log);
        }

        CompInstanceKpiGroupDetailsBean compInstanceKpiGroupDetailsBean = new CompInstanceKpiGroupDetailsBean();
        compInstanceKpiGroupDetailsBean.setStatus(groupBean.getStatus());
        compInstanceKpiGroupDetailsBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        compInstanceKpiGroupDetailsBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        compInstanceKpiGroupDetailsBean.setUserDetailsId(userDetails);
        compInstanceKpiGroupDetailsBean.setCompInstanceId(compInstanceId);
        compInstanceKpiGroupDetailsBean.setMstProducerKpiMappingId(viewProducerKPIsBean.getMstProducerKpiMappingId());
        compInstanceKpiGroupDetailsBean.setCollectionInterval(collectionInterval);
        compInstanceKpiGroupDetailsBean.setMstKpiDetailsId(viewCommonVersionKPIsBean.getKpiId());
        compInstanceKpiGroupDetailsBean.setIsDiscovery(groupBean.getDiscovery());
        compInstanceKpiGroupDetailsBean.setKpiGroupName(groupBean.getIdentifier());
        compInstanceKpiGroupDetailsBean.setMstKpiGroupId(groupId);
        compInstanceKpiGroupDetailsBean.setMstProducerId(viewProducerKPIsBean.getProducerId());
        List<Attributes> attributesVal = groupKPIs != null ? groupKPIs.getAttributes() : null;
        if (isDiscovery) {
            attributesVal = null;
            compInstanceKpiGroupDetailsBean.setAttributeValue(Constants.ALL);
            compInstanceKpiGroupDetailsBean.setAliasName(Constants.ALL);
        }
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
        if (attributesVal == null || attributesVal.isEmpty()) {
            int id = compInstanceDataService.addGroupComponentInstanceKPI(
                    compInstanceKpiGroupDetailsBean, handle);
            if (id == -1) {
                String log = "Unable to add group kpi for component instance-" +
                        compInstanceKpiGroupDetailsBean.getCompInstanceId() + " group kpi name-" +
                        kpiBean.getName();
                logger.warn(log);
                message.put("groupKpi-index" + index, log);
                throw new ControlCenterException(log);
            }
            logger.warn("Added group kpi for component instance-" +
                    compInstanceKpiGroupDetailsBean.getCompInstanceId() + " group kpi name-" +
                    kpiBean.getName());
        } else {
            for (Attributes attribute : attributesVal) {
                compInstanceKpiGroupDetailsBean.setAttributeValue(attribute.getValue());
                compInstanceKpiGroupDetailsBean.setAliasName(attribute.getValue());
                int id = compInstanceDataService.addGroupComponentInstanceKPI(
                        compInstanceKpiGroupDetailsBean, handle);
                if (id == -1) {
                    String log = "Unable to add group kpi for component instance-" +
                            compInstanceKpiGroupDetailsBean.getCompInstanceId() + " group kpi name-" +
                            kpiBean.getName();
                    logger.warn(log);
                    message.put("groupKpi-index" + index, log);
                    throw new ControlCenterException(log);
                }
                logger.info("Attribute value-" + attribute.getValue() +
                        " is added for group kpi for component instance-" +
                        compInstanceKpiGroupDetailsBean.getCompInstanceId() + " group kpi name-" +
                        kpiBean.getName());
                thresholdService.addCompInstGroupKpiThreshold(viewCommonVersionKPIsBean.getKpiId(),
                        groupKPIs.getGroupKpiId(), attribute, compInstanceId, userDetails,
                        accountId, handle);
            }
        }
    }

    /**
     * Add host API will call this method
     */
    public ResponseObject<Object> addHost(Request request, Response response) {
        ResponseObject<Object> responseObject = new ResponseObject<>();
        List<ComponentInstance> invalidJsonObjects;
        try {
            String accountIdString = request.params(IDENTIFIER);
            int accountId = ValidationUtils.validAndGetIdentifier(accountIdString);
            /*
             * Check the request body before doing processing
             */
            if (request.body() == null) {
                responseObject.setResponseStatus(StatusResponse.FAILURE.name());
                responseObject.setMessage(REQUEST_BODY_NULL_LOGGER_STRING);
                response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
                logger.error(REQUEST_BODY_NULL_LOGGER_STRING);
                return responseObject;
            }
            String authKey = request.headers(AUTHORIZATION);
            JWTData jwtData = KeyCloakAuthService.extractUserDetails(authKey);
            String userId = jwtData.getSub();
            invalidJsonObjects = addHostsDetails(request.body(), userId, accountId);
            if (!invalidJsonObjects.isEmpty()) {
                responseObject.setMessage("Invalid host instance details.");
                responseObject.setInvalidJsonObjects(invalidJsonObjects);
                responseObject.setResponseStatus(StatusResponse.FAILURE.name());
                response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            } else {
                responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            }
        } catch (RequestException e) {
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            logger.error("Error : ", e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
        return responseObject;
    }

    public List<ComponentInstance> addHostsDetails(String jsonString, String userId, int accountId) throws Exception {
        List<ComponentInstance> invalidJsonObjects = new ArrayList<>();

        List<ComponentInstance> componentInstanceList;
        try {
            componentInstanceList = objectMapper.readValue(jsonString, new TypeReference<List<ComponentInstance>>() {
            });
        } catch (IOException e) {
            logger.error(Constants.JSON_PARSE_ERROR + " : {}", e.getMessage());
            throw new RequestException("Error in parsing JSON request");
        }
        /*
         * Is given account id is valid
         */
        if (accountId == -1) {
            logger.error(INVALID_ACC_ID_STRING);
            throw new ControlCenterException(INVALID_ACC_ID_STRING);
        }
        /*
         * If component instance object is null or empty
         */
        if (componentInstanceList == null || componentInstanceList.isEmpty()) {
            logger.warn(NO_COMPONENT_INSTANCE_STRING);
            throw new ControlCenterException(NO_COMPONENT_INSTANCE_STRING);
        }

        List<ComponentInstance> validComponentInstanceList = new ArrayList<>();
        /*
         * Validate json data
         */
        for (ComponentInstance componentInstance : componentInstanceList) {
            componentInstance.validateMandatoryFields();
            if (!componentInstance.getErrorMessage().isEmpty()) {
                invalidJsonObjects.add(componentInstance);
                continue;
            }
            validComponentInstanceList.add(componentInstance);
        }

        controllerMap = MasterDataService.getAllControllerList().parallelStream()
                .filter(c -> c.getControllerTypeId() == serviceTypeId || c.getControllerTypeId() == applicationTypeId)
                .collect(Collectors.groupingBy(Controller::getAccountId,
                        Collectors.toMap(Controller::getIdentifier, c -> IdPojo.builder()
                                .id(Integer.parseInt(c.getAppId()))
                                .identifier(c.getIdentifier())
                                .name(c.getName())
                                .build())));

        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        dbi.inTransaction((conn, status) -> {
            for (ComponentInstance componentInstance : validComponentInstanceList) {
                try {
                    componentInstance.setIsHost(1);
                    addHost(componentInstance, accountId, userId, controllerMap, conn);
                } catch (ControlCenterException e) {
                    throw new RuntimeException(e.getMessage());
                }
            }
            return "Nothing to return";
        });
        for (ComponentInstance componentInstance : validComponentInstanceList) {
            componentInstance.setIsHost(1);
            if (!componentInstance.getErrorMessage().isEmpty()) {
                invalidJsonObjects.add(componentInstance);
            }
        }
        return invalidJsonObjects;
    }

    private boolean isCompInstanceAlreadyAvailable(ComponentInstance componentInstance, int mstComponentId, int accountId, List<AttributesViewBean> attributesViewBeanList, Handle handle) {
        if (attributesViewBeanList == null || attributesViewBeanList.isEmpty()) {
            return false;
        }
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
        BindInDataService bindInDataService = new BindInDataService();
        ComponentInstanceBean componentInstanceBean = compInstanceDataService.getComponentInstanceBean(componentInstance.getIdentifier(), componentInstance.getName(), accountId, handle);
        if (componentInstanceBean != null) {
            String log = componentInstanceBean.getName() + " is already available in DB for account id - " + componentInstanceBean.getAccountId();
            componentInstance.putErrorMessage("componentInstance", log);
            logger.warn(log);
            return true;
        }

        Map<String, AttributesViewBean> map = new HashMap<>();
        /*
         * Create map for app mandatory fields
         * Key :  name of attribute
         * Value: AttributesViewBean object
         */
        attributesViewBeanList.stream().filter(attributesViewBean -> attributesViewBean.getIsMandatory() == 1).forEach(a -> map.put(a.getAttributeName(), a));

        /*
         * Check all mandatory fields
         */
        List<Attributes> attributesList = componentInstance.getAttributes();
        if (attributesList == null || attributesList.isEmpty()) return false;
        /*
         * List all mandatory attribute values
         */
        List<String> attributesVal = attributesList.stream().filter(attributes -> map.containsKey(attributes.getName())).map(Attributes::getValue).collect(Collectors.toList());

        /*
         * List all mandatory attributes with name and value
         */
        List<Attributes> mandatoryAttributesList = attributesList.stream().filter(attributes -> map.containsKey(attributes.getName())).collect(Collectors.toList());
        if (attributesVal.isEmpty()) return false;
        /*
         * Get list of component instances with attribute name and value
         */
        List<ComponentInstanceAttributesBean> componentInstanceAttributesBeanList = bindInDataService.getCompInstBasedInAttributeValues(handle, attributesVal, accountId, mstComponentId);
        /*
         * If no any component instance is found for given mandatory attribute values then return false
         */
        if (componentInstanceAttributesBeanList == null || componentInstanceAttributesBeanList.isEmpty()) return false;
        Map<Integer, List<Attributes>> instantIdAttributesMap = new HashMap<>();
        /*
         * Make a list of component attributes bean
         */
        for (ComponentInstanceAttributesBean compInstIds : componentInstanceAttributesBeanList) {
            List<Attributes> instanceAttributesBeans = instantIdAttributesMap.get(compInstIds.getComponentInstanceId());
            if (instanceAttributesBeans == null) instanceAttributesBeans = new ArrayList<>();
            Attributes attributes = new Attributes();
            attributes.setName(compInstIds.getAttributeName());
            attributes.setValue(compInstIds.getAttributeValue());
            instanceAttributesBeans.add(attributes);
            instantIdAttributesMap.put(compInstIds.getComponentInstanceId(), instanceAttributesBeans);
        }
        /*
         * If all mandatory field is already available in DB then return true
         */
        for (Map.Entry<Integer, List<Attributes>> m : instantIdAttributesMap.entrySet()) {
            List<Attributes> customInstanceList = m.getValue();
            if (new HashSet<>(customInstanceList).containsAll(mandatoryAttributesList)) {
                String log = "component instance with ids-" + customInstanceList.get(0).toString() + " is already available for attributes -" + mandatoryAttributesList;
                logger.warn(log);
                componentInstance.putErrorMessage("attributes", log);
                return true;
            }
        }
        return false;
    }

    public static GenericResponse<List<IdPojo>> addComponentInstances(Request request, Response response) {
        GenericResponse<List<IdPojo>> responseObject = new GenericResponse<>();
        logger.trace("Method Invoked : ComponentInstanceService/addComponentInstance");

        try {

            List<ComponentInstancePojo> instances = ComponentInstanceBL.addClientValidations(request);
            List<ComponentInstanceBean> beanList = ComponentInstanceBL.addServerValidations(instances, request.headers(Constants.AUTHORIZATION), request.params(Constants.ACCOUNT_IDENTIFIER), Collections.emptyList(), null);
            List<IdPojo> process = ComponentInstanceBL.process(beanList, request.params(Constants.ACCOUNT_IDENTIFIER));
            responseObject.setData(process);
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(UIMessages.COMP_INSTANCE_ADD_SUCCESS);
            response.status(Constants.SUCCESS_STATUS_CODE);

        } catch (RequestException e) {
            logger.error("Invalid Request.", e);
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (ControlCenterException e) {
            logger.error("Validation errors.", e);
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            logger.error("Error occurred while adding component instance.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        return responseObject;
    }

    public GenericResponse<String> updateComponentInstance(Request request, Response response) {
        GenericResponse<String> responseObject = new GenericResponse<>();
        logger.trace("Method Invoked : ComponentInstanceService/updateComponentInstance");

        try {
            ComponentInstancePojo instance = ComponentInstanceBL.updateClientValidations(request);
            ComponentInstanceBean bean = ComponentInstanceBL.updateServerValidations(instance, request.headers(Constants.AUTHORIZATION), request.params(Constants.ACCOUNT_IDENTIFIER));
            ComponentInstanceBL.update(bean);

            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(UIMessages.COMP_INSTANCE_UPDATE_SUCCESS);
            response.status(Constants.SUCCESS_STATUS_CODE);

        } catch (RequestException e) {
            logger.error("Invalid Request.", e);
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (ControlCenterException e) {
            logger.error("Validation errors.", e);
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            logger.error("Error occurred while removing component instance.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        return responseObject;
    }

    private static void addController(Tags t, int compInstanceId, int accountId, String userId, Map<Integer, Map<String,
            IdPojo>> controllers, Handle handle) throws ControlCenterException {

        TagDetailsBean details = MasterCache.getTagDetails(Constants.CONTROLLER_TAG);
        if(Objects.isNull(details)) {
            logger.error("Failed to add tag mapping for component instance = {}", compInstanceId);
            return;
        }

        int tagId = details.getId();
        IdPojo c = null;

        if (controllers.containsKey(accountId) && controllers.get(accountId).containsKey(t.getIdentifier())) {
            c = controllers.get(accountId).get(t.getIdentifier());
        } else {
            if (Constants.SERVICES_CONTROLLER_TYPE.equalsIgnoreCase(t.getSubTypeName())) {
                c = AddServiceBL.addService(ServiceBean.builder()
                        .accountId(accountId)
                        .userId(userId)
                        .identifier(t.getIdentifier())
                        .name(t.getIdentifier())
                        .build(), handle);
            } else if (Constants.APPLICATION_CONTROLLER_TYPE.equalsIgnoreCase(t.getSubTypeName())) {
                ControllerBean controllerBean = ControllerBL.addController(ServiceBean.builder()
                        .accountId(accountId)
                        .userId(userId)
                        .identifier(t.getIdentifier())
                        .name(t.getIdentifier())
                        .build(), applicationTypeId, 0, handle);

                c = IdPojo.builder()
                        .identifier(t.getIdentifier())
                        .name(t.getIdentifier())
                        .id(controllerBean.getId())
                        .build();
            }

            if (!controllers.containsKey(accountId)) {
                controllers.put(accountId, new HashMap<>());
            }

            controllers.get(accountId).put(t.getIdentifier(), c);
        }

        if(Objects.isNull(c)) {
            logger.error("Failed to add tag mapping for component instance = {}", compInstanceId);
            return;
        }

        int tagMappingId = TagMappingBL.addTagMapping(tagId, compInstanceId, Constants.COMP_INSTANCE_TABLE, String.valueOf(c.getId()),
                c.getIdentifier(), userId, accountId, handle);

        if (tagMappingId < 1) {
            logger.error("Failed to add tag mapping for component instance = {}", compInstanceId);
        } else {
            logger.info("Successfully added tag for instance mapping, tagId = {}", tagMappingId);
        }
    }
}
