package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.AutoDiscoveryEntityStatusBL;
import com.appnomic.appsone.controlcenter.common.*;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.AutoDiscoveryEntityStatusPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;

import com.appnomic.appsone.controlcenter.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;

import spark.Request;
import spark.Response;

import java.util.List;

@Slf4j
public class AutoDiscoveryEntityStatusService {

    public GenericResponse isIgnored(Request request, Response response) {
        GenericResponse responseObject = new GenericResponse();
        AutoDiscoveryEntityStatusBL autoDiscoveryEntityStatusBL = new AutoDiscoveryEntityStatusBL();

        try{
            RequestObject requestObject = new RequestObject(request);
            UtilityBean<AutoDiscoveryEntityStatusPojo> utilityBean = autoDiscoveryEntityStatusBL.clientValidation(requestObject);
            AutoDiscoveryEntityStatusPojo adIgnoreHostsBean = autoDiscoveryEntityStatusBL.serverValidation(utilityBean);
            List<String> invalidJsonObjects = autoDiscoveryEntityStatusBL.process(adIgnoreHostsBean);

            if (!invalidJsonObjects.contains("CANNOT-UNIGNORE")) {
                if (invalidJsonObjects.size() == adIgnoreHostsBean.getIdentifiers().length) {
                    responseObject.setData("Invalid Json Objects :" + invalidJsonObjects);
                    responseObject.setMessage("Invalid identifier(s) found.");
                    responseObject.setResponseStatus(StatusResponse.FAILURE.name());
                    response.status(Constants.VALIDATION_FAILED_STATUS_CODE);
                } else if (invalidJsonObjects.isEmpty()) {
                    responseObject.setData("Entity(s) status changed successfully.");
                    responseObject.setMessage("No invalid objects found.");
                    responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
                    response.status(Constants.SUCCESS_STATUS_CODE);
                } else if (invalidJsonObjects.size() < adIgnoreHostsBean.getIdentifiers().length && !invalidJsonObjects.isEmpty()) {
                    responseObject.setData("Invalid identifier(s) found.");
                    responseObject.setMessage("Invalid Json Objects :" + invalidJsonObjects);
                    responseObject.setResponseStatus(StatusResponse.PARTIAL_FAILURE.name());
                    response.status(Constants.SUCCESS_STATUS_CODE);
                }
            } else if (invalidJsonObjects.contains("CANNOT-UNIGNORE")) {
                invalidJsonObjects.remove("CANNOT-UNIGNORE");
                responseObject.setData("Invalid Json Objects :" + invalidJsonObjects);
                responseObject.setMessage("The host of this entity(s) is ignored. Please undo ignore on the host first.");
                responseObject.setResponseStatus(StatusResponse.FAILURE.name());
                response.status(Constants.VALIDATION_FAILED_STATUS_CODE);
            }
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Error details: {}", e);
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            log.error("Error while processing the request. {}.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        return responseObject;
    }
}
