package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.ControlCenterDetailsBean;
import com.appnomic.appsone.controlcenter.beans.TagDetailsBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.ControlCenterDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.Controller;
import com.appnomic.appsone.controlcenter.pojo.IdValuePojo;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.*;
import java.util.stream.Collectors;

public class ControlCenterService {

    private ControlCenterService(){}
    private static final Logger logger = LoggerFactory.getLogger(ControlCenterService.class);
       public static GenericResponse getServiceList(Request request, Response response) {
        GenericResponse<List<ControlCenterDetailsBean>> responseObject = new GenericResponse<>();
        List<ControlCenterDetailsBean> data = new ArrayList<>();
        try {
            String accountIdString = request.params(":identifier");
            int accountId = ValidationUtils.validAndGetIdentifier(accountIdString);
            /**
             * Is given account id is valid
             */
            if (accountId == -1) {
                return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(), 400, "Invalid account id provided.", null, true);
            }

            ViewTypes serviceType = MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT, Constants.SERVICES_CONTROLLER_TYPE);
            if (serviceType == null) {
                return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(), 400, "unable to fetch subType information for provided tag value." + "Services", null, true);
            }

            ViewTypes jimAgentType = MasterCache.getMstTypeForSubTypeName(Constants.AGENT_DATA_TYPE, Constants.JIM_AGENT_TYPE);
            if (jimAgentType == null) {
                return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(), 400, "unable to fetch subType information for provided tag value." + "Services", null, true);
            }

            TagDetailsBean controllerTag = MasterCache.getTagDetails(Constants.CONTROLLER_TAG);
            if (controllerTag == null) {
                return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(), 400, "Tag detail is not found for given tag name-" + "JIMAgent" + " and account id:" + accountId, null, true);
            }


            //Get all the service ids for work load agent. (eg: JIM)
            List<Integer> serviceIds = ControlCenterService.getServiceList(jimAgentType.getSubTypeId(), controllerTag.getId(), Constants.AGENT_TABLE, accountId);

            Set<Integer> serviceIdSet = new HashSet<>(serviceIds);
            //Get all the service details for account id.
            List<Controller> controllerService=CommonUtils.getControllersByTypeBypassCache(Constants.SERVICES_CONTROLLER_TYPE, accountId);
            if(serviceIdSet.isEmpty()) {
                responseObject.setMessage("No services found for work load agents.");
                responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
                return responseObject;
            }

            data = serviceIdSet.stream()
                .map(id -> {
                    Optional<Controller> serviceDetails = controllerService.stream()
                        .filter(controller -> Integer.parseInt(controller.getAppId()) == id)
                        .findFirst();
                    if (serviceDetails.isPresent()) {
                        ControlCenterDetailsBean bean = new ControlCenterDetailsBean();
                        bean.setId(Integer.parseInt(serviceDetails.get().getAppId()));
                        bean.setName(serviceDetails.get().getName());
                        bean.setStatus(serviceDetails.get().getStatus());
                        int txnCount = ControlCenterService.getTotalTransaction(bean.getId(), Constants.TXN_TABLE,
                                controllerTag.getId(), accountId);
                        bean.setTxnCount(txnCount);
                        return bean;
                    } else {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            data.sort(Comparator.comparing(ControlCenterDetailsBean::getStatus, Comparator.reverseOrder())
                    .thenComparing(o -> o.getName().toLowerCase()));

            responseObject.setMessage("");
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setData(data);
        } catch (Exception e) {
            logger.error("Error occurred while getting the service Details.", e);
            responseObject.setMessage("Requested service data is not available for selected account.");
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
        }
        return responseObject;
    }


    static List<Integer> getServiceList(int agentTypeId, int tagId, String agentType, int accountId) {
        ControlCenterDao controlCenterDao = MySQLConnectionManager.getInstance().open(ControlCenterDao.class);
        try {
            return controlCenterDao.getServiceList(tagId,agentType,accountId,agentTypeId);
        } catch (Exception e) {
            logger.error("Exception while retrieving the service  details list, accountId:{}", accountId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(controlCenterDao);
        }
        return Collections.emptyList();
    }

    public static int getTotalTransaction(int tagValue, String transaction, int tagId, int accountId) {
        ControlCenterDao controlCenterDao = MySQLConnectionManager.getInstance().open(ControlCenterDao.class);
        try {
            return controlCenterDao.getTotalTransaction(tagValue, transaction, tagId, accountId);
        } catch (Exception e) {
            logger.error("Exception while retrieving the total transaction list", e);
        } finally {
            MySQLConnectionManager.getInstance().close(controlCenterDao);
        }
        return 0;
    }

    public static List<IdValuePojo> getAllTransaction(String transactionTable, int tagId, int accountId) {
        ControlCenterDao controlCenterDao = MySQLConnectionManager.getInstance().open(ControlCenterDao.class);
        try {
            return controlCenterDao.getAllTransaction(transactionTable, tagId, accountId);
        } catch (Exception e) {
            logger.error("Exception while retrieving the total transaction list", e);
        } finally {
            MySQLConnectionManager.getInstance().close(controlCenterDao);
        }
        return new ArrayList<>();
    }
}
