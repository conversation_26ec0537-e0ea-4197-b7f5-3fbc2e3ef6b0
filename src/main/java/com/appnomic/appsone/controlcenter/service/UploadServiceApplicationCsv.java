package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.TagDetailsBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.ConnectionConfig;
import com.appnomic.appsone.controlcenter.pojo.Controller;
import com.appnomic.appsone.controlcenter.pojo.ServiceAppUpload;
import com.appnomic.appsone.controlcenter.pojo.TagMappingDetails;
import com.appnomic.appsone.controlcenter.dao.mysql.TagsDataService;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.appnomic.appsone.model.JWTData;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.skife.jdbi.v2.DBI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class UploadServiceApplicationCsv {
    private static final Logger logger = LoggerFactory.getLogger(UploadServiceApplicationCsv.class);

    public GenericResponse<String> uploadServiceApplicationDetails(Request request, Response response) {
        GenericResponse<String> responseObject = new GenericResponse<>();
        try {

            String authKey = request.headers("Authorization");
            JWTData jwtData = KeyCloakAuthService.extractUserDetails(authKey);
            String userId = jwtData.getSub();
            String identifier = request.params(":identifier");
            int accountId = ValidationUtils.validAndGetIdentifier(identifier);

            if (accountId == -1) {
                responseObject.setResponseStatus(StatusResponse.FAILURE.name());
                responseObject.setMessage("Invalid account id provided");
                response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
                logger.error("Invalid account id provided");
                return responseObject;
            }

            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            responseObject = dbi.inTransaction((handle, status) -> importCsvFile(request, accountId, userId));

            if (!StatusResponse.SUCCESS.toString().equalsIgnoreCase(responseObject.getResponseStatus())) {
                responseObject.setResponseStatus(StatusResponse.FAILURE.name());
                responseObject.setMessage("Problem saving the uploaded file.");
                response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
                logger.error("Problem saving the uploaded file.");
                return responseObject;
            }

            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setData(responseObject.getData());
            responseObject.setMessage("File uploaded successfully");
            response.status(Constants.SUCCESS_STATUS_CODE);

        } catch (Exception e) {
            logger.error("Exception encountered while processing CSV upload file. Reason: {}", e.getMessage(), e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }

        return responseObject;
    }

    private GenericResponse<String> importCsvFile(Request request, int accountId, String userId) {
        long st = System.currentTimeMillis();
        GenericResponse<String> response = new GenericResponse<>();
        TagsService tagsService = new TagsService();

        try {
            DiskFileItemFactory factory = new DiskFileItemFactory();
            ServletFileUpload fileUpload = new ServletFileUpload(factory);
            List<FileItem> items = fileUpload.parseRequest(request.raw());
            FileItem item = items.stream()
                    .filter(e -> "file".equals(e.getFieldName()))
                    .findFirst().orElse(null);

            if (item == null) {
                logger.error("File field not exist in request for given  accountId: {}", accountId);
                response.setResponseStatus(StatusResponse.FAILURE.name());
                response.setMessage("Problem saving the upload details because item is empty.");

                return response;
            } else {
                String fileName = item.getName();
                item.write(new File(fileName));
                Pattern pattern = Pattern.compile(",");
                try (BufferedReader csvFile = new BufferedReader(new FileReader(fileName))) {
                    Set<ServiceAppUpload> importFiles = csvFile.lines().skip(1).map(row -> {
                        String[] x = pattern.split(row, 3);
                        return new ServiceAppUpload(x[0].trim(), x[1].trim(), x[2].trim());
                    }).collect(Collectors.toSet());

                    ViewTypes controllerSubType = MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT, Constants.SERVICES_CONTROLLER_TYPE.trim());
                    if (Objects.isNull(controllerSubType)) {
                        logger.error("unable to fetch subType information for provided tag value {}: ", Constants.SERVICES_CONTROLLER_TYPE);
                    }

                    List<Controller> serviceList = CommonUtils.getControllersByTypeBypassCache(Constants.SERVICES_CONTROLLER_TYPE, accountId);
                    importFiles.stream().map(ServiceAppUpload::getServices)
                            .filter(it -> it.trim().length() > 0)
                            .filter(serviceName -> serviceList.stream().noneMatch(con -> con.getName().equals(serviceName) || con.getIdentifier().equals(serviceName))).distinct()
                            .forEach(serviceName -> tagsService.addController(accountId, serviceName.trim(), UUID.randomUUID().toString(), userId, controllerSubType.getSubTypeId(), null));

                    ViewTypes appSubType = MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT, Constants.APPLICATION_CONTROLLER_TYPE.trim());
                    if (Objects.isNull(appSubType)) {
                        logger.error("unable to fetch subType information for provided tag value {}: ", Constants.APPLICATION_CONTROLLER_TYPE);
                    }

                    List<Controller> appList = CommonUtils.getControllersByTypeBypassCache(Constants.APPLICATION_CONTROLLER_TYPE, accountId);
                    importFiles.stream().map(ServiceAppUpload::getApplication)
                            .filter(it -> it.trim().length() > 0)
                            .filter(appName -> appList.stream().noneMatch(con -> con.getName().equals(appName) || con.getIdentifier().equals(appName))).distinct()
                            .forEach(appName -> tagsService.addController(accountId, appName.trim(), UUID.randomUUID().toString(), userId, appSubType.getSubTypeId(), null));

                    TagDetailsBean controllerTag = MasterCache.getTagDetails(Constants.CONTROLLER_TAG);

                    for (ServiceAppUpload importFile : importFiles) {
                        List<Controller> serviceDetailList = CommonUtils.getControllersByTypeBypassCache(Constants.SERVICES_CONTROLLER_TYPE, accountId);
                        Controller serviceController = serviceDetailList.stream().filter(con -> con.getName().equals(importFile.getServices().trim()) || con.getIdentifier().equals(importFile.getServices().trim())).findAny().orElse(null);

                        List<Controller> appDetailList = CommonUtils.getControllersByTypeBypassCache(Constants.APPLICATION_CONTROLLER_TYPE, accountId);
                        Controller appController = appDetailList.stream().filter(con -> con.getName().equals(importFile.getApplication().trim()) || con.getIdentifier().equals(importFile.getApplication().trim())).findAny().orElse(null);

                        if (serviceController == null || appController == null) continue;

                        List<TagMappingDetails> applicationServices = TagsDataService.getTagKeyId(controllerTag.getId(), Integer.parseInt(appController.getAppId()), Constants.CONTROLLER, serviceController.getAppId(), accountId);
                        TagMappingDetails serviceFilter = applicationServices.stream().filter(key -> key.getTagKey().equals(serviceController.getAppId())).findAny().orElse(null);
                        if (serviceFilter == null) {
                            TagMappingDetails tagMappingDetails = tagsService.createTagMappingDetailsObj(controllerTag.getId(), Integer.parseInt(appController.getAppId()), Constants.CONTROLLER,
                                    serviceController.getAppId(), serviceController.getIdentifier(), userId, accountId);
                            if (tagMappingDetails != null) {
                                TagsDataService.importTagMapping(tagMappingDetails);
                            }
                        }
                    }
                    // for connection details
                    DependencyConnectionService dependencyConnectionService = new DependencyConnectionService();
                    List<ConnectionConfig> validatedConnections = new ArrayList<>();
                    for (ServiceAppUpload importFile : importFiles) {
                        if (importFile.getConnections() == null || importFile.getConnections().isEmpty()) continue;
                        importFile.getConnections().forEach(c -> {
                            ConnectionConfig connectionDetailsBean = new ConnectionConfig();
                            connectionDetailsBean.setSourceServiceName(c.getSourceRefObject());
                            connectionDetailsBean.setDestinationServiceName(c.getDestinationRefObject());
                            connectionDetailsBean.setIsDiscovery(0);
                            validatedConnections.add(connectionDetailsBean);
                        });
                    }
                    List<ConnectionConfig> validatedList = dependencyConnectionService.validateConnections(validatedConnections, accountId);
                    List<ConnectionConfig> filteredList = validatedList.stream().filter(it -> (it.getStatus().equals(""))).collect(Collectors.toList());
                    if (!filteredList.isEmpty()) {
                        dependencyConnectionService.loadConnections(accountId);
                        dependencyConnectionService.addConnections(filteredList, accountId, userId);
                    }

                } catch (Exception e) {
                    logger.error("Error while importing file.", e);
                    response.setResponseStatus(StatusResponse.FAILURE.name());
                    response.setMessage("Error in processing input file. Please refer to log file for more details.");
                    response.setData("0");

                    return response;
                }
                Files.deleteIfExists(Paths.get(fileName));
            }
        } catch (Exception ex) {
            logger.error("Error while importing file.", ex);
            response.setResponseStatus(StatusResponse.FAILURE.name());
            response.setMessage("Error in processing input file. Please refer to log file for more details.");
            response.setData("0");

            return response;
        } finally {
            logger.debug("Time taken to complete the file processing is {} ms.", System.currentTimeMillis() - st);
        }

        response.setData("100");
        response.setMessage("Input file successfully processed.");
        response.setResponseStatus(StatusResponse.SUCCESS.name());

        return response;
    }
}
