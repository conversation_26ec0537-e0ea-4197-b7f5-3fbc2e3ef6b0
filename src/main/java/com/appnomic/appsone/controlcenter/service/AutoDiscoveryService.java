package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.AddToSystemBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.google.common.base.Throwables;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

public class AutoDiscoveryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AutoDiscoveryService.class);

    public GenericResponse<String> addToSystem(Request request, Response response) {
        GenericResponse<String> responseObject;
        try {
            RequestObject requestObject = new RequestObject(request);
            AddToSystemBL bl = new AddToSystemBL();

            UtilityBean<List<String>> clientValidation = bl.clientValidation(requestObject);
            UtilityBean<List<String>> serverValidation = bl.serverValidation(clientValidation);
            String data = bl.process(serverValidation);

            responseObject = CommonUtils.getGenericResponse(response, StatusResponse.SUCCESS.name(), Constants.SUCCESS_STATUS_CODE, "Host(s) are added to system.", null, false);
            responseObject.setData(data);
            return responseObject;
        } catch (ClientException | ServerException | DataProcessingException e) {
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, Throwables.getRootCause(e).getMessage(), null, true);
        } catch (Exception e) {
            LOGGER.error("Error occurred while adding data to system.", e);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, e.getMessage(), null, true);
        }
    }
}
