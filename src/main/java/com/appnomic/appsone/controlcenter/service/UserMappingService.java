package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.UserAccessDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.UserDataService;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.google.common.base.Throwables;
import com.google.common.util.concurrent.AbstractScheduledService;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.exceptions.CallbackFailedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.TimeUnit;


public class UserMappingService  extends AbstractScheduledService {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserMappingService.class);
    private static final int USER_SCHEDULER_TIME = ConfProperties.getInt(Constants.USER_SCHEDULER_TIME_PROPERTY, Constants.USER_SCHEDULER_TIME_DEFAULT);
    private static final int enableScheduler = ConfProperties.getInt(Constants.ENABLE_USER_MAPPING_SCHEDULER, Constants.ENABLE_USER_MAPPING_SCHEDULER_DEFAULT);

    @Override
    protected void runOneIteration() {
        try {
            LOGGER.info("UserMappingService invoked.");
            if(enableScheduler != 1) {
                LOGGER.info("Disabled the user mapping service scheduler.");
                return;
            }
            List<String> userDetails = UserAccessDataService.getUserIdentifiers();
            if (!userDetails.isEmpty()) {
                List<String> keycloakUserIds = UserAccessDataService.getKeycloakUserIdentifiers();
                for (String userId : userDetails) {
                    if (keycloakUserIds == null || !keycloakUserIds.contains(userId)) {
                        LOGGER.info("User [{}] exists in appsone, but unavailable in keycloak", userId);
                        deleteData(userId);
                        LOGGER.info("User [{}] deleted from appsone as this user is unavailable in keycloak", userId);
                    }
                }
            }
        } catch (Exception e){
            LOGGER.error("Error occurred while user mapping scheduler execute", e);
        }

    }

    private void deleteData(String userId) throws ControlCenterException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();

            dbi.inTransaction((conn, status) -> {
                NotificationPreferencesDataService.removeNotificationDetailsForUser(userId, conn);
                NotificationPreferencesDataService.removeUserNotificationPreferencesForUser(userId, conn);
                NotificationPreferencesDataService.removeForensicNotificationPreferencesForUser(userId, conn);
                new UserDataService().deleteUserAttributesAndAccessDetails(userId, conn);
                return userId;
            });
        } catch (CallbackFailedException e) {
            LOGGER.error("Exception encountered when deleting redundant users in scheduler. Details: {}", e.getMessage(), e);
            throw new ControlCenterException(Throwables.getRootCause(e).getMessage());
        }
    }

    @Override
    protected Scheduler scheduler() {
        try {
            return Scheduler.newFixedRateSchedule(1L, USER_SCHEDULER_TIME, TimeUnit.MINUTES);
        }   catch (Exception e) {
            LOGGER.error("Error while creating scheduler for user mapping",e);
        }
        return null;
    }
}
