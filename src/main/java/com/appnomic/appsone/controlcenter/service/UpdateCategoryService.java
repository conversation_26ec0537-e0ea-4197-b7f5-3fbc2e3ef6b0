package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.CategoryDetailBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.GetCategories;
import com.appnomic.appsone.controlcenter.businesslogic.UpdateCategory;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.CategoryDetails;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

public class UpdateCategoryService {
    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateCategoryService.class);

    public GenericResponse<IdPojo> updateCategory(Request request, Response response) {

        GenericResponse<IdPojo> responseObject = new GenericResponse<>();
        UpdateCategory updateCategory = new UpdateCategory();
        try {
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<CategoryDetails> categoryDetailsUtilityBean = updateCategory.clientValidation(requestObject);
            CategoryDetailBean categoryDetailBean = updateCategory.serverValidation(categoryDetailsUtilityBean);

            responseObject.setData(updateCategory.process(categoryDetailBean));

            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(UIMessages.CATEGORY_UPDATE_SUCCESS);
            response.status(Constants.SUCCESS_STATUS_CODE);
            return responseObject;

        }
        catch (ClientException | ServerException | DataProcessingException e) {
            LOGGER.error("Error while processing update category request. Details: {}", e.getMessage(), e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        }
        catch (Exception e) {
            LOGGER.error("Error while updating the category.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
    }
}