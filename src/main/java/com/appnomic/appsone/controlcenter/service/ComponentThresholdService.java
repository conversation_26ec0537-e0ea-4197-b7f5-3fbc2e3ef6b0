package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.ResponseObject;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.ComponentThreshold;
import com.appnomic.appsone.controlcenter.pojo.GroupKPIs;
import com.appnomic.appsone.controlcenter.pojo.KPI;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.appnomic.appsone.model.JWTData;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> : 15/3/19
 */
public class ComponentThresholdService {

    private static final Logger logger = LoggerFactory.getLogger(ComponentThresholdService.class);
    private ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    public ResponseObject addComponentThreshold(Request request, Response response) {
        ResponseObject responseObject = new ResponseObject();
        List<ComponentThreshold> invalidJsonObjects = new ArrayList<>();
        List<ComponentThreshold> errorMessages = new ArrayList<>();
        try {
            String identifier = request.params(":identifier");
            int accountId = ValidationUtils.validAndGetIdentifier(identifier);
            String authKey = request.headers("Authorization");
            JWTData jwtData = KeyCloakAuthService.extractUserDetails(authKey);
            String userId = jwtData.getSub();
            /*
             * Check the request body before doing processing
             */
            if(request.body() == null) {
                responseObject.setResponseStatus(StatusResponse.FAILURE.name());
                responseObject.setMessage("Request body can not be null.");
                response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
                logger.error("Request body can not be null.");
                return responseObject;
            }

            List<ComponentThreshold> componentList;
            try {
                componentList = objectMapper.readValue(request.body(), new TypeReference<List<ComponentThreshold>>() {
                });
            } catch (IOException e) {
                responseObject.setResponseStatus(StatusResponse.FAILURE.name());
                responseObject.setMessage(Constants.JSON_PARSE_ERROR);
                response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
                logger.error(Constants.JSON_PARSE_ERROR + " : {}", e.getMessage());
                return responseObject;
            }
            /*
             * Is given account id is valid
             */

            if(accountId == -1){
                responseObject.setResponseStatus(StatusResponse.FAILURE.name());
                responseObject.setMessage("Invalid account id provided");
                response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
                logger.error("Invalid account id provided");
            }
            /*
             * If component instance object is null or empty
             */
            if (componentList == null || componentList.isEmpty()){
                responseObject.setResponseStatus(StatusResponse.FAILURE.name());
                responseObject.setMessage("Received empty list of component instance objects");
                response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
                logger.warn("Received empty list of component instance objects");
            }
            /*
             * Validate json data
             */
            for (ComponentThreshold component : componentList) {
                component.validateComponentFields();
                if (!component.getErrorMessage().isEmpty()) {
                    invalidJsonObjects.add(component);
                }
            }
            if (!invalidJsonObjects.isEmpty()) {
                responseObject.setInvalidJsonObjects(invalidJsonObjects);
                responseObject.setResponseStatus(StatusResponse.FAILURE.name());
                responseObject.setMessage("Invalid data are provided");
                response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
                return responseObject;
            }

            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            ResponseObject finalResponseObject = responseObject;
            responseObject = dbi.inTransaction((conn, status) -> {
                for (ComponentThreshold component : componentList) {
                    addComponentThreshold(component, accountId, userId, conn);
                }
                finalResponseObject.setResponseStatus(StatusResponse.SUCCESS.name());
                return finalResponseObject;
            });
            List<ComponentThreshold> result = new ArrayList<>();
            for (ComponentThreshold component : errorMessages) {
                if (!component.getErrorMessage().isEmpty()) result.add(component);
            }
            responseObject.setMessage(result.toString());
            responseObject.setResult(result);
        } catch (Exception e) {
            logger.error("Error while converting request object into component instance object", e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(errorMessages.toString());
            responseObject.setResult(errorMessages);
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        }
        responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
        return responseObject;
    }

    private void addComponentThreshold(ComponentThreshold component, int accountId, String userId, Handle handle) throws Exception {

        ThresholdService thresholdService = new ThresholdService();
        for (KPI kp : component.getKpi()) {
            thresholdService.addComponentThresholdDetails(kp, component.getComponentId(), userId, accountId, component.getCommonVersionId(),  handle);

        }
        for (GroupKPIs groupKPIs : component.getGroupKpi()) {
            thresholdService.addComponentGroupThreshold(groupKPIs, component.getComponentId(), userId, accountId, component.getCommonVersionId(),  handle);

        }

    }
}
