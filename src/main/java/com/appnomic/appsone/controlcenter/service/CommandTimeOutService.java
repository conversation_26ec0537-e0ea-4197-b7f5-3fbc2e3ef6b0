package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentStatusDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CommandDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.RulesDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandTriggerBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.PhysicalAgentBean;
import com.appnomic.appsone.controlcenter.dao.opensearch.AgentHealthStatusRepo;
import com.appnomic.appsone.controlcenter.exceptions.CommandException;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.google.common.util.concurrent.AbstractScheduledService;
import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
public class CommandTimeOutService extends AbstractScheduledService {

    private static final String SCHEDULE_INTERVAL_IN_MINUTE = ConfProperties.getString(Constants.AGENT_COMMAND_TIMEOUT, Constants.DEFAULT_AGENT_COMMAND_TIMEOUT);
    private static final int enableScheduler = ConfProperties.getInt(Constants.ENABLE_COMMAND_OUT_SCHEDULER, Constants.ENABLE_COMMAND_OUT_SCHEDULER_DEFAULT);

    @Override
    protected void runOneIteration() {
        log.info("CommandTimeoutService scheduler running");
        if(enableScheduler != 1) {
            log.info("Disabled the command timeout service scheduler.");
            return;
        }

        AgentHealthStatusRepo healthStatusRepo = new AgentHealthStatusRepo();
        try {
            List<PhysicalAgentBean> commandExecuteDetails = AgentStatusDataService.getPhysicalAgentsForOngoingCommand();
            log.debug("Total physical agent for ongoing commands [{}]", commandExecuteDetails.size());
            for (PhysicalAgentBean c : commandExecuteDetails) {
                CommandTriggerBean commandDetails = AgentStatusDataService.getAgentCommandTriggerStatus(c.getId(), c.getLastJobId());

                if (Objects.nonNull(commandDetails) && c.getLastCommandExecuted() == 0) {
                    long fromEpochTime = DateTimeUtil.getGMTToEpochTime(String.valueOf(commandDetails.getTriggerTime()));
                    long toEpochTime = DateTimeUtil.getGMTToEpochTime(String.valueOf(new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime())));
                    long diffInMilli = toEpochTime - fromEpochTime;
                    long secondsPassed = (diffInMilli / 1000) / 60;
                    long timeInMinute = commandDetails.getTimeoutInSecs() / 60;

                    if (secondsPassed >= timeInMinute) {
                        CommandDataService.updateCommandFailureStatus(c.getIdentifier(), c.getLastJobId(), c.getUserDetailsId());

                        CommandDetailsBean commandBean = CommandDataService.getCommandDetail(commandDetails.getCommandId(), null);
                        if (commandBean == null) {
                            log.error("Default/Selected command not found for provided commandId [{}] to process the request.", commandDetails.getCommandId());
                            throw new CommandException("Default/Selected command not found to process the request.");
                        }

                        String accountIdentifier = AgentStatusDataService.getAccountIdentifier(c.getId());

                        Map<String, String> metaData = new HashMap<>();
                        metaData.put("CommandId", commandBean.getIdentifier());
                        metaData.put("AccountId", accountIdentifier);
                        metaData.put("CommandCompleteTime", String.valueOf(toEpochTime));
                        metaData.put("CommandStartTime", String.valueOf(fromEpochTime));
                        metaData.put("ErrorMessageFromPipeline", "Command timed out");
                        metaData.put("ExitCode", "1");
                        String desiredStatus = RulesDataService.getNameFromMSTSubType(commandBean.getActionId());
                        metaData.put("DesiredState", desiredStatus);

                        log.debug("Agent health status details added with following agentIdentifier {}, commandId {},commandjobid {}, status {}, lastDataReceivedTimeInGMT {}, metadata {}", c.getIdentifier(), commandBean.getName(), c.getLastJobId(), desiredStatus, commandDetails.getTriggerTime().getTime(), metaData);
                        healthStatusRepo.insertCommandDetails(accountIdentifier, c.getIdentifier(), commandBean.getName(), c.getLastJobId(), desiredStatus,
                                commandDetails.getTriggerTime().getTime(), metaData);

                        log.info("Command [{}] timed out for agent [{}]. Agent-wise command status updated in CC", c.getLastJobId(), c.getIdentifier());
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error occurred in CommandTimeoutService. Details: ", e);
        }

    }

    @Override
    protected Scheduler scheduler() {
        try {
            long schedulerPeriodInOneMinutes = Integer.parseInt(SCHEDULE_INTERVAL_IN_MINUTE);
            return Scheduler.newFixedRateSchedule(0L, schedulerPeriodInOneMinutes, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("Error while creating CommandTimeoutService scheduler. Details: ", e);
        }
        return null;
    }
}
