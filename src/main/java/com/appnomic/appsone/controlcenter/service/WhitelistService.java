package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.ApplicationWhitelistBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.ApplicationWhitelist;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.WhitelistPojo;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;

@Slf4j
public class WhitelistService {

    public GenericResponse<WhitelistPojo> getWhitelist(Request request, Response response) {
        GenericResponse<WhitelistPojo> responseObject = new GenericResponse<>();
        log.trace("Method Invoked : getWhitelist");
        try {
            RequestObject requestObject = new RequestObject(request);
            ApplicationWhitelistBL businessLogic = new ApplicationWhitelistBL();

            UtilityBean<List<ApplicationWhitelist>> clientData = businessLogic.clientValidation(requestObject);
            UtilityBean<List<ApplicationWhitelist>> validatedClientData = businessLogic.serverValidation(clientData);

            WhitelistPojo whitelist = businessLogic.process(validatedClientData);

            responseObject.setData(whitelist);
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(StatusResponse.SUCCESS.name());

            response.status(Constants.SUCCESS_STATUS_CODE);

        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Request validation failed", e);
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            log.error("Error retrieving whitelist", e);
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(), Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        return responseObject;
    }
}