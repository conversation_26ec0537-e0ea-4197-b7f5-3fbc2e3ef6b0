package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.keys.AccountKPIKey;
import com.appnomic.appsone.controlcenter.businesslogic.GetCategories;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.CategoryDetails;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

public class GetCategoriesService {

    private static final Logger LOGGER = LoggerFactory.getLogger(GetCategoriesService.class);

    public GenericResponse<List<CategoryDetails>> getCategories(Request request, Response response) {
        long st = System.currentTimeMillis();
        GenericResponse<List<CategoryDetails>> responseObject = new GenericResponse<>();
        GetCategories getCategories = new GetCategories();

        try {
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<String> utilityBean = getCategories.clientValidation(requestObject);
            AccountKPIKey accountKPIKey = getCategories.serverValidation(utilityBean);

            responseObject.setData(getCategories.process(accountKPIKey));

            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(UIMessages.CATEGORY_GET_SUCCESS);
            response.status(Constants.SUCCESS_STATUS_CODE);
            return responseObject;

        }
        catch (ClientException | ServerException | DataProcessingException e) {
            LOGGER.error("Error while fetching categories for the account. Details: {}", e.getMessage(), e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        }
        catch (Exception e) {
            LOGGER.error("Error while adding the category.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        } finally {
            LOGGER.debug("Total time taken for fetch Category api {} ms", System.currentTimeMillis() - st);
        }
    }
}
