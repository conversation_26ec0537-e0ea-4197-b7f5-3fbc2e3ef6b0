package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.TransactionCountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.TransactionCountBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

public class TransactionCountService {
    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateTransactionService.class);
    private static final String GET_ERR_MSG = "Error while fetching transaction count";
    private static final String OBJECT_GET_SUCCESS = "Service summary fetched successfully";

    public GenericResponse<TransactionCountBean> transactionCount(Request request, Response response) {
        GenericResponse<TransactionCountBean> responseObject = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);
            TransactionCountBL updateTransactionsBL = new TransactionCountBL();
            UtilityBean<Integer> utilityBean = updateTransactionsBL.clientValidation(requestObject);
            updateTransactionsBL.serverValidation(utilityBean);
            TransactionCountBean bean = updateTransactionsBL.process(utilityBean);
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(OBJECT_GET_SUCCESS);
            response.status(Constants.SUCCESS_STATUS_CODE);
            responseObject.setData(bean);
        } catch (ClientException | ServerException e) {
            LOGGER.error(UIMessages.INVALID_REQUEST_EXCEPTION_MESSAGE, e);
            CommonUtils.populateErrorResponse(responseObject, response, GET_ERR_MSG,
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (DataProcessingException e) {
            LOGGER.error(GET_ERR_MSG, e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    GET_ERR_MSG, Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            LOGGER.error(GET_ERR_MSG, e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    GET_ERR_MSG, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        return responseObject;
    }
}
