package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.DeleteApplicationsBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.Controller;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;

@Slf4j
public class DeleteApplicationsService {

    public GenericResponse<String> deleteApplications(Request request, Response response) {
        GenericResponse<String> responseObject = new GenericResponse<>();
        log.trace("Method Invoked : DeleteApplicationsService/deleteApplications");

        try {
            DeleteApplicationsBL deleteApplicationsBL = new DeleteApplicationsBL();
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<List<String>> applicationList = deleteApplicationsBL.clientValidation(requestObject);
            List<Controller> controllerBeanList = deleteApplicationsBL.serverValidation(applicationList);
            deleteApplicationsBL.process(controllerBeanList);

            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(UIMessages.APPLICATION_REM_SUCCESS);
            response.status(Constants.SUCCESS_STATUS_CODE);

        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Invalid Request.", e);
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            log.error("Error occurred while removing application(s).", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        return responseObject;
    }

}
