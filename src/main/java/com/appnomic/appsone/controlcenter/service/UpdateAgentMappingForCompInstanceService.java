package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.AgentCompInstMappingBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.UpdateAgentMappingForCompInstance;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.AgentCompInstanceMaping;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

public class UpdateAgentMappingForCompInstanceService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateAgentMappingForCompInstanceService.class);

    public GenericResponse<String> updateAgentMappingForCompInstance(Request request, Response response) {

        GenericResponse<String> responseObject = new GenericResponse<>();
        UpdateAgentMappingForCompInstance updateAgentMappingForCompInstance = new UpdateAgentMappingForCompInstance();

        try {
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<List<AgentCompInstanceMaping>> utilityBean = updateAgentMappingForCompInstance.clientValidation(requestObject);
            List<AgentCompInstMappingBean> requestBean = updateAgentMappingForCompInstance.serverValidation(utilityBean);

            responseObject.setData(updateAgentMappingForCompInstance.process(requestBean));
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(UIMessages.AGENT_INST_MAP_UPDATE_SUCCESS);
            response.status(Constants.SUCCESS_STATUS_CODE);
            return responseObject;

        } catch (ClientException | ServerException | DataProcessingException e) {
            LOGGER.error("Error while updating agent(s) mapping details with instance(s). Details: {}", e.getMessage(), e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            LOGGER.error("Exception while updating agent(s) mapping details with instance(s). ", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
    }
}
