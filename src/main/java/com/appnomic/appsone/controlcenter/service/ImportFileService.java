package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.businesslogic.CsvUploadValidation;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.CsvRow;
import com.appnomic.appsone.controlcenter.pojo.FileUploadStatusDetail;
import com.appnomic.appsone.controlcenter.pojo.ImportConnectionFile;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.appnomic.appsone.model.JWTData;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.skife.jdbi.v2.DBI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class ImportFileService {
    private static final Logger logger = LoggerFactory.getLogger(ImportFileService.class);
    public GenericResponse importUploadFile(Request request, Response response) {
        GenericResponse responseObject = new GenericResponse();
        try {

            String userId = getUserId(request);
            String identifier = request.params(Constants.ACCOUNT_IDENTIFIER);
            int accountId = ValidationUtils.validAndGetIdentifier(identifier);
            /**
             * Is given account id is valid
             */

            if (accountId == -1) {
                responseObject.setResponseStatus(StatusResponse.FAILURE.name());
                responseObject.setMessage(Constants.INVALID_ACCOUNT_ERROR_MESSAGE);
                response.status(200);
                logger.error(Constants.INVALID_ACCOUNT_ERROR_MESSAGE);
                return responseObject;
            }
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            responseObject = dbi.inTransaction((handle, status) -> (GenericResponse) importCsvFile(request, accountId, userId));

            if (!responseObject.getMessage().equals(Constants.SUCCESS)) {
                responseObject.setResponseStatus(StatusResponse.FAILURE.name());
                responseObject.setMessage(responseObject.getMessage());
                response.status(400);
                logger.error("Problem process the uploaded file.");
                return responseObject;
            }
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setData(responseObject.getData());
            response.status(200);
            responseObject.setMessage("file uploaded successfully");
        } catch (Exception e) {
            logger.error("Error while converting request object into  object", e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            response.status(200);
            return responseObject;
        }
        return responseObject;
    }

    private static GenericResponse<CsvRow> importCsvFile(Request request, int accountId, String userId)  {
        long st = System.currentTimeMillis();
        GenericResponse response = new GenericResponse();
        FileUploadStatusDetail detail = new FileUploadStatusDetail();
        CsvUploadValidation csvUploadValidation=new CsvUploadValidation();
        try {
            DiskFileItemFactory factory = new DiskFileItemFactory();
            ServletFileUpload fileUpload = new ServletFileUpload(factory);
            List<FileItem> items = fileUpload.parseRequest(request.raw());
            FileItem item = items.stream()
                    .filter(e -> "file".equals(e.getFieldName()))
                    .findFirst().orElse(null);
            if (item == null) {
                logger.error("File field not exist in request for given  accountId: {}", accountId);
                response.setResponseStatus(StatusResponse.FAILURE.name());
                response.setMessage("Problem saving the upload details because item is empty.");
            } else {
                String fileName = item.getName();
                item.write(new File(fileName));
                UploadValidationService uploadValidationService = new UploadValidationService();
                List<CsvRow> csvRows = uploadValidationService.parseCSV(fileName, detail);
                Map<String, List<CsvRow>> serviceMap = csvRows.stream().collect(Collectors.groupingBy(CsvRow::getServices));
                List<Integer> accounts=new ArrayList<>();
                accounts.add(Constants.DEFAULT_ACCOUNT_ID);
                accounts.add(accountId);
                csvUploadValidation.serviceMap(serviceMap,accountId,userId);
                Files.deleteIfExists(Paths.get(fileName));
            }
        }
        catch (Exception ex) {
            logger.error("Error while import csv file.", ex);
            response.setResponseStatus(StatusResponse.FAILURE.name());
            response.setMessage(detail.getErrorList()+"");
            return response;
        }
        finally {
            logger.debug("Time taken to complete the file processing is {} ms.", System.currentTimeMillis() - st);
        }
        if(detail.getErrorList()!=null)
        {
            String message="uploaded file has some problem please check the error details";
            String details=detail.getErrorList()+"";
            message=message+"    " +details;
            response.setMessage(message);

        }
        else{
            response.setData(detail);
            response.setMessage(Constants.SUCCESS);
        }
        return response;

    }



    public GenericResponse importUploadConnectionFile(Request request, Response response) {
        GenericResponse responseObject = new GenericResponse();
        try {
            String userId = getUserId(request);
            String identifier = request.params(Constants.ACCOUNT_IDENTIFIER);
            int accountId = ValidationUtils.validAndGetIdentifier(identifier);
            /**
             * Is given account id is valid
             */

            if (accountId == -1) {
                responseObject.setResponseStatus(StatusResponse.FAILURE.name());
                responseObject.setMessage(Constants.INVALID_ACCOUNT_ERROR_MESSAGE);
                response.status(200);
                logger.error(Constants.INVALID_ACCOUNT_ERROR_MESSAGE);
                return responseObject;
            }
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            responseObject = dbi.inTransaction((handle, status) -> (GenericResponse) importConnectionFile(request, accountId, userId));
            if (!responseObject.getMessage().equals(Constants.SUCCESS)) {
                responseObject.setResponseStatus(StatusResponse.FAILURE.name());
                responseObject.setMessage("Problem saving the uploaded file.");
                response.status(200);
                logger.error("Problem saving the uploaded file.");
                return responseObject;
            }
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setData(responseObject.getData());
            response.status(200);
            responseObject.setMessage("File uploaded successfully");
        } catch (Exception e) {
            logger.error("Error while converting request object into  object", e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            response.status(200);
            return responseObject;
        }
        return responseObject;
    }


    public GenericResponse<ImportConnectionFile> importConnectionFile(Request request, int accountId, String userId) throws Exception {
        GenericResponse response = new GenericResponse();
            DiskFileItemFactory factory = new DiskFileItemFactory();
            ServletFileUpload fileUpload = new ServletFileUpload(factory);
            List<FileItem> items = fileUpload.parseRequest(request.raw());
            FileItem item = items.stream()
                    .filter(e -> "file".equals(e.getFieldName()))
                    .findFirst().orElse(null);
            if (item == null) {
                logger.error("File field not exist in request for given  accountId: {}", accountId);
                response.setResponseStatus(StatusResponse.FAILURE.name());
                response.setMessage("Problem saving the upload details because item is empty.");
            } else {

                String fileName = item.getName();
                item.write(new File(fileName));
                Pattern pattern = Pattern.compile(",");
                try (BufferedReader csvFile = new BufferedReader(new FileReader(fileName))) {
                    Set<ImportConnectionFile> importFiles = csvFile.lines().skip(1).map(row -> {
                        String[] x = pattern.split(row, 2);
                        return new ImportConnectionFile(x[0].trim(), x[1].trim());
                    }).collect(Collectors.toSet());

                    /**
                     * for connection mapping
                     */
                    CsvUploadValidation.createConnectionMapping(importFiles,accountId,userId);

                } catch (Exception e) {
                    logger.error("Error while import file.", e);
                    response.setResponseStatus(StatusResponse.FAILURE.name());
                    response.setData("0");
                    return response;
                }
                Files.deleteIfExists(Paths.get(fileName));
            }
        response.setData("100");
        response.setMessage("Success");
        return response;
    }

    public String getUserId(Request request) throws ControlCenterException {
        String authKey = request.headers("Authorization");
        JWTData jwtData = KeyCloakAuthService.extractUserDetails(authKey);
        return jwtData.getSub();
    }
}
