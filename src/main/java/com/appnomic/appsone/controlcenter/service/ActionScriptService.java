package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.ActionScriptBean;
import com.appnomic.appsone.controlcenter.businesslogic.ActionScriptsBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ActionScriptException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.pojo.ActionPojo;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.appnomic.appsone.model.JWTData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

public class ActionScriptService {

    private ActionScriptService() {
    }

    private static final Logger LOGGER = LoggerFactory.getLogger(ActionScriptService.class);

    public static GenericResponse<IdPojo> addActions(Request request, Response response) {

        GenericResponse<IdPojo> responseObject = new GenericResponse<>();
        LOGGER.trace("Method Invoked : ActionScriptService/addActions");

        String userId;
        try {
            String authKey = request.headers("Authorization");
            JWTData jwtData = KeyCloakAuthService.extractUserDetails(authKey);
            userId = jwtData.getSub();
        } catch (Exception e) {
            LOGGER.error("Validation failure. Invalid user.");
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    "Invalid user identifier. For more details, refer application log file.", null, true);
        }

        String accountIdentifier = request.params(":identifier");
        int accountId;
        try {
            accountId = ValidationUtils.validateRequestAndGetAccountId(request, accountIdentifier);
        } catch (RequestException e) {
            LOGGER.error("Validation failure. : {}",e.getSimpleMessage());
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    "Validation failure : " + e.getSimpleMessage(), null, true);
        }
        ActionScriptBean bean;
        try {

            ActionScriptsBL actionScriptsBL = new ActionScriptsBL();

            ActionPojo actionPojo = actionScriptsBL.clientValidations(request);
            bean = actionScriptsBL.serverValidations(actionPojo, accountId);
            int id = actionScriptsBL.add(bean, userId, accountId);

            responseObject.setData(IdPojo.builder().id(id).name(bean.getName()).identifier(bean.getIdentifier()).build());
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(UIMessages.ACTION_ADD_SUCCESS);
            response.status(Constants.SUCCESS_STATUS_CODE);

        } catch (ActionScriptException e) {
            LOGGER.error("Error occurred while creating new action script.", e);
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            LOGGER.error("Error occurred while creating new action script.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }

        return responseObject;
    }
}
