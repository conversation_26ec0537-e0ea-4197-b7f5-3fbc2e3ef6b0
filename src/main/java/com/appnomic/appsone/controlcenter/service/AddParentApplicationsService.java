package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.AddParentApplicationBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.ParentApplicationPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.heal.configuration.entities.ParentApplicationBean;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;

@Slf4j
public class AddParentApplicationsService {

    public GenericResponse<String> addParentApplication(Request request, Response response) {
        GenericResponse<String> responseObject = new GenericResponse<>();
        try {
            AddParentApplicationBL addParentApplicationBL = new AddParentApplicationBL();
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<List<ParentApplicationPojo>> parentApplications = addParentApplicationBL.clientValidation(requestObject);
            List<ParentApplicationBean> beanList = addParentApplicationBL.serverValidation(parentApplications);
            responseObject.setData(addParentApplicationBL.process(beanList));

            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(UIMessages.PARENT_APPLICATION_ADD_SUCCESS);
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Invalid Request.", e);
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        } catch (Exception e) {
            log.error("Error occurred while adding new parent application(s).", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        }

        return responseObject;
    }
}

