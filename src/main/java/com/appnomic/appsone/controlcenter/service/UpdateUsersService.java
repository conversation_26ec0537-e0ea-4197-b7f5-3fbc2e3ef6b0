package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.UpdateUsers;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.UpdateUsersPojo;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

public class UpdateUsersService {
    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateUsersService.class);

    public GenericResponse<String> updateUsers(Request request, Response response) {
        GenericResponse<String> responseObject = new GenericResponse<>();
        UpdateUsers updateUsers = new UpdateUsers();

        try {
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<UpdateUsersPojo> utilityBean = updateUsers.clientValidation(requestObject);
            UpdateUsersPojo updateUsersPojo = updateUsers.serverValidation(utilityBean);

            responseObject.setData(updateUsers.process(updateUsersPojo));

            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage("SUCCESSFUL");
            response.status(Constants.SUCCESS_STATUS_CODE);
            return responseObject;

        } catch (ClientException | ServerException | DataProcessingException e) {
            LOGGER.error("Error. Details: {}", e.getMessage(), e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            LOGGER.error("Exception.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
    }
}
