package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.GetSchedulersBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.heal.configuration.entities.SchedulerDetails;
import com.heal.configuration.pojos.Schedulers;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;

@Slf4j
public class GetSchedulersService {

    public GenericResponse<List<Schedulers>> getSchedulers(Request request, Response response) {
        GenericResponse<List<Schedulers>> responseObject = new GenericResponse<>();

        RequestObject requestObject = new RequestObject(request);

        GetSchedulersBL getSchedulersBL = new GetSchedulersBL();

        UtilityBean<String> utilityBean;
        try {
            utilityBean = getSchedulersBL.clientValidation(requestObject);
            List<SchedulerDetails> schedulerDetails = getSchedulersBL.serverValidation(utilityBean);
            List<Schedulers> schedulers = getSchedulersBL.process(schedulerDetails);

            responseObject.setData(schedulers);
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);

            return responseObject;
        } catch (ClientException | ServerException | DataProcessingException e) {
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, e.getMessage(), null, true);
        } catch (Exception e) {
            log.error("Error occurred while fetching the schedulers list. Details: ", e);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, e.getMessage(), null, true);
        }
    }
}
