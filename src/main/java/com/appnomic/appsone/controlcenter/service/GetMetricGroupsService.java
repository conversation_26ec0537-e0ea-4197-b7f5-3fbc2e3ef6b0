package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.GetMetricGroups;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.MetricDetailsRequest;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

public class GetMetricGroupsService {

    private static final Logger LOGGER = LoggerFactory.getLogger(GetMetricGroupsService.class);

    public GenericResponse<List<IdPojo>> getMetricGroups(Request request, Response response) {

        GenericResponse<List<IdPojo>> responseObject = new GenericResponse<>();
        GetMetricGroups getMetricGroups = new GetMetricGroups();

        try {
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<List<Integer>> utilityBean = getMetricGroups.clientValidation(requestObject);
            MetricDetailsRequest key = getMetricGroups.serverValidation(utilityBean);

            responseObject.setData(getMetricGroups.process(key));

            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage("Metric groups fetched successfully.");
            response.status(Constants.SUCCESS_STATUS_CODE);
            return responseObject;

        } catch (ClientException | ServerException | DataProcessingException e) {
            LOGGER.error("Error while fetching metric groups for instances. Details: {}", e.getMessage(), e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            LOGGER.error("Exception while fetching metric groups for instances. ", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
    }
}
