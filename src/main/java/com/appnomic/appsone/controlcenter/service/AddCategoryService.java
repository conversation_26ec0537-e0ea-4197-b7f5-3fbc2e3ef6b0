package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.CategoryDetailBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.AddCategory;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.CategoryDetails;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

public class AddCategoryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AddCategoryService.class);

    public GenericResponse<IdPojo> addCategory(Request request, Response response) {
        GenericResponse<IdPojo> responseObject = new GenericResponse<>();
        AddCategory addCategory = new AddCategory();

        try {
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<CategoryDetails> categoryDetailsUtilityBean = addCategory.clientValidation(requestObject);
            CategoryDetailBean categoryDetailBean = addCategory.serverValidation(categoryDetailsUtilityBean);

            responseObject.setData(addCategory.process(categoryDetailBean));

            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(UIMessages.CATEGORY_ADD_SUCCESS);
            response.status(Constants.SUCCESS_STATUS_CODE);
            return responseObject;

        }
        catch (ClientException | ServerException | DataProcessingException e) {
            LOGGER.error("Error while processing add category request. Details: {}", e.getMessage(), e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        }
        catch (Exception e) {
            LOGGER.error("Error while adding the category.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
    }

}
