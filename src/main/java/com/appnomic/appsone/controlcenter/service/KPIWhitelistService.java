package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.KPIWhitelistBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;
@Slf4j
public class KPIWhitelistService {

    public GenericResponse<String> addKPIWhitelist(Request request, Response response) {
        GenericResponse<String> responseObject = new GenericResponse<>();
        log.trace("Method Invoked : addKPIWhitelist");
        try {
            RequestObject requestObject = new RequestObject(request);
            KPIWhitelistBL businessLogic = new KPIWhitelistBL();

            UtilityBean<List<String>> clientData = businessLogic.clientValidation(requestObject);
            UtilityBean<List<String>> validatedClientData = businessLogic.serverValidation(clientData);

            responseObject.setData(businessLogic.addWhitelist(validatedClientData));
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(StatusResponse.SUCCESS.name());

            response.status(Constants.SUCCESS_STATUS_CODE);

        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Request validation failed", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    e.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

            return responseObject;
        } catch (Exception e) {
            log.error("Error adding whitelist", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    e.getMessage(), Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);

            return responseObject;
        }
        return responseObject;
    }

    public GenericResponse<String> updateKPIWhitelist(Request request, Response response) {
        GenericResponse<String> responseObject = new GenericResponse<>();
        log.trace("Method Invoked : updateKPIWhitelist");
        try {
            RequestObject requestObject = new RequestObject(request);
            KPIWhitelistBL businessLogic = new KPIWhitelistBL();

            UtilityBean<List<String>> clientData = businessLogic.clientValidation(requestObject);
            UtilityBean<List<String>> validatedClientData = businessLogic.serverValidation(clientData);

            responseObject.setData(businessLogic.updateWhitelist(validatedClientData));
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(StatusResponse.SUCCESS.name());

            response.status(Constants.SUCCESS_STATUS_CODE);

        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Request validation failed", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    e.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

            return responseObject;
        } catch (Exception e) {
            log.error("Error updating kpi whitelist", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    e.getMessage(), Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);

            return responseObject;
        }
        return responseObject;
    }

    public GenericResponse<String> deleteKPIWhitelist(Request request, Response response) {
        GenericResponse<String> responseObject = new GenericResponse<>();
        log.trace("Method Invoked : deleteKPIWhitelist");
        try {
            RequestObject requestObject = new RequestObject(request);
            KPIWhitelistBL businessLogic = new KPIWhitelistBL();

            UtilityBean<List<String>> clientData = businessLogic.clientValidation(requestObject);
            UtilityBean<List<String>> validatedClientData = businessLogic.serverValidation(clientData);

            responseObject.setData(businessLogic.deleteWhitelist(validatedClientData));
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(StatusResponse.SUCCESS.name());

            response.status(Constants.SUCCESS_STATUS_CODE);

        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Request validation failed", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    e.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

            return responseObject;
        } catch (Exception e) {
            log.error("Error deleting kpi whitelist", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    e.getMessage(), Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);

            return responseObject;
        }
        return responseObject;
    }
}
