package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.UpdateAppPercentilesBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ApplicationPercentilesBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.Percentile;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

public class UpdateAppPercentilesService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateAppPercentilesService.class);

    public GenericResponse<String> updateAppPercentiles(Request request, Response response) {

        GenericResponse<String> responseObject = new GenericResponse<>();
        UpdateAppPercentilesBL bl = new UpdateAppPercentilesBL();

        try {
            RequestObject requestObject = new RequestObject(request);
            UtilityBean<List<Percentile>> utilityBean = bl.clientValidation(requestObject);
            List<ApplicationPercentilesBean> applicationPercentiles = bl.serverValidation(utilityBean);

            responseObject.setData(bl.process(applicationPercentiles));
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage("Application percentiles updated successfully.");

            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (ClientException | ServerException | DataProcessingException e) {

            LOGGER.error("Error while updating application percentiles. Details: {}", e.getMessage(), e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

            return responseObject;
        } catch (Exception e) {

            LOGGER.error("Error while updating application percentiles.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);

            return responseObject;
        }
        return responseObject;
    }
}

