package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.UpdateCustomKPI;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.CustomKpiFromUI;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;


public class UpdateCustomKpiService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateCustomKpiService.class);

    public GenericResponse<String> updateCustomKPI(Request request, Response response) {
        GenericResponse<String> responseObject = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);
            UpdateCustomKPI updateCustomKPI = new UpdateCustomKPI();

            UtilityBean<List<CustomKpiFromUI>> utilityBean = updateCustomKPI.clientValidation(requestObject);
            List<CustomKpiFromUI> bean = updateCustomKPI.serverValidation(utilityBean);
            String processOutputMessage = updateCustomKPI.process(bean);

            responseObject.setData(null);
            responseObject.setMessage(processOutputMessage);
            responseObject.setResponseStatus("SUCCESS");

            response.status(Constants.SUCCESS_STATUS_CODE);

        } catch (ClientException | ServerException e) {
            LOGGER.error("Error while updating custom KPI", e);
            responseObject.setMessage(e.getMessage());
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;

        } catch (Exception e) {
            LOGGER.error("Error while updating custom KPI", e);
            responseObject.setMessage(UIMessages.INTERNAL_SERVER_ERROR);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        }

        return responseObject;
    }

}

