package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.UpdateTransactionsBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.TransactionDetails;
import com.appnomic.appsone.controlcenter.pojo.UpdatedValue;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

public class UpdateTransactionService {
    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateTransactionService.class);
    private static final String UPD_ERR_MSG = "Error while updating transaction configuration";

    public GenericResponse<String> update(Request request, Response response) {
        GenericResponse<String> responseObject = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);
            UpdateTransactionsBL updateTransactionsBL = new UpdateTransactionsBL();
            UtilityBean<List<UpdatedValue>> editRulePojoUtilityBean = updateTransactionsBL.clientValidation(requestObject);
            UtilityBean<List<UpdatedValue>> pojoUtilityBean = updateTransactionsBL.serverValidation(editRulePojoUtilityBean);
            updateTransactionsBL.process(pojoUtilityBean);
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(UIMessages.OBJECT_MOD_SUCCESS);
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (ClientException | ServerException e) {
            LOGGER.error(UIMessages.INVALID_REQUEST_EXCEPTION_MESSAGE, e);
            CommonUtils.populateErrorResponse(responseObject, response, UPD_ERR_MSG,
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (DataProcessingException e) {
            LOGGER.error(UPD_ERR_MSG, e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UPD_ERR_MSG, Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            LOGGER.error(UPD_ERR_MSG, e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UPD_ERR_MSG, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        return responseObject;
    }
}
