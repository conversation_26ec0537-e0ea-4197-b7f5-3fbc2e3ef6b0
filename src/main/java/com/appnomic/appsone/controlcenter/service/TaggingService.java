package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.businesslogic.TagMappingBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.pojo.AddingTagsPojo;
import com.appnomic.appsone.controlcenter.pojo.EntryPointObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.heal.configuration.pojos.Service;
import com.heal.configuration.pojos.Tags;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import java.util.ArrayList;
import java.util.List;

public class TaggingService {

    private static final Logger LOGGER = LoggerFactory.getLogger(TaggingService.class);

    public GenericResponse addTagging(Request request, Response response) {
        try {
            return validateRequestAndAddTagging(request, response);
        } catch (Exception e) {
            LOGGER.error("Unexpected exception encountered while adding tag. Reason: {}", e.getMessage(), e);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, e.getMessage(), e, true);
        }
    }

    public GenericResponse deleteTagging(Request request, Response response) {
        try {
            return validateRequestAndDeleteTagging(request, response);
        } catch (Exception e) {
            LOGGER.error("Unexpected exception encountered while adding tag. Reason: {}", e.getMessage(), e);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, e.getMessage(), e, true);
        }
    }

    private GenericResponse validateRequestAndAddTagging(Request request, Response response) {
        try {
            EntryPointObject entryPointObject = TagMappingBL.clientValidation(request);
            List<AddingTagsPojo> addingTagsPojoList = entryPointObject.getAddingTagsPojoList();
            ServiceRepo serviceRepo = new ServiceRepo();
            for (AddingTagsPojo a : addingTagsPojoList) {
                EntryPointObject obj = TagMappingBL.serverTagValidation(a.getWhomToTag(), a.getReferenceId(), entryPointObject);
                TagMappingBL.addTagMapping(a.getTagValue(), entryPointObject.getUserId(), entryPointObject.getAccount().getId(),
                        obj.getTagDetailsBean(), obj.getServiceDetails());
                updateTags(Constants.ACTION_ADD,entryPointObject, serviceRepo, a, obj);
            }
        } catch (ControlCenterException | RequestException e) {
            LOGGER.error("Error while updating request object into tag object. Reason: {}", e.getMessage(), e);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    e.getMessage(), null, true);
        }

        return CommonUtils.getGenericResponse(response,
                StatusResponse.SUCCESS.name(),
                Constants.SUCCESS_STATUS_CODE, "Service tagged successfully", null, false);
    }
    private void updateTags(String action, EntryPointObject entryPointObject, ServiceRepo serviceRepo, AddingTagsPojo a, EntryPointObject obj) {
        Service existingServiceConfiguration = serviceRepo.getServiceConfigurationByIdentifier(entryPointObject.getAccount().getIdentifier(), obj.getServiceDetails().getIdentifier());
        List<Tags> serviceTags = new ArrayList<>(existingServiceConfiguration.getTags());
        Tags tag = Tags.builder().key(Constants.DEFAULT_TAG_VALUE).type(Constants.ENTRY_POINT).value(Constants.ENTRY_POINT_TAG_VALUE).build();
        if(action.equals(Constants.ACTION_ADD)) {
            serviceTags.add(tag);
        } else if(action.equals(Constants.ACTION_DELETE)) {
            serviceTags.remove(tag);
        }
        existingServiceConfiguration.setTags(serviceTags);
        serviceRepo.updateServiceConfigurationByServiceIdentifier(entryPointObject.getAccount().getIdentifier(), obj.getServiceDetails().getIdentifier(), existingServiceConfiguration);
    }

    private GenericResponse validateRequestAndDeleteTagging(Request request, Response response) {
        try {
            EntryPointObject entryPointObject = TagMappingBL.clientValidation(request);
            List<AddingTagsPojo> addingTagsPojoList = entryPointObject.getAddingTagsPojoList();
            ServiceRepo serviceRepo = new ServiceRepo();
            for (AddingTagsPojo a : addingTagsPojoList) {
                EntryPointObject obj = TagMappingBL.serverTagValidation(a.getWhomToTag(), a.getReferenceId(), entryPointObject);
                TagMappingBL.deleteTagMapping(a.getTagValue(), entryPointObject.getUserId(), entryPointObject.getAccount().getId(),
                        obj.getTagDetailsBean(), obj.getServiceDetails());
                updateTags(Constants.ACTION_DELETE,entryPointObject, serviceRepo, a, obj);
            }
        } catch (ControlCenterException | RequestException e) {
            LOGGER.error("Error while updating request object into tag object. Reason: {}", e.getMessage(), e);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    e.getMessage(), null, true);
        }
        return CommonUtils.getGenericResponse(response,
                StatusResponse.SUCCESS.name(),
                Constants.SUCCESS_STATUS_CODE, "Service untagged successfully", null, false);
    }
}

