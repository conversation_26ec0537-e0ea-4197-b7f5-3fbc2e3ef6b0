package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.AccessDetailsBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.AccountBL;
import com.appnomic.appsone.controlcenter.businesslogic.AccountListBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.Account;
import com.appnomic.appsone.controlcenter.pojo.AccountResponse;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

public class AccountService {
    private static final Logger logger = LoggerFactory.getLogger(AccountService.class);

    public static AccountResponse getAccountList(Request request, Response response) {
        AccountResponse accountResponse = new AccountResponse();

        try {
            RequestObject requestObject = new RequestObject(request);
            AccountListBL accountBL = new AccountListBL();

            UtilityBean<Account> utilityBean = accountBL.clientValidation(requestObject);
            AccessDetailsBean bean = accountBL.serverValidation(utilityBean);
            List<Account> accessibleAccounts= accountBL.process(bean);

            accountResponse.setData(accessibleAccounts);
            accountResponse.setResponse_message("");
            accountResponse.setResponse_status("SUCCESS");

            response.status(Constants.SUCCESS_STATUS_CODE);

        } catch (ClientException | ServerException e) {
            logger.error("Error while converting request object into account object.", e);
            accountResponse.setResponse_status(StatusResponse.FAILURE.name());
            accountResponse.setResponse_message(e.getMessage());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return accountResponse;

        } catch (Exception e) {
            logger.error("Error occurred while getting all accounts.", e);
            accountResponse.setResponse_message(UIMessages.INTERNAL_SERVER_ERROR);
            accountResponse.setResponse_status(StatusResponse.FAILURE.name());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
        }

        return accountResponse;
    }

    public GenericResponse addAccount(Request request, Response response) {
        GenericResponse<Integer> responseObject = new GenericResponse<>();

        try {
            RequestObject requestObject = new RequestObject(request);
            AccountBL accountBL = new AccountBL();

            UtilityBean<Account> utilityBean = accountBL.clientValidation(requestObject);
            Account account = accountBL.serverValidation(utilityBean);
            int accountId = accountBL.process(account);

            responseObject.setData(accountId);

        } catch (ClientException | ServerException | DataProcessingException e) {
            logger.error("Error while processing addAccount API request. Details: {}", e.getMessage(), e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            logger.error("Error while processing add account.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        return responseObject;
    }

    public GenericResponse<String> getFileSignature(Request req, Response res){

        GenericResponse<String> response = new GenericResponse<>();

        try {
            String accountIdString = req.params(":identifier");
            AccountBean account = ValidationUtils.validAndGetAccount(accountIdString);

            if(account == null){
                logger.error("Invalid account id: {}", accountIdString);
                return CommonUtils.getGenericResponse(res, StatusResponse.FAILURE.name(),
                                                      HttpStatus.SC_BAD_REQUEST,
                                                      "Invalid account id.", null, true);
            }

            DiskFileItemFactory factory = new DiskFileItemFactory();
            ServletFileUpload fileUpload = new ServletFileUpload(factory);
            List<FileItem> items = fileUpload.parseRequest(req.raw());
            FileItem item = items.stream()
                                 .filter(e -> "file".equals(e.getFieldName()))
                                 .findFirst().orElse(null);

            if (item == null) {
                logger.error("File field not exist in request for given  accountId: {}",
                             account.getId());
                response.setResponseStatus(StatusResponse.FAILURE.name());
                response.setMessage("Problem saving the upload details because item is empty.");
                return response;
            }

            byte[] fileBytes = IOUtils.toByteArray(item.getInputStream());
            String signature = new AccountBL().getFileSignature(
                    account, fileBytes);
            if(signature != null){
                response = CommonUtils.getGenericResponse(res,
                           StatusResponse.SUCCESS.name(),
                           Constants.SUCCESS_STATUS_CODE, "Signature generated successfully",
                                               null, false);
                response.setData(signature);
            }else{
                throw new ControlCenterException("Error in validating signature");
            }

        }catch (ControlCenterException e){
            logger.error(e.getMessage(), e);

            return CommonUtils.getGenericResponse(res, StatusResponse.FAILURE.name(),
                  Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, e.getMessage(), e, true);
        }catch (Exception e){
            return CommonUtils.getGenericResponse(res, StatusResponse.FAILURE.name(),
                  Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, "Error in generating signature", e, true);
        }
        return response;
    }

}
