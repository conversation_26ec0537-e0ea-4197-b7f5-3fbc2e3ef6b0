package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.TriggerAgentCommandBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.AgentCommandPojo;
import com.appnomic.appsone.controlcenter.pojo.AgentCommandTriggeredStatusPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

public class TriggerAgentCommandService {

    private static final Logger LOGGER = LoggerFactory.getLogger(TriggerAgentCommandService.class);

    public GenericResponse<List<AgentCommandTriggeredStatusPojo>> triggerAgentCommand(Request request, Response response) {
        GenericResponse<List<AgentCommandTriggeredStatusPojo>> responseObject = new GenericResponse<>();

        try {
            TriggerAgentCommandBL triggerAgentCommandBL = new TriggerAgentCommandBL();
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<List<AgentCommandPojo>> utilityBean = triggerAgentCommandBL.clientValidation(requestObject);
            List<AgentCommandPojo> agentCommandPojoList = triggerAgentCommandBL.serverValidation(utilityBean);
            List<AgentCommandTriggeredStatusPojo> agentCommandTriggeredStatusPojoList = triggerAgentCommandBL.process(agentCommandPojoList);

            responseObject.setData(agentCommandTriggeredStatusPojoList);
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);

        } catch (ClientException | ServerException | DataProcessingException e) {
            LOGGER.error("Invalid Request", e);
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            LOGGER.error("Error occurred while triggering agent command", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }

        return responseObject;
    }
}
