package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.KpiMaintenanceStatusBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.UpdateInstanceKpiSeverityAnomalyBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.InstanceKpiThresholdSeverityAnomalyPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;

@Slf4j
public class UpdateKpiSeverityAnomaly {


    public GenericResponse<String> updateKpiSeverityAnomaly(Request request, Response response) {
        GenericResponse<String> responseObject = new GenericResponse<>();
        UpdateInstanceKpiSeverityAnomalyBL updateInstanceKpiSeverityAnomaly = new UpdateInstanceKpiSeverityAnomalyBL();

        try {
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<InstanceKpiThresholdSeverityAnomalyPojo> kpiMaintenanceUtilityBean = updateInstanceKpiSeverityAnomaly.clientValidation(requestObject);
            List<KpiMaintenanceStatusBean> validateData = updateInstanceKpiSeverityAnomaly.serverValidation(kpiMaintenanceUtilityBean);
            String data = updateInstanceKpiSeverityAnomaly.process(validateData);
            responseObject.setData(data);

            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Invalid Request", e);
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            log.error("Error occurred while updating KPI maintenance status", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        return responseObject;
    }
}
