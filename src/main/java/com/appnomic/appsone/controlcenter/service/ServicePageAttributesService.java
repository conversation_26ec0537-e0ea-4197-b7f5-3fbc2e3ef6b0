package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.GetServicePageAttributes;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.ServicePageAttribute;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

public class ServicePageAttributesService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ServicePageAttributesService.class);

    public static GenericResponse<List<ServicePageAttribute>> getServicePageAttributes(Request request, Response response) {
        GenericResponse<List<ServicePageAttribute>> responseObject = new GenericResponse<>();
        GetServicePageAttributes servicePageAttributes = new GetServicePageAttributes();

        try {
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<Integer> utilityBean = servicePageAttributes.clientValidation(requestObject);
            int serviceId = servicePageAttributes.serverValidation(utilityBean);

            responseObject.setData(servicePageAttributes.process(serviceId));

            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage("Service page attributes fetched successfully.");
            response.status(Constants.SUCCESS_STATUS_CODE);
            return responseObject;

        }
        catch (ClientException | ServerException | DataProcessingException e) {
            LOGGER.error("Error while fetching service page attributes. Details: {}", e.getMessage(), e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        }
        catch (Exception e) {
            LOGGER.error("Error while fetching service page attributes.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
    }
}
