package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.GetProducerBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.GetProducerPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;

@Slf4j
public class GetProducerService {

    public GenericResponse<List<GetProducerPojo>> getProducers(Request request, Response response) {
        GenericResponse<List<GetProducerPojo>> responseObject = new GenericResponse<>();

        try {
            GetProducerBL producerBusinessLogic = new GetProducerBL();
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<Integer> utilityBean = producerBusinessLogic.clientValidation(requestObject);
            Integer accountId = producerBusinessLogic.serverValidation(utilityBean);
            List<GetProducerPojo> outputList = producerBusinessLogic.process(accountId);

            responseObject = CommonUtils.getGenericResponse(response, StatusResponse.SUCCESS.toString(),
                    Constants.SUCCESS_STATUS_CODE, Constants.SUCCESS_MESSAGE, null, false);
            responseObject.setData(outputList);

        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Error details: {}", e.getMessage(), e);
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            log.error("Error occurred while triggering get producers", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }

        return responseObject;
    }
}
