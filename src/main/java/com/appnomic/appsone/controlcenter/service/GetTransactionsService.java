package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.GetTransactionDetailsBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.RequestedTransactionTypeEntity;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;

@Slf4j
public class GetTransactionsService {
    public GenericResponse<List<RequestedTransactionTypeEntity>> performRequestedAction(Request request, Response response) {
        GenericResponse<List<RequestedTransactionTypeEntity>> responseObject = new GenericResponse<>();

        RequestObject requestObject = new RequestObject(request);

        GetTransactionDetailsBL getTransactionDetailsBL = new GetTransactionDetailsBL();

        UtilityBean<String> utilityBean;

        try {
            utilityBean = getTransactionDetailsBL.clientValidation(requestObject);
            utilityBean = getTransactionDetailsBL.serverValidation(utilityBean);

            List<RequestedTransactionTypeEntity> transactionDetails = getTransactionDetailsBL.process(utilityBean);

            responseObject.setData(transactionDetails);
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);

            return responseObject;
        } catch (ClientException | ServerException | DataProcessingException e) {
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, e.getMessage(), null, true);
        } catch (Exception e) {
            log.error("Error occurred while performing requested action on the transaction requests. Details: ", e);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, e.getMessage(), null, true);
        }
    }
}
