package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.UpdateBatchProcessDetailsBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ProcessDetailsBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.BatchProcessDetails;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

public class UpdateBatchProcessService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateBatchProcessService.class);

    public GenericResponse<String> updateBatchProcessDetails(Request request, Response response) {

        GenericResponse<String> responseObject = new GenericResponse<>();
        UpdateBatchProcessDetailsBL updateBatchProcessDetailsBL = new UpdateBatchProcessDetailsBL();

        try {
            RequestObject requestObject = new RequestObject(request);
            UtilityBean<BatchProcessDetails> utilityBean = updateBatchProcessDetailsBL.clientValidation(requestObject);
            ProcessDetailsBean bean = updateBatchProcessDetailsBL.serverValidation(utilityBean);

            responseObject.setData(updateBatchProcessDetailsBL.process(bean));
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(UIMessages.PROCESS_EDIT_SUCCESS);

            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (ClientException | ServerException | DataProcessingException e) {

            LOGGER.error("Error while updating process details for the batch. Details: {}", e.getMessage(), e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);

            return responseObject;
        } catch (Exception e) {

            LOGGER.error("Error while updating process details for the batch.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);

            return responseObject;
        }
        return responseObject;
    }

}
