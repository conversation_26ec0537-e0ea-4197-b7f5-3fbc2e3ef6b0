package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.AgentBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.UpdateAgentBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.Agent;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;

@Slf4j
public class UpdateAgentService {

    public GenericResponse<String> updateAgents(Request request, Response response) {
        GenericResponse<String> responseObject = new GenericResponse<>();
        try {
            UpdateAgentBL updateAgentBL = new UpdateAgentBL();
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<List<Agent>> agentUtilityBean = updateAgentBL.clientValidation(requestObject);
            List<AgentBean> agentBean = updateAgentBL.serverValidation(agentUtilityBean);
            String data = updateAgentBL.process(agentBean);

            responseObject.setData(data);
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Invalid Request.", e);
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            log.error("Error occurred while updating agent details", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        return responseObject;
    }
}
