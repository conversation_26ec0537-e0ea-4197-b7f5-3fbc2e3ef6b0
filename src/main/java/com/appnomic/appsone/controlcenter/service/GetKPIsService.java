package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.KpiDetailsBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.KpiDetailsPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;

public class GetKPIsService {
    private static final Logger LOGGER = LoggerFactory.getLogger(GetKPIsService.class);
    public GenericResponse<List<KpiDetailsPojo>> getKPIs(Request request, Response response) {
        GenericResponse<List<KpiDetailsPojo>> responseObject = new GenericResponse<>();
        try {
            KpiDetailsBL kpiServiceBL = new KpiDetailsBL();
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<Integer> compInstanceUtilityBean = kpiServiceBL.clientValidation(requestObject);
            int validateData = kpiServiceBL.serverValidation(compInstanceUtilityBean);
            List<KpiDetailsPojo> data = kpiServiceBL.process(validateData);
            responseObject.setData(data);
            responseObject.setMessage("KPI details fetched successfully for the account.");
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);

        } catch (ClientException | ServerException | DataProcessingException e) {
            LOGGER.error("Invalid Request for given account");
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting the KPI list for account", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        return responseObject;

    }
}
