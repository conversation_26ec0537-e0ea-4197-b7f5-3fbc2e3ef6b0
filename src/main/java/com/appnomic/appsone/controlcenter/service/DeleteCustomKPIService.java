package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.keys.AccountKPIKey;
import com.appnomic.appsone.controlcenter.businesslogic.DeleteKPI;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

public class DeleteCustomKPIService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeleteCustomKPIService.class);

    public GenericResponse<String> deleteCustomKPI(Request request, Response response) {

        GenericResponse<String> responseObject = new GenericResponse<>();
        DeleteKPI deleteKPI = new DeleteKPI();
        try {
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<String> kpiUtilityBean = deleteKPI.clientValidation(requestObject);
            AccountKPIKey accountKPIKey = deleteKPI.serverValidation(kpiUtilityBean);

            responseObject.setData(deleteKPI.process(accountKPIKey));

            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage("Custom KPI deleted successfully");
            response.status(Constants.SUCCESS_STATUS_CODE);
            return responseObject;

        } catch (ClientException | ServerException | DataProcessingException e) {
            LOGGER.error("Error while processing delete KPI request. Details: {}", e.getMessage(), e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            LOGGER.error("Exception occurred while deleting the KPI.", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
    }
}
