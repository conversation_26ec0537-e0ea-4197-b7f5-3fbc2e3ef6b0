package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.GetTransactionThresholdBL;
import com.appnomic.appsone.controlcenter.businesslogic.PostTransactionThresholdBL;
import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;

/**
 * <AUTHOR> on 09/06/22
 */
@Slf4j
public class TransactionThresholdService {


    public static GenericResponse<List<TransactionStaticThresholds>> getStaticThresholds(Request request, Response response) {

        GenericResponse<List<TransactionStaticThresholds>> responseObject = new GenericResponse<>();
        log.trace("Method Invoked : TransactionStaticThresholds/getStaticThresholds");

        try {
            GetTransactionThresholdBL getTransactionThresholdBL = new GetTransactionThresholdBL();
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<GetTransactionThresholdPojo> clValidation = getTransactionThresholdBL.clientValidation(requestObject);
            UtilityBean<GetTransactionThresholdPojo> srvValidation = getTransactionThresholdBL.serverValidation(clValidation);
            List<TransactionStaticThresholds> result = getTransactionThresholdBL.process(srvValidation);

            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(UIMessages.TRANSACTION_THRESHOLDS_FETCH_SUCCESS);
            responseObject.setData(result);
            response.status(Constants.SUCCESS_STATUS_CODE);

        } catch (ClientException | ServerException | DataProcessingException e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Invalid Request.", e);
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while getting transaction thresholds(s).", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        return responseObject;

    }

    public static GenericResponse<List<IdPojo>> createThresholds(Request request, Response response) {

        GenericResponse<List<IdPojo>> responseObject = new GenericResponse<>();
        log.trace("Method Invoked : TransactionStaticThresholds/createThresholds");

        try {
            PostTransactionThresholdBL postTransactionThresholdBL = new PostTransactionThresholdBL();
            RequestObject requestObject = new RequestObject(request);

            UtilityBean<PostTransactionThresholdPojo> clValidation = postTransactionThresholdBL.clientValidation(requestObject);
            UtilityBean<PostTransactionThresholdPojo> srvValidation = postTransactionThresholdBL.serverValidation(clValidation);
            List<IdPojo> result = postTransactionThresholdBL.process(srvValidation);

            if (result.size() == 0){
                responseObject.setResponseStatus(StatusResponse.FAILURE.name());
                responseObject.setMessage(UIMessages.TRANSACTION_THRESHOLDS_ADD_FAILURE);
                responseObject.setData(result);
                response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            }else if (result.size() != clValidation.getPojoObject().getTransactionStaticThresholdsList().size()) {
                responseObject.setResponseStatus(StatusResponse.PARTIAL_FAILURE.name());
                responseObject.setMessage(UIMessages.TRANSACTION_THRESHOLDS_ADD_SUCCESS);
                responseObject.setData(result);
                response.status(Constants.SUCCESS_STATUS_CODE);
            } else {
                responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
                responseObject.setMessage(UIMessages.TRANSACTION_THRESHOLDS_ADD_SUCCESS);
                responseObject.setData(result);
                response.status(Constants.SUCCESS_STATUS_CODE);
            }

        } catch (ClientException | ServerException | DataProcessingException e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Invalid Request.", e);
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while creating transaction thresholds(s).", e);
            CommonUtils.populateErrorResponse(responseObject, response,
                    UIMessages.INTERNAL_SERVER_ERROR, Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }
        return responseObject;

    }
}
