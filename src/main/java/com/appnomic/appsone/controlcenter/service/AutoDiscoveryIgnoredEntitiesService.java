package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.AutoDiscoveryIgnoredEntitiesBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.AutoDiscoveryIgnoredEntitiesPojo;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

import java.util.List;

@Slf4j
public class AutoDiscoveryIgnoredEntitiesService {

    public GenericResponse ignoredEntities(Request request, Response response) {
        AutoDiscoveryIgnoredEntitiesBL ignoredEntities = new AutoDiscoveryIgnoredEntitiesBL();
        GenericResponse<List<AutoDiscoveryIgnoredEntitiesPojo>> responseObject = new GenericResponse<>();
        Object bean = new Object();
        try {
            RequestObject requestObject = new RequestObject(request);
            UtilityBean utilityBean = ignoredEntities.clientValidation(requestObject);
            ignoredEntities.serverValidation(utilityBean);
            List<AutoDiscoveryIgnoredEntitiesPojo> ignoredEntitesList= ignoredEntities.process(bean);
            responseObject.setData(ignoredEntitesList);
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage("Ignored entities fetched successfully.");
            response.status(Constants.SUCCESS_STATUS_CODE);
        }
        catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Error details: {}", e.getMessage(), e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            log.error("Error while processing the request. {}.", e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        }

        return responseObject;
    }
}
