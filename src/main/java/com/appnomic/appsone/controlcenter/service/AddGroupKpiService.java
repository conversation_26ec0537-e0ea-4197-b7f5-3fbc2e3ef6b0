package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.KpiException;
import com.appnomic.appsone.controlcenter.pojo.GroupKpi;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

public class AddGroupKpiService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AddGroupKpiService.class);
    private static final String ERROR_MESSAGE = "addKpiGroup REST CALL failed. Reason: {}";
    private static final String INVALID_VALUES_FOR_ONE_OR_MORE_ATTRIBUTES = "Invalid request. Reason: Invalid values " +
            "for one or more attributes. For more details, refer application log file.";

    public GenericResponse addGroupKpi(Request request, Response response) {
        try {
            return validateAndProcessRequest(request, response);
        } catch (Exception e) {
            LOGGER.error("Encountered exception while processing addKpi REST call. Reason: {}", e.getMessage());
            return CommonUtils.getGenericResponse(response, StatusResponse.SUCCESS.name(), Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
                    "Internal Server Error. Kindly contact the administrator.", null, true);
        }
    }

    private GenericResponse validateAndProcessRequest(Request request, Response response) {
        if ((null == request) || (null == request.body()) || (request.body().trim().isEmpty())) {
            LOGGER.error("Validation failure. Request or request body cannot be NULL or empty.");
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    "Request or request body cannot be NULL or empty.", null, true);
        }

        LOGGER.info("addKpi API request body: {}", request);

        String accountIdentifier = request.params(":identifier");
        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

        if (null == account) {
            LOGGER.error("Validation failure. Invalid account ID: [{}]", accountIdentifier);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    "Invalid account identifier. For more details, refer application log file.", null, true);
        }
        int accountId = account.getId();

        com.appnomic.appsone.controlcenter.businesslogic.GroupKpi groupKpi = new com.appnomic.appsone.controlcenter.businesslogic.GroupKpi();
        String userId = groupKpi.getUserId(request.headers("Authorization"));

        if (null == userId) {
            LOGGER.error("Validation failure. Invalid user ID.");
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    "Invalid user identifier. For more details, refer application log file.", null, true);
        }

        GroupKpi groupKpiRequest;
        try {
            groupKpiRequest = groupKpi.clientValidation(request.body());
        } catch (KpiException e) {
            LOGGER.error(ERROR_MESSAGE, e.getMessage());
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    INVALID_VALUES_FOR_ONE_OR_MORE_ATTRIBUTES, null, true);
        }

        try {
            groupKpi.serverValidation(groupKpiRequest, accountId);
        } catch (KpiException e) {
            LOGGER.error(ERROR_MESSAGE, "Server validation failed.");
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    INVALID_VALUES_FOR_ONE_OR_MORE_ATTRIBUTES, null, true);
        }

        try {
            IdPojo output = groupKpi.processGroupKpi(groupKpiRequest, userId, accountId);
            GenericResponse<IdPojo> genericResponse = CommonUtils.getGenericResponse(response, StatusResponse.SUCCESS.name(),
                    Constants.SUCCESS_STATUS_CODE, "Group KPI successfully created.", null, false);
            genericResponse.setData(output);

            return genericResponse;
        } catch (KpiException e) {
            LOGGER.error(ERROR_MESSAGE, e.getMessage());
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE,
                    e.getMessage() + "For more details, refer application log file.", null, true);
        }

    }
}
