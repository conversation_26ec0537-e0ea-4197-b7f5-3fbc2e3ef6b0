package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.Host;
import com.appnomic.appsone.controlcenter.businesslogic.PushDiscoveryDataBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

public class PushDiscoveryDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PushDiscoveryDataService.class);

    public GenericResponse<String> pushDiscoveryData(Request request, Response response) {
        GenericResponse<String> responseObject = new GenericResponse<>();
        PushDiscoveryDataBL bl = new PushDiscoveryDataBL();

        try {
            RequestObject requestObject = new RequestObject(request);
            UtilityBean<com.appnomic.appsone.controlcenter.beans.autodiscovery.Host> clientValidation = bl.clientValidation(requestObject);
            UtilityBean<Host> serverValidation = bl.serverValidation(clientValidation);
            String data = bl.process(serverValidation);
            responseObject = CommonUtils.getGenericResponse(response, StatusResponse.SUCCESS.name(), Constants.SUCCESS_STATUS_CODE, "Host(s) are added to system.", null, false);
            responseObject.setData(data);
            return responseObject;
        } catch (ClientException | ServerException | DataProcessingException e) {
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, e.getMessage(), null, true);
        } catch (Exception e) {
                LOGGER.error("Error occurred while pushing discovery data.", e);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, e.getMessage(), null, true);
        }
    }
}
