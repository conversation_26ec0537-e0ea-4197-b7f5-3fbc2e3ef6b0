package com.appnomic.appsone.controlcenter.util;

import com.appnomic.appsone.controlcenter.common.Constants;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPrivateKey;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPublicKey;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

public class SignatureTool {

    private static final Logger logger = LoggerFactory.getLogger(SignatureTool.class);

    public byte[] generateSignature(byte[] content, PrivateKey privateKey) throws SignatureException, InvalidKeyException, NoSuchAlgorithmException, NoSuchProviderException {
        Signature ecdsaSign = Signature.getInstance("SHA256withECDSA", "BC");
        ecdsaSign.initSign(privateKey);
        ecdsaSign.update(content);
        return ecdsaSign.sign();
    }

    public BCECPrivateKey getPrivateKey(byte[] ecRemotePriKey) throws NoSuchProviderException,
            NoSuchAlgorithmException, InvalidKeySpecException {
        KeyFactory factory = KeyFactory.getInstance("ECDSA", "BC");
        java.security.PrivateKey ecPrivateKey = factory
                .generatePrivate(new PKCS8EncodedKeySpec(ecRemotePriKey)); // Helper.toByte(ecRemotePubKey)) is java.security.PublicKey#getEncoded()
        return (BCECPrivateKey) ecPrivateKey;
    }

    public boolean validateSignature(byte[] content, PublicKey publicKey, byte[] signature) throws
            SignatureException, InvalidKeyException, NoSuchAlgorithmException, NoSuchProviderException {
        Signature ecdsaVerify = Signature.getInstance("SHA256withECDSA", "BC");
        ecdsaVerify.initVerify(publicKey);
        ecdsaVerify.update(content);
        return ecdsaVerify.verify(signature);
    }

    public BCECPublicKey getPublicKey(byte[] ecRemotePubKey) throws NoSuchProviderException,
            NoSuchAlgorithmException, InvalidKeySpecException {
        KeyFactory factory = KeyFactory.getInstance("ECDSA", "BC");
        PublicKey ecPublicKey = factory
                .generatePublic(new X509EncodedKeySpec(ecRemotePubKey)); // Helper.toByte(ecRemotePubKey)) is java.security.PublicKey#getEncoded()
        return (BCECPublicKey) ecPublicKey;
    }

    public static String generateSignature(byte[] fileBytes, String key) {

        Security.removeProvider(Constants.BC_PROVIDER_NAME);
        Security.addProvider(new BouncyCastleProvider());
        SignatureTool tool = new SignatureTool();
        String signature = null;
        try {
            byte[] encodedPrivateKey = key.getBytes();
            PrivateKey privateKey = tool.getPrivateKey(Base64.decode(encodedPrivateKey));
            byte[] signatureBytes = tool.generateSignature(fileBytes, privateKey);
            byte[] encodedSignature = Base64.encode(signatureBytes);
            signature = new String(encodedSignature, StandardCharsets.UTF_8);
        }catch (Exception e) {
            logger.error("Error in generating signature", e);
        }
        return signature;
    }

    public static boolean validateSignature(byte[] fileByte, String pubKey, String oldSignature) {

        Security.removeProvider(Constants.BC_PROVIDER_NAME);
        Security.addProvider(new BouncyCastleProvider());
        SignatureTool tool = new SignatureTool();
        try {
            byte[] encodedPublicKey = pubKey.getBytes();
            PublicKey publicKey =tool.getPublicKey(Base64.decode(encodedPublicKey));
            byte[] signingContent = fileByte;
            byte[] signature = Base64.decode(oldSignature.getBytes());
            if(tool.validateSignature(signingContent, publicKey, signature)) {
                logger.debug("Signature verification passed.");
                return true;
            } else {
                logger.error("Signature verification failed.");
            }
        } catch (Exception e) {
            logger.error("Error in signature validation", e);
        }
        return false;
    }
}
