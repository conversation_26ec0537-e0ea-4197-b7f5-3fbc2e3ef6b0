package com.appnomic.appsone.controlcenter.util;


import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.service.KeyCloakAuthService;
import com.google.common.util.concurrent.AbstractScheduledService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> : 07/05/2019
 */
public class KeycloakSessionManagmentScheduler extends AbstractScheduledService {
    private static final Logger LOGGER = LoggerFactory.getLogger(KeycloakSessionManagmentScheduler.class);
    private static final String REFRESH_INTERVAL = ConfProperties.getString(Constants.KEYCLOAK_CONNECTION_REFRESH,
            Constants.KEYCLOAK_CONNECTION_REFRESH_DEFAULT);
    @Override
    protected void runOneIteration() throws Exception {
        LOGGER.debug("Refreshing keycloak connection");
        try {
            KeyCloakAuthService.init();
        }   catch (Exception e) {
            LOGGER.error("Error occurred while refreshing keycloak connection",e);
        }
    }

    @Override
    protected Scheduler scheduler() {
        try {
            long schedulerPeriodInMinutes = Integer.valueOf(REFRESH_INTERVAL);
            LOGGER.info("Connection refresh interval for keycloak is : {} minutes",schedulerPeriodInMinutes);
            return Scheduler.newFixedRateSchedule(0L, schedulerPeriodInMinutes, TimeUnit.MINUTES);
        }   catch (Exception e) {
            LOGGER.error("Error while creating scheduler for keycloak connection refresh",e);
        }
        return null;
    }
}
