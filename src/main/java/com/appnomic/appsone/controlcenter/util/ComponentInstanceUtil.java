package com.appnomic.appsone.controlcenter.util;

import com.appnomic.appsone.common.exception.AppsOneException;
import com.appnomic.appsone.common.util.Commons;
import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.businesslogic.TagMappingBL;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.cache.keys.CommVersionKPIs;
import com.appnomic.appsone.controlcenter.cache.keys.MstKpi;
import com.appnomic.appsone.controlcenter.cache.keys.ProducerKpis;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.TagMappingBean;
import com.appnomic.appsone.controlcenter.dao.redis.*;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.ViewTypes;
import com.heal.configuration.pojos.*;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.Handle;

import java.security.Security;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ComponentInstanceUtil {
    private static int hostComponentTypeId = 1;
    private static final int controllerId = MasterCache.getTagDetails(Constants.CONTROLLER_TAG).getId();

    public static ComponentInstanceBean validateAndGetComponentInstance(ComponentInstancePojo instance, String userId, int accountId,
                                                                        List<Controller> serviceList, List<Integer> agentIdsList, Handle handle) throws RequestException {
        String err;
        int isHost = 0;
        int isPod = 0;
        MasterComponentBean componentBean = Objects.requireNonNull(ComponentDataService
                        .getComponentDetailsWithNameandVersion(instance.getComponentName(), instance.getComponentVersion(), accountId, handle))
                .stream()
                .filter(c -> c.getName().equals(instance.getComponentName()) && c.getComponentVersionName().equals(instance.getComponentVersion()))
                .findAny()
                .orElse(null);

        if (componentBean == null) {
            err = "Component with name '" + instance.getComponentName() + "' and version '" + instance.getComponentVersion() + "' doesn't exist.";
            log.error(err);
            throw new RequestException(err);
        }

        MasterComponentTypeBean componentTypeBean = MasterCache.getMasterComponentTypeUsingName(Constants.COMPONENT_TYPE_HOST, String.valueOf(accountId));

        if (componentTypeBean == null) {
            err = "Component with type '" + Constants.HOST + "' doesn't exist.";
            log.error(err);
            throw new RequestException(err);
        }

        hostComponentTypeId = componentTypeBean.getId();

        if (componentTypeBean.getId() == componentBean.getComponentTypeId()) {
            isHost = 1;
        } else {
            componentTypeBean = MasterCache.getMasterComponentTypeUsingName(Constants.COMPONENT_TYPE_POD, String.valueOf(accountId));

            if (componentTypeBean != null && componentTypeBean.getId() == componentBean.getComponentTypeId()) {
                isPod = 1;
            }
        }

        int[] serviceId = null;

        if (serviceList != null) {
            serviceId = getServiceIds(serviceList, instance.getServiceIdentifiers());
        }

        Map<Integer, String> agentIdsMap = new HashMap<>();
        List<String> agentIdentifiers = instance.getAgentIdentifiers();
        if (agentIdentifiers != null && agentIdsList.isEmpty()) {
            for (String agentIdentifier : agentIdentifiers) {
                AgentBean agentBean = AgentDataService.getAgentBeanData(agentIdentifier, handle);
                if (agentBean == null) {
                    err = "Agent Identifier '" + agentIdentifier + "' " + Constants.DOES_NOT_EXIST;
                    log.error(err);
                    throw new RequestException(err);
                }
                agentIdsMap.put(agentBean.getId(), agentIdentifier);
            }
        }

        String name = instance.getName();
        if (name == null) {
            name = instance.getIdentifier();
        }

        Attributes hostAttribute = instance.getAttributes().stream().filter(a -> (a.getName().equals(Constants.HOST_ADDRESS))).findAny().orElse(null);
        Attributes monitorPortAttribute = instance.getAttributes().stream().filter(a -> (a.getName().equalsIgnoreCase("MonitorPort"))).findAny().orElse(null);

        if (hostAttribute == null) {
            err = Constants.HOST_ADDRESS + " attribute is missing for component instance name '" + name + "'";
            log.error(err);
            throw new RequestException(err);
        }

        // Check if the environment provided in the input is included in our supported subtypes
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();

        int environmentTypeIdFromTypeName = compInstanceDataService.getEnvTypeIdFromTypeName(Constants.ENVIRONMENT_TYPE_NAME, null);
        if(environmentTypeIdFromTypeName == 0) {
            String errMsg = String.format("Could not find the type id for the type name %s in Data source.", Constants.ENVIRONMENT_TYPE_NAME);
            log.error(errMsg);
            throw new RequestException(errMsg);
        }

        List<ObjPojo> envSubTypeDetails = compInstanceDataService.getEnvSubTypeDetails(environmentTypeIdFromTypeName, handle);

        ObjPojo envType = envSubTypeDetails.stream()
                .filter(f -> f.getName().equalsIgnoreCase(instance.getEnvironment()))
                .findAny()
                .orElseGet(() -> {
                    String errMsg = String.format("The environment detail provided in the input %s is not among the supported subtypes for type %s. Marking environment value as 'NONE'.", instance.getEnvironment(), Constants.ENVIRONMENT_TYPE_NAME);
                    log.error(errMsg);
                    return envSubTypeDetails.stream()
                            .filter(f -> f.getName().equalsIgnoreCase(Constants.NONE))
                            .findAny()
                            .orElseThrow(() -> new IllegalArgumentException("NONE subtype not found."));
                });

        int isDR = envType.getId();

        //Validate if the combination of Host address & environment exist. For Example: If Host Address is ************** and Environment is 'PROD' validate if **************_PROD exist.
        if (isHost == 1) {
            int instanceDetailsByHostAddress = new CompInstanceDataService()
                    .getInstanceDetailsByHostAddress(accountId, hostAttribute.getValue(), isDR, handle);

            if (instanceDetailsByHostAddress == -1) {
                String errMsg = String.format("Exception while getting count of the instances with host_address %s and environment %s for account %s from Data source", hostAttribute.getValue(), isDR, accountId);
                log.error(errMsg);
                throw new RequestException(errMsg);
            }

            if (instanceDetailsByHostAddress >= 1) {
                err = "Combination of host address and environment already exist for account.";
                log.error(err);
                throw new RequestException(err);
            }

            log.info("Host Address {} is being added for the first time. Continuing with request processing.", hostAttribute.getValue());

        } else {
            /*
            Host address:MonitorPort match at account level for component instance
             */
            if (monitorPortAttribute != null && !StringUtils.isEmpty(monitorPortAttribute.getValue())) {
                int hostPortExistCount = new CompInstanceDataService()
                        .checkHostAddressMonitorPortExistance(accountId, hostAttribute.getValue(), monitorPortAttribute.getValue(), handle);

                if (hostPortExistCount > 0) {
                    err = "Component Instance's host address and monitor port already exists in account with id " + accountId + ".";
                    log.error(err);
                    throw new RequestException(err);
                } else if (hostPortExistCount == -1) {
                    err = "Exception while checking for host_address and port existence for account with id " + accountId + ".";
                    log.error(err);
                    throw new RequestException(err);
                }
            }
        }

        /*Check whether host address exists while creating comp_instance */
        if(componentBean.getComponentTypeId() != 1){
            int hostExistCount = new CompInstanceDataService()
                    .checkHostAddressExistance(accountId, hostAttribute.getValue(), handle);
            if (hostExistCount == 0){
                log.error("Component Instance mapped to host address does not exists in account with id {}" , accountId );
                throw new RequestException("Host address does not exist");
            }
        }

        /*
        Cluster component match at service level
         */
        if (serviceId != null) {
            for (int sId : serviceId) {
                List<InstanceClusterServicePojo> instanceDetails = new CompInstanceDataService()
                        .getInstanceClusterServiceDetails(sId, accountId, handle);
                if (isHost == 1) {
                    /*
                    Cluster component match at service level for host instance
                     */
                    if (instanceDetails != null && !instanceDetails.isEmpty()) {
                        for (InstanceClusterServicePojo singleHostDetails : instanceDetails) {
                            if (singleHostDetails.getClusterComponentId() != componentBean.getId()
                                    && singleHostDetails.getClusterComponentTypeId() == hostComponentTypeId) {
                                err = "Host Instance's component is different from existing host " +
                                        "cluster's component for the service id " + sId + ".";
                                log.error(err);
                                throw new RequestException(err);
                            }

                            if (singleHostDetails.getClusterCommonVersionId() != componentBean.getCommonVersionId()
                                    && singleHostDetails.getClusterComponentTypeId() == hostComponentTypeId) {
                                err = "Host Instance's component common version is different from existing host " +
                                        "cluster's component common version for the service id " + sId + ".";
                                log.error(err);
                                throw new RequestException(err);
                            }
                        }
                    }
                } else {
                    /*
                    Cluster component match at service level for component instance
                     */
                    if (instanceDetails != null && !instanceDetails.isEmpty()) {
                        for (InstanceClusterServicePojo singleCompInstanceDetails : instanceDetails) {
                            if (singleCompInstanceDetails.getClusterComponentId() != componentBean.getId()
                                    && singleCompInstanceDetails.getClusterComponentTypeId() != hostComponentTypeId) {
                                err = "Component Instance's component is different from existing component " +
                                        "cluster's component for the service id " + sId + ".";
                                log.error(err);
                                throw new RequestException(err);
                            }

                            if (singleCompInstanceDetails.getClusterCommonVersionId() != componentBean.getCommonVersionId()
                                    && singleCompInstanceDetails.getClusterComponentTypeId() != hostComponentTypeId) {
                                err = "Component Instance's component common version is different from existing Component " +
                                        "cluster's component common version for the service id " + sId + ".";
                                log.error(err);
                                throw new RequestException(err);
                            }
                        }
                    }
                }
            }
        }

        return ComponentInstanceBean.builder()
                .name(name)
                .identifier(instance.getIdentifier())
                .isDR(isDR)
                .mstComponentId(componentBean.getId())
                .mstComponentName(instance.getComponentName())
                .mstComponentVersionId(componentBean.getComponentVersionId())
                .mstComponentVersion(componentBean.getComponentVersionName())
                .mstComponentTypeId(componentBean.getComponentTypeId())
                .mstComponentType(componentBean.getComponentTypeName())
                .mstCommonVersionId(componentBean.getCommonVersionId())
                .mstCommonVersionName(componentBean.getCommonVersionName())
                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .userDetailsId(userId)
                .hostAddress(hostAttribute.getValue())
                .accountId(accountId)
                .discovery(instance.getDiscovery())
                .serviceIds(serviceId)
                .serviceIdentifiers(instance.getServiceIdentifiers())
                .parentIdentifier(instance.getParentIdentifier())
                .parentName(instance.getParentName())
                .isHost(isHost)
                .status(1)
                .agentIdsMap(agentIdsMap)
                .agentIdentifiers(instance.getAgentIdentifiers())
                .isPod(isPod)
                .attributes(getComponentAttributesListBean(instance, componentBean.getId(), componentBean.getCommonVersionId(), userId))
                .supervisorId(instance.getSupervisorId())
                .build();

    }

    public static int addComponentInstance(ComponentInstanceBean bean, Handle handle) throws ControlCenterException {
        String err;
        int instanceId;
        String instanceIdentifier = bean.getIdentifier();
        String hostAddress = bean.getHostAddress();
        int hostId = 0;
        /*
        get host_id for the component instances
         */
        if (bean.getIsHost() == 0) {
            try {
                //Assuming that all the services are part of the same host.
                List<HostInstanceDetails> hostInstanceBean =
                        new BindInDataService().getHostInstanceId(hostComponentTypeId, hostAddress,
                                bean.getAccountId(), bean.getIsDR(), handle);
                log.debug("number of host instance found for hostAddress {} is {}", hostAddress, hostInstanceBean.size());
                if(hostInstanceBean.size() > 1) {
                        err = String.format("Multiple host instances found for hostAddress %s with DR value %s", hostAddress, bean.getIsDR());
                        log.error(err);
                        throw new ControlCenterException(err);
                } else {
                    hostId = hostInstanceBean.get(0).getHostInstanceId();
                }
            } catch (ControlCenterException e) {
                log.error("Unable to fetch hostId for component instances");
                throw new ControlCenterException(e.getMessage());
            }
        }

        //Add cluster if we're adding instance of component for the first time
        String instanceName = bean.getName();
        int clusterId = -1;

        moreServerValidations(bean, handle);

        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
        for (int serviceId : bean.getServiceIds()) {
            if (bean.getIsKubernetes() == 1 && bean.getIsHost() == 0 && bean.getIsPod() == 0) {
                CompInstanceAttributesBean attribute = bean.getAttributes().stream().filter(a -> (a.getAttributeName().equals(Constants.ATTRIBUTE_CONTAINER_NAME))).findAny().orElse(null);
                if (attribute == null) {
                    err = Constants.ATTRIBUTE_CONTAINER_NAME + " attribute is missing for container instance '" + bean.getName() + "'";
                    log.error(err);
                    throw new ControlCenterException(err);
                }
                clusterId = compInstanceDataService.getComponentInstanceIdByAttributeServiceCluster(bean.getMstComponentTypeId(), attribute.getAttributeName(), attribute.getAttributeValue(), serviceId, controllerId, bean.getAccountId(), handle);
            } else if (bean.getAppId() > 0)
                clusterId = compInstanceDataService.getClusterId(bean.getMstComponentId(), bean.getMstCommonVersionId(), bean.getMstComponentTypeId(), bean.getAppId(), controllerId, bean.getAccountId(), handle);
            else
                clusterId = compInstanceDataService.getClusterId(bean.getMstComponentId(), bean.getMstCommonVersionId(), bean.getMstComponentTypeId(), serviceId, controllerId, bean.getAccountId(), handle);

            if (clusterId > 0) break;
        }

        if (clusterId <= 0) {
            bean.setHostAddress(null);
            bean.setIdentifier(instanceIdentifier + "_cluster");
            bean.setName(instanceName + "_cluster");
            clusterId = addCluster(bean, handle);
        }

        bean.setHostId(hostId);
        //Now add the comp instance
        setHostInfo(bean, false, handle);
        bean.setIsCluster(0);
        bean.setIdentifier(instanceIdentifier);
        bean.setName(instanceName);
        bean.setHostAddress(hostAddress);

        if (bean.getIsUpdate() == 0)
            instanceId = compInstanceDataService.addComponentInstance(bean, handle);
        else
            instanceId = compInstanceDataService.updateComponentInstance(bean, handle);

        if (instanceId != -1) {
            if (bean.getIsUpdate() == 0) {
                log.info("Added comp instance  for identifier:{}, name :{}", bean.getIdentifier(), bean.getName());
                bean.setId(instanceId);
                //Add cluster mapping
                addClusterMapping(bean, clusterId, handle);
                //Add the attributes
                addAttributes(bean, handle, false);
                //Add KPIs
                addKPIs(bean, instanceId, handle);
                //Add Group KPIs
                addGroupKPIs(bean, instanceId, false, handle);
                //Agent mapping
                if (!bean.getAgentIdsMap().isEmpty()) {
                    addAgentMapping(instanceId, bean.getAgentIdsMap(), handle);
                }
            } else {
                log.info("Updated comp instance for identifier:{}, name :{}", bean.getIdentifier(), bean.getName());
            }

        } else {
            err = "Failed to add the comp instance data for comp instance name:" + bean.getName();
            log.error(err);
            throw new ControlCenterException(err);
        }
        bean.setId(instanceId);

        return instanceId;
    }

    private static int[] getServiceIds(List<Controller> serviceList, List<String> serviceIdentifiers) throws RequestException {
        int[] serviceIds = new int[serviceIdentifiers.size()];
        String err;

        if (serviceList != null && !serviceIdentifiers.isEmpty()) {
            int i = 0;
            for (String svcIdentifier : serviceIdentifiers) {
                Controller service = serviceList.stream().filter(c -> (c.getIdentifier().equals(svcIdentifier.trim())) && c.getStatus() == 1).findAny().orElse(null);
                if (service == null) {
                    err = "Service Identifier '" + svcIdentifier + "' " + Constants.DOES_NOT_EXIST;
                    log.error(err);
                    throw new RequestException(err);
                }
                serviceIds[i++] = Integer.parseInt(service.getAppId());
            }
        }
        return serviceIds;
    }

    private static void moreServerValidations(ComponentInstanceBean bean, Handle handle) throws ControlCenterException {
        String err;
        int isUpdate = 0;
        //for node instances, create app level cluster
        int appId = 0; //it will be set only for node instances
        int serviceId = bean.getServiceIds()[0];
        int serviceTypeTagId = MasterCache.getTagDetails(Constants.SERVICE_TYPE_TAG).getId();
        int tagMappingId = TagsDataService.getTagMappingId(serviceTypeTagId, serviceId, Constants.CONTROLLER, Constants.SERVICE_TYPE_DEFAULT, Constants.KUBERNETES, bean.getAccountId(), handle);
        if (tagMappingId > 0)
            bean.setIsKubernetes(1);

        if (bean.getIsKubernetes() == 1 && bean.getIsHost() == 1) {
            appId = TagsDataService.getTagMappingObjectId(controllerId, Constants.CONTROLLER, String.valueOf(serviceId), bean.getServiceIdentifiers().get(0), bean.getAccountId(), handle);
        }

        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
        ComponentInstanceBean componentInstanceBean = compInstanceDataService.getComponentInstanceByIdentifier(bean.getIdentifier(), bean.getAccountId(), handle);
        if (componentInstanceBean != null) {
            TagMappingBean tagMappingBean = compInstanceDataService.getComponentInstanceTagMappingDetails(componentInstanceBean.getId(), controllerId, bean.getAccountId(), handle);
            if (tagMappingBean != null && componentInstanceBean.getStatus() == 0) {
                if (appId > 0) {
                    int tmpAppId = TagsDataService.getTagMappingObjectId(controllerId, Constants.CONTROLLER, String.valueOf(tagMappingBean.getTagKey()), tagMappingBean.getTagValue(), bean.getAccountId(), handle);
                    if (appId == tmpAppId) {
                        log.info("Component Instance with identifier '" + bean.getIdentifier() + "' already exists. So Reactivating..");
                        isUpdate = 1;
                    }
                } else {
                    if (bean.getServiceIdentifiers().get(0).trim().equals(tagMappingBean.getTagValue())) {
                        log.info("Component Instance with identifier '" + bean.getIdentifier() + "' already exists. So Reactivating..");
                        isUpdate = 1;
                    }
                }
            } else {
                err = "Component Instance identifier '" + bean.getIdentifier() + "' already exists.";
                log.error(err);
                throw new ControlCenterException(err);
            }
        } else {
            componentInstanceBean = compInstanceDataService.getActiveComponentInstance(null, bean.getName(), bean.getAccountId(), handle);
            if (componentInstanceBean != null) {
                TagMappingBean tagMappingBean = compInstanceDataService.getComponentInstanceTagMappingDetails(componentInstanceBean.getId(), controllerId, bean.getAccountId(), handle);
                if (tagMappingBean != null && bean.getServiceIdentifiers().get(0).trim().equals(tagMappingBean.getTagValue())) {
                    err = "Component Instance with name '" + bean.getName() + "' already exists.";
                    log.error(err);
                    throw new ControlCenterException(err);
                }
            }
        }
        bean.setAppId(appId);
        bean.setIsUpdate(isUpdate);

    }

    private static void setHostInfo(ComponentInstanceBean bean, boolean isCluster, Handle handle) throws ControlCenterException {
        MasterComponentTypeBean componentTypeBean = MasterCache.getMasterComponentTypeUsingName(Constants.COMPONENT_TYPE_HOST, String.valueOf(bean.getAccountId()));
        //Host ID info
        int hostId = bean.getHostId();
        if (isCluster) {
            if (bean.getIsHost() == 1) {
                hostId = 0;
            } else {
                try {
                    //Assuming that all the services are part of the same host.
                    List<Integer> hostIds = new BindInDataService()
                            .getHostClusterId(Arrays.stream(bean.getServiceIds()).boxed().collect(Collectors.toList()), componentTypeBean != null ? componentTypeBean.getId() : 1, handle);
                    if (hostIds.size() != 1) {
                        log.warn("Multiple/No host cluster Ids available for the provided services. Resetting hostId due to this conflict.");
                        hostId = 0;
                    } else {
                        hostId = hostIds.get(0);
                    }
                } catch (ControlCenterException e) {
                    log.error("Unable to fetch the host clusterId for component instance cluster");
                    throw new ControlCenterException(e.getMessage());
                }
            }
        }

        String parentIdentifier = bean.getParentIdentifier();
        String parentName = bean.getParentName();
        if (parentIdentifier != null || parentName != null) {
            //if clustered, then host id should be cluster id of host instance
            if (isCluster) {
                parentIdentifier = parentIdentifier + "_cluster";
                parentName = parentName + "_cluster";

            }
            CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
            ComponentInstanceBean instanceBean = compInstanceDataService.getActiveComponentInstanceByIdentifierAndName(parentIdentifier, parentName, bean.getAccountId(), handle);
            if (instanceBean == null) {
                String err = null;
                if (isCluster) {
                    CompClusterMappingBean clusterMappingBean = compInstanceDataService.getCompClusterDetails(bean.getParentIdentifier(), bean.getParentName(), bean.getAccountId(), handle);
                    if (clusterMappingBean != null) {
                        hostId = clusterMappingBean.getClusterId();
                    } else {
                        err = "Unable to find Cluster id for Parent instance identifier '" + bean.getParentIdentifier() + "' or name '" + bean.getParentName() + "' " + Constants.DOES_NOT_EXIST;
                    }
                } else {
                    err = "Parent instance identifier '" + parentIdentifier + "' or name '" + parentName + "' " + Constants.DOES_NOT_EXIST;
                }

                if (err != null) {
                    log.error(err);
                    throw new ControlCenterException(err);
                }
            } else {
                hostId = instanceBean.getId();
            }
        }

        bean.setHostId(hostId);
        bean.setParentId(hostId);
    }

    private static int addCluster(ComponentInstanceBean bean, Handle handle) throws ControlCenterException {
        String err;
        int clusterId;

        setHostInfo(bean, true, handle);
        bean.setIsCluster(1);
        //Differentiate cluster identifier from instance identifier. Same identifier can't be inserted in comp instance

        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
        if (bean.getIsUpdate() == 0)
            clusterId = compInstanceDataService.addComponentInstance(bean, handle);
        else
            clusterId = compInstanceDataService.updateComponentInstance(bean, handle);

        if (clusterId != -1) {
            log.info("Comp instance cluster created/updated for comp instance name :{}, cluster_id:{}", bean.getName(), clusterId);
            addServiceDetails(clusterId, bean, handle);
            if (bean.getIsUpdate() == 0) {
                //Add KPIs
                addKPIs(bean, clusterId, handle);
                //Add Group KPIs
                addGroupKPIs(bean, clusterId, true, handle);
            }
        } else {
            err = "Failed to add the comp instance data for comp instance name:" + bean.getName();
            log.error(err);
            throw new ControlCenterException(err);
        }

        return clusterId;
    }

    public static void addKPIs(ComponentInstanceBean bean, int instanceId, Handle handle) throws ControlCenterException {
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();

        List<CompInstanceKpiDetailsBean> kpiList = compInstanceDataService.getDefaultCompInstanceKPIsData(bean.getMstComponentId(), bean.getMstCommonVersionId(), bean.getMstComponentVersionId(), bean.getMstComponentTypeId(), handle);
        if (kpiList != null && !kpiList.isEmpty()) {
            for (CompInstanceKpiDetailsBean kpiBean : kpiList) {
                kpiBean.setCompInstanceId(instanceId);
                kpiBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                kpiBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                kpiBean.setUserDetailsId(bean.getUserDetailsId());
                int id = compInstanceDataService.addNonGroupComponentInstanceKPI(kpiBean, handle);
                if (id == -1) {
                    String err = "Unable to add KPIs -" + kpiBean.getMstKpiDetailsId() + " for component instance id-" + instanceId;
                    log.error(err);
                    throw new ControlCenterException(err);
                }
                log.info("Added KPIs -" + kpiBean.getMstKpiDetailsId() + " for component instance id-" + instanceId);
            }
        } else {
            log.info("No KPIs found for component instance id-" + instanceId);
        }
    }

    public static void addGroupKPIs(ComponentInstanceBean bean, int instanceId, boolean isCluster, Handle handle) throws ControlCenterException {
        CommVersionKPIs commVersionKPIs = new CommVersionKPIs();
        commVersionKPIs.setAccountId(bean.getAccountId());
        commVersionKPIs.setMstCommonVersionId(bean.getMstCommonVersionId());
        List<ViewCommonVersionKPIsBean> viewCommonVersionKPIsBeansGroup = MasterCache.getGroupKPIUsingCommonVersionId(commVersionKPIs);
        if (viewCommonVersionKPIsBeansGroup == null) {
            String log = "No group kpi is available for given master component version -" + bean.getMstCommonVersionId() +
                    ", mstComponentType-" + bean.getMstComponentTypeId();
            ComponentInstanceUtil.log.warn(log);
            return;
        }
        if (isCluster) {
            final Set<Integer> clusterKpiIds = MasterDataService.getAllKpisList().stream().filter(
                            kpi -> !kpi.getClusterOperation().equalsIgnoreCase(Constants.NONE))
                    .map(AllKpiList::getKpiId).collect(Collectors.toSet());

            viewCommonVersionKPIsBeansGroup = viewCommonVersionKPIsBeansGroup.stream().filter(
                    group -> clusterKpiIds.contains(group.getKpiId())).collect(Collectors.toList());
        }
        for (ViewCommonVersionKPIsBean viewCommonVersionKPIsBean : viewCommonVersionKPIsBeansGroup) {
            if (viewCommonVersionKPIsBean.getStatus() == 0) {
                continue;
            }

            addEachGroupKPI(viewCommonVersionKPIsBean, bean, instanceId, handle);

        }
    }

    private static void addEachGroupKPI(ViewCommonVersionKPIsBean viewCommonVersionKPIsBean, ComponentInstanceBean bean,
                                        int instanceId, Handle handle) throws ControlCenterException {

        int collectionInterval = viewCommonVersionKPIsBean.getDefaultCollectionInterval();
        int groupId = viewCommonVersionKPIsBean.getKpiGroupId();
        MasterKpiGroupBean groupBean = MasterCache.getGroupKpiDetailList(bean.getAccountId(), groupId);
        if (groupBean == null) {
            String log = "No group kpis found in master kpi group details table for group id-" + groupId;
            ComponentInstanceUtil.log.warn(log);
            throw new ControlCenterException(log);
        }

        //only for discovered Group KPIs, add instance
        //Non-discovered ones, user has to manually add attributes entries in CC UI
        if (groupBean.getDiscovery() == 0) {
            return;
        }

        ProducerKpis producerKpis = new ProducerKpis();
        producerKpis.setMstKpiDetailsId(viewCommonVersionKPIsBean.getKpiId());
        producerKpis.setMstCompVersionId(bean.getMstComponentVersionId());
        producerKpis.setMstCompId(bean.getMstComponentId());
        producerKpis.setMstCompTypeId(bean.getMstComponentTypeId());
        producerKpis.setAccountId(bean.getAccountId());
        ViewProducerKPIsBean viewProducerKPIsBean = MasterCache.getViewProducerKPIsGroup(producerKpis);
        if (viewProducerKPIsBean == null) {
            String error = "No producers found for group kpi - " + viewCommonVersionKPIsBean.getKpiName();
            log.warn(error);
            return;
        }

        MstKpi mstKpi = new MstKpi();
        mstKpi.setAccountId(bean.getAccountId());
        mstKpi.setKpiId(viewProducerKPIsBean.getMstKpiDetailsId());
        MasterKPIDetailsBean kpiBean = MasterCache.getMasterKPIDetailsBean(mstKpi);
        if (kpiBean == null) {
            String error = "No group kpis found in master kpi details table for producer -" +
                    viewProducerKPIsBean.getProducerName();
            log.warn(error);
            throw new ControlCenterException(error);
        }

        CompInstanceKpiGroupDetailsBean compInstanceKpiGroupDetailsBean = new CompInstanceKpiGroupDetailsBean();
        compInstanceKpiGroupDetailsBean.setStatus(groupBean.getStatus());
        compInstanceKpiGroupDetailsBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        compInstanceKpiGroupDetailsBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        compInstanceKpiGroupDetailsBean.setUserDetailsId(bean.getUserDetailsId());
        compInstanceKpiGroupDetailsBean.setCompInstanceId(instanceId);
        compInstanceKpiGroupDetailsBean.setMstProducerKpiMappingId(viewProducerKPIsBean.getMstProducerKpiMappingId());
        compInstanceKpiGroupDetailsBean.setCollectionInterval(collectionInterval);
        compInstanceKpiGroupDetailsBean.setMstKpiDetailsId(viewCommonVersionKPIsBean.getKpiId());
        compInstanceKpiGroupDetailsBean.setIsDiscovery(groupBean.getDiscovery());
        compInstanceKpiGroupDetailsBean.setKpiGroupName(groupBean.getIdentifier());
        compInstanceKpiGroupDetailsBean.setMstKpiGroupId(groupId);
        compInstanceKpiGroupDetailsBean.setMstProducerId(viewProducerKPIsBean.getProducerId());
        compInstanceKpiGroupDetailsBean.setAttributeValue(Constants.ALL);
        compInstanceKpiGroupDetailsBean.setAliasName(Constants.ALL);
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
        int id = compInstanceDataService.addGroupComponentInstanceKPI(
                compInstanceKpiGroupDetailsBean, handle);
        if (id == -1) {
            String error = "Unable to add group kpi for component instance id-" +
                    compInstanceKpiGroupDetailsBean.getCompInstanceId() + ", group kpi name-" +
                    kpiBean.getName();
            log.warn(error);
            throw new ControlCenterException(error);
        }
        log.info("Added group kpi for component instance id-" +
                compInstanceKpiGroupDetailsBean.getCompInstanceId() + ", group kpi name-" +
                kpiBean.getName());

    }

    private static void addAgentMapping(int instanceId, Map<Integer, String> agentIdsMap, Handle handle) throws ControlCenterException {
        if (!agentIdsMap.isEmpty()) {
            List<AgentCompInstMappingBean> beanList = new ArrayList<>();
            for (int agentId : agentIdsMap.keySet()) {
                beanList.add(AgentCompInstMappingBean.builder()
                        .compInstanceId(instanceId)
                        .agentId(agentId)
                        .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                        .build());
            }

            int[] mappingId = new CompInstanceDataService().addAgentCompInstMapping(beanList, handle);
            if (mappingId == null) {
                String err = "Failed to add component instance agent mapping for instance id-" + instanceId;
                log.error(err);
                throw new ControlCenterException(err);
            } else {
                log.info("Added Comp instance agent mapping  for comp instance id :{}", instanceId);
            }
        }
    }

    public static void addAttributes(ComponentInstanceBean bean, Handle handle, boolean isUpdate) throws ControlCenterException {
        List<CompInstanceAttributesBean> attributesList = bean.getAttributes();
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();

        if (attributesList != null) {
            for (CompInstanceAttributesBean attribute : attributesList) {
                attribute.setCompInstanceId(bean.getId());
                attribute.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                int id;
                if (!isUpdate) {
                    attribute.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                    id = compInstanceDataService.addComponentInstanceAttributes(attribute, handle);
                } else {
                    id = compInstanceDataService.updateComponentInstanceAttributes(attribute, handle);
                }

                if (id == -1) {
                    String err = "Unable to add/update attribute-" + attribute.getAttributeName() + " for component instance id-" + bean.getId();
                    log.error(err);
                    throw new ControlCenterException(err);
                }
                log.info("Attribute-{} is added/updated for component instance id-{}", attribute.getAttributeName(), bean.getId());
            }
        } else {
            log.info("No Attributes found for component instance id-" + bean.getId());
        }
    }

    private static void addServiceDetails(int clusterId, ComponentInstanceBean bean, Handle handle) throws ControlCenterException {
        String err;
        if (bean.getIsUpdate() == 1) {
            int deleted = TagsDataService.deleteTagMapping(controllerId, clusterId, Constants.COMP_INSTANCE_TABLE, bean.getAccountId(), handle);
            if (deleted == -1) {
                err = "Failed to remove tag mapping info for comp instance name:" + bean.getName();
                log.error(err);
                throw new ControlCenterException(err);
            }
        }

        int[] svcIds = bean.getServiceIds();
        List<String> svcIdentifiers = bean.getServiceIdentifiers();
        for (int i = 0; i < svcIdentifiers.size(); i++) {
            int tagMappingId = TagMappingBL.addTagMapping(controllerId, clusterId, Constants.COMP_INSTANCE_TABLE,
                    String.valueOf(svcIds[i]), svcIdentifiers.get(i), bean.getUserDetailsId(), bean.getAccountId(), handle);
            if (tagMappingId != -1) {
                log.info("Tag mapping data is added successfully for comp instance name :{}, cluster_id:{}, service id:{}", bean.getName(), clusterId, svcIds[i]);
            } else {
                err = "Failed to add the Tag mapping data for comp instance name:" + bean.getName();
                log.error(err);
                throw new ControlCenterException(err);
            }
        }

        //for node instances, appId would be set for creating app level cluster
        if (bean.getAppId() > 0) {
            ControllerBean controllerBean = new ControllerDataService().getControllerById(bean.getAppId(), bean.getAccountId(), handle);
            int tagMappingId = TagMappingBL.addTagMapping(controllerId, clusterId, Constants.COMP_INSTANCE_TABLE,
                    String.valueOf(bean.getAppId()), controllerBean.getIdentifier(), bean.getUserDetailsId(), bean.getAccountId(), handle);
            if (tagMappingId != -1) {
                log.info("Tag mapping data is added successfully for comp instance name :{}, cluster_id:{}, app id:{}", bean.getName(), clusterId, bean.getAppId());
            } else {
                err = "Failed to add the Tag mapping data for comp instance name:" + bean.getName() + "and cluster id:" + clusterId + " and app id:" + bean.getAppId();
                log.error(err);
                throw new ControlCenterException(err);
            }
        }
    }

    private static void addClusterMapping(ComponentInstanceBean bean, int clusterId, Handle handle) throws ControlCenterException {
        CompClusterMappingBean compClusterMappingBean = new CompClusterMappingBean();
        compClusterMappingBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        compClusterMappingBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        compClusterMappingBean.setUserDetailsId(bean.getUserDetailsId());
        compClusterMappingBean.setAccountId(bean.getAccountId());
        compClusterMappingBean.setCompInstanceId(bean.getId());
        compClusterMappingBean.setClusterId(clusterId);
        int mappingId = new CompInstanceDataService().addCompClusterMapping(compClusterMappingBean, handle);
        if (mappingId == -1) {
            String err = "Failed to add component instance cluster mapping for instance name-" + bean.getName();
            log.error(err);
            throw new ControlCenterException(err);
        } else {
            log.info("Added Comp instance Cluster mapping  for comp instance name :{}, cluster_id:{}", bean.getName(), clusterId);
        }
    }

    private static List<CompInstanceAttributesBean> getComponentAttributesListBean(ComponentInstancePojo instance, int mstComponentId, int mstCommonVersionId, String userId) throws RequestException {
        List<CompInstanceAttributesBean> attributesBeanList = new ArrayList<>();
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
        List<AttributesViewBean> attributesViewBeanList = compInstanceDataService.getAttributeViewDataByComponentAndCommonVersion(mstComponentId, mstCommonVersionId, null);
        if (instance.getAttributes() != null) {
            String err;
            for (Attributes attribute : instance.getAttributes()) {
                if (attribute.getName().isEmpty()) {
                    err = "Attribute name is empty for component instance ' " + instance.getIdentifier() + " '";
                    log.error(err);
                    throw new RequestException(err);
                }
                //no need to check for empty value, as empty values will have an entry in db

                AttributesViewBean attributesViewBean = getValidAttribute(attributesViewBeanList, attribute.getName());

                if (attributesViewBean.getAttributeType().equalsIgnoreCase("password")
                        && !StringUtils.isEmpty(attribute.getValue())) {
                    String attributeValue = attribute.getValue();
                    try {
                        attributeValue = new AECSBouncyCastleUtil().decrypt(attributeValue);
                    } catch (Exception e) {
                        err = "Password is not properly encrypted.";
                        log.error(err + " Details: {}", e.getMessage(), e);
                        throw new RequestException(err);
                    }
                    try {
                        Security.removeProvider(Constants.BC_PROVIDER_NAME);
                        attributeValue = Commons.encrypt(attributeValue);
                        attribute.setValue(attributeValue);
                    } catch (AppsOneException e) {
                        log.error("Error while encrypting. Details: {}", e.getMessage(), e);
                        throw new RequestException("Error while encrypting");
                    }
                }

                CompInstanceAttributesBean instanceAttributesBean = CompInstanceAttributesBean.builder()
                        .attributeName(attributesViewBean.getAttributeName())
                        .attributeValue(attribute.getValue())
                        .mstComponentAttributeMappingId(attributesViewBean.getMstComponentAttributeMappingId())
                        .mstCommonAttributesId(attributesViewBean.getAttributeId())
                        .userDetailsId(userId)
                        .build();
                attributesBeanList.add(instanceAttributesBean);
            }
        }
        return attributesBeanList;
    }

    private static AttributesViewBean getValidAttribute(List<AttributesViewBean> attributesViewBeanList, String name) throws RequestException {
        if (attributesViewBeanList != null && !attributesViewBeanList.isEmpty()) {
            AttributesViewBean bean = attributesViewBeanList.stream().filter(a -> (a.getAttributeName().equals(name.trim()))).findAny().orElse(null);
            if (bean != null) {
                return bean;
            }
        }

        String err = "Attribute '" + name + "' " + Constants.DOES_NOT_EXIST;
        log.error(err);
        throw new RequestException(err);

    }

    private static List<ViewTypes> getEnvironmentTypeDetails() throws RequestException {
        MasterDataRepo masterDataRepo = new MasterDataRepo();
        List<ViewTypes> typeDetailsByTypeName = masterDataRepo.getTypeDetailsByTypeName(Constants.ENVIRONMENT_TYPE_NAME);

        if(typeDetailsByTypeName == null || typeDetailsByTypeName.isEmpty()) {
            log.error("Obtained empty results when queried for type details for type {} from Redis", Constants.ENVIRONMENT_TYPE_NAME);
            throw new RequestException(String.format("Obtained empty results when queried for type details for type %s from Redis", Constants.ENVIRONMENT_TYPE_NAME));
        }

        return typeDetailsByTypeName;
    }

    public static List<IdPojo> addInstancesToRedis(List<ComponentInstanceBean> beanList, String accountIdentifier) throws ControlCenterException {
        InstanceRepo instanceRepo = new InstanceRepo();
        BindInDataService bindInDataService = new BindInDataService();
        List<IdPojo> idPojosList = new ArrayList<>();

        List<com.heal.configuration.pojos.CompInstClusterDetails> instanceDetails = instanceRepo.getInstances(accountIdentifier);
        AccountRepo accountRepo = new AccountRepo();
        int accId = accountRepo.getAccountWithAccountIdentifier(accountIdentifier).getId();

        if (instanceDetails.isEmpty()) {
            instanceDetails = new ArrayList<>();
        }

        for (ComponentInstanceBean componentInstanceBean : beanList) {
            List<HostInstanceDetails> hostInstanceBean = bindInDataService.getHostInstanceId(hostComponentTypeId,
                    componentInstanceBean.getHostAddress(), accId, componentInstanceBean.getIsDR(),null);
            ClusterInstancePojo clusterInstanceMapping = bindInDataService.getClusterInstanceMapping(componentInstanceBean.getId(), null);
            List<String> clusterIdentifiers = Collections.singletonList(clusterInstanceMapping.getClusterIdentifier());

            CompInstClusterDetails clusterDetails = instanceDetails.parallelStream().filter(f -> f.getIdentifier().equalsIgnoreCase(clusterInstanceMapping.getClusterIdentifier())).findAny().orElse(null);
            if(clusterDetails == null) {
                log.info("Cluster details of this instance is not present in redis. Therefore, cluster details should be populated in redis");
                CompInstClusterDetails newClusterDetail = CompInstClusterDetails.builder()
                        .id(clusterInstanceMapping.getClusterId())
                        .name(clusterInstanceMapping.getClusterName())
                        .identifier(clusterInstanceMapping.getClusterIdentifier())
                        .status(clusterInstanceMapping.getStatus())
                        .createdTime(clusterInstanceMapping.getCreatedTime())
                        .updatedTime(clusterInstanceMapping.getUpdatedTime())
                        .lastModifiedBy(clusterInstanceMapping.getLastModifiedBy())
                        .componentId(clusterInstanceMapping.getComponentId())
                        .componentName(componentInstanceBean.getMstComponentName())
                        .componentTypeId(clusterInstanceMapping.getComponentTypeId())
                        .componentTypeName(componentInstanceBean.getMstComponentType())
                        .componentVersionId(clusterInstanceMapping.getComponentVersionId())
                        .componentVersionName(componentInstanceBean.getMstComponentVersion())
                        .commonVersionId(clusterInstanceMapping.getCommonVersionId())
                        .commonVersionName(componentInstanceBean.getMstCommonVersionName())
                        .supervisorId(clusterInstanceMapping.getSupervisorId())
                        .hostId(clusterInstanceMapping.getHostId())
                        .clusterIdentifiers(Collections.emptyList())
                        .hostName(hostInstanceBean.get(0).getHostInstanceName())
                        .hostAddress(clusterInstanceMapping.getHostAddress())
                        .isDR(clusterInstanceMapping.getIsDR())
                        .discovery(clusterInstanceMapping.getDiscovery())
                        .agentIds(componentInstanceBean.getAgentIdentifiers() != null ? componentInstanceBean.getAgentIdentifiers() : new ArrayList<>())
                        .parentInstanceId(clusterInstanceMapping.getParentInstanceId())
                        .accountId(clusterInstanceMapping.getAccountId())
                        .isCluster(clusterInstanceMapping.getIsCluster() == 1)
                        .build();
                instanceDetails.add(newClusterDetail);
                instanceRepo.updateInstances(accountIdentifier, instanceDetails);
                instanceRepo.updateInstanceByIdentifier(accountIdentifier, newClusterDetail);
                addCompInstKpiDetailsInRedis(accountIdentifier, newClusterDetail.getId(), newClusterDetail.getAccountId(), newClusterDetail.getIdentifier(), newClusterDetail.getComponentName());
            }

            CompInstClusterDetails newCompInstanceDetail = addCompInstanceDetailsInRedis(accountIdentifier, instanceDetails, componentInstanceBean, hostInstanceBean, clusterIdentifiers);

            addInstanceAttributesInRedis(accountIdentifier, instanceRepo, componentInstanceBean);

            addCompInstKpiDetailsInRedis(accountIdentifier, componentInstanceBean.getId(), componentInstanceBean.getAccountId(), componentInstanceBean.getIdentifier(), componentInstanceBean.getMstComponentName());

            if (!componentInstanceBean.getAgentIdsMap().isEmpty()) {
                addAgentToInstanceMapping(accountIdentifier, componentInstanceBean);
            }

            BasicInstanceBean basicInstanceBean = getBasicInstanceBean(componentInstanceBean, clusterInstanceMapping);
            Set<BasicEntity> serviceDetailsSet = new HashSet<>();
            for (String serviceIdentifier : componentInstanceBean.getServiceIdentifiers()) {
                BasicEntity basicEntity = addCompInstanceDetailsAtServiceLevel(accountIdentifier, basicInstanceBean, serviceIdentifier);
                serviceDetailsSet.add(basicEntity);
            }
            List<BasicEntity> serviceDetails = serviceDetailsSet.parallelStream().collect(Collectors.toList());
            instanceRepo.updateInstanceWiseServices(accountIdentifier, newCompInstanceDetail.getIdentifier(), serviceDetails);
            idPojosList.add(IdPojo.builder()
                    .id(componentInstanceBean.getId())
                    .name(componentInstanceBean.getName())
                    .identifier(componentInstanceBean.getIdentifier())
                    .build());
        }
        return idPojosList;
    }

    private static CompInstClusterDetails addCompInstanceDetailsInRedis(String accountIdentifier, List<CompInstClusterDetails> allInstances, ComponentInstanceBean componentInstanceBean, List<HostInstanceDetails> hostInstanceBean, List<String> clusterIdentifier) {
        InstanceRepo instanceRepo = new InstanceRepo();
        CompInstClusterDetails newCompInstClusterDetail = CompInstClusterDetails.builder()
                .id(componentInstanceBean.getId())
                .name(componentInstanceBean.getName())
                .identifier(componentInstanceBean.getIdentifier())
                .status(componentInstanceBean.getStatus())
                .createdTime(componentInstanceBean.getCreatedTime())
                .updatedTime(componentInstanceBean.getUpdatedTime())
                .lastModifiedBy(componentInstanceBean.getUserDetailsId())
                .componentId(componentInstanceBean.getMstComponentId())
                .componentName(componentInstanceBean.getMstComponentName())
                .componentTypeId(componentInstanceBean.getMstComponentTypeId())
                .componentTypeName(componentInstanceBean.getMstComponentType())
                .componentVersionId(componentInstanceBean.getMstComponentVersionId())
                .componentVersionName(componentInstanceBean.getMstComponentVersion())
                .commonVersionId(componentInstanceBean.getMstCommonVersionId())
                .commonVersionName(componentInstanceBean.getMstCommonVersionName())
                .supervisorId(componentInstanceBean.getSupervisorId())
                .hostId(componentInstanceBean.getHostId())
                .clusterIdentifiers(clusterIdentifier)
                .hostName(hostInstanceBean.get(0).getHostInstanceName())
                .hostAddress(componentInstanceBean.getHostAddress())
                .isDR(componentInstanceBean.getIsDR())
                .discovery(componentInstanceBean.getDiscovery())
                .agentIds(componentInstanceBean.getAgentIdentifiers() != null ? componentInstanceBean.getAgentIdentifiers() : new ArrayList<>())
                .parentInstanceId(componentInstanceBean.getParentId())
                .accountId(componentInstanceBean.getAccountId())
                .isCluster(componentInstanceBean.getIsCluster() == 1)
                .build();
        allInstances.add(newCompInstClusterDetail);
        instanceRepo.updateInstances(accountIdentifier, allInstances);
        instanceRepo.updateInstanceByIdentifier(accountIdentifier, newCompInstClusterDetail);
        return newCompInstClusterDetail;
    }

    private static void addInstanceAttributesInRedis(String accountIdentifier, InstanceRepo instanceRepo, ComponentInstanceBean componentInstanceBean) {
        List<InstanceAttributes> instanceAttributes = new CompInstanceDataService().getInstanceAttributeDetailsForCompInstanceId(componentInstanceBean.getAccountId(), componentInstanceBean.getId(), null);

        if (instanceAttributes.isEmpty()) {
            log.error("Could not find instance attributes details for component instance [{}]", componentInstanceBean.getId());
            return;
        }
        instanceRepo.updateAttributeDetails(accountIdentifier, componentInstanceBean.getIdentifier(), instanceAttributes);

    }

    private static void addCompInstKpiDetailsInRedis(String accountIdentifier, int compInstanceBeanId, int accountId, String compInstanceBeanIdentifier, String compInstanceMstComponentName) throws ControlCenterException {
        InstanceRepo instanceRepo = new InstanceRepo();
        ComponentRepo componentRepo = new ComponentRepo();
        BindInDataService bindInDataService = new BindInDataService();
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();

        List<CompInstKpiEntity> compInstKpiEntityList = new ArrayList<>();

        List<ComponentKpiEntity> componentKpiDetails = componentRepo.getComponentKpiDetails(accountIdentifier, compInstanceMstComponentName);
        List<GroupKpiAttributeMapping> groupKpiAttributeMapping = bindInDataService.getGroupKpiAttributeMapping(accountId, compInstanceBeanId, null);
        List<CompInstKpiMapping> compInstKpiMappingList = compInstanceDataService.getCompInstKpiMapping(compInstanceBeanId, null);


        for (CompInstKpiMapping compInstKpiMapping : compInstKpiMappingList) {
            ComponentKpiEntity componentKpiEntity = componentKpiDetails.parallelStream().filter(f -> f.getId() == compInstKpiMapping.getMstKpiId()).findAny().orElse(null);
            if(componentKpiEntity == null){
                continue;
            }
            Map<String, String> attributeValues = groupKpiAttributeMapping.parallelStream()
                    .filter(f -> f.getKpiId() == componentKpiEntity.getId())
                    .collect(Collectors.toMap(GroupKpiAttributeMapping::getAttributeValue, GroupKpiAttributeMapping::getAliasName));

            CompInstKpiEntity compInstKpiEntityObject = CompInstKpiEntity.builder()
                    .compInstKpiId(compInstKpiMapping.getCompInstKpiId())
                    .collectionInterval(compInstKpiMapping.getCollectionInterval())
                    .status(compInstKpiMapping.getStatus())
                    .defaultProducerId(compInstKpiMapping.getMstProducerId())
                    .producerKpiMappingId(compInstKpiMapping.getMstProducerKpiMappingId())
                    .isBaseMetric(componentKpiEntity.getIsBaseMetric())
                    .notification(compInstKpiMapping.getNotification())
                    .attributeValues(attributeValues)
                    .custom(componentKpiEntity.getCustom())
                    .discovery(componentKpiEntity.getDiscovery())
                    .name(componentKpiEntity.getName())
                    .id(componentKpiEntity.getId())
                    .type(componentKpiEntity.getType())
                    .isGroup(componentKpiEntity.getIsGroup())
                    .groupName(componentKpiEntity.getGroupName())
                    .groupStatus(componentKpiEntity.getGroupStatus())
                    .status(componentKpiEntity.getStatus())
                    .groupId(componentKpiEntity.getGroupId())
                    .groupIdentifier(componentKpiEntity.getGroupIdentifier())
                    .unit(componentKpiEntity.getUnit())
                    .aggOperation(componentKpiEntity.getAggOperation())
                    .rollupOperation(componentKpiEntity.getRollupOperation())
                    .clusterAggType(componentKpiEntity.getClusterAggType())
                    .instanceAggType(componentKpiEntity.getInstanceAggType())
                    .identifier(componentKpiEntity.getIdentifier())
                    .availableForAnalytics(componentKpiEntity.getAvailableForAnalytics())
                    .categoryDetails(componentKpiEntity.getCategoryDetails())
                    .valueType(componentKpiEntity.getValueType())
                    .dataType(componentKpiEntity.getDataType())
                    .isInfo(componentKpiEntity.getIsInfo())
                    .resetDeltaValue(componentKpiEntity.getResetDeltaValue())
                    .deltaPerSec(componentKpiEntity.getDeltaPerSec())
                    .cronExpression(componentKpiEntity.getCronExpression())
                    .description(componentKpiEntity.getDescription())
                    .natureId(componentKpiEntity.getNatureId())
                    .isComputed(componentKpiEntity.getIsComputed())
                    .computedKpiPojo(componentKpiEntity.getComputedKpiPojo())
                    .build();

            compInstKpiEntityList.add(compInstKpiEntityObject);
            instanceRepo.updateKpiDetailsForKpiId(accountIdentifier, compInstanceBeanIdentifier, compInstKpiEntityObject);
            instanceRepo.updateKpiDetailsForKpiIdentifier(accountIdentifier, compInstanceBeanIdentifier, compInstKpiEntityObject);
        }
        instanceRepo.updateKpiDetails(accountIdentifier, compInstanceBeanIdentifier, compInstKpiEntityList);
    }

    private static BasicInstanceBean getBasicInstanceBean(ComponentInstanceBean componentInstanceBean, ClusterInstancePojo clusterInstanceMapping) {
        return BasicInstanceBean.builder()
                .id(componentInstanceBean.getId())
                .name(componentInstanceBean.getName())
                .identifier(componentInstanceBean.getIdentifier())
                .status(componentInstanceBean.getStatus())
                .createdTime(componentInstanceBean.getCreatedTime())
                .updatedTime(componentInstanceBean.getUpdatedTime())
                .lastModifiedBy(componentInstanceBean.getUserDetailsId())
                .componentId(componentInstanceBean.getMstComponentId())
                .componentTypeId(componentInstanceBean.getMstComponentTypeId())
                .componentVersionId(componentInstanceBean.getMstComponentVersionId())
                .commonVersionId(componentInstanceBean.getMstCommonVersionId())
                .clusterId(clusterInstanceMapping.getClusterId())
                .clusterIdentifier(clusterInstanceMapping.getClusterIdentifier())
                .accountId(componentInstanceBean.getAccountId())
                .build();
    }


    private static BasicEntity addCompInstanceDetailsAtServiceLevel(String accountIdentifier, BasicInstanceBean basicInstanceBean, String serviceIdentifier) {
        ServiceRepo serviceRepo = new ServiceRepo();
        Service serviceConfigurationDetail = serviceRepo.getServiceConfigurationByIdentifier(accountIdentifier, serviceIdentifier);

        if(serviceConfigurationDetail == null){
            log.error("Could not find service detail for given service identifier [{}] and account identifier [{}] ", serviceIdentifier, accountIdentifier);
            return null;
        }
        basicInstanceBean.setConcernedConfigId(serviceConfigurationDetail.getId());
        basicInstanceBean.setConcernedConfigIdentifier(serviceIdentifier);
        List<BasicInstanceBean> serviceInstances = serviceRepo.getServiceInstances(accountIdentifier, serviceIdentifier);

        if(serviceInstances.isEmpty()){
            serviceInstances = new ArrayList<>();
        }

        serviceInstances.add(basicInstanceBean);
        serviceRepo.updateServiceInstances(accountIdentifier, serviceIdentifier, serviceInstances);

        return BasicEntity.builder()
                .id(serviceConfigurationDetail.getId())
                .status(serviceConfigurationDetail.getStatus())
                .createdTime(serviceConfigurationDetail.getCreatedTime())
                .updatedTime(serviceConfigurationDetail.getUpdatedTime())
                .name(serviceConfigurationDetail.getName())
                .identifier(serviceConfigurationDetail.getIdentifier())
                .lastModifiedBy(serviceConfigurationDetail.getLastModifiedBy())
                .build();
    }

    private static void addAgentToInstanceMapping(String accountIdentifier, ComponentInstanceBean componentInstanceBean){
        AgentRepo agentRepo = new AgentRepo();

        Map<Integer, String> agentIdsMap = componentInstanceBean.getAgentIdsMap();
        for (int agentId : agentIdsMap.keySet()) {
            List<BasicEntity> agentToInstanceMapping = AgentDataService.getAgentToInstanceMapping(componentInstanceBean.getId(), componentInstanceBean.getAccountId(), agentId, null);
            List<BasicEntity> existingAgentInstanceMappingDetailsFromRedis = agentRepo.getAgentInstanceMappingDetails(accountIdentifier, componentInstanceBean.getAgentIdsMap().get(agentId));

            if(existingAgentInstanceMappingDetailsFromRedis.isEmpty()){
                existingAgentInstanceMappingDetailsFromRedis = new ArrayList<>();
            }

            for (BasicEntity basicEntity : agentToInstanceMapping) {
                existingAgentInstanceMappingDetailsFromRedis.add(BasicEntity.builder()
                        .id(basicEntity.getId())
                        .status(basicEntity.getStatus())
                        .createdTime(basicEntity.getCreatedTime())
                        .updatedTime(basicEntity.getUpdatedTime())
                        .name(basicEntity.getName())
                        .identifier(basicEntity.getIdentifier())
                        .lastModifiedBy(basicEntity.getLastModifiedBy())
                        .build());
            }
            agentRepo.updateAgentInstanceMappingDetails(accountIdentifier, componentInstanceBean.getAgentIdsMap().get(agentId),existingAgentInstanceMappingDetailsFromRedis);
        }
    }
}
