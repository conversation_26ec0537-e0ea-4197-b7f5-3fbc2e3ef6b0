package com.appnomic.appsone.controlcenter.util.connector;

import com.appnomic.appsone.controlcenter.dao.mysql.connectors.ConnectorDataService;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;

import java.sql.Timestamp;
import java.text.ParseException;

@Slf4j
public class UploadErrorUtil {

    public static void updateError(Exception e, int connectorId, int accountId) throws ParseException {
        ConnectorDataService connectorDataService = new ConnectorDataService();
        Timestamp errorDate = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
        connectorDataService.updateUploadError("Upload Error: " + ExceptionUtils.getStackTrace(e), errorDate, connectorId, accountId);
    }
}
