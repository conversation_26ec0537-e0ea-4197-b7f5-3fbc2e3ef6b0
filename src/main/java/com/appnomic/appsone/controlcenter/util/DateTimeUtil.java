package com.appnomic.appsone.controlcenter.util;

import com.appnomic.appsone.controlcenter.common.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.Format;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.TimeZone;

/**
 * <AUTHOR> : 17/1/19
 */
public class DateTimeUtil {

    private static final Logger logger = LoggerFactory.getLogger(DateTimeUtil.class);

    /**
     * Private constructor to prevent Instance creation
     */
    private DateTimeUtil() {
    }

    private static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static String getTimeInGMT(long time) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DATE_TIME_PATTERN);
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        return simpleDateFormat.format(time);
    }

    public static String getTimeInGMT() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        return simpleDateFormat.format(System.currentTimeMillis());
    }

    public static Date getDateInGMT(long time) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DATE_TIME_PATTERN);
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        String dateTime = simpleDateFormat.format(time);
        return simpleDateFormat.parse(dateTime);
    }

    public static Timestamp getTimestampInGMT(String time) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DATE_TIME_PATTERN);
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        Date date = simpleDateFormat.parse(time.trim());
        return new Timestamp(date.getTime());
    }

    public static Timestamp getCurrentTimestampInGMT() {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(Constants.DATE_TIME);
            simpleDateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
            SimpleDateFormat localDateFormat = new SimpleDateFormat(Constants.DATE_TIME);
            return new Timestamp(localDateFormat.parse( simpleDateFormat.format(new Date())).getTime());
        } catch (ParseException e) {
            logger.error("Error in getting current time stamp in GMT");
        }
        return null;
    }

    public static Long getGMTToEpochTime(String time) {
        DateFormat simpleDateFormat;
        try {
            simpleDateFormat = new SimpleDateFormat(Constants.DATE_TIME);
            simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
            Date date = simpleDateFormat.parse(time.trim());
            return date.getTime();
        }catch (Exception e)    {
            return 0L;
        }
    }

    public static Long getEpochTime(String time) {
        DateFormat simpleDateFormat;
        try {
            simpleDateFormat = new SimpleDateFormat(Constants.DATE_TIME);
            Date date = simpleDateFormat.parse(time.trim());
            return date.getTime();
        } catch (Exception e)    {
            return 0L;
        }
    }

    public static String date2GMTConversion(Date date, String format) {
        DateFormat gmtFormat = new SimpleDateFormat(format);
        TimeZone gmtTime = TimeZone.getTimeZone("GMT");
        gmtFormat.setTimeZone(gmtTime);
        return gmtFormat.format(date);
    }

    public static String getDateFromEpoch(long time){
        Date date = new Date(time);
        Format format = new SimpleDateFormat(Constants.INDEX_POSTFIX_DATE_FORMAT);
        return format.format(date);
    }

    public static long getLastTwentyFourHoursTimeInEpoch() {
        LocalDateTime currentTime = LocalDateTime.now();
        LocalDateTime twentyFourHoursAgo = currentTime.minusHours(24);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(Constants.DATE_TIME);
        String twentyFourHoursAgoFormatted = twentyFourHoursAgo.format(formatter);

        return getEpochTime(twentyFourHoursAgoFormatted);
    }

    public static long getCurrTimeInEpoch() {
        LocalDateTime currentTime = LocalDateTime.now();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(Constants.DATE_TIME);
        String currTime = currentTime.format(formatter);

        return getEpochTime(currTime);
    }

    public static int getHourTime(String time) {
        DateFormat simpleDateFormat;
        try {
            simpleDateFormat = new SimpleDateFormat(Constants.DATE_TIME);
            Date date = simpleDateFormat.parse(time.trim());
            return date.getHours();
        } catch (Exception e) {
            return 0;
        }
    }
    public static Date getDateFromString(String date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return simpleDateFormat.parse(date);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }
}
