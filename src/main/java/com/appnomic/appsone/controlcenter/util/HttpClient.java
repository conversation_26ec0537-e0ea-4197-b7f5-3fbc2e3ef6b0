package com.appnomic.appsone.controlcenter.util;

import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.google.gson.Gson;
import com.heal.configuration.pojos.datareceiver.RawKpiData;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;

import java.util.List;

@Slf4j
public class HttpClient {
    private static final String DATA_RECEIVER_KPI_ENDPOINT = ConfProperties.getString(Constants.DATA_RECEIVER_KPI_ENDPOINT, Constants.DATA_RECEIVER_KPI_ENDPOINT_DEFAULT);
    private static final int MAX_CONNECTIONS = ConfProperties.getInt(Constants.HTTP_CLIENT_MAX_CONNECTIONS, Constants.HTTP_CLIENT_MAX_CONNECTIONS_DEFAULT);
    private static final int MAX_CONNECTIONS_PER_ROUTE = ConfProperties.getInt(Constants.HTTP_CLIENT_MAX_CONNECTIONS_PER_ROUTE, Constants.HTTP_CLIENT_MAX_CONNECTIONS_PER_ROUTE_DEFAULT);
    private static final int CONNECTION_TIME_OUT = ConfProperties.getInt(Constants.HTTP_CONNECTION_TIMEOUT_PROPERTY_NAME, Constants.HTTP_CONNECTION_TIMEOUT_DEFAULT_VALUE);
    private static final int SOCKET_TIME_OUT = ConfProperties.getInt(Constants.HTTP_SOCKET_TIMEOUT_PROPERTY_NAME, Constants.HTTP_SOCKET_TIMEOUT_DEFAULT_VALUE);
    private static final PoolingHttpClientConnectionManager CONNECTION_MANAGER = createConnectionManager();
    private static final CloseableHttpClient client = HttpClients.custom().setConnectionManager(CONNECTION_MANAGER).build();

    private static PoolingHttpClientConnectionManager createConnectionManager() {
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(MAX_CONNECTIONS);
        connectionManager.setDefaultMaxPerRoute(MAX_CONNECTIONS_PER_ROUTE);
        log.debug("Connection manager, max connections:{}, per route:{}", MAX_CONNECTIONS, MAX_CONNECTIONS_PER_ROUTE);
        return connectionManager;
    }

    public static boolean sendHostAvailabilityToDR(List<RawKpiData> rawKpiDataList) {
        try {
            HttpPost httpPost = new HttpPost(DATA_RECEIVER_KPI_ENDPOINT);
            httpPost.setEntity(new StringEntity(new Gson().toJson(rawKpiDataList)));
            httpPost.setHeader("Content-type", "application/json");

            RequestConfig.Builder requestConfig = RequestConfig.custom();
            requestConfig.setConnectionRequestTimeout(CONNECTION_TIME_OUT);
            requestConfig.setSocketTimeout(SOCKET_TIME_OUT);
            httpPost.setConfig(requestConfig.build());

            try(CloseableHttpResponse response = client.execute(httpPost)) {
                int returnStatusCode = response.getStatusLine().getStatusCode();
                if (returnStatusCode != 200) {
                    log.error("Failed to host availability data to end point:{}, response code:{}, data points:{} ",
                            DATA_RECEIVER_KPI_ENDPOINT, returnStatusCode, rawKpiDataList.size());
                    CCCache.INSTANCE.updateCCErrors(1);
                    return false;
                } else {
                    log.info("Send the host availability data to end point:{}, response code:{}, data points:{} ",
                            DATA_RECEIVER_KPI_ENDPOINT, returnStatusCode, rawKpiDataList.size());
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("Error occurred while sending host availability data to end point:{}, number of data points:{}.",
                    rawKpiDataList.size(), DATA_RECEIVER_KPI_ENDPOINT, e);
            CCCache.INSTANCE.updateCCErrors(1);
            return false;
        }

    }
}
