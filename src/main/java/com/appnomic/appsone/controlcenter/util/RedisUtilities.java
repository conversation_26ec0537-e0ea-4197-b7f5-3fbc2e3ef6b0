package com.appnomic.appsone.controlcenter.util;

import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.manager.RedisConnectionManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.lettuce.core.cluster.api.sync.RedisAdvancedClusterCommands;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class RedisUtilities {
    private static final ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    public static void updateKey(String key, String hashKey, Object data) {
        try {
            if (data == null) {
                log.error("Value received to update redis Key:{}, Hash key:{} is null.", key, hashKey);
                return;
            }

            log.trace("Redis UPDATE Query details:- Key:{}, Hash key:{}, Value: {}", key, hashKey, data);

            String cacheData = getKey(key, hashKey);
            if (cacheData != null && cacheData.hashCode() == objectMapper.writeValueAsString(data).hashCode()) {
                log.debug("No change in value for the redis Key:{}, Hash key:{}, existing value:{}, new value:{}",
                        key, hashKey, cacheData, data);
                return;
            }

            RedisAdvancedClusterCommands<String, String> redisClient = RedisConnectionManager.INSTANCE.getRedisClient();
            if (redisClient == null) {
                log.error("Could not connect to Redis cluster.");
                return;
            }

            String stringData;
            /*If the data object is already in string form, we leave it that way; if not, we convert it to string before storing it in redis.*/
            if(data instanceof String) {
                stringData = (String) data;
            }
            else {
                stringData = objectMapper.writeValueAsString(data);
            }

            redisClient.hset(key, hashKey, stringData);
        } catch (Exception ex) {
            log.error("Exception encountered while updating Redis Key:{}, Hash key:{} with Value:{}. ", key, hashKey, data, ex);
            CCCache.INSTANCE.updateCCErrors(1);
        }
    }

    public static String getKey(String key, String hashKey) {
        try {
            log.trace("Redis GET Query details:- Key:{}, Hash key:{}", key, hashKey);

            RedisAdvancedClusterCommands<String, String> redisClient = RedisConnectionManager.INSTANCE.getRedisClient();
            if (redisClient == null) {
                log.debug("Could not connect to Redis cluster.");
                return null;
            }

            String data = redisClient.hget(key, hashKey);
            if (data == null || data.isEmpty()) {
                log.debug("Failed to get details for Redis Key:{}, Hash key:{}", key, hashKey);
                return null;
            } else log.trace("Redis GET Query successful. Key:{}, Hash key:{}, Value: {}", key, hashKey, data);
            return data;
        } catch (Exception ex) {
            log.error("Exception encountered while getting value of Redis Key:{}, Hash key:{}. ", key, hashKey, ex);
            CCCache.INSTANCE.updateCCErrors(1);
            return null;
        }
    }

    public static boolean deleteKey(String key, String hashKey) {
        try {
            log.trace("Redis DELETE Query details:- Key:{}, Hash key:{}", key, hashKey);

            RedisAdvancedClusterCommands<String, String> redisClient = RedisConnectionManager.INSTANCE.getRedisClient();
            if (redisClient == null) {
                log.debug("Could not connect to Redis cluster.");
                return false;
            }

            Long keyDeleteCount = redisClient.hdel(key, hashKey);
            if (keyDeleteCount == null || keyDeleteCount <= 0) {
                log.debug("Failed to delete Redis Key:{}, Hash key:{}, keyDeleteCount: {}", key, hashKey, keyDeleteCount);
                return false;
            } else {
                log.trace("Redis DELETE Query successful. Key:{}, Hash key:{}, keyDeleteCount: {}", key, hashKey, keyDeleteCount);
                return true;
            }
        } catch (Exception ex) {
            log.error("Exception encountered while deleting Redis Key:{}, Hash key:{}. ", key, hashKey, ex);
            CCCache.INSTANCE.updateCCErrors(1);
            return false;
        }
    }

    public static int deleteMultipleKeys(String pattern) {
        try {
            log.trace("Redis DELETE Query details:- Pattern:{}", pattern);

            RedisAdvancedClusterCommands<String, String> redisClient = RedisConnectionManager.INSTANCE.getRedisClient();
            if (redisClient == null) {
                log.debug("Could not connect to Redis cluster.");
                return 0;
            }

            List<String> keys = redisClient.hkeys(pattern);
            for (String key : keys) {
                Long keyDeleteCount = redisClient.hdel(key);
                if (keyDeleteCount == null || keyDeleteCount <= 0) {
                    log.debug("Failed to delete Redis Key:{}", key);
                } else {
                    log.trace("Redis DELETE Query successful. Key:{}", key);
                }
            }

            return keys.size();
        } catch (Exception e) {
            log.error("Exception encountered while getting and deleting Redis key pattern '{}'.", pattern, e);
            CCCache.INSTANCE.updateCCErrors(1);
            return 0;
        }
    }

    public static boolean setExpiry(String key, long time) {
        try {
            log.trace("Set Expiry Query details:- Pattern:{}, time:{}", key, time);

            RedisAdvancedClusterCommands<String, String> redisClient = RedisConnectionManager.INSTANCE.getRedisClient();

            if (redisClient == null) {
                log.debug("Could not connect to Redis cluster.");
                return false;
            }

            boolean retVal = redisClient.expire(key, time);
            log.debug("Expiration:{} set for key:{}, time:{}", retVal, key, time);
            return retVal;

        } catch (Exception e) {
            log.error("Exception encountered while getting and deleting Redis key pattern:{}, time:{}.", key, time, e);
            CCCache.INSTANCE.updateCCErrors(1);
            return false;
        }
    }

    public static boolean updateWithExpiry(String key, Object data, long time) {
        try {
            log.trace("Update key with  Expiry Query details:- Pattern:{},time:{}", key, time);

            RedisAdvancedClusterCommands<String, String> redisClient = RedisConnectionManager.INSTANCE.getRedisClient();

            if (redisClient == null) {
                log.debug("Could not connect to Redis cluster.");
                return false;
            }

            String setex = redisClient.setex(key, time, objectMapper.writeValueAsString(data));
            log.debug("Updated with Expiration key {}, set {} seconds result: {}", key, time, setex);
            return true;

        } catch (Exception e) {
            log.error("Exception encountered while updating with  expiry for Redis key pattern:{}, time:{}.", key, time, e);
            CCCache.INSTANCE.updateCCErrors(1);
            return false;
        }
    }
}
