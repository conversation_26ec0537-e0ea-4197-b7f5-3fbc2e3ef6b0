package com.appnomic.appsone.controlcenter.util;

import com.appnomic.appsone.common.beans.MaintenanceScheduledBean;
import com.appnomic.appsone.common.beans.MaintenanceWindowBean;
import com.appnomic.appsone.common.beans.RecurringDataBean;
import com.appnomic.appsone.common.beans.RecurringDetailsBean;
import com.appnomic.appsone.common.exception.AppsOneException;
import com.appnomic.appsone.common.util.MaintenanceUtils;
import com.appnomic.appsone.controlcenter.beans.MaintenanceDetailsBean;
import com.appnomic.appsone.controlcenter.beans.MaintenanceRecurringDetailsBean;
import com.appnomic.appsone.controlcenter.beans.RecurringWindowBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.MaintenanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CompInstanceMaintenanceMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.MaintenanceDetails;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.RecurringDetails;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ServiceMaintenanceMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.MaintenanceWindowDao;
import com.appnomic.appsone.controlcenter.dao.opensearch.MaintenanceWindowServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.Handle;

import java.sql.Timestamp;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class MaintenanceWindowUtility {

    private static final ViewTypes scheduledType = MasterCache
            .getMstTypeForSubTypeName(Constants.MST_TYPE_MAINTENANCE, Constants.MST_SUB_TYPE_SCHEDULED);
    private static final ViewTypes recurringType = MasterCache
            .getMstTypeForSubTypeName(Constants.MST_TYPE_MAINTENANCE, Constants.MST_SUB_TYPE_RECURRING);
    private static final ViewTypes recurringDailyType = MasterCache
            .getMstTypeForSubTypeName(Constants.MST_TYPE_RECURRING, Constants.MST_SUB_TYPE_RECURRING_DAILY);
    private static final ViewTypes recurringWeeklyType = MasterCache
            .getMstTypeForSubTypeName(Constants.MST_TYPE_RECURRING, Constants.MST_SUB_TYPE_RECURRING_WEEKLY);
    private static final ViewTypes recurringMonthlyType = MasterCache
            .getMstTypeForSubTypeName(Constants.MST_TYPE_RECURRING, Constants.MST_SUB_TYPE_RECURRING_MONTHLY);
    private static final Map<String, Integer> weekMap = ImmutableMap.<String, Integer>builder()
            .put("sunday", 1).put("monday", 2).put("tuesday", 3).put("wednesday", 4).put("thursday", 5).put("friday", 6).put("saturday", 7).build();

    private static final MaintenanceDataService MAINTENANCE_DATA_SERVICE = new MaintenanceDataService();

    public static MaintenanceRecurringDetailsBean getConfiguredMaintenanceDetails(List<Integer> maintenanceList, Timestamp startTime, Timestamp endTime) {
        //for querying from db, remove time. use only date
        Calendar tempStartCal = Calendar.getInstance(TimeZone.getTimeZone("Asia/Calcutta"));
        tempStartCal.setTimeInMillis(startTime.getTime());
        tempStartCal.set(Calendar.HOUR_OF_DAY, 0);
        tempStartCal.set(Calendar.MINUTE, 0);
        tempStartCal.set(Calendar.SECOND, 0);
        tempStartCal.set(Calendar.MILLISECOND, 0);

        List<MaintenanceDetails> maintenanceDetailsList = new BindInDataService().getMaintenanceWindowDetailsList(maintenanceList, new Timestamp(tempStartCal.getTimeInMillis()), endTime);
        if (maintenanceDetailsList == null) {
            log.warn("getMaintenanceScheduled: maintenanceDetailsList is null");
            return null;
        }

        List<MaintenanceWindowBean> maintenanceWindowBeanList = maintenanceDetailsList
                .parallelStream()
                .map(
                        i -> {
                            long startDate = DateTimeUtil.getEpochTime(i.getStartTimeString());
                            long endDate = DateTimeUtil.getEpochTime(i.getEndTimeString());
                            if (startDate == 0L || endDate == 0L) {
                                log.error("Maintenance beans are skipped because of invalid dates. Reason: startDate or endDate is 0 in maintenance window {}", i);
                                return null;
                            }
                            if (i.getTypeId() == scheduledType.getSubTypeId()) {
                                return new MaintenanceWindowBean(i.getId(), Constants.MST_SUB_TYPE_SCHEDULED, i.getName(), i.getStartTime().getTime(), i.getEndTime().getTime());
                            } else if (i.getTypeId() == recurringType.getSubTypeId()) {
                                return new MaintenanceWindowBean(i.getId(), Constants.MST_SUB_TYPE_RECURRING, i.getName(), i.getStartTime().getTime(), i.getEndTime().getTime());
                            } else {
                                return new MaintenanceWindowBean(i.getId(), String.valueOf(i.getTypeId()), i.getName(), startDate, endDate);
                            }
                        })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<RecurringDetails> recurringDetailsList = new BindInDataService().getRecurringDetailsList(maintenanceList);
        Map<Integer, RecurringDetailsBean> recurringDetailsMap = new HashMap<>();
        if (recurringDetailsList != null && !recurringDetailsList.isEmpty()) {
            recurringDetailsMap = recurringDetailsList
                    .parallelStream()
                    .collect(Collectors.toMap(RecurringDetails::getMaintenanceId,
                            r -> {
                                if (r.getRecurringTypeId() == recurringDailyType.getSubTypeId()) {
                                    return new RecurringDetailsBean(r.getMaintenanceId(), Constants.MST_SUB_TYPE_RECURRING_DAILY, r.getStartHrMin(), r.getEndHrMin(), r.getRecurringData());
                                } else if (r.getRecurringTypeId() == recurringWeeklyType.getSubTypeId()) {
                                    return new RecurringDetailsBean(r.getMaintenanceId(), Constants.MST_SUB_TYPE_RECURRING_WEEKLY, r.getStartHrMin(), r.getEndHrMin(), r.getRecurringData());
                                } else if (r.getRecurringTypeId() == recurringMonthlyType.getSubTypeId()) {
                                    return new RecurringDetailsBean(r.getMaintenanceId(), Constants.MST_SUB_TYPE_RECURRING_MONTHLY, r.getStartHrMin(), r.getEndHrMin(), r.getRecurringData());
                                } else {
                                    return new RecurringDetailsBean(r.getMaintenanceId(), String.valueOf(r.getRecurringTypeId()), r.getStartHrMin(), r.getEndHrMin(), r.getRecurringData());
                                }
                            }));
        }
        return new MaintenanceRecurringDetailsBean(maintenanceWindowBeanList, recurringDetailsMap);
    }

    public static MaintenanceDetailsBean getOngoingMaintenance(String accountIdentifier, String serviceIdentifier, MaintenanceScheduledBean maintenanceWindowBean) throws ControlCenterException {
        if (maintenanceWindowBean.getOnGoing() != null) {
            if (maintenanceWindowBean.getOnGoing().getType().equals(Constants.MST_SUB_TYPE_RECURRING_DAILY) ||
                    maintenanceWindowBean.getOnGoing().getType().equals(Constants.MST_SUB_TYPE_RECURRING_MONTHLY) ||
                    maintenanceWindowBean.getOnGoing().getType().equals(Constants.MST_SUB_TYPE_RECURRING_WEEKLY)) {

                if (!new MaintenanceWindowServiceRepo().getMaintenanceCompleteStatus(accountIdentifier, serviceIdentifier, maintenanceWindowBean.getOnGoing().getStartTime()))
                    return MaintenanceDetailsBean.builder()
                            .id(maintenanceWindowBean.getOnGoing().getId())
                            .name(maintenanceWindowBean.getOnGoing().getName())
                            .startTime(new Timestamp(maintenanceWindowBean.getOnGoing().getStartTime()))
                            .endTime(new Timestamp(maintenanceWindowBean.getOnGoing().getEndTime()))
                            .type(Constants.MST_SUB_TYPE_RECURRING)
                            .ongoing(true)
                            .build();
            } else {
                return MaintenanceDetailsBean.builder()
                        .id(maintenanceWindowBean.getOnGoing().getId())
                        .name(maintenanceWindowBean.getOnGoing().getName())
                        .startTime(new Timestamp(maintenanceWindowBean.getOnGoing().getStartTime()))
                        .endTime(new Timestamp(maintenanceWindowBean.getOnGoing().getEndTime()))
                        .type(Constants.MST_SUB_TYPE_SCHEDULED)
                        .ongoing(true)
                        .build();
            }

            log.debug("Maintenance Details name : {}, start time {} end time {} ", maintenanceWindowBean.getOnGoing().getName(),
                    new Timestamp(maintenanceWindowBean.getOnGoing().getStartTime()), new Timestamp(maintenanceWindowBean.getOnGoing().getEndTime()));
        }

        return null;
    }

    public static List<MaintenanceDetailsBean> getMaintenanceDetails(String accountIdentifier, String serviceIdentifier, int serviceId) throws ParseException, AppsOneException, ControlCenterException {
        Gson gson = new Gson();
        List<MaintenanceDetailsBean> maintenanceDetailsBeanBeanList = new ArrayList<>();
        Calendar startCal = Calendar.getInstance(TimeZone.getTimeZone("Asia/Calcutta"));
        Timestamp date = new Timestamp(startCal.getTime().getTime());

        List<ServiceMaintenanceMapping> maintenanceMappingList = MAINTENANCE_DATA_SERVICE.getMaintenanceWindowsByServiceId(serviceId);
        if (maintenanceMappingList == null || maintenanceMappingList.isEmpty()) {
            return maintenanceDetailsBeanBeanList;
        }

        List<Integer> maintenanceList = maintenanceMappingList
                .parallelStream()
                .map(ServiceMaintenanceMapping::getMaintenanceId)
                .collect(Collectors.toList());

        log.debug("Getting maintenance scheduled for service id:{}, date:{}", serviceId, date);

        MaintenanceRecurringDetailsBean maintenanceDetailsLists = MaintenanceWindowUtility.getConfiguredMaintenanceDetails(maintenanceList, date, date);
        if (maintenanceDetailsLists == null) {
            return maintenanceDetailsBeanBeanList;
        }

        List<MaintenanceWindowBean> maintenanceDetails = maintenanceDetailsLists.getMaintenanceWindowBeanList();
        Map<Integer, RecurringDetailsBean> recurringDetails = maintenanceDetailsLists.getRecurringDetailsMap();
        MaintenanceScheduledBean maintenanceWindowBean = MaintenanceUtils.getMaintenanceScheduled(maintenanceDetails, recurringDetails, date, date);

        maintenanceDetailsBeanBeanList.add(MaintenanceWindowUtility.getOngoingMaintenance(accountIdentifier, serviceIdentifier, maintenanceWindowBean));

        if (!maintenanceDetails.isEmpty()) {
            for (MaintenanceWindowBean maintenanceWindow : maintenanceDetails) {
                if (maintenanceWindow.getType().equals(Constants.MST_SUB_TYPE_SCHEDULED)) {
                    if (date.before(new Timestamp(maintenanceWindow.getStartTime()))) {
                        maintenanceDetailsBeanBeanList.add(MaintenanceDetailsBean.builder()
                                .id(maintenanceWindow.getId())
                                .type(maintenanceWindow.getType())
                                .endTime(new Timestamp(maintenanceWindow.getEndTime()))
                                .startTime(new Timestamp(maintenanceWindow.getStartTime()))
                                .name(maintenanceWindow.getName())
                                .build());
                    }
                } else {
                    RecurringDetailsBean recurringDetailsBean = recurringDetails.get(maintenanceWindow.getId());
                    RecurringDataBean recurringData = gson.fromJson(recurringDetailsBean.getRecurringData(), RecurringDataBean.class);

                    RecurringWindowBean recurringWindowBean = RecurringWindowBean.builder()
                            .recurringType(recurringDetailsBean.getRecurringType())
                            .startHrMin(recurringDetailsBean.getStartHrMin())
                            .endHrMin(recurringDetailsBean.getEndHrMin())
                            .day(recurringData.getDay())
                            .week(recurringData.getWeek())
                            .month(recurringData.getMonth())
                            .weekNames(recurringData.getWeeknames())
                            .build();

                    maintenanceDetailsBeanBeanList.add(MaintenanceDetailsBean.builder()
                            .name(maintenanceWindow.getName())
                            .startTime(new Timestamp(maintenanceWindow.getStartTime()))
                            .endTime(new Timestamp(maintenanceWindow.getEndTime()))
                            .id(maintenanceWindow.getId())
                            .type(maintenanceWindow.getType())
                            .recurring(recurringWindowBean)
                            .build());
                }
            }
        }

        return maintenanceDetailsBeanBeanList.parallelStream().filter(Objects::nonNull).collect(Collectors.toList());
    }


    public static MaintenanceWindowBean getMaintenanceConflictByCompInstanceId(int compInstanceId, int maintenanceId, Timestamp startTime, Timestamp endTime) throws AppsOneException {
        List<Integer> maintenanceList = MAINTENANCE_DATA_SERVICE.getMaintenanceWindowsByCompInstanceId(compInstanceId)
                .parallelStream()
                .map(CompInstanceMaintenanceMapping::getMaintenanceId)
                .collect(Collectors.toList())
                .stream().filter(e -> !e.equals(maintenanceId))
                .collect(Collectors.toList());

        if (maintenanceList.isEmpty()) {
            return null;
        }

        log.debug("Checking Maintenance Conflict for comp instance id : {} startTime : {} endTime : {}", compInstanceId, startTime, endTime);
        MaintenanceRecurringDetailsBean maintenanceDetails = MaintenanceWindowUtility.getConfiguredMaintenanceDetails(maintenanceList, startTime, endTime);
        if (maintenanceDetails == null)
            return null;

        return MaintenanceUtils.getMaintenanceConflict(maintenanceDetails.getMaintenanceWindowBeanList(),
                maintenanceDetails.getRecurringDetailsMap(), startTime, endTime);
    }

    public static int addScheduledMaintenanceWindowDetails(List<MaintenanceDetails> maintenanceDetailsList, Handle handle) throws ControlCenterException {
        MaintenanceWindowDao maintenanceWindowDao = handle.attach(MaintenanceWindowDao.class);

        int maintenanceWindowDetailsId = maintenanceWindowDao.addMaintenanceWindowDetails(maintenanceDetailsList.get(0));
        if (maintenanceWindowDetailsId == -1) {
            String err = "Unable to add maintenance details(startTime = "
                    + maintenanceDetailsList.get(0).getStartTime() + " endTime = " + maintenanceDetailsList.get(0).getEndTime()
                    + " maintenanceType = " + maintenanceDetailsList.get(0).getMaintenanceType() + " ) into Maintenance Window Details table";
            throw new ControlCenterException(err);
        }

        for (MaintenanceDetails maintenanceDetails : maintenanceDetailsList) {
            int maintenanceServiceWindowDetails = maintenanceWindowDao.addInstanceMaintenanceWindowDetails(maintenanceDetails, maintenanceWindowDetailsId);
            if (maintenanceServiceWindowDetails == -1) {
                String err = "Unable to add maintenance details(startTime = "
                        + maintenanceDetails.getStartTime() + " endTime = " + maintenanceDetails.getEndTime()
                        + " maintenanceType = " + maintenanceDetails.getMaintenanceType() + " ) into Maintenance Window Service Details table";
                throw new ControlCenterException(err);
            }
            new MaintenanceWindowServiceRepo().insertInstanceMaintenanceOS(maintenanceDetails.getAccountIdentifier(), maintenanceDetails.getInstanceIdentifier(),
                    maintenanceDetails.getStartTime(), maintenanceDetails.getEndTime());
        }
        return maintenanceWindowDetailsId;
    }

    public static MaintenanceWindowBean getMaintenanceConflictByServiceId(int maintenanceId, int serviceId, Timestamp startTime, Timestamp endTime) throws AppsOneException {
        log.debug("startTime {} and endTime {}", startTime, endTime);

        if (startTime.compareTo(endTime) >= 0) {
            log.error("Invalid start and end time. Reason: startTime is greater than endTime.");
            throw new AppsOneException("Invalid start and end time. Reason: startTime is greater than endTime.");
        }

        List<Integer> maintenanceList = MAINTENANCE_DATA_SERVICE.getMaintenanceWindowsByServiceId(serviceId)
                .parallelStream()
                .map(ServiceMaintenanceMapping::getMaintenanceId)
                .collect(Collectors.toList())
                .stream().filter(e -> !e.equals(maintenanceId))
                .collect(Collectors.toList());

        if (maintenanceList.isEmpty()) {
            log.debug("There are no maintenance window for the provided service ID [{}]", serviceId);
            return null;
        }

        log.debug("Checking maintenance conflict for service id:{} with Start Time : {} & End Time : {}", serviceId, startTime, endTime);

        MaintenanceRecurringDetailsBean maintenanceDetails = MaintenanceWindowUtility.getConfiguredMaintenanceDetails(maintenanceList, startTime, endTime);
        if (maintenanceDetails == null) {
            return null;
        }

        return MaintenanceUtils.getMaintenanceConflict(maintenanceDetails.getMaintenanceWindowBeanList(),
                maintenanceDetails.getRecurringDetailsMap(), startTime, endTime);
    }

    public static List<MaintenanceWindowBean> buildRecurringCycles(MaintenanceWindowPojo maintenanceWindowPojo) {
        List<MaintenanceWindowBean> maintenanceWindowBeanList = new ArrayList<>();
        ViewTypes recurringType = MasterCache.getMstTypeForSubTypeName(Constants.MST_TYPE_RECURRING, maintenanceWindowPojo.getRecurring().getRecurringType());

        if (maintenanceWindowPojo.getRecurring().getEndHour().equalsIgnoreCase("00:00")) {
            maintenanceWindowPojo.getRecurring().setEndHour("23:59");
        }

        String[] startHrMin = maintenanceWindowPojo.getRecurring().getStartHour().split(":");
        String[] endHrMin = maintenanceWindowPojo.getRecurring().getEndHour().split(":");

        Calendar startHr = Calendar.getInstance(TimeZone.getTimeZone("Asia/Calcutta"));
        startHr.setTimeInMillis(maintenanceWindowPojo.getStartTime().getTime());
        startHr.add(Calendar.HOUR_OF_DAY, Integer.parseInt(startHrMin[0]));
        startHr.add(Calendar.MINUTE, Integer.parseInt(startHrMin[1]));
        startHr.set(Calendar.MILLISECOND, 0);

        Calendar endHr = Calendar.getInstance(TimeZone.getTimeZone("Asia/Calcutta"));
        endHr.setTimeInMillis(maintenanceWindowPojo.getStartTime().getTime());
        endHr.add(Calendar.HOUR_OF_DAY, Integer.parseInt(endHrMin[0]));
        endHr.add(Calendar.MINUTE, Integer.parseInt(endHrMin[1]));
        endHr.set(Calendar.MILLISECOND, 0);

        Timestamp startTime = new Timestamp(startHr.getTimeInMillis());
        Calendar endRecurring = Calendar.getInstance(TimeZone.getTimeZone("Asia/Calcutta"));
        endRecurring.setTimeInMillis(maintenanceWindowPojo.getEndTime().getTime());

        switch (recurringType.getSubTypeName()) {
            case Constants.MST_SUB_TYPE_RECURRING_DAILY: {
                int day = maintenanceWindowPojo.getRecurring().getDay();
                while (startHr.before(endRecurring)) {
                    log.debug("Start Time : {}  End Time : {} ", startHr.getTime(), endHr.getTime());
                    maintenanceWindowBeanList.add(MaintenanceWindowBean.builder().startTime(startHr.getTimeInMillis()).endTime(endHr.getTimeInMillis()).type(recurringType.getSubTypeName()).build());
                    startHr.add(Calendar.DATE, day);
                    endHr.add(Calendar.DATE, day);
                }
                break;
            }
            case Constants.MST_SUB_TYPE_RECURRING_WEEKLY:
                String[] weekNames = maintenanceWindowPojo.getRecurring().getWeeknames().split(",");
                int week = 0;
                ArrayList<Integer> dayOfWeek = new ArrayList<>();
                for (String weekName : weekNames) {
                    dayOfWeek.add(weekMap.get(weekName.toLowerCase()));
                }
                Collections.sort(dayOfWeek);
                while (startHr.before(endRecurring)) {
                    startHr.add(Calendar.DATE, week * 7);
                    endHr.add(Calendar.DATE, week * 7);

                    for (Integer integer : dayOfWeek) {
                        startHr.set(Calendar.DAY_OF_WEEK, integer);
                        endHr.set(Calendar.DAY_OF_WEEK, integer);

                        if ((startHr.getTime().after(startTime) || startHr.getTime().equals(startTime)) && startHr.before(endRecurring)) {
                            log.debug("Start Time : {}  End Time : {} ", startHr.getTime(), endHr.getTime());
                            maintenanceWindowBeanList.add(MaintenanceWindowBean.builder().startTime(startHr.getTimeInMillis()).endTime(endHr.getTimeInMillis()).type(recurringType.getSubTypeName()).build());
                        }

                        week = maintenanceWindowPojo.getRecurring().getWeek();
                    }
                }
                break;
            case Constants.MST_SUB_TYPE_RECURRING_MONTHLY: {
                int day = maintenanceWindowPojo.getRecurring().getDay();
                int month = maintenanceWindowPojo.getRecurring().getMonth();

                while (startHr.before(endRecurring)) {
                    startHr.add(Calendar.MONTH, month);
                    endHr.add(Calendar.MONTH, month);
                    startHr.set(Calendar.DATE, day);
                    endHr.set(Calendar.DATE, day);
                    log.debug("Start Time : {}  End Time : {} ", startHr.getTime(), endHr.getTime());
                    maintenanceWindowBeanList.add(MaintenanceWindowBean.builder().startTime(startHr.getTimeInMillis()).endTime(endHr.getTimeInMillis()).type(recurringType.getSubTypeName()).build());
                }
                break;
            }
        }
        return maintenanceWindowBeanList;
    }

    public static void verifyAndBuildRecurringMaintenanceWindow(MaintenanceWindowPojo maintenanceWindowPojo, int serviceId, RecurringBean recurringDetailsBean, boolean updateFlow) throws ServerException {
        ObjectMapper mapper = new ObjectMapper();
        if (recurringDetailsBean.getRecurringType().equals(Constants.MST_SUB_TYPE_RECURRING_DAILY)) {
            DailyRecurringBean dailyRecurringBean = DailyRecurringBean.builder()
                    .selected(Constants.RECURRING_SELECTED_ED)
                    .day(String.valueOf(recurringDetailsBean.getDay()))
                    .build();
            try {
                recurringDetailsBean.setRecurringData(mapper.writeValueAsString(dailyRecurringBean));
            } catch (JsonProcessingException e) {
                log.error("Error while parsing daily recurring bean information. Details: ", e);
                throw new ServerException("Error while parsing daily recurring bean information.");
            }
        } else if (recurringDetailsBean.getRecurringType().equals(Constants.MST_SUB_TYPE_RECURRING_WEEKLY)) {
            WeeklyRecurringBean weeklyRecurringBean = WeeklyRecurringBean.builder()
                    .selected(Constants.RECURRING_SELECTED_EW)
                    .week(String.valueOf(recurringDetailsBean.getWeek()))
                    .weeknames(recurringDetailsBean.getWeeknames())
                    .build();
            try {
                recurringDetailsBean.setRecurringData(mapper.writeValueAsString(weeklyRecurringBean));
            } catch (JsonProcessingException e) {
                log.error("Error while parsing weekly recurring bean information. Details: ", e);
                throw new ServerException("Error while parsing weekly recurring bean information.");
            }
        } else {
            MonthlyRecurringBean monthlyRecurringBean = MonthlyRecurringBean.builder()
                    .selected(Constants.RECURRING_SELECTED_DATE)
                    .day(String.valueOf(recurringDetailsBean.getDay()))
                    .month(String.valueOf(recurringDetailsBean.getMonth()))
                    .build();
            try {
                recurringDetailsBean.setRecurringData(mapper.writeValueAsString(monthlyRecurringBean));
            } catch (JsonProcessingException e) {
                log.error("Error while parsing monthly recurring bean information. Details: ", e);
                throw new ServerException("Error while parsing monthly recurring bean information.");
            }
        }

        checkForConflictsInRecurringWindows(maintenanceWindowPojo, serviceId, updateFlow);
    }

    public static void checkForConflictsInRecurringWindows(MaintenanceWindowPojo maintenanceWindowPojo, int serviceId, boolean updateFlow) throws ServerException {
        List<MaintenanceWindowBean> recurringCycles = buildRecurringCycles(maintenanceWindowPojo);

        if (recurringCycles.isEmpty()) {
            log.error("Error while building recurring cycles from the input {}", maintenanceWindowPojo);
            throw new ServerException("Error while building recurring cycles for the specified time interval in the request");
        }

        if(updateFlow) {
            recurringCycles = recurringCycles.parallelStream().filter(r -> {
                Timestamp recurringStartTime = new Timestamp(r.getStartTime());
                Calendar startCal = Calendar.getInstance(TimeZone.getTimeZone("Asia/Calcutta"));
                Timestamp date = new Timestamp(startCal.getTime().getTime());

                return recurringStartTime.after(date);
            }).collect(Collectors.toList());
        }

        for (MaintenanceWindowBean recurringCycle : recurringCycles) {
            MaintenanceWindowBean maintenanceWindowBean;
            try {
                maintenanceWindowBean = MaintenanceWindowUtility.getMaintenanceConflictByServiceId(maintenanceWindowPojo.getId(), serviceId,
                        new Timestamp(recurringCycle.getStartTime()), new Timestamp(recurringCycle.getEndTime()));
            } catch (AppsOneException e) {
                log.error("Error while checking for maintenance window conflicts for service ID [{}]", serviceId);
                throw new ServerException(String.format("Error while checking for maintenance window conflicts for service ID [%d]", serviceId));
            }

            if (maintenanceWindowBean != null && !(new Timestamp(recurringCycle.getStartTime()).equals(new Timestamp(maintenanceWindowBean.getEndTime()))
            || new Timestamp(recurringCycle.getEndTime()).equals(new Timestamp((maintenanceWindowBean.getStartTime()))))) {
                String error = "Maintenance window already exists from " + new Date(maintenanceWindowBean.getStartTime()) + " to " + new Date(maintenanceWindowBean.getEndTime());
                throw new ServerException(error);
            }
        }
    }
}
