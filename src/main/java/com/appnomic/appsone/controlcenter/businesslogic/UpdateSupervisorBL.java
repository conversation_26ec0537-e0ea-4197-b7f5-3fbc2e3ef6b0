package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.SupervisorDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.SupervisorBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.Supervisor;
import com.appnomic.appsone.controlcenter.service.KeyCloakAuthService;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.model.JWTData;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

public class UpdateSupervisorBL implements BusinessLogic<Supervisor, SupervisorBean, String> {
    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateSupervisorBL.class);

    @Override
    public UtilityBean<Supervisor> clientValidation(RequestObject requestObject) throws ClientException {
        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if(authKey == null || authKey.trim().isEmpty()) {
            LOGGER.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String supervisorIdentifier = requestObject.getParams().get(Constants.SUPERVISOR_IDENTIFIER);
        if(supervisorIdentifier == null || supervisorIdentifier.trim().isEmpty()) {
            LOGGER.error("Supervisor identifier is invalid. Reason: It is either NULL or empty");
            throw new ClientException("Supervisor identifier is invalid");
        }

        ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

        Supervisor supervisor;
        try {
            supervisor = objectMapper.readValue(requestObject.getBody(),
                    new TypeReference<Supervisor>(){});
        } catch (IOException e) {
            LOGGER.error("IOException encountered while parsing request body. Details {}", e.getMessage());
            throw new ClientException(Constants.JSON_PARSE_ERROR);
        }

        String invalidParameters = supervisor.validateForUpdate();
        if (!invalidParameters.trim().isEmpty()) {
            LOGGER.error("Input parameter(s) '[{}]' are invalid", invalidParameters);
            throw new ClientException(String.format("Input parameter(s) '%s' invalid", invalidParameters));
        }

        supervisor.setSupervisorId(supervisorIdentifier);

        return UtilityBean.<Supervisor>builder()
                .authToken(authKey)
                .pojoObject(supervisor)
                .build();
    }

    @Override
    public SupervisorBean serverValidation(UtilityBean<Supervisor> utilityBean) throws ServerException {
        Supervisor supervisor = utilityBean.getPojoObject();

        JWTData jwtData;
        try {
            jwtData = KeyCloakAuthService.extractUserDetails(utilityBean.getAuthToken());
        } catch (ControlCenterException e) {
            LOGGER.error("Exception encountered while fetching the user identifier");
            throw new ServerException("Error in retrieving user identifier");
        }

        String userId = jwtData.getSub();

        SupervisorDataService supervisorDataService = new SupervisorDataService();
        List<SupervisorBean> supervisorBeans = supervisorDataService.getAllSupervisorDetails(null);

        boolean supIdExists = supervisorBeans.parallelStream().anyMatch(sup -> sup.getIdentifier().equalsIgnoreCase(supervisor.getSupervisorId()));
        if(!supIdExists) {
            LOGGER.error("Supervisor with identifier [{}] is unavailable", supervisor.getSupervisorId());
            throw new ServerException(String.format("Supervisor with identifier %s is unavailable", supervisor.getSupervisorId()));
        }

        SupervisorBean existingSupervisor = supervisorDataService.getSupervisorByIdentifier(supervisor.getSupervisorId(), null);

        if(supervisor.getName() != null && !existingSupervisor.getName().equalsIgnoreCase(supervisor.getName())) {
            boolean supNameExists = supervisorBeans.parallelStream().anyMatch(sup -> sup.getName().equalsIgnoreCase(supervisor.getName()));
            if (supNameExists) {
                LOGGER.error("Supervisor with name [{}] already exists", supervisor.getName());
                throw new ServerException(String.format("Supervisor with name %s already exists", supervisor.getName()));
            }
        } else {
            supervisor.setName(existingSupervisor.getName());
        }

        if(supervisor.getHostAddress() != null) {
            boolean hostAddressExists = supervisorBeans.parallelStream().anyMatch(sup -> sup.getHostAddress().equalsIgnoreCase(supervisor.getHostAddress()));

            if(hostAddressExists) {
                LOGGER.error("Supervisor for host address [{}] already exists", supervisor.getHostAddress());
                throw new ServerException(String.format("Supervisor for host address %s already exists", supervisor.getHostAddress()));
            }
        } else {
            supervisor.setHostAddress(existingSupervisor.getHostAddress());
        }

        if(supervisor.getHostBoxName() == null) {
            supervisor.setHostBoxName(existingSupervisor.getHostBoxName());
        }

        int supervisorTypeId;
        if(supervisor.getSupervisorTypeName() != null) {
            ViewTypes supervisorType = MasterCache.getMstTypeForSubTypeName(Constants.SUPERVISOR_TYPE, supervisor.getSupervisorTypeName());

            if (supervisorType == null) {
                LOGGER.error("Supervisor Type [{}] is invalid", supervisor.getSupervisorTypeName());
                throw new ServerException(String.format("Supervisor type %s is invalid", supervisor.getSupervisorTypeName()));
            }

            supervisorTypeId = supervisorType.getSubTypeId();
        } else {
            supervisorTypeId = existingSupervisor.getSupervisorType();
        }

        if(supervisor.getMode() != null) {
            if (Constants.SUPERVISOR_REMOTE_MODE.equalsIgnoreCase(supervisor.getMode())) {
                boolean doesModeExist = supervisorBeans.parallelStream()
                        .anyMatch(sup -> sup.getMode().equalsIgnoreCase(supervisor.getMode()));

                if (doesModeExist) {
                    LOGGER.error("Supervisor with mode [{}] already exists", supervisor.getMode());
                    throw new ServerException(String.format("Supervisor with mode %s already exists", supervisor.getMode()));
                }
            }
        } else {
            supervisor.setMode(existingSupervisor.getMode());
        }

        if(supervisor.getVersion() == null) {
            supervisor.setVersion(existingSupervisor.getVersion());
        }

        return SupervisorBean.builder()
                .identifier(supervisor.getSupervisorId())
                .name(supervisor.getName())
                .supervisorType(supervisorTypeId)
                .hostAddress(supervisor.getHostAddress())
                .hostBoxName(supervisor.getHostBoxName())
                .version(supervisor.getVersion())
                .userId(userId)
                .status(supervisor.isStatus())
                .mode(supervisor.getMode())
                .updatedTime(new Timestamp(new Date().getTime()))
                .build();
    }

    @Override
    public String process(SupervisorBean bean) throws DataProcessingException {
        int result = new SupervisorDataService().updateSupervisor(bean, null);
        if(result <= 0){
            LOGGER.error("Error while updating supervisor details");
            throw new DataProcessingException("Error while updating supervisor details");
        }

        return "Supervisor details successfully updated";
    }
}
