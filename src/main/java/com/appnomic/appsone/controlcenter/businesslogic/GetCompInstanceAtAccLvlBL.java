package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.enums.DiscoveryStatus;
import com.appnomic.appsone.controlcenter.beans.MasterComponentTypeBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.AutoDiscoveryDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.AgentInstanceMappingPojo;
import com.appnomic.appsone.controlcenter.pojo.InstanceServiceApplicationDetailsPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.AgentDetails;
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.GetCompInstance;
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.IdNamePojo;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.EnvironmentHelper;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.InstanceAttributes;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class GetCompInstanceAtAccLvlBL implements BusinessLogic<Object, AccountBean, List<GetCompInstance>> {

    private int hostComponentTypeId;

    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty");
            throw new ClientException("Invalid account identifier");
        }

        return UtilityBean.builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .build();
    }

    public AccountBean serverValidation(UtilityBean<Object> utilityBean) throws ServerException {

        String accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        int accountId = account.getId();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        MasterComponentTypeBean componentTypeBean = MasterCache.getMasterComponentTypeUsingName(Constants.COMPONENT_TYPE_HOST, String.valueOf(accountId));
        if (componentTypeBean == null) {
            String err = "Component with type '" + Constants.HOST + "' doesn't exist.";
            log.error(err);
            throw new ServerException(err);
        }
        hostComponentTypeId = componentTypeBean.getId();
        return account;
    }


       public List<GetCompInstance> process(AccountBean accountInfo) throws DataProcessingException {
        long st = System.currentTimeMillis();
        try {
            List<GetCompInstance> preExistingData = preExistingData(accountInfo.getIdentifier(), accountInfo.getId());
            List<GetCompInstance> stagingTableData = autoDiscoveryPart();

            List<GetCompInstance> duplicates = new ArrayList<>();
            preExistingData.forEach(compInstanceInfo-> stagingTableData
                    .forEach(preExistenData -> {
                        if(compInstanceInfo.getHostAddress().get(0)!=null) {
                            if (compInstanceInfo.getInstanceIdentifier().equals(preExistenData.getInstanceIdentifier()) ||
                                    (compInstanceInfo.getHostAddress().get(0).equals(preExistenData.getHostAddress().get(0))
                                            && preExistenData.getHostStatus() == DiscoveryStatus.DISCOVERED_NOT_ADDED_TO_SYSTEM) ||
                                    (compInstanceInfo.getHostAddress() != null && compInstanceInfo.getHostAddress().size() > 0 && compInstanceInfo.getPort() != null &&
                                            compInstanceInfo.getPort().size() > 0 && preExistenData.getHostAddress() != null && preExistenData.getHostAddress().size() > 0 &&
                                            preExistenData.getPort() != null && preExistenData.getPort().size() > 0 &&
                                            (compInstanceInfo.getHostAddress().get(0) + ":" + compInstanceInfo.getPort().get(0).getAttributeValue())
                                                    .equalsIgnoreCase(preExistenData.getHostAddress().get(0) + ":" + preExistenData.getPort().get(0).getAttributeValue()))) {
                                duplicates.add(preExistenData);
                            }
                        }
                    }));

            stagingTableData.removeAll(duplicates.stream().distinct().collect(Collectors.toList()));
            preExistingData.addAll(stagingTableData);

            preExistingData.sort(Comparator.comparing(GetCompInstance::getStatus, Comparator.reverseOrder())
                    .thenComparing(GetCompInstance::getLastDiscoveryRunTime, Comparator.reverseOrder())
                    .thenComparing(GetCompInstance::getInstanceName, Comparator.comparingInt(o -> Character.toLowerCase(o.charAt(0)))));

            return preExistingData;
        } catch (Exception e) {
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        } finally {
            log.debug("Time taken for getting component instances is {} ms.", System.currentTimeMillis() - st);
        }
    }

    public List<GetCompInstance> preExistingData(String accountIdentifier, int accountId) throws ControlCenterException {
        long st = System.currentTimeMillis();
        InstanceRepo instanceRepo = new InstanceRepo();
        ServiceRepo serviceRepo = new ServiceRepo();
        EnvironmentHelper envHelper = new EnvironmentHelper();
        try {

            long time = System.currentTimeMillis();

            Map<Integer, CompInstClusterDetails> instanceBeanMap = instanceRepo.getInstances(accountIdentifier)
                    .parallelStream()
                    .filter(i -> !i.isCluster())
                    .collect(Collectors.toMap(CompInstClusterDetails::getId, Function.identity()));
            log.debug("Time taken for instanceBeans:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            Map<Integer, List<IdNamePojo>> serviceApplicationMap = serviceRepo.getAllServicesDetails(accountIdentifier)
                    .parallelStream()
                    .collect(Collectors.toMap(BasicEntity::getId,
                            s -> serviceRepo.getApplicationsByServiceIdentifier(accountIdentifier, s.getIdentifier())
                                    .stream().map(a -> IdNamePojo.builder()
                                    .id(a.getId())
                                    .name(a.getName())
                                    .identifier(a.getIdentifier())
                                    .build())
                            .collect(Collectors.toList())));

            log.debug("Time taken for serviceApplicationMap:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            Map<Integer, List<AgentInstanceMappingPojo>> agentBeanMap = AgentDataService.getInstanceAgentMapping(accountId, null)
                    .parallelStream()
                    .collect(Collectors.groupingBy(AgentInstanceMappingPojo::getInstanceId));
            log.debug("Time taken for agentBeanMap:{} ms", (System.currentTimeMillis() - time));

            /*
            Create get component instance details list
             */
            return instanceBeanMap.values().parallelStream()
                    .filter(c -> c.getComponentTypeId() != hostComponentTypeId) // only non-host instances
                    .map(c -> {
                        GetCompInstance compInstance = GetCompInstance.builder()
                                .instanceId(String.valueOf(c.getId()))
                                .instanceName(c.getName())
                                .instanceIdentifier(c.getIdentifier())
                                .hostId(String.valueOf(c.getHostId()))
                                .hostName(c.getHostName())
                                .hostIdentifier(instanceBeanMap.get(c.getHostId()) == null ?
                                        null : instanceBeanMap.get(c.getHostId()).getIdentifier())
                                .hostAddress(Collections.singletonList(c.getHostAddress()))
                                .componentId(c.getComponentId())
                                .componentName(c.getComponentName())
                                .componentVersionId(c.getComponentVersionId())
                                .componentVersionName(c.getComponentVersionName())
                                .commonVersionId(c.getCommonVersionId())
                                .commonVersionName(c.getCommonVersionName())
                                .componentTypeId(c.getComponentTypeId())
                                .componentTypeName(c.getComponentTypeName())
                                .status(DiscoveryStatus.ADDED_TO_SYSTEM)
                                .process(c.getDiscovery() == 0 ? "Manual" : "Auto")
                                .environment(envHelper.getOrDefaultEnvironmentName(c.getIsDR()))
                                .lastDiscoveryRunTime(DateTimeUtil.getGMTToEpochTime(c.getUpdatedTime()))
                                .build();

                        List<IdNamePojo> services = instanceRepo.getServices(accountIdentifier, c.getIdentifier())
                                .stream()
                                .map(s -> IdNamePojo.builder()
                                        .id(s.getId())
                                        .name(s.getName())
                                        .identifier(s.getIdentifier())
                                        .build())
                                .collect(Collectors.toList());

                        List<IdNamePojo> applications = services.stream()
                                .map(s -> serviceApplicationMap.get(s.getId()))
                                .filter(Objects::nonNull)
                                .flatMap(Collection::stream)
                                .collect(Collectors.toList());

                        compInstance.setService(services);
                        compInstance.setApplication(applications);

                        /*
                         * Get Agent details for each Comp Instance
                         */
                        compInstance.setMappedAgents(agentBeanMap.getOrDefault(c.getId(), new ArrayList<>())
                                .parallelStream().distinct().map(agentInstance -> AgentDetails.builder()
                                        .agentId(agentInstance.getAgentId())
                                        .agentName(agentInstance.getAgentName())
                                        .agentIdentifier(agentInstance.getUniqueToken())
                                        .physicalAgentId(agentInstance.getPhysicalAgentId())
                                        .physicalAgentIdentifier(agentInstance.getPhysicalAgentIdentifier())
                                        .agentTypeId(agentInstance.getAgentTypeId())
                                        .agentTypeName(agentInstance.getAgentTypeName())
                                        .status(agentInstance.getStatus())
                                        .build()).collect(Collectors.toList()));

                        InstanceAttributes portAttribute = instanceRepo.getAttributes(accountIdentifier, c.getIdentifier())
                                .stream()
                                .filter(a -> a.getAttributeName().equalsIgnoreCase("MonitorPort"))
                                .findAny()
                                .orElse(null);

                        //Set port
                        compInstance.setPort(portAttribute == null ? new ArrayList<>() : Collections.singletonList(GetCompInstance.AttributeNameValue.builder()
                                .attributeName(portAttribute.getAttributeName())
                                .attributeValue(portAttribute.getAttributeValue())
                                .build()));

                        return compInstance;
                    }).collect(Collectors.toList());
        } catch (Exception e) {
            throw new ControlCenterException(e, "Error while getting HEAL system side component instance data, accountId:"+accountId);
        } finally {
            log.debug("Time taken for getCompInstanceList:{} ms", (System.currentTimeMillis() - st));
        }

    }

    private List<GetCompInstance> autoDiscoveryPart() {
        AutoDiscoveryDataService autoDiscoveryDataService = new AutoDiscoveryDataService();
        EnvironmentHelper envHelper = new EnvironmentHelper();
        List<InstanceServiceApplicationDetailsPojo> componentInstanceBeanList = autoDiscoveryDataService.getComponentInstanceServiceAppDetails(null);

        /*
         * Create GetHost Details List
         */
        List<GetCompInstance> getCompInstanceList = componentInstanceBeanList.parallelStream()
                .map(c -> GetCompInstance.builder()
                        .instanceId(c.getInstanceIdentifier())
                        .instanceName(c.getInstanceName())
                        .instanceIdentifier(c.getInstanceIdentifier())
                        .hostId(c.getHostIdentifier())
                        .hostName(c.getHostName())
                        .hostIdentifier(c.getHostIdentifier())
                        .componentId(c.getComponentId())
                        .componentName(c.getComponentName())
                        .componentVersionId(c.getComponentVersionId())
                        .componentVersionName(c.getComponentVersionName())
                        .commonVersionId(c.getCommonVersionId())
                        .commonVersionName(c.getCommonVersionName())
                        .componentTypeId(c.getComponentTypeId())
                        .componentTypeName(c.getComponentTypeName())
                        .service(Collections.singletonList(IdNamePojo.builder()
                                .id(c.getServiceId())
                                .name(c.getServiceName())
                                .identifier(c.getServiceIdentifier())
                                .build()))
                        .application(Collections.singletonList(IdNamePojo.builder()
                                .id(c.getApplicationId())
                                .name(c.getApplicationName())
                                .identifier(c.getApplicationIdentifier())
                                .build()))
                        .port(new LinkedList<>(Collections.singletonList(GetCompInstance.AttributeNameValue.builder()
                                .attributeName(c.getAttributeName())
                                .attributeValue(c.getAttributeValue())
                                .build())))
                        .status(c.getStatus())
                        .process("Auto")
                        .environment(envHelper.getOrDefaultEnvironmentName(c.getIsDR()))
                        .lastDiscoveryRunTime(c.getLastDiscoveryRunTime() == null ? 0 : Long.parseLong(c.getLastDiscoveryRunTime()))
                        .hostStatus(c.getHostStatus())
                        .build()).sorted(Comparator.comparing(GetCompInstance::getInstanceId)).collect(Collectors.toList());

        /*
         * Remove Duplicates
         */
        for (int i = 0; i < getCompInstanceList.size() - 1; i++) {
            List<IdNamePojo> temp = new ArrayList<>();
            List<GetCompInstance.AttributeNameValue> tempPort = new ArrayList<>();
            if (getCompInstanceList.get(i).getInstanceId().equals(getCompInstanceList.get(i + 1).getInstanceId())) {
                /*
                Remove Instances on basis of Service duplicates
                 */
                if (!getCompInstanceList.get(i).getService().containsAll(getCompInstanceList.get(i + 1).getService())) {
                    temp.addAll(getCompInstanceList.get(i).getService());
                    temp.addAll(getCompInstanceList.get(i + 1).getService());
                    getCompInstanceList.get(i).setService(temp);
                    temp = new ArrayList<>();
                }

                /*
                Remove Instances on basis of Application duplicates
                 */
                if (!getCompInstanceList.get(i).getApplication().containsAll(getCompInstanceList.get(i + 1).getApplication())) {
                    temp.addAll(getCompInstanceList.get(i).getApplication());
                    temp.addAll(getCompInstanceList.get(i + 1).getApplication());
                    getCompInstanceList.get(i).setApplication(temp);
                }

                /*
                Remove Instances on basis of Port duplicates
                 */
                if (!getCompInstanceList.get(i).getPort().containsAll(getCompInstanceList.get(i + 1).getPort())) {
                    tempPort.addAll(getCompInstanceList.get(i).getPort());
                    tempPort.addAll(getCompInstanceList.get(i + 1).getPort());
                    getCompInstanceList.get(i).setPort(tempPort);
                }

                getCompInstanceList.remove(i + 1);
                i--;
            }
        }

        for (GetCompInstance getCompInstance : getCompInstanceList) {
            /*
            Filter out MonitorPort only
             */
            for (int i = 0; i < getCompInstance.getPort().size(); i++) {
                if (getCompInstance.getPort().get(i).getAttributeName().equalsIgnoreCase("HostAddress")) {
                    getCompInstance.setHostAddress(Collections.singletonList(getCompInstance.getPort().get(i).getAttributeValue()));
                }

                if (!getCompInstance.getPort().get(i).getAttributeName().equalsIgnoreCase("MonitorPort")) {
                    i--;
                    getCompInstance.getPort().remove(i + 1);
                }
            }
        }

        return getCompInstanceList;
    }
}
