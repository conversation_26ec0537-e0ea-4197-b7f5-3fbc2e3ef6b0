package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.DBTestCache;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.ProducerType;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ComponentDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.MasterDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ProducerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.*;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
public class AddProducerBL implements BusinessLogic<ProducerDetails, ProducerDetails, Map<Integer, String>> {
    private AccountBean account = new AccountBean();
    private String userId;


    @Override
    public UtilityBean<ProducerDetails> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (StringUtils.isEmpty(requestObject.getBody())) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty");
            throw new ClientException("Invalid account identifier");
        }

        ProducerDetails parsedRequestBody;

        String requestContent = requestObject.getBody();

        try {
            ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder()
                    .configure(DeserializationFeature.EAGER_DESERIALIZER_FETCH, false);
            parsedRequestBody = objectMapper.readValue(requestContent,
                    new TypeReference<ProducerDetails>() {
                    });
        } catch (IOException e) {
            log.error("Error occurred while parsing request for add producer data.", e);
            throw new ClientException("Error occurred while parsing request for add producer data.");
        }

        if (!parsedRequestBody.isValid()) {
            log.error("Validation failure for ProducerDetails");
            throw new ClientException("Validation failure for ProducerDetails");
        }

        return UtilityBean.<ProducerDetails>builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .pojoObject(parsedRequestBody)
                .build();
    }

    @Override
    public ProducerDetails serverValidation(UtilityBean<ProducerDetails> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getAccountIdentifier();

        account = ValidationUtils.validAndGetAccount(accountIdentifier);
        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        try {
            if (!validate(utilityBean.getPojoObject(), account.getId())) {
                log.error("Invalid ProducerDetails.");
                throw new ServerException("Invalid ProducerDetails.");
            }
        } catch (Exception e) {
            log.error("Exception while validating ProducerDetails. Error:- " + Throwables.getRootCause(e).getMessage());
            throw new ServerException("Exception while validating ProducerDetails. Check CC Logs.");
        }


        return utilityBean.getPojoObject();
    }

    private boolean validate(ProducerDetails producerDetails, int accountId) throws ControlCenterException {
        log.debug("Validating if JSON for add producer request is valid. accountId: {}", accountId);

        MasterProducerBean producerBean = ProducerDataService.getProducerWithName(accountId, producerDetails.getName());
        if (producerBean != null) {
            log.error("The requested name: {} is already present in the database", producerDetails.getName());
            return false;
        }

        List<ProducerKpiMappingDetails> validList = producerDetails.getKpiMapping().parallelStream()
                .filter(mappedKpi -> validateKpiDetails(mappedKpi, accountId, producerDetails.getKpiType(), producerDetails.getIsGroupKpi()))
                .collect(Collectors.toList());

        if (validList.size() != producerDetails.getKpiMapping().size()) {
            log.error(" There are {} invalid kpi mapping", (producerDetails.getKpiMapping().size() - validList.size()));
            return false;
        }

        MasterProducerTypeBean producer = ProducerDataService.getProducerTypeData(accountId, producerDetails.getProducerType().trim());
        if (producer == null) {
            log.error("Invalid producer type provided: {}.", producerDetails.getProducerType());
            return false;
        }

        if (!areValidAttributes(producer, producerDetails)) {
            log.error("Invalid attributes are present.");
            return false;
        }

        if (!areValidParameters(producer, producerDetails)) {
            log.error("Invalid parameters provided");
            return false;
        }

        return true;
    }

    private boolean validateKpiDetails(ProducerKpiMappingDetails kpiMapping, int accountId, String kpiType, int isGroup) {
        AllKpiList kpiDetails = MasterDataService.getKpi(kpiMapping.getKpiIdentifier(), accountId);
        if (kpiDetails == null) {
            log.error("KPI with identifier [{}] is unavailable", kpiMapping.getKpiIdentifier());
            return false;
        }

        if (!kpiDetails.getKpiType().equalsIgnoreCase(kpiType)) {
            log.error("Invalid kpi of type: {} found, expected: {}", kpiDetails.getKpiType(), kpiType);
            return false;
        }

        if (kpiDetails.getGroupStatus() != isGroup) {
            log.error("Invalid kpi group status received, actual: {}, expected: {}", kpiDetails.getGroupStatus(), isGroup);
            return false;
        }

        MasterComponentBean componentBean = ComponentDataService.getComponentDetails(accountId, kpiMapping.getComponentName(),
                kpiMapping.getComponentVersionId(), kpiMapping.getComponentTypeName(), null);
        if (componentBean == null) {
            log.error("No components available with name [{}], version [{}], type [{}] for global account or account [{}]",
                    kpiMapping.getComponentName(), kpiMapping.getComponentVersionId(), kpiMapping.getComponentTypeName(), accountId);
            return false;
        }

        ViewCommonVersionKPIsBean mappedKpi = ComponentDataService.getKpisMappedToComponentCommonVersion(accountId, componentBean.getCommonVersionId(),
                kpiDetails.getGroupId(), kpiMapping.getKpiIdentifier(), null);
        if (mappedKpi == null) {
            log.error("Kpi with identifier [{}] unavailable for component common version [{}]", kpiMapping.getKpiIdentifier(), componentBean.getCommonVersionId());
            return false;
        }

        return true;
    }

    private boolean areValidAttributes(MasterProducerTypeBean producer, ProducerDetails producerDetails) throws ControlCenterException {
        if (producer.getProducerTableName() == null) {
            log.info("There are no attributes required for producer type: {}", producerDetails.getProducerType());
            producerDetails.setProducerAttributes(new HashMap<>());
            return true;
        }

        List<MasterProducerAttributeBean> attributesList = ProducerDataService.getAttributesBasedOnProducerType(producer.getId());
        if (attributesList.isEmpty()) {
            log.info("Unable to get any attribute data for producer type: {}, skipping validation.",
                    producerDetails.getProducerType());
        } else {
            AtomicBoolean mandatoryAttributeMissing = new AtomicBoolean(false);
            Map<String, String> tempAttributes = new HashMap<>(producerDetails.getProducerAttributes());

            // Validate mandatory attributes
            attributesList.stream()
                    .filter(attribute -> (attribute.getIsMandatory() == 1))
                    .forEach(attribute -> {
                        if (tempAttributes.get(attribute.getAttributeName()) == null) {
                            mandatoryAttributeMissing.set(true);
                            log.error("Mandatory attribute: {} , is missing.", attribute.getAttributeName());
                            return;
                        }
                        tempAttributes.remove(attribute.getAttributeName());
                    });

            if (mandatoryAttributeMissing.get()) {
                return false;
            }

            // Validate remaining attributes are valid attributes
            attributesList.stream()
                    .filter(attribute -> (attribute.getIsMandatory() == 0))
                    .forEach(attribute -> {
                        if (tempAttributes.get(attribute.getAttributeName()) != null) {
                            tempAttributes.remove(attribute.getAttributeName());
                        }
                    });

            if (!tempAttributes.isEmpty()) {
                log.error("Invalid attributes are present: {}", tempAttributes);
                return false;
            }
        }

        return validateAttributesValues(producerDetails);
    }

    private boolean validateAttributesValues(ProducerDetails producerDetails) throws ControlCenterException {
        ProducerType producerType = ProducerType.valueOf(producerDetails.getProducerType().trim());
        String timestampString = DateTimeUtil.getTimeInGMT(new Date().getTime());
        int producerId = 100000;
        String userDetailsId = "test";
        boolean validationFlag = false;

        try {
            switch (producerType) {

                case SCRIPT:
                    SSHProducerTypeDetail sshProducerTypeDetail = new SSHProducerTypeDetail(producerId, timestampString,
                            timestampString, userDetailsId);
                    validationFlag = sshProducerTypeDetail.populate(producerDetails.getProducerAttributes());
                    break;

                case WMI:
                    WMIProducerTypeDetail wmiProducerTypeDetail = new WMIProducerTypeDetail(producerId, timestampString,
                            timestampString, userDetailsId);
                    validationFlag = wmiProducerTypeDetail.populate(producerDetails.getProducerAttributes());
                    break;

                case HTTP:
                    HTTPProducerTypeDetail httpProducerTypeDetail = new HTTPProducerTypeDetail(producerId, timestampString,
                            timestampString, userDetailsId);
                    validationFlag = httpProducerTypeDetail.populate(producerDetails.getProducerAttributes());
                    break;

                case JMX:
                    JMXProducerTypeDetail jmxProducerTypeDetail = new JMXProducerTypeDetail(producerId, timestampString,
                            timestampString, userDetailsId);
                    validationFlag = jmxProducerTypeDetail.populate(producerDetails.getProducerAttributes());
                    break;

                case WAS:
                    WASProducerTypeDetail wasProducerTypeDetail = new WASProducerTypeDetail(producerId, timestampString,
                            timestampString, userDetailsId);
                    validationFlag = wasProducerTypeDetail.populate(producerDetails.getProducerAttributes());
                    break;

                case JDBC:
                    JDBCProducerTypeDetail jdbcProducerTypeDetail = new JDBCProducerTypeDetail(producerId, timestampString,
                            timestampString, userDetailsId);
                    validationFlag = jdbcProducerTypeDetail.populate(producerDetails.getProducerAttributes());
                    break;

                case HTTP_JSON:
                    HTTPJsonProducerTypeDetail httpJsonProducerTypeDetail = new HTTPJsonProducerTypeDetail(producerId,
                            timestampString, timestampString, userDetailsId);
                    validationFlag = httpJsonProducerTypeDetail.populate(producerDetails.getProducerAttributes());
                    break;

                case JPPF:
                    JPPFProducerTypeDetail jppfProducerTypeDetail = new JPPFProducerTypeDetail(producerId,
                            timestampString, timestampString, userDetailsId);
                    validationFlag = jppfProducerTypeDetail.populate(producerDetails.getProducerAttributes());
                    break;

                default:

            }
        } catch (Exception e) {
            log.error("Error while validating Producer attribute values. Error :- ", e);
            throw new ControlCenterException("Error while validating Producer attribute values. " + e.getMessage());
        }

        return validationFlag;
    }

    private boolean areValidParameters(MasterProducerTypeBean producer, ProducerDetails producerDetails) {
        if (producer.getParameterTypeId() == null) {
            log.info("No parameter mapped for producerType: {}", producer.getType());
            producerDetails.setParameters(new ArrayList<>());
            return true;
        }

        int parameterTypeId = producer.getParameterTypeId();
        List<ViewTypes> parameterDetails = MasterCache.getSubtypesForTypeIdBypassCache(parameterTypeId);

        boolean paramsValid = true;
        for (ProducerParameterDetails param : producerDetails.getParameters()) {
            boolean flag = parameterDetails.stream()
                    .anyMatch(validParam -> (validParam.getSubTypeName().equalsIgnoreCase(param.getParameterType())));
            if (!flag) {
                log.error("Invalid parameter found: {}, the type {}, is not valid", param.getParameterName(),
                        param.getParameterType());
                paramsValid = false;
            } else {
                log.debug("The valid parameter: {}", param);
            }
        }
        return paramsValid;
    }

    @Override
    public Map<Integer, String> process(ProducerDetails producerDetails) throws DataProcessingException {
        Map<Integer, String> outputMap = new HashMap<>();
        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        AtomicInteger id = new AtomicInteger();

        try {
            return dbi.inTransaction((conn, status) -> {
                int producerId = addMasterProducerInfo(producerDetails, account, userId, conn);
                id.set(producerId);

                if (producerId > 0) {
                    //The below block is used for rolling back integration test
                    if (MySQLConnectionManager.getInstance().isIntegrationTest()) {
                        DBTestCache.addToCache("mst_producer", producerId);
                    }
                    /*
                     * Add all the KPI mapping for the producer
                     */
                    List<MasterProducerKpiMappingBean> getKpiMappingBeans = getKpiMappingBeans(account.getId(),
                            producerId, userId, producerDetails, conn);

                    if (getKpiMappingBeans.isEmpty()) {
                        throw new ControlCenterException("Invalid kpi mapping found");
                    }

                    addKPIMapping(getKpiMappingBeans, account, producerId, producerDetails.getIsGroupKpi(), userId,
                            conn);

                    /*
                     * Add all the parameters for the producer
                     */
                    List<ProducerParameterBean> parameterList = getProducerParameterBeanList(producerId, userId, producerDetails);
                    parameterList.forEach(param -> {
                        log.debug("Adding producer parameter: {}", param);
                        int paramId = ProducerDataService.addProducerParameter(param, conn);
                        //The below block is used for rolling back integration test
                        if (MySQLConnectionManager.getInstance().isIntegrationTest() && paramId > 0) {
                            DBTestCache.addToCache("producer_parameters", paramId);
                        }
                        log.info("Added parameter with id: {}", paramId);
                    });

                    /*
                     * Add all the valid attributes
                     */
                    if (!producerDetails.getProducerAttributes().isEmpty()) {
                        int attributeId = addAttribute(producerId, userId, producerDetails, conn);
                        log.info("Added producer attributes return value: {}", attributeId);
                        if (attributeId == -1) {
                            log.error("Unable to add attributes for producer: " + producerDetails);
                            throw new ControlCenterException("Unable to add attributes for producer: " + producerDetails);
                        }
                    }
                }
                outputMap.put(id.get(), producerDetails.getName());
                return outputMap;
            });
        } catch (Exception e) {
            log.error("DataProcessing Exception:- " + Throwables.getRootCause(e).getMessage());
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }

    private int addMasterProducerInfo(ProducerDetails producerDetails, AccountBean account, String userDetailsId, Handle handle) {
        int producerId = 0;

        MasterProducerTypeBean producer = ProducerDataService.getProducerTypeData(account.getId(), producerDetails.getProducerType().trim());
        ViewTypes kpiTypeDetails = MasterCache.getMstTypeForSubTypeName(Constants.KPI_TYPE, producerDetails.getKpiType());

        if (producer != null && kpiTypeDetails != null) {
            MasterProducerBean producerBean = new MasterProducerBean();
            producerBean.populate(account, userDetailsId, producerDetails, producer.getId(),
                    kpiTypeDetails.getSubTypeId());
            producerId = ProducerDataService.addProducer(producerBean, handle);
            log.info("Added producer with id: {}", producerId);
        } else {
            log.error("Unable to fetch component type id and or kpi sub type id");
        }
        return producerId;
    }

    private List<MasterProducerKpiMappingBean> getKpiMappingBeans(int accountId, int producerId, String userDetailsId,
                                                                  ProducerDetails producerDetails, Handle handle) {
        log.trace("{} getKpiMappingBeans, accId: {}, producerId: {}, userId: {}, producer: {}",
                Constants.INVOKED_METHOD, accountId, producerId, userDetailsId, producerDetails);

        List<MasterProducerKpiMappingBean> beanList = new ArrayList<>();
        AtomicBoolean issueFoundInMapping = new AtomicBoolean(false);

        producerDetails.getKpiMapping().forEach(kpi -> {
            AllKpiList kpiDetails = MasterDataService.getKpi(kpi.getKpiIdentifier().trim(), accountId);

            MasterComponentBean componentBean = ComponentDataService.getComponentDetails(accountId, kpi.getComponentName(), kpi.getComponentVersionId(), kpi.getComponentTypeName(), null);
            if (componentBean != null) {
                ViewCommonVersionKPIsBean mappedKpi = ComponentDataService.getKpisMappedToComponentCommonVersion(accountId, componentBean.getCommonVersionId(), kpiDetails.getGroupId(),
                        kpi.getKpiIdentifier(), null);
                if (mappedKpi == null) {
                    log.error("Kpi with identifier [{}] unavailable for component common version [{}]", kpi.getKpiIdentifier(), componentBean.getCommonVersionId());
                    issueFoundInMapping.set(true);
                }
            }

            if (kpiDetails != null && componentBean != null) {
                MasterProducerKpiMappingBean bean = new MasterProducerKpiMappingBean();
                bean.populate(accountId, producerId, userDetailsId, kpi, kpiDetails.getKpiId(),
                        componentBean.getComponentVersionId(), componentBean.getId(), componentBean.getComponentTypeId(),
                        handle);
                beanList.add(bean);
            }
        });

        if (producerDetails.getKpiMapping().size() != beanList.size() || issueFoundInMapping.get()) {
            log.error("All the kpis have not been mapped in the DB, kindly go through the logs for details.");
            return new ArrayList<>();
        }

        return beanList;
    }

    private void addKPIMapping(List<MasterProducerKpiMappingBean> kpiMappingList, AccountBean account, int producerId,
                               int isGroup, String userDetailsId, Handle conn) throws ControlCenterException {
        log.trace("{} addKPIMapping, kpiMappingListSize: {}, account: {}, producerId: {}, isGroup: {}," +
                        "userId: {}", Constants.INVOKED_METHOD, kpiMappingList.size(), account, producerId, isGroup,
                userDetailsId);

        for (MasterProducerKpiMappingBean kpiMapping : kpiMappingList) {
            if (kpiMapping != null) {
                log.debug("Adding kpi mapping: {}", kpiMapping);
                int defaultProducerId = getDefaultProducerId(kpiMapping, account.getId());
                int kpiMappingId = ProducerDataService.addProducerMapping(kpiMapping, conn);
                //The below block is used for rolling back integration test
                if (MySQLConnectionManager.getInstance().isIntegrationTest() && kpiMappingId > 0) {
                    DBTestCache.addToCache("mst_producer_kpi_mapping", kpiMappingId);
                }

                //Currently the mapping is supported only for non group KPI
                if (isGroup == 0) {
                    log.debug("Group KPI flag is disabled");
                    if (defaultProducerId != -1) {
                        log.debug("There are instances which have default producer mapped, hence updating them.");
                        //check this
                        ProducerDataService.updateDefaultProducerId(defaultProducerId, kpiMapping.getKpiDetailsId(), producerId, conn);
                    } else {
                        log.debug("This producer is the first producer for the KPI, hence adding all instance KPI to" +
                                " producer mapping");
                        addProducerMappingForInstanceKpi(account.getId(), kpiMapping.getKpiDetailsId(),
                                kpiMapping.getComponentVersionId(), kpiMappingId, producerId, userDetailsId, conn);
                    }
                }

                log.info("Added kpi mapping with id: {}, details: {}", kpiMappingId, kpiMapping);
            }
        }
    }

    private int getDefaultProducerId(MasterProducerKpiMappingBean kpiMappingBean, int accountId) {
        log.trace("{} getDefaultProducerId, kpiMappingDetails: {} , accId: {}", Constants.INVOKED_METHOD,
                kpiMappingBean, accountId);

        int defaultProducerId = -1;
        if (kpiMappingBean != null) {
            List<ViewProducerKPIsBean> mappedKpiList = ProducerDataService.getViewProducerKPIsData(
                    accountId, kpiMappingBean.getKpiDetailsId(), kpiMappingBean.getComponentVersionId());

            if (mappedKpiList != null) {
                mappedKpiList = mappedKpiList.stream()
                        .filter(it -> (it.getIsDefault() == 1))
                        .collect(Collectors.toList());

                if (!mappedKpiList.isEmpty()) {
                    defaultProducerId = mappedKpiList.stream()
                            .mapToInt(ViewProducerKPIsBean::getProducerId)
                            .toArray()[0];
                }
            }


        }
        return defaultProducerId;
    }

    private void addProducerMappingForInstanceKpi(int accountId, int kpiId, int compVersionId, int kpiMappingId, int producerId, String userDetailsId,
                                                  Handle handle) throws ControlCenterException {
        log.trace("{} addProducerMappingForInstanceKpi, accId: {}, kpiId: {}, compVersionId: {}, kpiMappingId: {}," +
                        "producerId: {}, userId: {}", Constants.INVOKED_METHOD, accountId, kpiId, compVersionId, kpiMappingId,
                producerId, userDetailsId);

        Map<Integer, InstanceMappingDetails> instanceMap = getMappedInstances(accountId, kpiId, compVersionId);
        List<CompInstanceKpiDetailsBean> beanList;

        if (!instanceMap.isEmpty()) {
            beanList = getinstanceKpiProducerMappingDetailsBeanList(kpiId, kpiMappingId, producerId, userDetailsId,
                    instanceMap);

            for (CompInstanceKpiDetailsBean bean : beanList) {
                log.debug("Adding comp instance kpi producer mapping: {}", bean);
                int id = new CompInstanceDataService().addNonGroupComponentInstanceKPI(bean, handle);

                if (id == -1) {
                    log.error("Error occurred while adding non group producer kpi instance mapping data: {} , " +
                            "into DB.", bean);
                    throw new ControlCenterException("Error occurred while adding non group producer kpi instance mapping");
                } else {
                    //The below block is used for rolling back integration test
                    if (MySQLConnectionManager.getInstance().isIntegrationTest() && id > 0) {
                        DBTestCache.addToCache("comp_instance_kpi_details", id);
                    }
                    log.debug("Added non group producer kpi instance mapping: {}", bean);
                }
            }
        }
    }

    private Map<Integer, InstanceMappingDetails> getMappedInstances(int accountId, int kpiId, int compVersionId) {
        List<CompInstClusterDetails> instanceList = MasterDataService.getCompInstanceDetails(accountId).stream()
                .filter(instance -> (instance.getCompVersionId() == compVersionId))
                .filter(instance -> (instance.getIsCluster() == 0))
                .collect(Collectors.toList());
        Map<Integer, InstanceMappingDetails> result = new HashMap<>();

        if (!instanceList.isEmpty()) {
            instanceList.forEach(instance -> {
                InstanceMappingDetails instanceMappingDetails = MasterCache
                        .getInstanceMappingDetails(instance.getInstanceId());

                if (instanceMappingDetails != null && instanceMappingDetails.getNonGroupKpi().stream().anyMatch(it -> (it.getMstKpiDetailsId() == kpiId))) {
                    result.put(instance.getInstanceId(), instanceMappingDetails);
                }
            });
        }
        return result;
    }

    private List<CompInstanceKpiDetailsBean> getinstanceKpiProducerMappingDetailsBeanList(int kpiId, int kpiMappingId,
                                                                                          int producerId,
                                                                                          String userDetailsId,
                                                                                          Map<Integer, InstanceMappingDetails> instanceMap) {
        log.trace("{} getinstanceKpiProducerMappingDetailsBeanList, kpiId: {}, kpiMappingId: {}, producerId: {}, " +
                        "userId: {}, mappedInstancescount: {}", Constants.INVOKED_METHOD, kpiId, kpiMappingId, producerId,
                userDetailsId, instanceMap.size());

        List<CompInstanceKpiDetailsBean> result = new ArrayList<>();
        instanceMap.forEach((instanceId, mappingDetails) -> {
            log.debug("mappedInstance: {} - {}", instanceId, mappingDetails);
            CompInstanceKpiDetailsBean temp = new CompInstanceKpiDetailsBean();
            temp.populate(instanceId, mappingDetails, kpiMappingId, producerId, kpiId, userDetailsId);
            result.add(temp);
        });
        return result;
    }

    private List<ProducerParameterBean> getProducerParameterBeanList(int producerId, String userId, ProducerDetails producerDetails) {
        if (producerDetails.getParameters().isEmpty()) {
            log.info("There are no parameters mapped, hence bean generation is skipped.");
            return new ArrayList<>();
        }

        List<ProducerParameterBean> result = new ArrayList<>();
        //This map is used to maintain order of different parameter types, which is required for insertion into table
        Map<String, Integer> order = new HashMap<>();
        int counter;
        for (ProducerParameterDetails param : producerDetails.getParameters()) {
            ProducerParameterBean bean = new ProducerParameterBean();
            String paramType = param.getParameterType().toUpperCase();

            if (order.containsKey(paramType)) {
                counter = order.get(paramType);
                order.put(paramType, ++counter);
            } else {
                counter = 1;
                order.put(paramType, counter);
            }

            bean.populate(producerId, userId, counter, param);
            log.debug("Generated parameter bean: {}", bean);
            result.add(bean);
        }

        return result;
    }

    private int addAttribute(int producerId, String userDetailsId, ProducerDetails producerDetails, Handle handle) throws ControlCenterException {
        ProducerType producerType = ProducerType.valueOf(producerDetails.getProducerType().trim());
        int attributeId = -1;
        String timestampString = DateTimeUtil.getTimeInGMT(new Date().getTime());
        String tableName = null;
        boolean validationFlag = false;

        switch (producerType) {
            case SCRIPT:
                tableName = "ssh_producer";
                SSHProducerTypeDetail sshProducerTypeDetail = new SSHProducerTypeDetail(producerId, timestampString,
                        timestampString, userDetailsId);
                validationFlag = sshProducerTypeDetail.populate(producerDetails.getProducerAttributes());
                if (validationFlag) {
                    attributeId = ProducerDataService.addSSHProducerAttributes(sshProducerTypeDetail, handle);
                }
                break;

            case WMI:
                tableName = "shell_producer";
                WMIProducerTypeDetail wmiProducerTypeDetail = new WMIProducerTypeDetail(producerId, timestampString,
                        timestampString, userDetailsId);
                validationFlag = wmiProducerTypeDetail.populate(producerDetails.getProducerAttributes());
                if (validationFlag) {
                    attributeId = ProducerDataService.addWMIProducerAttributes(wmiProducerTypeDetail, handle);
                }
                break;

            case HTTP:
                tableName = "httpd_producer";
                HTTPProducerTypeDetail httpProducerTypeDetail = new HTTPProducerTypeDetail(producerId, timestampString,
                        timestampString, userDetailsId);
                validationFlag = httpProducerTypeDetail.populate(producerDetails.getProducerAttributes());
                if (validationFlag) {
                    attributeId = ProducerDataService.addHTTPProducerAttributes(httpProducerTypeDetail, handle);
                }
                break;

            case JMX:
                tableName = "jmx_producer";
                JMXProducerTypeDetail jmxProducerTypeDetail = new JMXProducerTypeDetail(producerId, timestampString,
                        timestampString, userDetailsId);
                validationFlag = jmxProducerTypeDetail.populate(producerDetails.getProducerAttributes());
                if (validationFlag) {
                    attributeId = ProducerDataService.addJMXProducerAttributes(jmxProducerTypeDetail, handle);
                }
                break;

            case WAS:
                tableName = "was_producer";
                WASProducerTypeDetail wasProducerTypeDetail = new WASProducerTypeDetail(producerId, timestampString,
                        timestampString, userDetailsId);
                validationFlag = wasProducerTypeDetail.populate(producerDetails.getProducerAttributes());
                if (validationFlag) {
                    attributeId = ProducerDataService.addWASProducerAttributes(wasProducerTypeDetail, handle);
                }
                break;

            case JDBC:
                tableName = "jdbc_producer";
                JDBCProducerTypeDetail jdbcProducerTypeDetail = new JDBCProducerTypeDetail(producerId, timestampString,
                        timestampString, userDetailsId);
                validationFlag = jdbcProducerTypeDetail.populate(producerDetails.getProducerAttributes());
                if (validationFlag) {
                    attributeId = ProducerDataService.addJDBCProducerAttributes(jdbcProducerTypeDetail, handle);
                }
                break;

            case HTTP_JSON:
                tableName = "http_json_producer";
                HTTPJsonProducerTypeDetail httpJsonProducerTypeDetail = new HTTPJsonProducerTypeDetail(producerId,
                        timestampString, timestampString, userDetailsId);
                validationFlag = httpJsonProducerTypeDetail.populate(producerDetails.getProducerAttributes());
                if (validationFlag) {
                    attributeId = ProducerDataService.addHTTPJSONProducerAttributes(httpJsonProducerTypeDetail, handle);
                }
                break;

            case JPPF:
                tableName = "jppf_producer";
                JPPFProducerTypeDetail jppfProducerTypeDetail = new JPPFProducerTypeDetail(producerId,
                        timestampString, timestampString, userDetailsId);
                validationFlag = jppfProducerTypeDetail.populate(producerDetails.getProducerAttributes());
                if (validationFlag) {
                    attributeId = ProducerDataService.addJPPFProducerAttributes(jppfProducerTypeDetail, handle);
                }
                break;

            case EXTERNAL:
                // In current impl there is no attributes or parameters for external producers

            default:
                log.error("Unknown type received, failed to add attributes. Type: {}", producerType);
        }

        if (!validationFlag) {
            throw new ControlCenterException("Invalid attributes for provided, add attributes to DB failed");
        }

        //The below block is used for rolling back integration test
        if (MySQLConnectionManager.getInstance().isIntegrationTest() && attributeId > 0) {
            DBTestCache.addToCache(tableName, attributeId);
        }

        return attributeId;
    }
}
