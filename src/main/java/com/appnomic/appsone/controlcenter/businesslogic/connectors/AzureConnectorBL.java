package com.appnomic.appsone.controlcenter.businesslogic.connectors;

import com.appnomic.appsone.controlcenter.common.ConnectorConstants;
import com.appnomic.appsone.controlcenter.dao.mysql.connectors.AzureConnectorDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.connectors.ConnectorDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.*;
import com.appnomic.appsone.controlcenter.exceptions.FileUploadException;
import com.appnomic.appsone.controlcenter.pojo.connectors.*;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class AzureConnectorBL {

    public List<AzureApplicationDetails> getAzureApplicationDetails(File fileName) {
        List<AzureApplicationDetails> azureApplicationDetails = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro;
                if (sheet.getRow(i) != null) ro = sheet.getRow(i);
                else continue;
                if (ro.getCell(ConnectorConstants.AZURE_APPLICATION_ID_IDX) != null) {
                    AzureApplicationDetails azureApplicationDetail = AzureApplicationDetails.builder()
                            .id((int) ro.getCell(ConnectorConstants.AZURE_APPLICATION_ID_IDX).getNumericCellValue())
                            .applicationId(ro.getCell(ConnectorConstants.AZURE_APPLICATION_APPID_IDX).getStringCellValue())
                            .applicationKey(ro.getCell(ConnectorConstants.AZURE_APPLICATION_APPKEY_IDX).getStringCellValue())
                            .applicationInstanceName(ro.getCell(ConnectorConstants.AZURE_APPLICATION_APPINSTNAME_IDX).getStringCellValue())
                            .agentIdentifier(ro.getCell(ConnectorConstants.AZURE_APPLICATION_AGENT_IDENTIFIER_IDX).getStringCellValue())
                            .build();
                    log.info("this is no of row in system {}", ro.getPhysicalNumberOfCells());
                    azureApplicationDetails.add(azureApplicationDetail);
                }
            }
        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the Azure Application Data from excel file {}", ex.getMessage());
            throw new FileUploadException("Exception when reading the Azure Application Data from excel file");
        }
        return azureApplicationDetails;
    }

    public List<AzureResourceDetails> getAzureResourceDetails(File fileName) {
        List<AzureResourceDetails> azureResourceDetails = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro;
                if (sheet.getRow(i) != null) ro = sheet.getRow(i);
                else continue;
                if (ro.getCell(ConnectorConstants.AZURE_RESOURCE_ID_IDX) == null) break;
                AzureResourceDetails azureResourceDetail = AzureResourceDetails.builder()
                        .id((int) ro.getCell(ConnectorConstants.AZURE_RESOURCE_ID_IDX).getNumericCellValue())
                        .resourceName(ro.getCell(ConnectorConstants.AZURE_RESOURCE_NAME_IDX).getStringCellValue())
                        .resourceGroupName(ro.getCell(ConnectorConstants.AZURE_RESOURCE_GROUP_NAME_IDX).getStringCellValue())
                        .subscriptionId(ro.getCell(ConnectorConstants.AZURE_RESOURCE_SUBSCRIPTION_ID_IDX).getStringCellValue())
                        .tokenId((int) ro.getCell(ConnectorConstants.AZURE_RESOURCE_TOKEN_ID_IDX).getNumericCellValue())
                        .applicationInstanceName(ro.getCell(ConnectorConstants.AZURE_RESOURCE_APPINSTNAME_IDX).getStringCellValue())
                        .build();
                log.info("this is no of row in system {}", ro.getPhysicalNumberOfCells());
                azureResourceDetails.add(azureResourceDetail);
            }
        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the Azure Resource Data from excel file {}", ex.getMessage());
            throw new FileUploadException("Exception when reading the Azure Resource Data from excel file");
        }
        return azureResourceDetails;
    }

    public List<AzureTokenDetails> getAzureTokenDetails(File fileName) {
        List<AzureTokenDetails> azureTokenDetails = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro;
                if (sheet.getRow(i) != null) ro = sheet.getRow(i);
                else continue;
                if (ro.getCell(ConnectorConstants.AZURE_TOKEN_ID_IDX) == null) break;
                AzureTokenDetails azureTokenDetail = AzureTokenDetails.builder()
                        .id((int) ro.getCell(ConnectorConstants.AZURE_TOKEN_ID_IDX).getNumericCellValue())
                        .tenantId(ro.getCell(ConnectorConstants.AZURE_TOKEN_TENANT_ID_IDX).getStringCellValue())
                        .grantType(ro.getCell(ConnectorConstants.AZURE_TOKEN_GRANT_TYPE_IDX).getStringCellValue())
                        .clientId(String.valueOf(ro.getCell(ConnectorConstants.AZURE_TOKEN_CLIENT_ID_IDX).getNumericCellValue()))
                        .clientSecret(ro.getCell(ConnectorConstants.AZURE_TOKEN_CLIENT_SECRET_IDX).getStringCellValue())
                        .resourceName(ro.getCell(ConnectorConstants.AZURE_TOKEN_RESOURCE_NAME_IDX).getStringCellValue())
                        .build();
                log.info("this is no of row in system {}", ro.getPhysicalNumberOfCells());
                azureTokenDetails.add(azureTokenDetail);

            }
        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the Azure Resource Data from excel file {}", ex.getMessage());
            throw new FileUploadException("Exception when reading the Azure Resource Data from excel file");
        }
        return azureTokenDetails;
    }

    public List<AzureHealKpi> getAzureHealKpiDetails(File fileName) {
        List<AzureHealKpi> azureHealKpis = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro;
                if (sheet.getRow(i) != null) ro = sheet.getRow(i);
                else continue;
                if (ro.getCell(ConnectorConstants.AZURE_KPI_ID_IDX) == null) break;
                AzureHealKpi azureHealKpi = AzureHealKpi.builder()
                        .id((int) ro.getCell(ConnectorConstants.AZURE_KPI_ID_IDX).getNumericCellValue())
                        .kpiName(ro.getCell(ConnectorConstants.AZURE_KPI_NAME_IDX).getStringCellValue())
                        .kpiDomain(ro.getCell(ConnectorConstants.AZURE_KPI_DOMAIN_IDX).getStringCellValue())
                        .kpiType(ro.getCell(ConnectorConstants.AZURE_KPI_TYPE_IDX).getStringCellValue())
                        .kpiAggregator(ro.getCell(ConnectorConstants.AZURE_KPI_AGGREGATOR_IDX).getStringCellValue())
                        .healKpiId((int) ro.getCell(ConnectorConstants.AZURE_HEAL_KPI_ID_IDX).getNumericCellValue())
                        .healKpiName(ro.getCell(ConnectorConstants.AZURE_HEAL_KPI_NAME_IDX).getStringCellValue())
                        .healKpiIdentifier(ro.getCell(ConnectorConstants.AZURE_HEAL_KPI_IDENTIFIER_IDX).getStringCellValue())
                        .isGroupKpi((int) ro.getCell(ConnectorConstants.AZURE_HEAL_IS_GROUP_KPI_IDX).getNumericCellValue() == 1)
                        .healGroupName(ro.getCell(ConnectorConstants.AZURE_HEAL_GROUP_NAME_IDX).getStringCellValue())
                        .build();
                log.info("this is no of row in system {}", ro.getPhysicalNumberOfCells());
                azureHealKpis.add(azureHealKpi);
            }
        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the Sap Kpi Data from excel file {}", ex.getMessage());
            throw new FileUploadException("Exception when reading the Sap Kpi Data from excel file");
        }
        return azureHealKpis;
    }

    public List<HealAgentInstance> getSourceHealInstanceMappingFromFile(File fileName) {
        List<HealAgentInstance> healAgentInstances = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro = sheet.getRow(i);
                if (ro.getCell(ConnectorConstants.AZURE_AGENT_AZURE_INSTANCE_NAME_INDEX) == null)
                    break;
                HealAgentInstance agentInstance = HealAgentInstance.builder()
                        .sourceInstanceName(ro.getCell(ConnectorConstants.AZURE_AGENT_AZURE_INSTANCE_NAME_INDEX).getStringCellValue())
                        .agentIdentifier(ro.getCell(ConnectorConstants.AZURE_AGENT_NAME_INDEX).getStringCellValue())
                        .healInstanceName(ro.getCell(ConnectorConstants.AZURE_AGENT_HEAL_INSTANCE_NAME_INDEX).getStringCellValue())
                        .build();
                healAgentInstances.add(agentInstance);
            }

        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the Sap Instance data from excel file {}", ex.getMessage());
            throw new FileUploadException("Exception when reading the Sap Instance data from excel file");
        }
        return healAgentInstances;
    }


    public void addAzureHealKpis(List<AzureHealKpi> azureHealKpis) {
        AzureConnectorDataService azureConnectorDataService = new AzureConnectorDataService();
        ConnectorDataService connectorDataService = new ConnectorDataService();
        String schemaName = "dataadapter_azure";
        azureHealKpis.removeIf(x -> x.getKpiName().equals("") || x.getKpiName().equals(" ") || x.getKpiName() == null);
        List<HealKpi> healKpiList = azureHealKpis.stream()
                .map(this::getHealKpiDetails)
                .collect(Collectors.toList());
        List<AzureKpi> azureKpisList = azureHealKpis.stream()
                .map(this::getAzureKpiDetails)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<String> kpis = azureConnectorDataService.getAzureKpis();
        List<String> kpisHeal = azureConnectorDataService.getHealKpis();
        List<AzureKpi> updateList = azureKpisList.stream().filter(x -> kpis.contains(x.getKpiName())).collect(Collectors.toList());
        List<HealKpi> updateListHeal = healKpiList.stream().filter(x -> kpisHeal.contains(x.getKpiName())).collect(Collectors.toList());
        azureKpisList.removeAll(updateList);
        healKpiList.removeAll(updateListHeal);
        azureHealKpis.removeIf(x -> kpis.contains(x.getKpiName()));
        int[] ids = azureConnectorDataService.addAzureKpis(azureKpisList);
        azureConnectorDataService.updateAzureKpis(updateList);
        connectorDataService.updateHealKpis(schemaName, updateListHeal);
        int i = 0;
        for (AzureHealKpi healKpi : azureHealKpis) {
            if (i < ids.length)
                healKpi.setId(ids[i]);
            i++;
        }
        List<DomainToHealKpiMapping> domainToHealKpiMappings = azureHealKpis.stream()
                .map(this::getDomainToHealKpiMapping)
                .collect(Collectors.toList());
        connectorDataService.addHealKpi(schemaName, healKpiList);
        connectorDataService.addDomainToHealKpiMapping(schemaName, domainToHealKpiMappings);
    }

    public int[] addAzureApplicationDetails(List<AzureApplicationDetails> azureApplicationDetails) {
        AzureConnectorDataService azureConnectorDataService = new AzureConnectorDataService();
        Map<Integer, List<Integer>> applicationKpiMap = new HashMap<>();
        List<Integer> kpiIds = azureConnectorDataService.getAzureKpisList(ConnectorConstants.AZURE_APP_KPI);
        for (AzureApplicationDetails azureApplicationDetail : azureApplicationDetails) {
            applicationKpiMap.put(azureApplicationDetail.getId(), new ArrayList<>(kpiIds));
        }
        List<ApplicationToKpiMapping> azureApplicationToKpiMappings = getAzureApplicationToKpiMapping(applicationKpiMap);

        azureConnectorDataService.addAzureApplicationDetails(azureApplicationDetails);
        azureConnectorDataService.addAzureApplicationKpiMapping(azureApplicationToKpiMappings);
        return new int[]{};
    }

    public void addAzureResourceDetails(List<AzureResourceDetails> azureResourceDetails) {
        Map<Integer, List<Integer>> resourceKpiMap = new HashMap<>();
        AzureConnectorDataService azureConnectorDataService = new AzureConnectorDataService();
        for (AzureResourceDetails azureResourceDetail : azureResourceDetails) {
            resourceKpiMap.put(azureResourceDetail.getId(), new ArrayList<>(azureConnectorDataService.getAzureKpisList(ConnectorConstants.AZURE_RES_KPI)));
        }
        List<AzureResourceToKpiMapping> azureResourceToKpiMappings = getAzureResourceToKpiMapping(resourceKpiMap);
        azureConnectorDataService.addAzureResourceDetails(azureResourceDetails);
        azureConnectorDataService.addAzureResourceKpiMapping(azureResourceToKpiMappings);
    }

    public void addAzureTokenDetails(List<AzureTokenDetails> azureTokenDetails) {
        AzureConnectorDataService azureConnectorDataService = new AzureConnectorDataService();
        azureConnectorDataService.addAzureTokenDetails(azureTokenDetails);
    }

    public AzureKpi getAzureKpiDetails(AzureHealKpi azureHealKpi) {
        return AzureKpi.builder()
                .id(azureHealKpi.getId())
                .kpiName(azureHealKpi.getKpiName())
                .kpiDomain(azureHealKpi.getKpiDomain())
                .kpiType(azureHealKpi.getKpiType())
                .kpiAggregator(azureHealKpi.getKpiAggregator())
                .build();
    }

    public HealKpi getHealKpiDetails(AzureHealKpi azureHealKpi) {
        return HealKpi.builder()
                .id(azureHealKpi.getId())
                .kpiId(azureHealKpi.getHealKpiId())
                .kpiName(azureHealKpi.getHealKpiName())
                .kpiIdentifier(azureHealKpi.getHealKpiIdentifier())
                .groupName(azureHealKpi.getHealGroupName())
                .isGroupKpi(azureHealKpi.isGroupKpi() ? 1 : 0)
                .build();
    }

    public DomainToHealKpiMapping getDomainToHealKpiMapping(AzureHealKpi azureHealKpi) {
        return DomainToHealKpiMapping.builder()
                .id(azureHealKpi.getId())
                .domainName("azure")
                .sourceId(azureHealKpi.getId())
                .healIdentifier(azureHealKpi.getHealKpiIdentifier())
                .build();
    }

    public List<ApplicationToKpiMapping> getAzureApplicationToKpiMapping(Map<Integer, List<Integer>> details) {
        List<ApplicationToKpiMapping> azureApplicationToKpiMappings = new ArrayList<>();
        for (Map.Entry<Integer, List<Integer>> entry : details.entrySet()) {
            int applicationId = entry.getKey();
            for (Integer a : entry.getValue()) {
                azureApplicationToKpiMappings.add(
                        ApplicationToKpiMapping.builder()
                                .application_id(applicationId)
                                .kpi_id(a)
                                .build()
                );
            }
        }
        return azureApplicationToKpiMappings;
    }

    public List<AzureResourceToKpiMapping> getAzureResourceToKpiMapping(Map<Integer, List<Integer>> details) {
        List<AzureResourceToKpiMapping> azureResourceToKpiMappings = new ArrayList<>();
        for (Map.Entry<Integer, List<Integer>> entry : details.entrySet()) {
            int resourceId = entry.getKey();
            for (Integer a : entry.getValue()) {
                azureResourceToKpiMappings.add(
                        AzureResourceToKpiMapping.builder()
                                .resource_id(resourceId)
                                .kpi_id(a)
                                .build()
                );
            }
        }
        return azureResourceToKpiMappings;
    }

}
