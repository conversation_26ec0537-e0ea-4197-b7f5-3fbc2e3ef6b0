package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ComponentBean;
import com.appnomic.appsone.controlcenter.dao.mysql.KPIDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.*;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class KpiDetailsBL implements BusinessLogic<Integer, Integer, List<KpiDetailsPojo>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(KpiDetailsBL.class);

    @Override
    public UtilityBean<Integer> clientValidation(RequestObject request) throws ClientException {

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            LOGGER.error(UIMessages.AUTH_KEY_EMPTY);
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }

        String accountIdentifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(accountIdentifier)) {
            LOGGER.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        return UtilityBean.<Integer>builder()
                .accountIdentifier(accountIdentifier)
                .authToken(authToken)
                .build();
    }

    @Override
    public Integer serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());

        if (account == null) {
            LOGGER.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }

        return account.getId();
    }

    @Override
    public List<KpiDetailsPojo> process(Integer accountId) throws DataProcessingException {

        KPIDataService kpiDataService = new KPIDataService();
        List<KpiListBean> kpiDetails;
        Map<Integer, List<ComponentBean>> commonVersionVsComponentBeans;
        Map<Integer, List<ComputedKpiBean>> computedKpiVsSupportingKpis;
        Map<Integer, KpiCategoryMapping> kpiVsCategoryMapping = null;
        Map<Integer, Integer> instanceCountForComponentsMap;
        Map<Integer, Integer> kpiMappedToComputedKpiCountMap;

        try {
            kpiDetails = kpiDataService.getKpiList(accountId, null);
            if (!kpiDetails.isEmpty()) {
                List<Integer> kpiIds = kpiDetails.parallelStream().map(KpiListBean::getId).collect(Collectors.toList());
                kpiVsCategoryMapping = new BindInDataService().getCategoryDetailsForKpis(kpiIds)
                        .parallelStream().collect(Collectors.toMap(KpiCategoryMapping::getKpiId, Function.identity()));
            }

            List<ComponentBean> versionList = kpiDataService.getComponentDetailsForAccount(accountId, null);
            commonVersionVsComponentBeans = versionList.parallelStream().collect(Collectors.groupingBy(ComponentBean::getCommonVersionId));

            List<ComputedKpiBean> computedKpiDetails = kpiDataService.getComputedKpiDetails(accountId, null);
            computedKpiVsSupportingKpis = computedKpiDetails.parallelStream()
                    .collect(Collectors.groupingBy(ComputedKpiBean::getComputedKpiId));

            instanceCountForComponentsMap = new CompInstanceDataService().getActiveInstanceCountForComponents(accountId, null)
                    .parallelStream().collect(Collectors.toMap(CountBean::getId, CountBean::getCount));

            kpiMappedToComputedKpiCountMap = kpiDataService.getKPIsMappedToComputedKpi(accountId, null)
                    .parallelStream().collect(Collectors.toMap(CountBean::getId, CountBean::getCount));
        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }

        Map<Integer, KpiCategoryMapping> finalKpiVsCategoryMapping = kpiVsCategoryMapping;

        return kpiDetails.parallelStream().distinct().map(kpi -> {
            KpiDetailsPojo kpiDetailsPojo = new KpiDetailsPojo();
            kpiDetailsPojo.setId(kpi.getId());
            kpiDetailsPojo.setName(kpi.getName());
            kpiDetailsPojo.setIdentifier(kpi.getIdentifier());
            kpiDetailsPojo.setDataType(kpi.getDataType());
            kpiDetailsPojo.setValueType(kpi.getValueType());
            kpiDetailsPojo.setClusterAggregation(MasterCache.getMstSubTypeForSubTypeId(Integer.parseInt(kpi.getClusterAggregation())).getSubTypeName());
            kpiDetailsPojo.setInstanceAggregation(MasterCache.getMstSubTypeForSubTypeId(Integer.parseInt(kpi.getInstanceAggregation())).getSubTypeName());
            kpiDetailsPojo.setClusterOperation(kpi.getClusterOperation());
            kpiDetailsPojo.setRollupOperation(kpi.getRollupOperation());
            kpiDetailsPojo.setDescription(kpi.getDescription());
            kpiDetailsPojo.setKpiUnit(kpi.getKpiUnit());
            kpiDetailsPojo.setKpiType(kpi.getKpiType());
            kpiDetailsPojo.setStatus(kpi.getStatus());
            kpiDetailsPojo.setGroupKpiId(kpi.getGroupKpiId());
            kpiDetailsPojo.setGroupKpiName(kpi.getGroupKpiName());
            kpiDetailsPojo.setGroupKpiDescription(kpi.getGroupKpiDescription());
            kpiDetailsPojo.setGroupKpiDiscovery(kpi.getGroupKpiDiscovery());
            kpiDetailsPojo.setGroupKpiStandardType(kpi.getGroupKpiStandardType());
            kpiDetailsPojo.setCollectionInterval(kpi.getCollectionInterval());
            kpiDetailsPojo.setStandardType(kpi.getStandardType());
            kpiDetailsPojo.setAvailableForAnalytics(kpi.getAvailableForAnalytics());
            kpiDetailsPojo.setIsMappedToComputedKPI(kpiMappedToComputedKpiCountMap.getOrDefault(kpi.getId(), 0));
            kpiDetailsPojo.setCronExpression(kpi.getCronExpression());
            kpiDetailsPojo.setDeltaPerSec(kpi.getDeltaPerSec());
            kpiDetailsPojo.setResetDeltaValue(kpi.getResetDeltaValue());

            KpiCategoryMapping kpiCategoryDetails = finalKpiVsCategoryMapping.get(kpi.getId());
            if (Objects.nonNull(kpiCategoryDetails)) {
                kpiDetailsPojo.setCategoryName(kpiCategoryDetails.getCategoryName());
                kpiDetailsPojo.setWorkloadCategory(kpiCategoryDetails.getWorkLoad() == 1);
                kpiDetailsPojo.setCategoryId(kpiCategoryDetails.getCategoryId());
            }

            ComponentListPojo componentListPojo = new ComponentListPojo();
            componentListPojo.setId(kpi.getComponentId());
            componentListPojo.setTypeId(kpi.getComponentTypeId());

            ComponentKpiDetail component = new CompInstanceDataService().getComponentDetails(kpi.getComponentId());
            componentListPojo.setName((component == null) ? "" : component.getName());

            componentListPojo.setInstanceCount(instanceCountForComponentsMap.getOrDefault(kpi.getComponentId(), 0));
            kpiDetailsPojo.setComponent(componentListPojo);

            List<ComponentBean> versions = commonVersionVsComponentBeans.getOrDefault(kpi.getCommonVersionId(), new ArrayList<>());

            if (!versions.isEmpty()) {
                List<SupportedVersionPojo> supportedVersionsList = versions.parallelStream()
                        .map(v -> SupportedVersionPojo.builder()
                                .id(v.getComponentVersionId())
                                .name(v.getComponentVersionName())
                                .build())
                        .collect(Collectors.toList());

                CommonVersionPojo commonVersionPojo = CommonVersionPojo.builder()
                        .id(versions.get(0).getCommonVersionId())
                        .name(versions.get(0).getCommonVersionName())
                        .supportedVersions(supportedVersionsList)
                        .build();

                kpiDetailsPojo.setComponentCommonVersion(commonVersionPojo);
            } else {
                kpiDetailsPojo.setComponentCommonVersion(new CommonVersionPojo());
            }

            List<ComputedKpiBean> computedList = computedKpiVsSupportingKpis.getOrDefault(kpi.getId(), new ArrayList<>());

            if (!computedList.isEmpty()) {
                List<Integer> computedKpiList = computedList.parallelStream()
                        .map(ComputedKpiBean::getKpiDetailsId)
                        .collect(Collectors.toList());

                ComputedKpiPojo computedKpiPojo = new ComputedKpiPojo();
                computedKpiPojo.setKpisUsed(computedKpiList);
                computedKpiPojo.setFormula(computedList.get(0).getFormula());
                computedKpiPojo.setDisplayFormula(computedList.get(0).getDisplayFormula());

                kpiDetailsPojo.setComputedKpiDetails(computedKpiPojo);
            }

            return kpiDetailsPojo;

        }).sorted(Comparator.comparing((KpiDetailsPojo kpiDetailsPojo) -> kpiDetailsPojo.getComponent().getName(), Comparator.nullsLast(Comparator.reverseOrder()))
                .thenComparing(kpiDetailsPojo -> kpiDetailsPojo.getComponentCommonVersion().getName(), Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());
    }


}
