package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.TransactionDataService;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TransactionCountBL implements BusinessLogic<Integer, UtilityBean<Integer>, TransactionCountBean> {
    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionCountBL.class);
    private static final Integer ONE = 1;

    @Override
    public UtilityBean<Integer> clientValidation(RequestObject requestObject) throws ClientException {
        LOGGER.trace("Inside Client validation");

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authKey)) {
            LOGGER.error(UIMessages.AUTH_KEY_EMPTY);
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }

        String serviceId = requestObject.getParams().get(Constants.SERVICE_ID);
        if (StringUtils.isEmpty(serviceId.trim())) {
            LOGGER.error(UIMessages.INVALID_SERVICE);
            throw new ClientException(UIMessages.INVALID_SERVICE);
        }
        try {
            if(Integer.parseInt(serviceId.trim()) < ONE)
            {
                LOGGER.error(UIMessages.INVALID_SERVICE);
                throw new ClientException(UIMessages.INVALID_SERVICE);
            }
        }catch (NumberFormatException e)
        {
            LOGGER.error(UIMessages.INVALID_SERVICE);
            throw new ClientException(UIMessages.INVALID_SERVICE);
        }

        return UtilityBean.<Integer>builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .pojoObject(Integer.parseInt(serviceId.trim()))
                .build();
    }

    @Override
    public UtilityBean<Integer> serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        LOGGER.trace("Inside Server validation");
        long st = System.currentTimeMillis();
        UserAccountBean userAccountBean;
        try {
            userAccountBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            String err = e.getSimpleMessage();
            LOGGER.error(err);
            throw new ServerException(err);
        }
        ControllerBean controllerBean = new ControllerDataService().getControllerById(utilityBean.getPojoObject(), userAccountBean.getAccount().getId(), null);
        ViewTypes subTypeBean = MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT, Constants.SERVICES_CONTROLLER_TYPE);
        if (controllerBean == null || controllerBean.getControllerTypeId() != subTypeBean.getSubTypeId()) {
            LOGGER.error(UIMessages.INVALID_SERVICE_ACCOUNT + "{}", userAccountBean.getAccount().getId());
            throw new ServerException(UIMessages.INVALID_SERVICE_ACCOUNT);
        }
        utilityBean.setAccount(userAccountBean.getAccount());
        LOGGER.trace("Server validation took time {}", System.currentTimeMillis() - st);
        return utilityBean;
    }

    @Override
    public TransactionCountBean process(UtilityBean<Integer> bean) throws DataProcessingException {
        long st = System.currentTimeMillis();

        TransactionCountBean serviceTransactionCount = new TransactionDataService()
                .getServiceTransactionCount(bean.getPojoObject(), bean.getAccount().getId());

        LOGGER.trace("Transaction fetch time {}", System.currentTimeMillis() - st);
        return serviceTransactionCount;
    }
}
