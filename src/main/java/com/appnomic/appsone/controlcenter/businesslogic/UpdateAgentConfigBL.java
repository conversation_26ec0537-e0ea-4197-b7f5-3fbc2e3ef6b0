package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.TagsDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.TagMappingBean;
import com.appnomic.appsone.controlcenter.dao.redis.AgentRepo;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.Agent;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.TagMappingDetails;
import com.appnomic.appsone.controlcenter.service.ComponentAgentConfigService;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.BasicAgentEntity;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.Tags;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.utils.CollectionUtils;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> Kudva on 28/10/2021
 */
public class UpdateAgentConfigBL implements BusinessLogic<Agent, AgentBean, IdPojo> {
    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateAgentConfigBL.class);
    AgentRepo agentRepo = new AgentRepo();

    @Override
    public UtilityBean<Agent> clientValidation(RequestObject requestObject) throws ClientException {
        String authToken = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }

        if (StringUtils.isEmpty(requestObject.getBody())) {
            LOGGER.error("Request body is empty.");
            throw new ClientException("Request body is empty.");
        }
        String agentIdStr = requestObject.getQueryParams().get(Constants.AGENT_ID)[0];
        if (StringUtils.isEmpty(agentIdStr)) {
            LOGGER.error("Agent Id is null or empty.");
            throw new ClientException("Agent Id is null or empty.");
        }

        int agentId;
        try {
            agentId = Integer.parseInt(agentIdStr);
        } catch (NumberFormatException e) {
            LOGGER.error("Agent Id {} should be a positive integer.", agentIdStr);
            throw new ClientException(String.format("Agent Id [%s] should be a positive integer.", agentIdStr));
        }

        Agent agent;
        try {
            agent = new ObjectMapper().readValue(requestObject.getBody(),
                    new TypeReference<Agent>() {
                    });
            agent.validate();
            if (!agent.validateForUpdate()) {
                LOGGER.error("Request validation failure.");
                throw new ClientException("Request validation failure. Kindly check the logs.");
            }
        } catch (IOException e) {
            LOGGER.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }catch (Exception e) {
            throw new ClientException(e.getMessage());
        }
        agent.setId(agentId);

        return UtilityBean.<Agent>builder()
                .accountIdentifier(identifier)
                .pojoObject(agent)
                .authToken(authToken)
                .build();
    }

    @Override
    public AgentBean serverValidation(UtilityBean<Agent> utilityBean) throws ServerException {
        UserAccountBean userAccBean;
        try {
            userAccBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            throw new ServerException(e.getMessage());
        }

        Agent agent = utilityBean.getPojoObject();
        int accountId = userAccBean.getAccount().getId();
        String accountIdentifier = userAccBean.getAccount().getIdentifier();
        boolean agentIdExists = AgentDataService.isAgentIdExists(agent.getId());
        if (!agentIdExists) {
            LOGGER.error("Agent to be updated has invalid Id: {}", agent.getId());
            throw new ServerException(String.format("Agent to be updated has invalid Id:: [%s]", agent.getId()));
        }

        boolean agentIdetifierExists = AgentDataService.isAgentIdetifierExists(agent.getUniqueToken());
        if (!agentIdetifierExists) {
            LOGGER.error("Agent to be updated has invalid identifier: {}", agent.getUniqueToken());
            throw new ServerException(String.format("Agent to be updated has invalid identifier:: [%s]", agent.getUniqueToken()));
        }
        AgentBean agentBean = AgentDataService.getAgentBeanDataForName(agent.getName());
        if(agentBean != null && !agent.getUniqueToken().equals(agentBean.getUniqueToken()) ) {
            LOGGER.error("Agent name: {} already exists", agent.getName());
            throw new ServerException(String.format("Agent name: [%s] already exists.", agent.getName()));
        }
        if (!ValidationUtils.isValidServiceId(accountIdentifier, agent.getServiceId())) {
            LOGGER.error("Service Id {} is invalid.", agent.getServiceId());
            throw new ServerException(String.format("Service Id [%s] is invalid.", agent.getServiceId()));
        }

        String serviceIdentifier = null;
        if (agent.getServiceId() != 0) {
            ControllerBean controllerBean = new ControllerDataService().getControllerById(agent.getServiceId(), accountId, null);
            serviceIdentifier = controllerBean.getIdentifier();
        }
        return AgentBean.builder()
                .id(agent.getId())
                .name(agent.getName())
                .uniqueToken(agent.getUniqueToken())
                .subType(agent.getSubType())
                .status(agent.getStatus())
                .hostAddress(agent.getHostAddress())
                .mode(agent.getMode())
                .description(agent.getDescription())
                .serviceId(agent.getServiceId())
                .serviceIdentifier(serviceIdentifier)
                .userDetailsId(userAccBean.getUserId())
                .accountId(accountId)
                .agentMappingDetails(agent.getAgentMappingDetails())
                .addedDataSources(agent.getAddedDataSources())
                .deletedDataSources(agent.getDeletedDataSources())
                .accountIdentifier(accountIdentifier)
                .build();
    }

    @Override
    public IdPojo process(AgentBean agentConfigBean) throws DataProcessingException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            return dbi.inTransaction((conn, status) -> updateAgentConfig(agentConfigBean, conn));
        } catch (Exception e) {
            LOGGER.error("Unable to update agent data. Details: ", e);
            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
            } else {
                throw e;
            }
        }
    }

    private IdPojo updateAgentConfig(AgentBean agentConfigBean, Handle handle) throws DataProcessingException {
        updateAgent(agentConfigBean, handle);

        int tagId = MasterCache.getTagDetails(Constants.CONTROLLER_TAG).getId();
        TagMappingDetails tagMappingData = TagsDataService.getTagMappingData(tagId, agentConfigBean.getId(), Constants.AGENT_TABLE, agentConfigBean.getAccountId());
        if (agentConfigBean.getServiceId() != 0) {
            if (tagMappingData == null) {
                addAgentServiceMapping(agentConfigBean, tagId, handle);
            } else {
                updateAgentServiceMapping(agentConfigBean, tagMappingData.getId(), handle);
            }
        } else {
            if (tagMappingData != null) {
                deleteAgentServiceMapping(tagMappingData.getId(), handle);
            }
        }
        if (agentConfigBean.getAddedDataSources().size() > 0 || agentConfigBean.getDeletedDataSources().size() > 0) {
            TagDetailsBean tagDetails = MasterCache.getTagDetails(Constants.AGENT_DATA_SOURCES);
            agentConfigBean.getAddedDataSources().parallelStream()
                    .forEach(dataSource -> {
                        String date = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
                        AgentDataService.addAgentDataSource(TagMappingBean.builder()
                                .tagId(tagDetails.getId())
                                .objectId(agentConfigBean.getId())
                                .objectRefTable(Constants.AGENT_TABLE)
                                .tagKey("Name")
                                .tagValue(dataSource)
                                .accountId(agentConfigBean.getAccountId())
                                .userDetailsId(agentConfigBean.getUserDetailsId())
                                .createdTime(date)
                                .updatedTime(date)
                                .build(), handle);
                    });
            agentConfigBean.getDeletedDataSources().parallelStream()
                    .forEach(dataSource -> {
                        TagMappingDetails tagMapping = TagsDataService.getTagMappingDataByTagVal(tagDetails.getId(), agentConfigBean.getId(), Constants.AGENT_TABLE, agentConfigBean.getAccountId(), dataSource);
                        TagsDataService.deleteTagMapping(tagMapping.getId(), handle);
                    });
        }
        if (agentConfigBean.getAgentMappingDetails() != null) {
            updateComponentAgent(agentConfigBean, handle);
        }
        updateAgentForAccountIdentifierInRedis(agentConfigBean);
        updateAgentInRedis(agentConfigBean);
        updateAgentServiceMappingInRedis(agentConfigBean);
        return IdPojo.builder()
                .id(agentConfigBean.getId())
                .name(agentConfigBean.getName())
                .build();
    }

    private void updateAgent(AgentBean agentConfigBean, Handle handle) throws DataProcessingException {
        Timestamp timestamp;
        try {
            timestamp = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
        } catch (ParseException e) {
            LOGGER.error("Error in adding agent. Details: {}", e.getMessage(), e);
            throw new DataProcessingException("Error in adding agent.");
        }
        agentConfigBean.setUpdatedTime(timestamp);
        int id = AgentDataService.updateAgentConfig(agentConfigBean, handle);
        if (id == 0) {
            throw new DataProcessingException("Unable to update agent data for agent name-" + agentConfigBean.getName());
        }
    }

    private void addAgentServiceMapping(AgentBean agentConfigBean, int tagId, Handle handle) throws DataProcessingException {
        int id = TagMappingBL.addTagMapping(tagId, agentConfigBean.getId(), Constants.AGENT_TABLE, String.valueOf(agentConfigBean.getServiceId()), agentConfigBean.getServiceIdentifier(), agentConfigBean.getUserDetailsId(), agentConfigBean.getAccountId(), handle);
        if(id == -1) {
            throw new DataProcessingException("Error in adding agent service mapping");
        }
    }

    private void updateAgentServiceMapping(AgentBean agentConfigBean, int tagMappingId, Handle handle) throws DataProcessingException {
        int id = TagMappingBL.updateTagMapping(tagMappingId, String.valueOf(agentConfigBean.getServiceId()), agentConfigBean.getServiceIdentifier(), agentConfigBean.getUserDetailsId(), handle);
        if (id == -1) {
            throw new DataProcessingException("Error in updating agent service mapping");
        }
    }

    private void deleteAgentServiceMapping(int tagMappingId, Handle handle) throws DataProcessingException {
        int id = TagsDataService.deleteTagMapping(tagMappingId, handle);
        if (id == 0) {
            LOGGER.error("Failed to delete tag mapping data for Id: {}", id);
            throw new DataProcessingException("Error in updating agent service mapping");
        }
    }

    private void updateComponentAgent(AgentBean agentConfigBean, Handle handle) throws DataProcessingException {
        try {
            new ComponentAgentConfigService().updateComponentAgent(agentConfigBean, handle);
        } catch (ControlCenterException e) {
            LOGGER.error("Error in updating component agent. Details: {}", e.getMessage(), e);
            throw new DataProcessingException("Error in updating component agent. Details: {}");
        }
    }

    private void updateAgentForAccountIdentifierInRedis(AgentBean agentConfigBean) {
        List<BasicAgentEntity> existingAgentList = agentRepo.getAgents(agentConfigBean.getAccountIdentifier());
        if (existingAgentList.parallelStream().noneMatch(agent -> agent.getId() == agentConfigBean.getId())) {
            LOGGER.error("Agent detail not found for agentId: {} and accountIdentifier: {}", agentConfigBean.getId(), agentConfigBean.getAccountIdentifier());
            return;
        }
        BasicAgentEntity agentEntity = existingAgentList.parallelStream().filter(agent -> agent.getId() == agentConfigBean.getId()).findFirst().orElse(null);
        if(agentEntity == null){
            LOGGER.error("Agent detail not found agentId: {}" , agentConfigBean.getId());
        }
        agentEntity.setAccountId(agentConfigBean.getAccountId());
        agentEntity.setLastModifiedBy(agentConfigBean.getUpdatedBy());
        agentEntity.setCommunicationInterval(agentConfigBean.getCommunicationInterval());
        agentEntity.setUpdatedTime(String.valueOf(agentConfigBean.getUpdatedTime()));
        agentEntity.setMode(agentConfigBean.getMode());
        agentEntity.setName(agentConfigBean.getName());
        agentRepo.updateAgents(agentConfigBean.getAccountIdentifier(), existingAgentList);
    }

    private void updateAgentInRedis(AgentBean agentConfigBean) {
        com.heal.configuration.pojos.Agent existingAgent = agentRepo.getAgent(agentConfigBean.getUniqueToken());
        if (existingAgent != null) {
            if(agentConfigBean.getName() != null){
                existingAgent.setName(agentConfigBean.getName());
            }
           if (agentConfigBean.getMode() != null){
               existingAgent.setMode(agentConfigBean.getMode());
           }
           if (agentConfigBean.getUpdatedBy() != null){
               existingAgent.setLastModifiedBy(agentConfigBean.getUpdatedBy());
           }
            existingAgent.setStatus(agentConfigBean.getStatus());
            existingAgent.setLastModifiedBy(agentConfigBean.getUpdatedBy());
            Map<Boolean, List<Tags>> existingTags = existingAgent.getTags().parallelStream().collect(Collectors.partitioningBy(p -> p.getType().equalsIgnoreCase(Constants.CONTROLLER_TAG)));
            List<Tags> serviceTags = existingTags.get(true);
            if(CollectionUtils.isEmpty(serviceTags)){
                Tags service = Tags.builder()
                        .type(Constants.CONTROLLER_TAG)
                        .key(String.valueOf(agentConfigBean.getServiceId()))
                        .value(agentConfigBean.getServiceIdentifier())
                        .build();
                serviceTags.add(service);
            }

            List<Tags> updatedServiceTag = serviceTags.parallelStream().peek(p -> {
                if(agentConfigBean.getServiceId() != 0 && agentConfigBean.getServiceIdentifier() != null ) {
                    p.setKey(String.valueOf(agentConfigBean.getServiceId()));
                    p.setValue(agentConfigBean.getServiceIdentifier());
                }
            }).collect(Collectors.toList());

            List<Tags> updatedTags = new ArrayList<>();
           updatedTags.addAll(updatedServiceTag);

            List<Tags> agentDataSource = existingTags.get(false);
            if (agentConfigBean.getAddedDataSources() != null){
                TagDetailsBean tagDetails = MasterCache.getTagDetails(Constants.AGENT_DATA_SOURCES);
                for(String resource : agentConfigBean.getAddedDataSources()){
                  if(agentDataSource.parallelStream().anyMatch(r -> r.getValue().equalsIgnoreCase(resource)))  {
                      LOGGER.debug("The agent details for getName: {} and getUpdatedBy: {} already exists", agentConfigBean.getName(), agentConfigBean.getUpdatedBy());
                  }else{
                          Tags newTag = Tags.builder()
                              .key("Name")
                              .value(resource)
                              .type(tagDetails != null ? tagDetails.getName() : null)
                              .build();
                      agentDataSource.add(newTag);
                  }
                }
                updatedTags.addAll(agentDataSource);
            }
            existingAgent.setTags(updatedTags);
            if(CollectionUtils.isEmpty(agentConfigBean.getDeletedDataSources())){
                List<Tags> newTagList  = existingAgent.getTags().parallelStream().filter(f -> !agentConfigBean.getDeletedDataSources().contains(f.getValue())).collect(Collectors.toList());
                existingAgent.setTags(newTagList);
            }
            existingAgent.setAccountId(agentConfigBean.getAccountId());
            existingAgent.setUpdatedTime(String.valueOf(agentConfigBean.getUpdatedTime()));
            existingAgent.setMode(agentConfigBean.getMode());
            existingAgent.setCommunicationInterval(agentConfigBean.getCommunicationInterval());
            if (existingAgent.getTypeName().equals(Constants.COMPONENT_AGENT_SUB_TYPE) && agentConfigBean.getAgentMappingDetails() != null) {
                existingAgent.getAgentDataCollectionDetails().setName(agentConfigBean.getAgentMappingDetails().getDataCommunication().getName());
                existingAgent.getAgentDataCollectionDetails().setDataAddress(agentConfigBean.getAgentMappingDetails().getDataCommunication().getHost());
                existingAgent.getAgentDataCollectionDetails().setDataPort(agentConfigBean.getAgentMappingDetails().getDataCommunication().getPort());
                existingAgent.getAgentDataCollectionDetails().setDataProtocol(agentConfigBean.getAgentMappingDetails().getDataCommunication().getProtocol());
                existingAgent.getAgentDataCollectionDetails().setTimeoutMultiplier(agentConfigBean.getAgentMappingDetails().getTimeoutMultiplier());
                existingAgent.getAgentDataCollectionDetails().setDataEndPoint(agentConfigBean.getAgentMappingDetails().getDataCommunication().getEndpoint());
            }
        }
        agentRepo.updateAgentDetailsForAgentIdentifier(existingAgent);
    }

    private void updateAgentServiceMappingInRedis(AgentBean agentConfigBean){
        BasicEntity serviceDetail = BasicEntity.builder()
                .id(agentConfigBean.getServiceId())
                .status(agentConfigBean.getStatus())
                .name(agentConfigBean.getServiceIdentifier())
                .accountId(agentConfigBean.getAccountId())
                .identifier(agentConfigBean.getServiceIdentifier())
                .lastModifiedBy(String.valueOf(agentConfigBean.getUserDetailsId()))
                .createdTime(String.valueOf(agentConfigBean.getUpdatedTime()))
                .updatedTime(String.valueOf(agentConfigBean.getUpdatedTime()))
                .build();
        List<BasicEntity> existingAgentList = new ArrayList<>();
        existingAgentList.add(serviceDetail);
        agentRepo.updateServices(agentConfigBean.getAccountIdentifier(), agentConfigBean.getUniqueToken(), existingAgentList);
        }
    }




