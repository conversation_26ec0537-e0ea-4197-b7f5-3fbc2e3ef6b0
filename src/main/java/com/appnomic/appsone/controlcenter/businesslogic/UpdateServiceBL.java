package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.ServiceBean;
import com.appnomic.appsone.controlcenter.beans.TagDetailsBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.TagsDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.redis.AccountRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ApplicationRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.ServicePojo;
import com.appnomic.appsone.controlcenter.pojo.TimezoneDetail;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.Service;
import com.heal.configuration.pojos.Tags;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.util.*;

@Slf4j
public class UpdateServiceBL implements BusinessLogic<ServicePojo, ServiceBean, IdPojo> {

    private static final ControllerDataService CONTROLLER_DATA_SERVICE = new ControllerDataService();

    @Override
    public UtilityBean<ServicePojo> clientValidation(RequestObject request) throws ClientException {

        if (StringUtils.isEmpty(request.getBody())) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }

        String serviceIdentifier = request.getParams().get(Constants.SERVICE_IDENTIFIER);
        if (serviceIdentifier == null || serviceIdentifier.trim().isEmpty()) {
            log.error("Service identifier is null or empty.");
            throw new ClientException("Service identifier is null or empty.");
        }

        String requestBody = request.getBody();
        ServicePojo service;
        try {
            service = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestBody, new TypeReference<ServicePojo>() {
            });
        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        if (!service.validateForUpdate()) {
            log.error("Validation failure of details provided");
            throw new ClientException(UIMessages.INVALID_REQUEST);
        }

        service.setIdentifier(serviceIdentifier);

        return UtilityBean.<com.appnomic.appsone.controlcenter.pojo.ServicePojo>builder()
                .authToken(authToken)
                .accountIdentifier(identifier)
                .pojoObject(service)
                .build();
    }

    @Override
    public ServiceBean serverValidation(UtilityBean<ServicePojo> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be " +
                    "invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        int accountId = account.getId();
        ServicePojo service = utilityBean.getPojoObject();

        ControllerBean existingServiceDetails = CONTROLLER_DATA_SERVICE.getControllerByIdentifierOrName(service.getIdentifier(), null, null);
        if (existingServiceDetails == null) {
            log.error("Service identifier is invalid");
            throw new ServerException("Service identifier is invalid");
        }

        if (!StringUtils.isEmpty(service.getName())) {
            try {
                if (CONTROLLER_DATA_SERVICE.getServicesForAccount(accountId, null).parallelStream()
                        .anyMatch(c -> c.getName().equals(service.getName().trim()))) {
                    log.error("Service already exists with this name. {}", service.getName());
                    throw new ServerException("Service already exists with this name");
                }
            } catch (ControlCenterException e) {
                throw new ServerException(e.getMessage());
            }
        }

        TimezoneDetail timezone = null;
        if (!StringUtils.isEmpty(service.getTimezone())) {
            List<TimezoneDetail> timezoneDetails = MasterCache.getTimezones();
            Optional<TimezoneDetail> optional = timezoneDetails.parallelStream().filter(t -> t.getTimeZoneId()
                    .equalsIgnoreCase(service.getTimezone())).findAny();
            if (optional.isPresent()) {
                timezone = optional.get();
            } else {
                log.error("No active timezone found with timezone id: {}", service.getTimezone());
                throw new ServerException("No active timezone found with timezone id: " + service.getTimezone());
            }
        }

        List<Integer> appIds = new ArrayList<>();
        if (service.getAppIdentifiers() != null && !service.getAppIdentifiers().isEmpty()) {
            for (String appIdentifier : service.getAppIdentifiers()) {
                ControllerBean controllerBean = CONTROLLER_DATA_SERVICE.getControllerByIdentifierOrName(appIdentifier.trim(), null, null);
                if (controllerBean == null || controllerBean.getAccountId() != accountId) {
                    String err = "App Identifier '" + appIdentifier + "' does not exist.";
                    log.error(err);
                    throw new ServerException(err);
                }
                appIds.add(controllerBean.getId());
            }
        }

        return ServiceBean.builder()
                .id(existingServiceDetails.getId())
                .name(StringUtils.isEmpty(service.getName()) ? null : service.getName().trim())
                .identifier(existingServiceDetails.getIdentifier())
                .layer(service.getLayer())
                .type(service.getType())
                .appIds(appIds)
                .accountId(accountId)
                .userId(userId)
                .timezoneDetail(timezone)
                .accountIdentifier(utilityBean.getAccountIdentifier())
                .build();
    }

    @Override
    public IdPojo process(ServiceBean serviceBean) throws DataProcessingException {
        try {
            return MySQLConnectionManager.getInstance().getHandle()
                    .inTransaction((conn, status) -> updateServiceDetails(serviceBean, conn));
        } catch (Exception e) {
            log.error("Error : ", e);
            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw (DataProcessingException) Throwables.getRootCause(e);
            } else {
                throw e;
            }
        }
    }

    private IdPojo updateServiceDetails(ServiceBean serviceBean, Handle handle) throws ControlCenterException {

        Map<String, List<Integer>> allPayloadAppIds = new HashMap<>();

        if (!StringUtils.isEmpty(serviceBean.getName())) {
            CONTROLLER_DATA_SERVICE.editControllerName(serviceBean.getId(), serviceBean.getName(), DateTimeUtil.getTimeInGMT(System.currentTimeMillis()), handle);
        }

        if (serviceBean.getAppIds().size() > 0) {
            List<Integer> appIds = serviceBean.getAppIds();
            List<Integer> deleteAppMapping = new ArrayList<>();
            List<Integer> existingApps = CONTROLLER_DATA_SERVICE.getApplicationIdsForService(serviceBean.getId());
            allPayloadAppIds.put("existingAppId", existingApps);
            existingApps.parallelStream().forEach(id -> {
                if (!appIds.contains(id)) {
                    deleteAppMapping.add(id);
                    allPayloadAppIds.put("deleteAppIds", deleteAppMapping);
                } else {
                    appIds.remove(id);
                }
            });

            TagDetailsBean controllerTagDetails = MasterCache.getTagDetails(Constants.CONTROLLER_TAG);
            if (controllerTagDetails == null) {
                log.error("Tag details unavailable for 'Controller'");
                throw new ControlCenterException("Tag details unavailable for 'Controller'");
            }

            int controllerTagId = controllerTagDetails.getId();
            for (int app : appIds) {
                int id = TagMappingBL.addTagMapping(controllerTagId, app, Constants.CONTROLLER, String.valueOf(serviceBean.getId()),
                        serviceBean.getIdentifier(), serviceBean.getUserId(), serviceBean.getAccountId(), handle);
                if (id == -1) {
                    String err = "Unable to create service - application mapping.";
                    log.error(err);
                    throw new ControlCenterException(err);
                }
            }
            for (int app : deleteAppMapping) {
                TagsDataService.deleteTag(controllerTagId, app, String.valueOf(serviceBean.getId()), Constants.CONTROLLER,
                        serviceBean.getAccountId(), handle);
            }
        }

        if (serviceBean.getLayer() != null) {
            String err = "Unable to save layer details for service.";

            TagDetailsBean layerTagDetails = MasterCache.getTagDetails(Constants.LAYER_TAG);
            if (layerTagDetails == null) {
                log.error("Tag details unavailable for 'Layer' tag");
                throw new ControlCenterException("Tag details unavailable for 'Layer' tag");
            }

            int layerTagId = layerTagDetails.getId();

            int tagMappingId = TagsDataService.deleteTagMapping(layerTagId, serviceBean.getId(), Constants.CONTROLLER,
                    serviceBean.getAccountId(), handle);
            if (tagMappingId == -1) {
                log.error(err);
                throw new ControlCenterException(err);
            }
            if (serviceBean.getLayer().trim().length() > 0) {
                int id = TagMappingBL.addTagMapping(layerTagId, serviceBean.getId(), Constants.CONTROLLER, Constants.LAYER_DEFAULT,
                        serviceBean.getLayer().trim(), serviceBean.getUserId(), serviceBean.getAccountId(), handle);
                if (id == -1) {
                    log.error(err);
                    throw new ControlCenterException(err);
                }
            }
        }
        if (serviceBean.getType() != null) {
            String err = "Unable to save Type details for service.";

            TagDetailsBean serviceTypeTagDetails = MasterCache.getTagDetails(Constants.SERVICE_TYPE_TAG);
            if (serviceTypeTagDetails == null) {
                log.error("Tag details unavailable for 'Type' tag");
                throw new ControlCenterException("Tag details unavailable for 'Type' tag");
            }

            int serviceTypeTagId = serviceTypeTagDetails.getId();
            String type;
            if (serviceBean.getType().equals(Constants.KUBERNETES)) {
                type = Constants.NON_KUBERNETES;
            } else {
                type = Constants.KUBERNETES;
            }
            int tagMappingId = TagsDataService.deleteAgentServiceTagMapping(serviceTypeTagId, serviceBean.getId(), Constants.CONTROLLER, Constants.SERVICE_TYPE_DEFAULT, type,
                    serviceBean.getAccountId(), handle);
            if (tagMappingId == -1) {
                log.error(err);
                throw new ControlCenterException(err);
            }
            if (serviceBean.getType().trim().length() > 0) {
                int id = TagMappingBL.addTagMapping(serviceTypeTagId, serviceBean.getId(), Constants.CONTROLLER,
                        Constants.SERVICE_TYPE_DEFAULT, serviceBean.getType(), serviceBean.getUserId(), serviceBean.getAccountId(), handle);
                if (id == -1) {
                    log.error(err);
                    throw new ControlCenterException(err);
                }
            }
        }

        if (serviceBean.getTimezoneDetail() != null) {
            String err = "Unable to save timezone details for service.";

            TagDetailsBean timezoneTagDetails = MasterCache.getTagDetails(Constants.TIME_ZONE_TAG);
            if (timezoneTagDetails == null) {
                log.error("Tag details unavailable for 'Timezone' tag");
                throw new ControlCenterException("Tag details unavailable for 'Timezone' tag");
            }

            int timezoneTagId = timezoneTagDetails.getId();

            int tagMappingId = TagsDataService.deleteTagMapping(timezoneTagId, serviceBean.getId(), Constants.CONTROLLER,
                    serviceBean.getAccountId(), handle);
            if (tagMappingId == -1) {
                log.error(err);
                throw new ControlCenterException(err);
            }
            int id = TagMappingBL.addTagMapping(timezoneTagId, serviceBean.getId(), Constants.CONTROLLER, String.valueOf
                    (serviceBean.getTimezoneDetail().getId()), String.valueOf(serviceBean.getTimezoneDetail()
                    .getOffset()), serviceBean.getUserId(), serviceBean.getAccountId(), handle);
            if (id == -1) {
                log.error(err);
                throw new ControlCenterException(err);
            }
        }
        ServiceRepo serviceRepo = new ServiceRepo();
        List<BasicEntity> allServicesDetails = serviceRepo.getAllServicesDetails(serviceBean.getAccountIdentifier());
        if (allServicesDetails == null) {
            log.error("Error occurred while fetching service details");
            throw new ControlCenterException("Error occurred while fetching service details");
        }
        BasicEntity serviceConfigurationData = allServicesDetails.parallelStream().filter(f -> f.getId() == serviceBean.getId()).findAny().orElse(null);

        if (serviceConfigurationData != null && serviceBean.getName() != null) {
            serviceConfigurationData.setName(serviceBean.getName());
            serviceConfigurationData.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
            serviceRepo.updateServiceConfiguration(serviceBean.getAccountIdentifier(), allServicesDetails);
        }

        Service serviceConfigurationDetail = serviceRepo.getServiceConfigurationByIdentifier(serviceBean.getAccountIdentifier(), serviceBean.getIdentifier());
        List<Tags> existingTags = serviceConfigurationDetail.getTags();

        if (serviceBean.getLayer() != null) {
            Tags existingLayerTag = existingTags.parallelStream().filter(f -> f.getType().equals(Constants.LAYER_TAG)).findAny().orElse(null);

            if (existingLayerTag != null) {
                existingLayerTag.setValue(serviceBean.getLayer().trim());
            } else {
                Tags newLayerTag = Tags.builder()
                        .type(Constants.LAYER_TAG)
                        .value(serviceBean.getLayer().trim())
                        .key(Constants.LAYER_DEFAULT)
                        .build();
                existingTags.add(newLayerTag);
            }
        }

        if (serviceBean.getType() != null) {
            Tags existingTypeTag = existingTags.parallelStream().filter(f -> f.getType().equals(Constants.SERVICE_TYPE_TAG)).findAny().orElse(null);

            if (existingTypeTag != null) {
                existingTypeTag.setValue(serviceBean.getType().trim());
            } else {
                Tags newTypeTag = Tags.builder()
                        .type(Constants.SERVICE_TYPE_TAG)
                        .value(serviceBean.getType().trim())
                        .key(Constants.DEFAULT_TAG_VALUE)
                        .build();
                existingTags.add(newTypeTag);
            }
        }

        if (serviceBean.getTimezoneDetail() != null) {
            Tags existingTimeZoneTag = existingTags.parallelStream().filter(f -> f.getType().equals(Constants.TIME_ZONE_TAG)).findAny().orElse(null);

            if (existingTimeZoneTag != null) {
                existingTimeZoneTag.setKey(String.valueOf(serviceBean.getTimezoneDetail().getId()));
                existingTimeZoneTag.setValue(String.valueOf(serviceBean.getTimezoneDetail().getOffset()));
            } else {
                Tags newTimeZoneTag = Tags.builder()
                        .type(Constants.TIME_ZONE_TAG)
                        .value(String.valueOf(serviceBean.getTimezoneDetail().getOffset()))
                        .key(String.valueOf(serviceBean.getTimezoneDetail().getId()))
                        .build();
                existingTags.add(newTimeZoneTag);
            }
        }

        if (serviceBean.getName() != null) {
            serviceConfigurationDetail.setName(serviceBean.getName());
        }
        serviceConfigurationDetail.setTags(existingTags);
        serviceConfigurationDetail.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        serviceRepo.updateServiceConfigurationByServiceIdentifier(serviceBean.getAccountIdentifier(), serviceBean.getIdentifier(), serviceConfigurationDetail);
        updateApplicationDetailsInRedis(serviceBean, serviceRepo, allPayloadAppIds);

        return IdPojo.builder()
                .id(serviceBean.getId())
                .identifier(serviceBean.getIdentifier())
                .name(serviceBean.getName())
                .build();
    }

    private void updateApplicationDetailsInRedis(ServiceBean serviceBean, ServiceRepo serviceRepo, Map<String, List<Integer>> allPayloadAppIds) {

        AccountRepo accountRepo = new AccountRepo();
        List<Integer> payloadAppIds = serviceBean.getAppIds();
        List<Integer> deleteAppIds = allPayloadAppIds.get("deleteAppIds");

        List<BasicEntity> allApplicationDetails = accountRepo.getAllApplications(serviceBean.getAccountIdentifier());
        if (allApplicationDetails == null) {
            log.error("Error occurred while fetching application details for account : [{}] ", serviceBean.getAccountIdentifier());
            return;
        }

        List<BasicEntity> existingApplications = serviceRepo.getApplicationsByServiceIdentifier(serviceBean.getAccountIdentifier(), serviceBean.getIdentifier());

        if (existingApplications.isEmpty()) {
            existingApplications = new ArrayList<>();
        }

        if (deleteAppIds == null) {
            log.debug("No application is deleted from service");
        } else {
            for (Integer deleteAppId : deleteAppIds) {
                existingApplications.removeIf(existingApplication -> existingApplication.getId() == deleteAppId);
            }
        }

        if (payloadAppIds.isEmpty()) {
            log.debug("No new application added to the service");
        } else {
            for (Integer payloadAppId : payloadAppIds) {
                BasicEntity payloadApplicationObject = allApplicationDetails
                        .parallelStream()
                        .filter(f -> f.getId() == payloadAppId)
                        .findAny().orElse(null);
                if (payloadApplicationObject == null) {
                    log.error("No match found for the appId in existing application details");
                    return;
                }
                existingApplications.add(payloadApplicationObject);
            }
        }
        serviceRepo.updateApplicationsByServiceIdentifier(serviceBean.getAccountIdentifier(), serviceBean.getIdentifier(), existingApplications);
        updateServiceMappedApplicationInRedis(serviceBean, serviceRepo, allPayloadAppIds, allApplicationDetails);
    }

    private void updateServiceMappedApplicationInRedis(ServiceBean serviceBean, ServiceRepo serviceRepo, Map<String, List<Integer>> allPayloadAppIds, List<BasicEntity> allApplicationDetails) {

        ApplicationRepo applicationRepo = new ApplicationRepo();
        List<BasicEntity> allServicesDetails = serviceRepo.getAllServicesDetails(serviceBean.getAccountIdentifier());
        BasicEntity serviceDetailToBeMappedToApplication = allServicesDetails
                .parallelStream()
                .filter(f -> f.getIdentifier().equalsIgnoreCase(serviceBean.getIdentifier()))
                .findAny().orElse(null);
        if (serviceDetailToBeMappedToApplication != null && serviceBean.getName() != null) {
            serviceDetailToBeMappedToApplication.setName(serviceBean.getName());
            serviceDetailToBeMappedToApplication.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        }

        if (serviceBean.getAppIds().isEmpty() && serviceBean.getName() != null) {
            log.debug("No new application is added to service");
            updatingExistingApplicationDetails(serviceBean, allPayloadAppIds, allApplicationDetails, applicationRepo, serviceDetailToBeMappedToApplication);
        } else {
            updatingExistingApplicationDetails(serviceBean, allPayloadAppIds, allApplicationDetails, applicationRepo, serviceDetailToBeMappedToApplication);
            updatingNewApplicationDetails(serviceBean, allApplicationDetails, applicationRepo, serviceDetailToBeMappedToApplication);
        }


        List<Integer> appIdsToBeDeleted = allPayloadAppIds.get("deleteAppIds");
        if (appIdsToBeDeleted == null) {
            log.debug("No application is deleted from service");
        } else {
            updatingDeletedApplicationDetails(serviceBean, allApplicationDetails, applicationRepo, appIdsToBeDeleted);
        }
    }

    private void updatingNewApplicationDetails(ServiceBean serviceBean, List<BasicEntity> allApplicationDetails, ApplicationRepo applicationRepo, BasicEntity serviceDetailToBeMappedToApplication) {
        for (Integer newAppId : serviceBean.getAppIds()) {
            String newAppIdentifier = allApplicationDetails
                    .parallelStream()
                    .filter(f -> f.getId() == newAppId)
                    .map(BasicEntity::getIdentifier)
                    .findAny().orElse(null);
            List<BasicEntity> servicesMappedToApplication = applicationRepo.getServicesMappedToApplication(serviceBean.getAccountIdentifier(), newAppIdentifier);
            servicesMappedToApplication.add(serviceDetailToBeMappedToApplication);
            applicationRepo.updateServiceApplication(serviceBean.getAccountIdentifier(), newAppIdentifier, servicesMappedToApplication);
        }
    }

    private void updatingDeletedApplicationDetails(ServiceBean serviceBean, List<BasicEntity> allApplicationDetails, ApplicationRepo applicationRepo, List<Integer> appIdsToBeDeleted) {
        for (Integer appIdToDelete : appIdsToBeDeleted) {
            String appIdentifierToBeDeleted = allApplicationDetails
                    .parallelStream()
                    .filter(f -> f.getId() == appIdToDelete)
                    .map(BasicEntity::getIdentifier)
                    .findAny().orElse(null);
            List<BasicEntity> servicesMappedToApplication = applicationRepo.getServicesMappedToApplication(serviceBean.getAccountIdentifier(), appIdentifierToBeDeleted);
            servicesMappedToApplication.removeIf(b -> b.getId() == serviceBean.getId());
            applicationRepo.updateServiceApplication(serviceBean.getAccountIdentifier(), appIdentifierToBeDeleted, servicesMappedToApplication);
        }
    }

    private void updatingExistingApplicationDetails(ServiceBean serviceBean, Map<String, List<Integer>> allPayloadAppIds, List<BasicEntity> allApplicationDetails, ApplicationRepo applicationRepo, BasicEntity serviceDetailToBeMappedToApplication) {
        List<Integer> existingAppIds = allPayloadAppIds.getOrDefault("existingAppId", new ArrayList<>());
        for (Integer existingAppId : existingAppIds) {
            String existingAppIdentifier = allApplicationDetails
                    .parallelStream()
                    .filter(f -> f.getId() == existingAppId)
                    .map(BasicEntity::getIdentifier)
                    .findAny().orElse(null);
            List<BasicEntity> servicesMappedToApplication = applicationRepo.getServicesMappedToApplication(serviceBean.getAccountIdentifier(), existingAppIdentifier);
            servicesMappedToApplication.add(serviceDetailToBeMappedToApplication);
            applicationRepo.updateServiceApplication(serviceBean.getAccountIdentifier(), existingAppIdentifier, servicesMappedToApplication);
        }
    }
}

