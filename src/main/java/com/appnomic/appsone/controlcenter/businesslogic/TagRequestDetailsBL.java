package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.GroupTagsDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.dao.redis.TransactionRepo;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.exceptions.TransactionGroupingException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.TagRequestPojo;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.Rule;
import com.heal.configuration.pojos.Transaction;
import com.heal.configuration.pojos.TransactionGroup;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.appnomic.appsone.controlcenter.common.Constants.RULES_TABLE;
import static com.appnomic.appsone.controlcenter.common.Constants.TXN_TABLE;

public class TagRequestDetailsBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(TagRequestDetailsBL.class);
    private static final TransactionRepo transactionRepo = new TransactionRepo();
    private static final ServiceRepo serviceRepo = new ServiceRepo();


    public UtilityBean<TagRequestPojo> clientValidation(RequestObject requestObject) throws RequestException {
        LOGGER.debug("Inside Client validation");
        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        String txnIdStr = requestObject.getParams().get(Constants.TRANSACTION_IDENTIFIER);
        TagRequestPojo tagRequest;
        int txnId;

        if (StringUtils.isEmpty(identifier)) {
            throw new RequestException(MessageFormat.format(UIMessages.INVALID_VALUE, Constants.ACCOUNT_IDENTIFIER, identifier));
        }

        if (StringUtils.isEmpty(txnIdStr)) {
            throw new RequestException(MessageFormat.format(UIMessages.INVALID_VALUE, Constants.TRANSACTION_IDENTIFIER, txnIdStr));
        }
        try{
            ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();
            tagRequest = objectMapper.readValue(requestObject.getBody(), new TypeReference<TagRequestPojo>() {});
        }
        catch (IOException e){
            LOGGER.error(Constants.JSON_PARSE_ERROR + " : {}",e.getMessage());
            throw new RequestException(UIMessages.INVALID_REQUEST_BODY);
        }
        try{
            txnId = Integer.parseInt(txnIdStr);
            tagRequest.getObjectIds().add(txnId);
        } catch (NumberFormatException e){
            throw new RequestException(e, MessageFormat.format(UIMessages.INVALID_VALUE, Constants.TRANSACTION_IDENTIFIER, txnIdStr));
        }
        tagRequest.validate();
        if (!tagRequest.getError().isEmpty()) {
            throw new RequestException(tagRequest.getError().toString());
        }
        return UtilityBean.<TagRequestPojo>builder().accountIdentifier(identifier).pojoObject(tagRequest).build();
    }

    public void serverValidation(UtilityBean<TagRequestPojo> utilityBean, String objectRefTable, String authToken) throws RequestException {
        LOGGER.debug("Inside Server validation");

        UserAccountBean userAccountBean = getCommonServerValidations(authToken, utilityBean.getAccountIdentifier());
        utilityBean.setAccount(userAccountBean.getAccount());

        if(!utilityBean.getPojoObject().getAddDiscoveryTags().isEmpty() || !utilityBean.getPojoObject().getRemoveDiscoveryTags().isEmpty()){
            removeAddTagValidation(utilityBean.getAccount().getId(), utilityBean.getPojoObject(), objectRefTable);
        }
    }

    public static void removeAddTagValidation(int accountId, TagRequestPojo tagRequest, String objectRefTable) throws RequestException{
        Map<Integer, DiscoveryTagDetailsBean> existingTagMap = getDiscoveryTagsDetails(accountId, tagRequest.getObjectIds(), objectRefTable);
        if(!existingTagMap.isEmpty() && existingTagMap.size()==tagRequest.getObjectIds().size()){
            removeTagValidation(tagRequest, existingTagMap);
            addTagValidation(tagRequest, existingTagMap);
        }
    }

    public static void removeTagValidation(TagRequestPojo tagRequest, Map<Integer, DiscoveryTagDetailsBean> existingTagMap) throws RequestException {
        if(null!=tagRequest.getRemoveDiscoveryTags() && !tagRequest.getRemoveDiscoveryTags().isEmpty()){
            for(int objId : tagRequest.getObjectIds()) {
                for (String removeTag : tagRequest.getRemoveDiscoveryTags()) {
                    if ((null!=existingTagMap.get(objId)) &&!existingTagMap.get(objId).getTagNameList().contains(removeTag)) {
                        LOGGER.error("Tag with the name is not present: {}", removeTag);
                        throw new RequestException("Tag with the name is not present: " + removeTag);
                    }
                }
            }
        }
    }

    public static void addTagValidation(TagRequestPojo tagRequest, Map<Integer, DiscoveryTagDetailsBean> existingTagMap) throws RequestException {
        if(null!=tagRequest.getAddDiscoveryTags() && !tagRequest.getAddDiscoveryTags().isEmpty()){
            for(int objId : tagRequest.getObjectIds()) {
                checkTagWithSameName(objId, tagRequest, existingTagMap);
                if((null!=existingTagMap.get(objId)) &&
                        ((existingTagMap.get(objId).getTagCount()-tagRequest.getRemoveDiscoveryTags().size())+tagRequest.getAddDiscoveryTags().size())>existingTagMap.get(objId).getMaxTags()){
                    throw new RequestException("Max Tag limit reached object id: "+ objId + " Limit: "+existingTagMap.get(objId).getMaxTags()+
                            " Count: "+ existingTagMap.get(objId).getTagCount() +" New Tags: "+ tagRequest.getAddDiscoveryTags().size());
                }
            }
        }
    }

    private static void checkTagWithSameName(int objId, TagRequestPojo tagRequest, Map<Integer, DiscoveryTagDetailsBean> existingTagMap) throws RequestException {
        for (String addTag : tagRequest.getAddDiscoveryTags()) {
            if ((null!=existingTagMap.get(objId)) && existingTagMap.get(objId).getTagNameList().contains(addTag)) {
                LOGGER.error("Tag with the same name is already present: {}", addTag);
                throw new RequestException("Tag with the same name is already present: "+ addTag);
            }
        }
    }

    public UtilityBean<TagRequestPojo> clientValidationsBulk(RequestObject requestObject) throws RequestException {
        LOGGER.debug("Inside Bulk Tagging Client validation");
        TagRequestPojo tagRequest;
        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (StringUtils.isEmpty(identifier)) {
            throw new RequestException(MessageFormat.format(UIMessages.INVALID_VALUE, Constants.ACCOUNT_IDENTIFIER, identifier));
        }

        try{
            ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();
            tagRequest = objectMapper.readValue(requestObject.getBody(), new TypeReference<TagRequestPojo>() {});
            if(tagRequest.getObjectIds().isEmpty()){
                LOGGER.error("Invalid object id.");
                throw new RequestException("Invalid object ids.");
            }
        }
        catch (IOException e){
            LOGGER.error(Constants.JSON_PARSE_ERROR + " : {}",e.getMessage());
            throw new RequestException(UIMessages.INVALID_REQUEST_BODY);
        }
        tagRequest.validate();
        if (!tagRequest.getError().isEmpty()) {
            LOGGER.error("Error in tag request {}",tagRequest.getError());
            throw new RequestException(tagRequest.getError().toString());
        }
        return UtilityBean.<TagRequestPojo>builder().accountIdentifier(identifier).pojoObject(tagRequest).build();
    }

    public static String discoveryTagsBL(AccountBean account, TagRequestPojo requestTagsList, String objRefTable, Handle handle) throws TransactionGroupingException {
        StringBuilder string = new StringBuilder();
        List<TransactionGroupMapBean> tagsToDelete = new ArrayList<>();

        if (requestTagsList.getRemoveDiscoveryTags() != null && !requestTagsList.getRemoveDiscoveryTags().isEmpty()) {
            tagsToDelete = new BindInDataService().getDiscoveryTagIds(account.getId(), requestTagsList.getObjectIds(), objRefTable, requestTagsList.getRemoveDiscoveryTags());
            Map<Integer, Integer> tagAndObjIdsMapToDelete = tagsToDelete.stream().collect(Collectors.toMap(TransactionGroupMapBean::getId, TransactionGroupMapBean::getObjectId));

            if (!tagAndObjIdsMapToDelete.isEmpty()) {
                //delete tags
                int[] tagsRemoved = GroupTagsDataService.removeTags(tagAndObjIdsMapToDelete.keySet(), account.getId(), handle);
                if (Arrays.stream(tagsRemoved).anyMatch(t -> t < 1)) {
                    LOGGER.info("Error occurred while deleting entries from table transaction_group_mapping for TxnId {} ", tagAndObjIdsMapToDelete);
                    throw new TransactionGroupingException("Error occurred while deleting entries from table transaction_group_mapping for TxnId {} " + tagAndObjIdsMapToDelete);
                } else {
                    string.append("Tag(s) were removed successfully.");

                    for (Integer ObjId : tagAndObjIdsMapToDelete.values()) {
                        if (objRefTable.equals(TXN_TABLE)) {
                            deleteTransactionTagsFromRedis(account, ObjId, requestTagsList);
                        } else if (objRefTable.equals(RULES_TABLE)) {
                            deleteRuleTagsFromRedis(account, requestTagsList);
                        }
                    }
                }
            }
        }
        if (!requestTagsList.getAddDiscoveryTags().isEmpty()) {
            //add new tags
            Map<Integer, List<DiscoveryTagsBean>> tagsAdded = addTransactionDiscoveryTags(requestTagsList.getObjectIds(), account, requestTagsList.getAddDiscoveryTags(), objRefTable, handle);

            if (tagsAdded.isEmpty()) {
                LOGGER.info("Error occurred while adding entries to table transaction_group_mapping for tags {}", requestTagsList.getAddDiscoveryTags());
                throw new TransactionGroupingException("Error occurred while adding entries to table transaction_group_mapping for tags {} " + requestTagsList.getAddDiscoveryTags().toString());
            } else {
                string.append("New tag(s) were added successfully.");
                for (Integer ObjId : requestTagsList.getObjectIds()) {
                    if (objRefTable.equals(TXN_TABLE)) {
                        addTransactionTagsToRedis(account, ObjId, tagsAdded);
                    } else if (objRefTable.equals(RULES_TABLE)) {
                        addRuleTagsToRedis(account, ObjId, tagsAdded, requestTagsList);
                    }
                }
            }
        }
        if (tagsToDelete.isEmpty() && requestTagsList.getAddDiscoveryTags().isEmpty()) {
            string.append("Rule has same tag(s) associated to it.");
        }

        return string.toString();
    }

    public static Map<Integer, List<DiscoveryTagsBean>> addTransactionDiscoveryTags(List<Integer> objectIds, AccountBean account, Set<String> discoveryTags,
                                                  String objectRefTable, Handle handle) throws TransactionGroupingException {

        int result;
        Map<Integer, List<DiscoveryTagsBean>> addedTxnGroups = new HashMap<>();
        for (Integer objId : objectIds) {
            TransactionGroupsBean txnGrp = getTransactionGroupBean(account);
            TransactionGroupMapBean txnGrpMap = getTransactionGrpMap(account, objId, objectRefTable);

            DiscoveryTagsBean tagBean;
            List<DiscoveryTagsBean> tagsBeanList = new ArrayList<>();

            for (String tag : discoveryTags) {
                tagBean = GroupTagsDataService.getTransactionTags(account.getId(), tag, handle);
                txnGrp.setName(tag);
                if (tagBean == null) {
                    //Add entry to table `transaction_groups` and `transaction_group_mapping`
                    result = addNewTransactionGroup(account.getId(), tag, txnGrp, txnGrpMap, handle);
                    tagBean = DiscoveryTagsBean.builder()
                                    .id(result)
                                    .name(txnGrp.getName())
                                    .build();
                } else {
                    //Add entry to table `transaction_group_mapping`
                    txnGrpMap.setTxnGroupId(tagBean.getId());
                    result = GroupTagsDataService.addTransactionGroupMap(txnGrpMap, handle);
                    if (result == -1)
                        throw new TransactionGroupingException("Error occurred while adding records to table " + RULES_TABLE + " :addTransactionGroupMap");
                }
                tagsBeanList.add(tagBean);
            }
            addedTxnGroups.put(objId, tagsBeanList);
        }
        return addedTxnGroups;
    }

    public static int addNewTransactionGroup(int accountId, String tag, TransactionGroupsBean txnGrp, TransactionGroupMapBean txnGrpMap,
                                              Handle handle) throws TransactionGroupingException {
        DiscoveryTagsBean tagBean;
        int result;
        int txnGrpResponse = GroupTagsDataService.addTransactionGroups(txnGrp, handle);
        if(txnGrpResponse==-1) throw new TransactionGroupingException("Error occurred while adding transaction groups: addTransactionGroups");
        tagBean = GroupTagsDataService.getTransactionTags(accountId, tag, handle);
        if(tagBean!=null){
            txnGrpMap.setTxnGroupId(tagBean.getId());
            result = GroupTagsDataService.addTransactionGroupMap(txnGrpMap, handle);

            if(result==-1) throw new TransactionGroupingException("Error occurred while adding records to table "+ TXN_TABLE+ " :addTransactionGroupMap");
            result = tagBean.getId();
        }
        else{
            throw new TransactionGroupingException("Exception encountered while fetching transaction tags: getTransactionTags");
        }
        return result;
    }

    public static Map<Integer, DiscoveryTagDetailsBean> getDiscoveryTagsDetails(int accountId, List<Integer> objectIds, String objRefTable){
        List<DiscoveryTagDetailsBean> existingTags = new BindInDataService().getDiscoveryTagsDetails(accountId, objectIds, objRefTable);
        //mapping id and object id refers to same thing i.e. rule id, transaction id
        return existingTags.parallelStream().collect(Collectors.toMap(DiscoveryTagDetailsBean::getMappingId, t -> t));
    }

    public static TransactionGroupsBean getTransactionGroupBean(AccountBean account){
        TransactionGroupsBean txnGrp = new TransactionGroupsBean();
        Timestamp time = DateTimeUtil.getCurrentTimestampInGMT();
        txnGrp.setAccountId(account.getId());
        txnGrp.setStatus(1);
        txnGrp.setUserDetailsId(account.getUserIdDetails());
        txnGrp.setCreatedTime(time);
        txnGrp.setUpdatedTime(time);
        return txnGrp;
    }

    public static TransactionGroupMapBean getTransactionGrpMap(AccountBean account, int objId, String objectRefTable){
        TransactionGroupMapBean txnGrpMap = new TransactionGroupMapBean();
        Timestamp time = DateTimeUtil.getCurrentTimestampInGMT();
        txnGrpMap.setAccountId(account.getId());
        txnGrpMap.setCreatedTime(time);
        txnGrpMap.setUpdatedTime(time);
        txnGrpMap.setObjectId(objId);
        txnGrpMap.setObjectRefTable(objectRefTable);
        txnGrpMap.setUserDetailsId(account.getUserIdDetails());
        return txnGrpMap;
    }

    protected UserAccountBean getCommonServerValidations(String authToken, String accountIdentifier) throws RequestException {
        return ValidationUtils.commonServerValidations(authToken, accountIdentifier);
    }

    public static void deleteTransactionTagsFromRedis(AccountBean account, Integer objId, TagRequestPojo requestTagsList) {
        Transaction transaction = transactionRepo.getTransactionById(account.getIdentifier(), objId);

        for (String discoveryTagToDelete : requestTagsList.getRemoveDiscoveryTags()) {
            TransactionGroup transactionGroup = transaction.getTransactionGroups().parallelStream().filter(f -> f.getTransactionGroupName().equals(discoveryTagToDelete)).findFirst().orElse(null);
            transaction.getTransactionGroups().remove(transactionGroup);
        }
            transactionRepo.updateTransactionDetailsById(account.getIdentifier(), transaction);
            transactionRepo.updateTransactionDetailsByIdentifier(account.getIdentifier(), transaction);
    }


    public static void deleteRuleTagsFromRedis(AccountBean account,TagRequestPojo requestTagsList){
        BasicEntity serviceDetail = serviceRepo.getServiceConfigurationById(account.getIdentifier(), Integer.parseInt(requestTagsList.getServiceId()));
        if(serviceDetail == null){
            LOGGER.error("Could not find Service-details for serviceId [{}]", requestTagsList.getServiceId());
            return;
        }
        List<Rule> serviceWiseRules = serviceRepo.getServiceRules(account.getIdentifier(), serviceDetail.getIdentifier());
        if(serviceWiseRules.isEmpty()){
            LOGGER.error("Could not find serviceWiseRules for serviceIdentifier [{}]",serviceDetail.getIdentifier());
            return;
        }
        for (String discoveryTagToDelete : requestTagsList.getRemoveDiscoveryTags()) {
            for (Rule serviceWiseRule : serviceWiseRules) {
                TransactionGroup transactionGroup = serviceWiseRule.getTransactionGroups().parallelStream().filter(f -> f.getTransactionGroupName().equals(discoveryTagToDelete)).findAny().orElse(null);
                serviceWiseRule.getTransactionGroups().remove(transactionGroup);
            }
        }
            serviceRepo.updateServiceRules(account.getIdentifier(), serviceDetail.getIdentifier(), serviceWiseRules);
    }


    public static void addTransactionTagsToRedis(AccountBean account, Integer objId, Map<Integer, List<DiscoveryTagsBean>> tagsAdded){
        Transaction transaction = transactionRepo.getTransactionById(account.getIdentifier(), objId);
        if(transaction == null){
            LOGGER.error("Could not find transactions for the TxnId [{}]",objId );
            return;
        }
        boolean transactionUpdated = false;
        List<DiscoveryTagsBean> tagsBeanList = tagsAdded.get(objId);
        for (DiscoveryTagsBean discoveryTagsBean : tagsBeanList) {
            if (transaction.getTransactionGroups().parallelStream().noneMatch(f -> f.getTransactionGroupId() == discoveryTagsBean.getId())) {
                TransactionGroup newTransactionGroup = new TransactionGroup();
                newTransactionGroup.setTransactionGroupName(discoveryTagsBean.getName());
                newTransactionGroup.setTransactionGroupId(discoveryTagsBean.getId());
                transaction.getTransactionGroups().add(newTransactionGroup);
                transactionUpdated =true;
            }
        }
       if(transactionUpdated) {
           transactionRepo.updateTransactionDetailsById(account.getIdentifier(), transaction);
           transactionRepo.updateTransactionDetailsByIdentifier(account.getIdentifier(), transaction);
       }
    }

    public static void addRuleTagsToRedis(AccountBean account,Integer objId, Map<Integer, List<DiscoveryTagsBean>> tagsAdded, TagRequestPojo requestTagsList){
        BasicEntity serviceDetail = serviceRepo.getServiceConfigurationById(account.getIdentifier(), Integer.parseInt(requestTagsList.getServiceId()));
        if(serviceDetail == null){
            LOGGER.error("Could not find Service details for serviceId [{}]",requestTagsList.getServiceId());
            return;
        }
        List<Rule> serviceWiseRules = serviceRepo.getServiceRules(account.getIdentifier(), serviceDetail.getIdentifier());
        if(serviceWiseRules.isEmpty()){
            LOGGER.error("Could not find serviceWiseRules for serviceIdentifier [{}]",serviceDetail.getIdentifier());
            return;
        }
        boolean updatedRules = false;
        List<DiscoveryTagsBean> tagsBeanList = tagsAdded.get(objId);
            for (DiscoveryTagsBean discoveryTagsBean : tagsBeanList) {
                for (Rule serviceWiseRule : serviceWiseRules) {
                    if(serviceWiseRule.getId() == objId) {
                        if (serviceWiseRule.getTransactionGroups().parallelStream().noneMatch(f -> f.getTransactionGroupId() == discoveryTagsBean.getId())) {
                            TransactionGroup newTransactionGroup = new TransactionGroup();
                            newTransactionGroup.setTransactionGroupName(discoveryTagsBean.getName());
                            newTransactionGroup.setTransactionGroupId(discoveryTagsBean.getId());
                            serviceWiseRule.getTransactionGroups().add(newTransactionGroup);
                            updatedRules = true;
                        }
                    }
                }
                if(updatedRules) {
                    serviceRepo.updateServiceRules(account.getIdentifier(), serviceDetail.getIdentifier(), serviceWiseRules);
                }
            }
    }
}
