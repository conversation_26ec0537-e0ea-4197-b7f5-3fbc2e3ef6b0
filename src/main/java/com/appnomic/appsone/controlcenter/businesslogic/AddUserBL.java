package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.UserAccessDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.UserDataService;
import com.appnomic.appsone.controlcenter.dao.redis.UsersRepo;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.NameValuePojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.UserInfo;
import com.appnomic.appsone.controlcenter.util.AECSBouncyCastleUtil;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.UserUtility;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.appnomic.appsone.keycloak.KeycloakConnectionManager;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.NotificationChoice;
import com.heal.configuration.pojos.User;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.util.List;

@Slf4j
public class AddUserBL implements BusinessLogic<UserInfo, UserInfoBean, String> {

    private static final ObjectMapper OBJECT_MAPPER = CommonUtils.getObjectMapperWithHtmlEncoder();

    private static final int USER_MANAGER = 3;

    @Override
    public UtilityBean<UserInfo> clientValidation(RequestObject request) throws ClientException {
        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (StringUtils.isEmpty(request.getBody())) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String requestBody = request.getBody();
        String decryptedRequestBody;
        try {
            decryptedRequestBody = new AECSBouncyCastleUtil().decrypt(requestBody);
        } catch (InvalidCipherTextException e) {
            throw new ClientException(e, e.getMessage());
        }
        UserInfo userInfo;
        try {
            userInfo = OBJECT_MAPPER.readValue(decryptedRequestBody, new TypeReference<UserInfo>() {
            });
        } catch (IOException e) {
            log.error(Constants.JSON_PARSE_ERROR, e);
            throw new ClientException(Constants.JSON_PARSE_ERROR);
        }

        if (!userInfo.validate()) {
            log.error("Validation failure of details provided");
            throw new ClientException(UIMessages.INVALID_REQUEST);
        }

        return UtilityBean.<com.appnomic.appsone.controlcenter.pojo.UserInfo>builder()
                .authToken(authToken)
                .pojoObject(userInfo)
                .build();
    }

    @Override
    public UserInfoBean serverValidation(UtilityBean<UserInfo> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        UserInfo user = utilityBean.getPojoObject();
        user.setUserDetailsId(userId);
        user.setEditInKeycloak(false);

        UsersBL usersBL = new UsersBL();
        GenericResponse<String> response;

        try {
            response = usersBL.validateUserName(user.getUserName());
            if (!response.getResponseStatus().equals(StatusResponse.SUCCESS.name())) {
                log.error("User name validation failed. Either the user is already mapped or added in keycloak.");
                throw new ServerException("Invalid user name");
            }
        } catch (Exception e) {
            String error = "Unable to validate user name";
            log.error(error, e);
            throw new ServerException(error);
        }

        //accessDetails refers to the application and account-wise permissions set at the time of adding the user.
        if (user.getAccessDetails() == null || user.getAccessDetails().isEmpty()) {
            log.error("User Access Details are not specified.");
            throw new ServerException("User Access Details are not specified.");
        }

        String setup = new UserDataService().getSetup();
        if (setup == null) {
            log.error("User management related integration details unavailable");
            throw new ServerException("Unable to fetch setup.");
        }

        try {
            List<UserBean> userBeans = UserAccessDataService.getUserDetailsFromKeycloak();

            if (Constants.SETUP_AD_INTEGRATION.equalsIgnoreCase(setup.trim())) {
                String responseData = new AECSBouncyCastleUtil().decrypt(response.getData());
                UserInfo userInfo = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(responseData, UserInfo.class);

                user.setId(userInfo.getId());

                if (user.getEmailId() == null || user.getEmailId().isEmpty()) {
                    user.setEmailId(userInfo.getEmailId());
                } else {
                    usersBL.validateEmailId(userBeans, user.getEmailId());
                    user.setEditInKeycloak(true);
                }

                if (user.getFirstName() == null || user.getFirstName().isEmpty()) {
                    user.setFirstName(userInfo.getFirstName());
                } else {
                    user.setEditInKeycloak(true);
                }

                if (user.getLastName() == null || user.getLastName().isEmpty()) {
                    user.setLastName(userInfo.getLastName());
                } else {
                    user.setEditInKeycloak(true);
                }
            } else {
                usersBL.validateEmailId(userBeans, user.getEmailId());
            }

            usersBL.validateRoleAndProfile(user.getRoleId(), user.getProfileId());

            if (user.getAccessDetails().parallelStream()
                    .anyMatch(u -> !u.getAction().trim().equalsIgnoreCase("add")
                            || (u.getApplications() != null
                            && u.getApplications().parallelStream()
                            .anyMatch(a -> !a.getAction().trim().equalsIgnoreCase("add"))))) {
                log.error("Only 'add' action is valid for applications and accounts in case of adding/mapping user.");
                throw new ServerException("Only 'add' action is valid.");
            }

            AccessDetailsBean accessDetailsBean = usersBL.validateAndGetAccessDetailsUser(user.getAccessDetails());

            return usersBL.getUserInfoBean(user, accessDetailsBean);

        } catch (ControlCenterException | RequestException | JsonProcessingException e) {
            throw new ServerException(e.getMessage());
        } catch (InvalidCipherTextException e) {
            log.error("Error occurred while decrypting the user data: [{}]", response.getData(), e);
            throw new ServerException(e.getMessage());
        }
    }

    @Override
    public String process(UserInfoBean bean) throws DataProcessingException {
        DBI dbi = MySQLConnectionManager.getInstance().getHandle();

        try {
            NameValuePojo newlyAddedUserIdAndIdentifier = dbi.inTransaction((conn, status) -> addUser(bean, conn));
            addUserDetailsToRedis(bean, newlyAddedUserIdAndIdentifier);
            return "NOTHING";
        } catch (Exception e) {
            throw new DataProcessingException(e.getCause().getCause().getMessage());
        }
    }

    private NameValuePojo addUser(UserInfoBean user, Handle conn) throws ControlCenterException {
        UsersBL usersBL = new UsersBL();
        String setup = new UserDataService().getSetup();
        if (setup == null) {
            log.error("User management related integration details unavailable");
            throw new ControlCenterException("Unable to fetch setup.");
        }

        if (Constants.SETUP_KEYCLOAK.equalsIgnoreCase(setup.trim())) {
            KeycloakUserBean keycloakUserBean = usersBL.getKeycloakUserBean(user);

            if (user.getId() == null) {
                keycloakUserBean.setCredentials(usersBL.getDefaultCredentials());
                try {
                    KeycloakConnectionManager.addUser(OBJECT_MAPPER.writeValueAsString(keycloakUserBean));
                } catch (IOException e) {
                    log.error("Add operation failed in keycloak.");
                    throw new ControlCenterException("Add operation failed in keycloak.");
                }

                UserBean userBean = UserAccessDataService.getUserDetailsFromUsername(user.getUserName());

                if (null == userBean) {
                    log.error("User details unavailable in keycloak after successful invocation of HTTP POST call to add the user to keycloak");
                    throw new ControlCenterException("User was not successfully added");
                }
                user.setId(userBean.getId());

            } else if (user.isEditInKeycloak()) {
                try {
                    KeycloakConnectionManager.editKeycloakUser(OBJECT_MAPPER.writeValueAsString(keycloakUserBean), user.getId());
                } catch (IOException e) {
                    log.error("Edit operation failed in keycloak.");
                    throw new ControlCenterException("Edit operation failed in keycloak.");
                }
            }
        }

        try {
            if (user.getRoleId() == USER_MANAGER) {
                usersBL.mapAdminRoleToUserManager(user.getId());
            }

            UserDataService userDataService = new UserDataService();
            int userAttributeId = userDataService.addUserAttributes(usersBL.getUserAttributesBean(user), conn);
            userDataService.addUserAccessDetails(usersBL.getUserAccessDetailsBean(user), conn);

            usersBL.updateNotificationPreferences(user, conn);

            return new NameValuePojo(userAttributeId, user.getId(), null);
        } catch (Exception e) {
            if (Constants.SETUP_KEYCLOAK.equalsIgnoreCase(setup.trim())) {
                try {
                    KeycloakConnectionManager.deleteKeycloakUser(user.getId());
                } catch (IOException exc) {
                    log.error("Error in rollback : User is present in Keycloak but not in schema.");
                }
            }
            throw new ControlCenterException(e.getMessage());
        }
    }

    public void addUserDetailsToRedis(UserInfoBean bean, NameValuePojo newlyAddedUserIdAndIdentifier) {
        try {
            UserUtility userUtility = new UserUtility();

            populateUserDetails(bean, newlyAddedUserIdAndIdentifier);

            userUtility.populateUserAccessDetails(bean);

            userUtility.populateUserDetailsInAppLevelKeys(newlyAddedUserIdAndIdentifier.getName());

        } catch (Exception e) {
            log.error("Failed to add user details in redis", e);
        }
    }

    private void populateUserDetails(UserInfoBean bean, NameValuePojo newlyAddedUserIdAndIdentifier) {
        UserDataService userDataService = new UserDataService();

        try {
            User userDetailsByUserId = userDataService.getUserDetailsByUserId(bean.getStatus(), newlyAddedUserIdAndIdentifier.getId(), null);
            NotificationChoiceBean userNotificationChoice = userDataService.getUserNotificationChoiceByIdentifier(newlyAddedUserIdAndIdentifier.getName());

            if (userNotificationChoice != null) {
                List<String> categoryIdentifiersByIds = new BindInDataService().getCategoryIdentifiersByIds(userNotificationChoice.getCategoryIds(), null);
                NotificationChoice notificationChoice = NotificationChoice.builder()
                        .categories(categoryIdentifiersByIds)
                        .component(userNotificationChoice.getComponentSelection())
                        .build();
                userDetailsByUserId.setNotificationChoice(notificationChoice);
            }

            userDetailsByUserId.setFirstName(bean.getFirstName());
            userDetailsByUserId.setLastName(bean.getLastName());
            userDetailsByUserId.setTimezoneDetail(userDataService.getUserTimeZone(newlyAddedUserIdAndIdentifier.getId(), null));

            new UsersRepo().addUser(userDetailsByUserId);

        } catch (Exception e) {
            log.error("Failed to add user details in user level key in redis", e);
        }
    }
}
