package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.RulesDataService;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.pojo.AgentTypePojo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

public class AgentType {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentType.class);

    public List<AgentTypePojo> processRequestAndGetAgentTypeList(String serviceId, int accountId) throws ControlCenterException {
        ControllerBean service = new ControllerDataService().getControllerById(Integer.parseInt(serviceId), accountId, null);

        if(null == service) {
            LOGGER.error("Invalid service identifier. Reason: No mapping found in controller table for the provided serviceId [{}]", serviceId);
            throw new ControlCenterException(UIMessages.INVALID_SERVICE);
        }

        return getAgentTypeList(serviceId, accountId);
    }


    public static List<AgentTypePojo> getAgentTypeList(String serviceId, int accountId) {
        long time = System.currentTimeMillis();
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
        Set<Integer> agentIds = compInstanceDataService.getAgentIdForService(Integer.parseInt(serviceId), accountId, null);
        agentIds.addAll(compInstanceDataService.getAgentIdForServiceFromTagMapping(Integer.parseInt(serviceId), accountId, null));

        LOGGER.trace("Time taken to fetch agentIds is {}", System.currentTimeMillis()-time);

        if(agentIds.isEmpty()) {
            LOGGER.warn("There are no agents mapped to the service [{}]", serviceId);
            return Collections.emptyList();
        }

        time = System.currentTimeMillis();
        Set<AgentTypePojo> agentTypePojos = new BindInDataService().getAgentTypeList(agentIds)
                .parallelStream()
                .filter(k -> k.getAgentTypeId() != Integer.parseInt(Constants.JIM_AGENT_TYPE_ID)
                        && k.getAgentTypeId() != Integer.parseInt(Constants.FORENSIC_AGENT_TYPE_ID))
                .map(h -> AgentTypePojo.builder()
                        .id(h.getAgentTypeId())
                        .name(RulesDataService.getNameFromMSTSubType(h.getAgentTypeId()))
                        .build()).filter(Objects::nonNull)
                .sorted(Comparator.comparing(AgentTypePojo::getId)
                        .thenComparing(o -> o.getName().toLowerCase()))
                .collect(Collectors.toCollection(LinkedHashSet::new));

        LOGGER.trace("Time taken to create AgentTypePojo list {}", System.currentTimeMillis()-time);

        return new ArrayList<>(agentTypePojos);
    }
}
