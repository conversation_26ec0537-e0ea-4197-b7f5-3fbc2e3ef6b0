package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.beans.MaintenanceScheduledBean;
import com.appnomic.appsone.common.beans.MaintenanceWindowBean;
import com.appnomic.appsone.common.beans.RecurringDetailsBean;
import com.appnomic.appsone.common.exception.AppsOneException;
import com.appnomic.appsone.common.util.MaintenanceUtils;
import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.MaintenanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ServiceMaintenanceMapping;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.MaintenanceWindowUtility;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;
import java.text.MessageFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class GetMaintenanceWindowBL implements BusinessLogic<MaintenanceWindowHelperBean, MaintenanceWindowHelperBean, List<MaintenanceDetailsBean>>{
    @Override
    public UtilityBean<MaintenanceWindowHelperBean> clientValidation(RequestObject requestObject) throws ClientException {
        String accountIdentifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        String serviceIdStr = requestObject.getParams().get(Constants.SERVICE_ID);

        if (StringUtils.isEmpty(accountIdentifier)) {
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        if (StringUtils.isEmpty(serviceIdStr)) {
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR);
        }

        int serviceId;
        try {
            serviceId = Integer.parseInt(serviceIdStr);
        } catch (Exception e) {
            throw new ClientException(MessageFormat.format(UIMessages.INVALID_VALUE, Constants.SERVICE_IDENTIFIER, serviceIdStr));
        }

        MaintenanceWindowHelperBean bean = MaintenanceWindowHelperBean.builder()
                .accountIdentifier(accountIdentifier)
                .serviceId(serviceId)
                .build();

        return UtilityBean.<MaintenanceWindowHelperBean>builder()
                .pojoObject(bean)
                .build();
    }

    @Override
    public MaintenanceWindowHelperBean serverValidation(UtilityBean<MaintenanceWindowHelperBean> utilityBean) throws ServerException {
        MaintenanceWindowHelperBean maintenanceBean = utilityBean.getPojoObject();

        AccountBean account = ValidationUtils.validAndGetAccount(maintenanceBean.getAccountIdentifier());
        if (account == null) {
            log.error("Invalid account identifier [{}]", maintenanceBean.getAccountIdentifier());
            throw new ServerException(MessageFormat.format(UIMessages.INVALID_VALUE, Constants.ACCOUNT_IDENTIFIER, maintenanceBean.getAccountIdentifier()));
        }

        ControllerBean controller = new ControllerDataService().getControllerById(maintenanceBean.getServiceId(), account.getId(), null);

        if (controller == null) {
            log.error("Service with id [{}] is unavailable for account id [{}]", maintenanceBean.getServiceId(), account.getId());
            throw new ServerException(String.format("Service with id [%d] is unavailable for account id [%d]", maintenanceBean.getServiceId(), account.getId()));
        }

        maintenanceBean.setAccount(account);
        maintenanceBean.setServiceIdentifier(controller.getIdentifier());

        return maintenanceBean;
    }

    @Override
    public List<MaintenanceDetailsBean> process(MaintenanceWindowHelperBean bean) throws DataProcessingException {
        try {
            if (bean.getFromTime() != null && bean.getToTime() != null) {
                return getMaintenanceDetailsConflicts(bean.getAccount(), bean.getServiceId(), bean.getServiceIdentifier(),
                        Long.parseLong(bean.getFromTime()), Long.parseLong(bean.getToTime()));
            } else {
                return MaintenanceWindowUtility.getMaintenanceDetails(bean.getAccount().getIdentifier(), bean.getServiceIdentifier(), bean.getServiceId());
            }
        } catch (ParseException | AppsOneException | ControlCenterException e) {
            log.error("Error occurred while fetching maintenance window details. Reason: ", e);
            throw new DataProcessingException(e.getMessage());
        }
    }

    private List<MaintenanceDetailsBean> getMaintenanceDetailsConflicts(AccountBean accountBean, int serviceId, String serviceIdentifier, Long fromT, Long toT) throws AppsOneException, ControlCenterException {
        List<MaintenanceDetailsBean> maintenanceDetailsBeanList = new ArrayList<>();
        Timestamp fromTime = new Timestamp(fromT);
        Timestamp toTime = new Timestamp(toT);

        List<ServiceMaintenanceMapping> maintenanceMappingList = new MaintenanceDataService().getMaintenanceWindowsByServiceId(serviceId);
        if (maintenanceMappingList == null || maintenanceMappingList.isEmpty()) {
            return maintenanceDetailsBeanList;
        }

        List<Integer> maintenanceList = maintenanceMappingList
                .parallelStream()
                .map(ServiceMaintenanceMapping::getMaintenanceId)
                .collect(Collectors.toList());

        log.debug("Getting maintenance scheduled for service id:{}", serviceId);
        MaintenanceRecurringDetailsBean maintenanceDetailsLists = MaintenanceWindowUtility.getConfiguredMaintenanceDetails(maintenanceList, fromTime, fromTime);
        if (maintenanceDetailsLists == null) {
            return maintenanceDetailsBeanList;
        }

        List<MaintenanceWindowBean> maintenanceDetails = maintenanceDetailsLists.getMaintenanceWindowBeanList();
        Map<Integer, RecurringDetailsBean> recurringDetails = maintenanceDetailsLists.getRecurringDetailsMap();
        MaintenanceScheduledBean maintenanceWindowBean = MaintenanceUtils.getMaintenanceScheduled(maintenanceDetails, recurringDetails, fromTime, fromTime);

        maintenanceDetailsBeanList.add(MaintenanceWindowUtility.getOngoingMaintenance(accountBean.getIdentifier(), serviceIdentifier, maintenanceWindowBean));

        if (!maintenanceDetails.isEmpty()) {
            for (MaintenanceWindowBean maintenanceWindow : maintenanceWindowBean.getUpComing()) {
                Timestamp startTime = new Timestamp(maintenanceWindow.getStartTime());
                Timestamp endTime = new Timestamp(maintenanceWindow.getEndTime());
                if (startTime.before(toTime) && endTime.after(fromTime)) {
                    maintenanceDetailsBeanList.add(MaintenanceDetailsBean.builder()
                            .id(maintenanceWindow.getId())
                            .type(maintenanceWindow.getType())
                            .endTime(endTime)
                            .startTime(startTime)
                            .name(maintenanceWindow.getName())
                            .build());
                }
            }
        }

        return maintenanceDetailsBeanList.parallelStream().filter(Objects::nonNull).collect(Collectors.toList());
    }
}
