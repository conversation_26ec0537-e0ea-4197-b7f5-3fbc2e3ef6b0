package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ComponentDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class GetComponentAttributesBL implements BusinessLogic<Object, Integer, List<ComponentAttributesMapping>> {

    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty");
            throw new ClientException("Invalid account identifier");
        }

        return UtilityBean.builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .build();
    }

    public Integer serverValidation(UtilityBean<Object> utilityBean) throws ServerException {

        String accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        int accountId = account.getId();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }
        return accountId;
    }

    public List<ComponentAttributesMapping> process(Integer accountId) throws DataProcessingException {
        try {

            List<ViewComponentAttributesPojo> viewComponentAttributesList = ComponentDataService.getComponentAttributeDetails(null);
            if (viewComponentAttributesList == null) {
                log.error("No Component-Attributes data found.");
                throw new DataProcessingException("No Component-Attributes data found.");
            }

            List<ComponentAttributesMapping> componentAttributesMappings = viewComponentAttributesList.parallelStream()
                    .map(c -> ComponentAttributesMapping.builder()
                            .id(c.getComponentId())
                            .name(c.getComponentName())
                            .type(c.getComponentTypeName())
                            .commonVersion(viewComponentAttributesList.parallelStream()
                                    .filter(d -> d.getComponentId() == c.getComponentId())
                                    .map(d -> CommonVersionAttributes.builder()
                                            .id(d.getCommonVersionId())
                                            .name(d.getCommonVersionName())
                                            .build())
                                    .distinct()
                                    .collect(Collectors.toList()))
                            .build())
                    .distinct()
                    .sorted(Comparator.comparing(ComponentAttributesMapping::getName))
                    .collect(Collectors.toList());

            for (ComponentAttributesMapping componentAttributesMapping : componentAttributesMappings) {
                for(CommonVersionAttributes commonVersionAttributes : componentAttributesMapping.getCommonVersion()){
                    commonVersionAttributes.setComponentVersion(viewComponentAttributesList.parallelStream()
                            .filter(two -> componentAttributesMapping.getId() == two.getComponentId() &&
                                    commonVersionAttributes.getId() == two.getCommonVersionId())
                            .map(c -> IdPojo.builder()
                                    .id(c.getComponentVersionId())
                                    .name(c.getComponentVersionName())
                                    .build())
                            .distinct()
                            .collect(Collectors.toList()));

                    commonVersionAttributes.setAttributes(viewComponentAttributesList.parallelStream()
                            .filter(two -> componentAttributesMapping.getId() == two.getComponentId() &&
                                    commonVersionAttributes.getId() == two.getCommonVersionId())
                            .map(d -> AttributesList.builder()
                                    .id(d.getAttributeId())
                                    .name(d.getName())
                                    .attributeName(d.getAttributeName())
                                    .defaultValue(d.getDefaultValue())
                                    .isMandatory(d.getIsMandatory())
                                    .build())
                            .distinct()
                            .collect(Collectors.toList()));

                }
            }

            return componentAttributesMappings;
        } catch (Exception e) {
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }

}
