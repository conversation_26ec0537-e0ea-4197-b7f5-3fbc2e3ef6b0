package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.GetEntityTagsDao;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.heal.configuration.pojos.EntityTags;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class GetEntityTagsBL implements BusinessLogic<Object, String, List<EntityTags>> {

    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }
        String entityName = requestObject.getQueryParams().get("name")[0];
        if (entityName == null || entityName.trim().length() == 0) {
            log.error("Invalid entity name provided {}. Name is empty", entityName);
            throw new ClientException("Invalid entity name provided. Name is empty");
        }
        return UtilityBean.builder()
                .authToken(authKey)
                .pojoObject(entityName)
                .build();
    }

    @Override
    public String serverValidation(UtilityBean<Object> utilityBean) throws ServerException {

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        String entityName = utilityBean.getPojoObject().toString();

        GetEntityTagsDao dao = MySQLConnectionManager.getInstance().open(GetEntityTagsDao.class);

        String isEntityNameValid = dao.getValidEntityName(entityName);
        if (isEntityNameValid == null || isEntityNameValid.trim().length() == 0) {
            log.error("Invalid Entity Name provided or Entity Name : {} does not exist in the db", entityName);
            throw new ServerException("Invalid Entity Name provided or Entity Name does not exist in the db");
        }
        return entityName;
    }

    @Override
    public List<EntityTags> process(String name) throws DataProcessingException {
        List<EntityTags> entityTagsList;
        try {
            GetEntityTagsDao dao = MySQLConnectionManager.getInstance().open(GetEntityTagsDao.class);
            List<EntityTags.Values> valuesForEntityTags = dao.getValuesForEntityTags(name);
            entityTagsList = dao.getEntityTags(name);
            entityTagsList.forEach(entityTags -> entityTags.setValues(valuesForEntityTags));
        } catch (Exception e) {
            log.error("Error occurred while fetching entity tags for the give entity name : {}. Reason : {}", name, e.getMessage());
            throw new DataProcessingException("Error occurred while fetching entity tags for the give entity name. Kindly check the logs");
        }
        return entityTagsList;
    }
}
