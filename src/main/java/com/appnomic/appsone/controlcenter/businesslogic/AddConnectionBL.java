package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ConnectionDetailsBean;
import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ConnectionDetailsDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.redis.ConnectionsRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.ConnectionDetailsPojo;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;
import com.heal.configuration.entities.ConnectionBean;
import com.heal.configuration.pojos.BasicEntity;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
public class AddConnectionBL implements BusinessLogic<List<ConnectionDetailsPojo>, UtilityBean<List<ConnectionDetailsBean>>, List<IdPojo>> {

    public UtilityBean<List<ConnectionDetailsPojo>> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (StringUtils.isEmpty(requestObject.getBody())) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty");
            throw new ClientException("Invalid account identifier");
        }
        List<ConnectionDetailsPojo> connections;

        try {
            connections = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestObject.getBody(),
                    new TypeReference<List<ConnectionDetailsPojo>>() {
                    });

        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID + " : {}", e.getMessage());
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        if (connections.isEmpty()) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        Set<ConnectionDetailsPojo> connSet = new HashSet<>(connections);
        if (connSet.size() < connections.size()) {
            log.error(UIMessages.DUPLICATE_CONNECTION);
            throw new ClientException(UIMessages.DUPLICATE_CONNECTION);
        }

        for (ConnectionDetailsPojo conn : connections) {
            if (!conn.isValid()) {
                log.error(UIMessages.INVALID_SERVICE_IDENTIFIER_FOR_CONNECTION);
                throw new ClientException(UIMessages.INVALID_SERVICE_IDENTIFIER_FOR_CONNECTION);
            }
        }

        return UtilityBean.<List<ConnectionDetailsPojo>>builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .pojoObject(connections)
                .build();
    }

    public UtilityBean<List<ConnectionDetailsBean>> serverValidation(UtilityBean<List<ConnectionDetailsPojo>> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        int accountId = account.getId();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }


        List<ConnectionDetailsBean> beanList = new ArrayList<>();

        List<BasicEntity> serviceList = new ServiceRepo().getAllServicesDetails(accountIdentifier);

        for (ConnectionDetailsPojo conn : utilityBean.getPojoObject()) {
            BasicEntity srcController = serviceList.stream()
                    .filter(c -> (c.getIdentifier().equals(conn.getSourceServiceIdentifier().trim())) && c.getStatus() == 1)
                    .findAny().orElse(null);
            if (srcController == null) {
                log.error("Source Service Identifier '[{}]' does not exist.", conn.getSourceServiceIdentifier());
                throw new ServerException(String.format("Source Service Identifier '[%s]' does not exist.", conn.getSourceServiceIdentifier()));
            }

            BasicEntity destController = serviceList.stream()
                    .filter(c -> (c.getIdentifier().equals(conn.getDestinationServiceIdentifier().trim())) && c.getStatus() == 1)
                    .findAny().orElse(null);
            if (destController == null) {
                log.error("Destination Service Identifier '[{}]' does not exist.", conn.getDestinationServiceIdentifier());
                throw new ServerException(String.format("Destination Service Identifier '[%s]' does not exist.", conn.getDestinationServiceIdentifier()));
            }

            ConnectionDetailsBean bean = ConnectionDetailsDataService.getConnection(srcController.getId(), destController.getId(), accountId, null);
            if (bean != null) {
                log.error("Connection between Source Service Identifier '[{}]' and Destination Service Identifier '[{}]' already exists.",
                        conn.getSourceServiceIdentifier(), conn.getDestinationServiceIdentifier());
                throw new ServerException(String.format("Connection between Source Service Identifier '[%s]' and Destination Service Identifier '[%s]' already exists.",
                        conn.getSourceServiceIdentifier(), conn.getDestinationServiceIdentifier()));
            }

            Timestamp timestamp;
            try {
                timestamp = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
            } catch (ParseException e) {
                log.error("Exception occurred while fetching current time in GMT. Details: ", e);
                throw new ServerException("Error while fetching current time in GMT");
            }

            beanList.add(ConnectionDetailsBean.builder()
                    .sourceId(srcController.getId())
                    .destinationId(destController.getId())
                    .sourceRefObject(Constants.CONTROLLER)
                    .destinationRefObject(Constants.CONTROLLER)
                    .createdTime(timestamp)
                    .updatedTime(timestamp)
                    .isDiscovery(conn.getIsDiscovery())
                    .accountId(accountId)
                    .userDetailsId(userId)
                    .build());
        }
        return UtilityBean.<List<ConnectionDetailsBean>>builder().
                account(account)
                .pojoObject(beanList)
                .build();

    }

    public List<IdPojo> process(UtilityBean<List<ConnectionDetailsBean>> beanList) throws DataProcessingException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            List<IdPojo> list = new ArrayList<>();

            return dbi.inTransaction((conn, status) -> {
                int[] ids = ConnectionDetailsDataService.addConnection(beanList.getPojoObject(), conn);

                if (ids.length != 0) {

                    for (int id : ids)
                        list.add(IdPojo.builder().id(id).build());

                    updateRedisCache(beanList.getAccount(), conn);

                    return list;
                } else {
                    log.error("Unable to add connections");
                    throw new ControlCenterException("Unable to add connections");
                }
            });
        } catch (Exception e) {
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }

    public void updateRedisCache(AccountBean accountBean, Handle handle) {

        try {
            String accountIdentifier = accountBean.getIdentifier();
            ConnectionsRepo connectionsRepo = new ConnectionsRepo();
            ControllerDataService controllerDataService = new ControllerDataService();

            List<ControllerBean> servicesList = controllerDataService.getAllServicesByAccountId(accountBean.getId(), handle);
            if (servicesList.isEmpty()) {
                log.debug("No service details exists for account. Account identifier:{}", accountIdentifier);
            } else {
                List<ConnectionBean> connectionsList = ConnectionDetailsDataService.getServiceConnectionBeansForAccount(accountBean.getId(), handle);
                connectionsRepo.updateServiceConnections(accountIdentifier, connectionsList, servicesList);
                connectionsRepo.updateNeighbours(accountBean, connectionsList, servicesList);
                connectionsRepo.updateOutbounds(accountIdentifier, connectionsList);
                connectionsRepo.updateInbounds(accountIdentifier, connectionsList);
            }
        } catch (Exception e) {
            log.error("Failed to update connection details for account in redis cache. Account identifier:{}", accountBean.getIdentifier(), e);
        }
    }
}
