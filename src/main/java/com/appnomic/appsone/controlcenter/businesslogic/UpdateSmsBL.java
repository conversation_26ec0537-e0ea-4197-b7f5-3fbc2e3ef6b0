package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.NotificationDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.SMSDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.SMSParameterBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.SmsDetails;
import com.appnomic.appsone.controlcenter.pojo.SmsParameter;
import com.appnomic.appsone.controlcenter.util.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.bouncycastle.crypto.DataLengthException;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.skife.jdbi.v2.DBI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;

public class UpdateSmsBL implements BusinessLogic<SmsDetails, UtilityBean<SmsDetails>,String> {
    private static final ObjectMapper OBJECT_MAPPER = CommonUtils.getObjectMapperWithHtmlEncoder();
    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateSmsBL.class);
    private static final String ADD_LIST = "AddList";
    private static final String MODIFY_LIST = "ModifyList";
    private static final String DELETE_LIST = "DeleteList";

    @Override
    public UtilityBean<SmsDetails> clientValidation(RequestObject requestObject) throws ClientException {
        SmsDetails smsDetails;

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authKey)) {
            LOGGER.error(UIMessages.AUTH_KEY_EMPTY);
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }

        try {
            smsDetails = OBJECT_MAPPER.readValue(requestObject.getBody(),
                    new TypeReference<SmsDetails>() {
                    });
        } catch (IOException e) {
            LOGGER.error(UIMessages.JSON_INVALID + " Details: {}", e.getMessage());
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        Map<String, String> error = smsDetails.validate();

        if (!error.isEmpty()) {
            String err = error.toString();
            LOGGER.error(err);
            throw new ClientException(err);
        }

        Set<SmsParameter> parameterSet = new HashSet<>(smsDetails.getParameters());
        if (parameterSet.size() < smsDetails.getParameters().size()) {
            LOGGER.error(UIMessages.DUPLICATE_SMS_PARAMETER);
            throw new ClientException(UIMessages.DUPLICATE_SMS_PARAMETER);
        }

        return UtilityBean.<SmsDetails>builder().accountIdentifier(identifier).pojoObject(smsDetails).authToken(authKey).build();
    }

    @Override
    public UtilityBean<SmsDetails> serverValidation(UtilityBean<SmsDetails> utilityBean) throws ServerException {
        UserAccountBean userAccountBean;
        try{
            userAccountBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        }catch(RequestException e){
            String err = e.getSimpleMessage();
            LOGGER.error(err);
            throw new ServerException(err);
        }
        SMSDetailsBean smsDetailsBeanExists = new NotificationDataService().getSMSDetails(userAccountBean.getAccount().getId(), null);
        if(smsDetailsBeanExists == null){
            String err = "Sms settings is not available for account ".concat(String.valueOf(userAccountBean.getAccount().getId()));
            LOGGER.error(err);
            throw new ServerException(err);
        }
        utilityBean.setAccount(userAccountBean.getAccount());
        this.commonEmailServerValidation(utilityBean);

        utilityBean.getPojoObject().setId(smsDetailsBeanExists.getId());
        String plainTxt = "";
        if (utilityBean.getPojoObject().getPassword() == null || utilityBean.getPojoObject().getPassword().trim().isEmpty()) {
            utilityBean.getPojoObject().setPassword("");
        } else {
            try {
                plainTxt = new AECSBouncyCastleUtil().decrypt(utilityBean.getPojoObject().getPassword());
                if (StringUtils.isEmpty(plainTxt)) {
                    String err = "Password is not properly encrypted.";
                    LOGGER.error(err);
                    throw new ServerException(err);
                }
            } catch (InvalidCipherTextException | DataLengthException e) {
                LOGGER.error("Exception encountered while decrypting the password. Details: {}", utilityBean, e);
                throw new ServerException(e, "Error while decrypting the password");
            }
        }
        utilityBean.getPojoObject().setPassword(CommonUtils.encryptInBCEC(plainTxt));

        return utilityBean;
    }

    @Override
    public String process(UtilityBean<SmsDetails> bean) throws DataProcessingException {
        Timestamp time = DateTimeUtil.getCurrentTimestampInGMT();

        String userId = ValidationUtils.getUserId(bean.getAuthToken());
        if (userId == null) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID);
            throw new DataProcessingException(UIMessages.AUTH_KEY_INVALID);
        }

        SMSDetailsBean smsDetailsBean = CreateSmsBL.createSMSDetailsBean(bean.getPojoObject(), userId, bean.getAccount().getId(), time);

        DBI dbi = MySQLConnectionManager.getInstance().getHandle();

        String result = dbi.inTransaction((conn, status) -> {
            new NotificationDataService().updateSMSDetails(smsDetailsBean, conn);

            Map<String, List<SMSParameterBean>> smsParametersBeanMap = createSMSParameterList(bean.getPojoObject().getId(), bean.getPojoObject().getParameters(), userId, time);

            List<SMSParameterBean> smsParametersBeansAdd = smsParametersBeanMap.getOrDefault(ADD_LIST, new ArrayList<>());
            if (!smsParametersBeansAdd.isEmpty()) {
                new NotificationDataService().addSMSParameter(smsParametersBeansAdd, conn);
            }

            List<SMSParameterBean> smsParametersBeansModified = smsParametersBeanMap.getOrDefault(MODIFY_LIST, new ArrayList<>());
            if (!smsParametersBeansModified.isEmpty()) {
                new NotificationDataService().updateSMSParameters(smsParametersBeansModified, conn);
            }

            List<SMSParameterBean> smsParametersBeansDeleted = smsParametersBeanMap.getOrDefault(DELETE_LIST, new ArrayList<>());
            if (!smsParametersBeansDeleted.isEmpty()) {
                new NotificationDataService().deleteSmsParameters(smsParametersBeansDeleted, conn);
            }

            return Constants.SUCCESS;
        });

        if (Constants.SUCCESS.equals(result)) {
            return Constants.SUCCESS;
        } else {
            throw new DataProcessingException("Unable to update sms details.");
        }
    }

    private Map<String,List<SMSParameterBean>> createSMSParameterList(int smsDetailsId, List<SmsParameter> smsParameterList, String authToken, Timestamp time) throws DataProcessingException {
        Map<String,List<SMSParameterBean>> result = new HashMap<>();
        List<SMSParameterBean> addSmsParamList = new ArrayList<>();
        List<SMSParameterBean> modifySmsParamList = new ArrayList<>();
        List<SMSParameterBean> delSmsParamList = new ArrayList<>();
        for (SmsParameter smsParameter : smsParameterList) {
            ViewTypes parameterType = MasterCache.getMstTypeForSubTypeName(Constants.SMS_PARAMETER_TYPE_NAME, smsParameter.getParameterType());
            if (parameterType == null) {
                String err = "Parameter type details unavailable for parameter type:".concat(String.valueOf(smsParameter.getParameterType()));
                LOGGER.error(err);
                throw new DataProcessingException(err);
            }
            SMSParameterBean smsBean = SMSParameterBean.builder()
                    .smsDetailsId(smsDetailsId)
                    .parameterName(smsParameter.getParameterName())
                    .parameterValue(smsParameter.getParameterValue())
                    .parameterTypeId(parameterType.getSubTypeId())
                    .createdTime(time)
                    .updatedTime(time)
                    .userDetailsId(authToken)
                    .isPlaceholder(smsParameter.getIsPlaceholder().equals(Boolean.TRUE) ? 1 : 0)
                    .build();
            if(smsParameter.getAction().equalsIgnoreCase("add")){
                addSmsParamList.add(smsBean);
            }
            else if(smsParameter.getAction().equalsIgnoreCase("edit")){
                smsBean.setId(smsParameter.getParameterId());
                modifySmsParamList.add(smsBean);
            } else {
                smsBean.setId(smsParameter.getParameterId());
                delSmsParamList.add(smsBean);
            }
        }
        result.put(ADD_LIST, addSmsParamList);
        result.put(MODIFY_LIST, modifySmsParamList);
        result.put(DELETE_LIST, delSmsParamList);
        return result;
    }

    private void addServerValidationsForSMSParameters(List<SmsParameter> smsParameters, Integer accountId) throws ServerException {
        SMSDetailsBean smsDetailsBean = new NotificationDataService().getSMSDetails(accountId, null);
        List<SMSParameterBean> smsParameterBeans = new NotificationDataService().getSMSParameters(smsDetailsBean.getId(), null);


        for (SmsParameter smsParameter : smsParameters) {
            ViewTypes parameterType = MasterCache.getMstTypeForSubTypeName(Constants.SMS_PARAMETER_TYPE_NAME, smsParameter.getParameterType());
            if (parameterType == null) {
                String err = "Parameter type details unavailable for parameter type:".concat(String.valueOf(smsParameter.getParameterType()));
                LOGGER.error(err);
                throw new ServerException(err);
            }
            if (smsParameter.getIsPlaceholder().equals(Boolean.TRUE)) {
                Optional<ViewTypes> parameterNameType = MasterCache.getSubTypeDetails(Constants.SMS_PLACEHOLDERS)
                        .stream()
                        .filter(p -> smsParameter.getParameterValue().contains(p.getSubTypeName()))
                        .findAny();

                if (!parameterNameType.isPresent()) {
                    String err = "Sub type is not found for the given parameter name type: ".concat(smsParameter.getParameterName());
                    LOGGER.error(err);
                    throw new ServerException(err);
                }
            }
            validateAction(smsParameterBeans, smsParameter);
        }
    }

    private void validateAction(List<SMSParameterBean> smsParameterBeans, SmsParameter smsParameter) throws ServerException {
        if (smsParameter.getAction().equalsIgnoreCase("add")) {

            Optional<SMSParameterBean> alreadyPresentSmsParam = validationOfSmsParameterInputData(smsParameter, smsParameterBeans);
            if (alreadyPresentSmsParam.isPresent()) {
                String err = UIMessages.INVALID_SMS_PARAM_ADD + " for parameterName: " + smsParameter.getParameterName() + ", parameterValue: " + smsParameter.getParameterValue();
                LOGGER.error(err);
                throw new ServerException(err);
            }
        } else {
            Optional<SMSParameterBean> optionalSMSParameterBean = smsParameterBeans.stream().filter(smsParam -> (smsParam.getId() == smsParameter.getParameterId())).findAny();
            if (!optionalSMSParameterBean.isPresent()) {
                String err = "Parameter id: " + smsParameter.getParameterId() + " is not valid.";
                LOGGER.error(err);
                throw new ServerException(err);
            }
            validationOfRequestDataForActionDelete(smsParameter, optionalSMSParameterBean.get());
        }
    }

    private void validationOfRequestDataForActionDelete(SmsParameter smsParameter, SMSParameterBean smsParameterBean) throws ServerException {
        if (smsParameter.getAction().equalsIgnoreCase("DELETE")) {
            Optional<SMSParameterBean> optionalSMSParameterBean = validationOfSmsParameterInputData(smsParameter, Collections.singletonList(smsParameterBean));
            if (!optionalSMSParameterBean.isPresent()) {
                String err = "Invalid request data for sms parameter for action: Delete where parameterId: " + smsParameterBean.getId();
                LOGGER.error(err);
                throw new ServerException(err);

            }
        }
    }

    private Optional<SMSParameterBean> validationOfSmsParameterInputData(SmsParameter smsParameter, List<SMSParameterBean> smsParameterBeans) {
        ViewTypes viewTypes = MasterCache.getMstTypeForSubTypeName(Constants.SMS_PARAMETER_TYPE_NAME, smsParameter.getParameterType());
        return smsParameterBeans.stream()
                .filter(smsParam -> (smsParam.getParameterName().equalsIgnoreCase(smsParameter.getParameterName())))
                .filter(smsParam -> (smsParam.getParameterValue().equalsIgnoreCase(smsParameter.getParameterValue())))
                .filter (smsParam -> (smsParam.getParameterTypeId() == viewTypes.getSubTypeId()))
                .filter(smsParam -> ((smsParam.getIsPlaceholder() == 0 && smsParameter.getIsPlaceholder().equals(Boolean.FALSE)) || (smsParam.getIsPlaceholder() == 1 && smsParameter.getIsPlaceholder().equals(Boolean.TRUE))))
                .findAny();
    }

    private void commonEmailServerValidation(UtilityBean<SmsDetails> utilityBean) throws ServerException {
        SmsDetails smsDetails = utilityBean.getPojoObject();
        ViewTypes protocolType = MasterCache.getMstTypeForSubTypeName(Constants.SMS_PROTOCOLS, smsDetails.getProtocolName());
        if (protocolType == null) {
            String err = "SMS protocol unavailable for protocol name:".concat(smsDetails.getProtocolName());
            LOGGER.error(err);
            throw new ServerException(err);
        }
        if(protocolType.getSubTypeName().equalsIgnoreCase("HTTP")) {
            ViewTypes httpMethodType = MasterCache.getMstTypeForSubTypeName(Constants.SMS_HTTP_METHODS, smsDetails.getHttpMethod());
            if (httpMethodType == null) {
                String err = "HTTP method type unavailable for http method:".concat(smsDetails.getHttpMethod());
                LOGGER.error(err);
                throw new ServerException(err);
            }
            if(httpMethodType.getSubTypeName().equalsIgnoreCase("GET")){
                smsDetails.setPostData("");
            }
        }  else if(protocolType.getSubTypeName().equalsIgnoreCase("TCP")) {
            smsDetails.setPostData("");
            smsDetails.setHttpMethod("");
            smsDetails.setHttpRelativeUrl("");
            smsDetails.setIsMultiRequest(0);
        }
        addServerValidationsForSMSParameters(smsDetails.getParameters(), utilityBean.getAccount().getId());

    }
}
