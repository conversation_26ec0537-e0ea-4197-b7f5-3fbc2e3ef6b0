package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.NotificationSettingsBean;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.DefaultNotificationPreferences;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.dao.mysql.NotificationPreferencesDataService;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.*;

/**
 * <AUTHOR> Kumar(<EMAIL>) : 28/05/2020
 */
public class ApplicationNotificationBL {

    private ApplicationNotificationBL() {
    }

    private static final ObjectMapper obj_mapper = CommonUtils.getObjectMapperWithHtmlEncoder();
    private static final Logger logger = LoggerFactory.getLogger(ApplicationNotificationBL.class);

    private static final int notificationStart = MasterCache.getMstTypeForSubTypeName(Constants.NOTIFICATION_TYPE_LITERAL, Constants.IMMEDIATELY).getSubTypeId();
    private static final int notificationEnd = MasterCache.getMstTypeForSubTypeName(Constants.NOTIFICATION_TYPE_LITERAL, Constants.OFF).getSubTypeId();

    private static final ViewTypes signalSeveritySevere = MasterCache.getMstTypeForSubTypeName(Constants.SIGNAL_SEVERITY_TYPE_LITERAL, Constants.SEVERE);
    private static final ViewTypes signalSeverityDefault = MasterCache.getMstTypeForSubTypeName(Constants.SIGNAL_SEVERITY_TYPE_LITERAL, Constants.DEFAULT);

    private static final ViewTypes signalTypeProb = MasterCache.getMstTypeForSubTypeName(Constants.SIGNAL_TYPE_LITERAL, Constants.PROBLEM);
    private static final ViewTypes signalTypeEW = MasterCache.getMstTypeForSubTypeName(Constants.SIGNAL_TYPE_LITERAL, Constants.EARLY_WARNING);
    private static final ViewTypes signalTypeInfo = MasterCache.getMstTypeForSubTypeName(Constants.SIGNAL_TYPE_LITERAL, Constants.INFO);
    private static final ViewTypes signalTypeBatch = MasterCache.getMstTypeForSubTypeName(Constants.SIGNAL_TYPE_LITERAL, Constants.BATCH);

    private static final String SEVERITY="Severity";
    private static final String SIGNAL="Signal";

    public static ApplicationNotificationDetails getNotificationConfig(List<GetApplication> list, int accountId) {

        ApplicationNotificationMetaData applicationNotificationMetaData = new ApplicationNotificationMetaData();
        applicationNotificationMetaData.setNotificationSubType(getNotificationSubTypes(accountId));

        ApplicationNotificationDetails applicationNotificationDetails = new ApplicationNotificationDetails();
        applicationNotificationDetails.setMetaData(applicationNotificationMetaData);
        applicationNotificationDetails.setPreferences(getNotificationConfigDetails(list));

        return applicationNotificationDetails;
    }

    private static List<NotificationTypePojo> getNotificationSubTypes(int accountId) {
        List<NotificationTypePojo> notificationSubTypeList = new ArrayList<>();
        int defaultNotificationTypeId = MasterCache.getTypeDetails(Constants.NOTIFICATION_TYPE_LITERAL).getTypeId();
        List<ViewTypes> subTypes = MasterCache.getSubtypesForTypeId(defaultNotificationTypeId);
        List<NotificationSettingsBean> notificationSetting = com.appnomic.appsone.controlcenter.service.NotificationPreferencesDataService.getNotificationSettingsForAccount(accountId);
        for (ViewTypes subType : subTypes) {
            NotificationTypePojo notificationSubType = new NotificationTypePojo();
            notificationSubType.setId(subType.getSubTypeId());
            notificationSubType.setName(subType.getSubTypeName());
            NotificationSettingsBean notificationSettingsBean = notificationSetting.parallelStream().filter(s -> s.getTypeId()==subType.getSubTypeId()).findAny().orElse(null);
            if(Objects.nonNull(notificationSettingsBean)) {
                notificationSubType.setDuration(notificationSettingsBean.getDurationInMin());
            }

            notificationSubTypeList.add(notificationSubType);
        }
        return notificationSubTypeList;
    }

    private static List<NotificationPreferencesPojo> getNotificationConfigDetails(List<GetApplication> list) {
        List<NotificationPreferencesPojo> notificationPreferencesList = new ArrayList<>();
        for (GetApplication application : list) {
            List<NotificationPreferencesPojo> preferencesDb = new NotificationPreferencesDataService().getApplicationNotificationMappingDetails(application.getId());
            for (NotificationPreferencesPojo notificationPreference : preferencesDb) {

                int severityTypeId = notificationPreference.getSeverityTypeId();
                int signalTypeId = notificationPreference.getSignalTypeId();

                String severityType = MasterCache.getMstSubTypeForSubTypeId(severityTypeId).getSubTypeName();
                String signalType = MasterCache.getMstSubTypeForSubTypeId(signalTypeId).getSubTypeName();

                notificationPreference.setSeverityType(severityType);
                notificationPreference.setSignalType(signalType);
                notificationPreference.setApplicationName(application.getName());

                notificationPreferencesList.add(notificationPreference);
            }
        }
        return notificationPreferencesList;
    }

    public static List<NotificationPreferencesPojo> addClientValidations(Request request) throws RequestException {

        List<NotificationPreferencesPojo> notificationPreferences;
        ValidationUtils.commonClientValidations(request);

        if (StringUtils.isEmpty(request.body())) {
            logger.error(UIMessages.REQUEST_NULL);
            throw new RequestException(UIMessages.REQUEST_NULL);
        }

        try {
            notificationPreferences = obj_mapper.readValue(request.body(),
                    new TypeReference<List<NotificationPreferencesPojo>>() {
                    });

        } catch (IOException e) {
            logger.error(UIMessages.JSON_INVALID+ "err:{}", e.getMessage());
            throw new RequestException(UIMessages.JSON_INVALID);
        }

        if(notificationPreferences.isEmpty()){
            logger.error(UIMessages.REQUEST_NULL);
            throw new RequestException(UIMessages.REQUEST_NULL);
        }

        Set<NotificationPreferencesPojo> notificationPreferencesSet = new HashSet<>(notificationPreferences);
        if(notificationPreferencesSet.size() < notificationPreferences.size()){
            logger.error(UIMessages.DUPLICATE_SERVICE);
            throw new RequestException(UIMessages.DUPLICATE_SERVICE);
        }

        for (NotificationPreferencesPojo preference : notificationPreferences) {
            preference.validate();
            if (!preference.getError().isEmpty()) {
                logger.error(preference.getError().toString());
                throw new RequestException(preference.getError().toString());
            }
        }
        return notificationPreferences;
    }

    public static List<DefaultNotificationPreferences> addServerValidations(List<NotificationPreferencesPojo> notificationPreferences, String authToken, String accountIdentifier) throws RequestException, ControlCenterException {

        UserAccountBean userAccBean = ValidationUtils.commonServerValidations(authToken, accountIdentifier);
        String userId = userAccBean.getUserId();
        int accountId = userAccBean.getAccount().getId();

        validateData(notificationPreferences, userAccBean);

        List<DefaultNotificationPreferences> entityList = new ArrayList<>();
        Timestamp timestamp;
        try {
            timestamp = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
        } catch (ParseException e) {
            logger.error(e.getMessage(), e);
            throw new RequestException(UIMessages.INTERNAL_SERVER_ERROR);
        }

        for (NotificationPreferencesPojo preference : notificationPreferences) {
            DefaultNotificationPreferences entity = new DefaultNotificationPreferences();
            entity.setApplicationId(preference.getApplicationId());
            entity.setNotificationTypeId(preference.getNotificationTypeId());
            entity.setSignalTypeId(preference.getSignalTypeId());
            entity.setSignalSeverityId(preference.getSeverityTypeId());
            entity.setAccountId(accountId);
            entity.setUpdatedTime(String.valueOf(timestamp));
            entity.setUserDetailsId(userId);
            entityList.add(entity);
        }
        return entityList;
    }

    private static void validateData(List<NotificationPreferencesPojo> notificationPreferences, UserAccountBean userAccountBean)
            throws RequestException, ControlCenterException {

        Map<String, String> error = new HashMap<>();

        List<GetApplication> list = Applications.get(userAccountBean.getAccount(), userAccountBean.getUserId());

        for (NotificationPreferencesPojo preference : notificationPreferences) {
            if (!(preference.getNotificationTypeId() >= notificationStart && preference.getNotificationTypeId() <= notificationEnd)) {
                logger.error(UIMessages.INVALID_NOTIFICATION_TYPE);
                error.put("Notification Type", UIMessages.INVALID_NOTIFICATION_TYPE);
            }
            if (preference.getSeverityType().equals(signalSeveritySevere.getSubTypeName()) ||
                    preference.getSeverityType().equals(signalSeverityDefault.getSubTypeName())) {

                if ((preference.getSeverityType().equals(signalSeveritySevere.getSubTypeName()) &&
                        (preference.getSeverityTypeId() != signalSeveritySevere.getSubTypeId()))) {
                    logger.error(UIMessages.INVALID_SEVERITY);
                    error.put(SEVERITY, UIMessages.INVALID_SEVERITY);
                }

                if ((preference.getSeverityType().equals(signalSeverityDefault.getSubTypeName()) &&
                        (preference.getSeverityTypeId() != signalSeverityDefault.getSubTypeId()))) {
                    logger.error(UIMessages.INVALID_SEVERITY);
                    error.put(SEVERITY, UIMessages.INVALID_SEVERITY);
                }

            } else {
                logger.error(UIMessages.INVALID_SEVERITY);
                error.put(SEVERITY, UIMessages.INVALID_SEVERITY);
            }

            if (preference.getSignalType().equals(signalTypeEW.getSubTypeName()) ||
                    preference.getSignalType().equals(signalTypeProb.getSubTypeName())
                    || preference.getSignalType().equals(signalTypeInfo.getSubTypeName())
                    || preference.getSignalType().equals(signalTypeBatch.getSubTypeName())) {

                if ((preference.getSignalType().equals(signalTypeProb.getSubTypeName()) &&
                        (preference.getSignalTypeId() != signalTypeProb.getSubTypeId()))) {
                    logger.error(UIMessages.INVALID_SIGNAL);
                    error.put(SIGNAL, UIMessages.INVALID_SIGNAL);
                }
                if ((preference.getSignalType().equals(signalTypeEW.getSubTypeName()) &&
                        (preference.getSignalTypeId() != signalTypeEW.getSubTypeId()))) {
                    logger.error(UIMessages.INVALID_SIGNAL);
                    error.put(SIGNAL, UIMessages.INVALID_SIGNAL);
                }
                if ((preference.getSignalType().equals(signalTypeInfo.getSubTypeName()) &&
                        (preference.getSignalTypeId() != signalTypeInfo.getSubTypeId()))) {
                    logger.error(UIMessages.INVALID_SIGNAL);
                    error.put(SIGNAL, UIMessages.INVALID_SIGNAL);
                }
                if ((preference.getSignalType().equals(signalTypeBatch.getSubTypeName()) &&
                        (preference.getSignalTypeId() != signalTypeBatch.getSubTypeId()))) {
                    logger.error(UIMessages.INVALID_SIGNAL);
                    error.put(SIGNAL, UIMessages.INVALID_SIGNAL);
                }
            } else {
                logger.error(UIMessages.INVALID_SIGNAL);
                error.put(SIGNAL, UIMessages.INVALID_SIGNAL);
            }
            Iterator<GetApplication> itr = list.listIterator();
            boolean appFound  = false;
            while(itr.hasNext()){
                GetApplication app = itr.next();
                if(app.getId() == preference.getApplicationId()){
                    appFound = true;
                    break;
                }
            }
            if (!appFound) {
                logger.error(UIMessages.INVALID_APPLICATION);
                error.put("Application Id", UIMessages.INVALID_APPLICATION);
            }

            if (!error.isEmpty()) {
                logger.error(error.toString());
                throw new RequestException(error.toString());
            }
        }
    }
}
