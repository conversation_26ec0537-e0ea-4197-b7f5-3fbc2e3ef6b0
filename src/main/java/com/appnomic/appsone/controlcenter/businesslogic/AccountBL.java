package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.AccountDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.redis.AccountRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.Account;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.Tags;
import com.appnomic.appsone.controlcenter.service.TagsService;
import com.appnomic.appsone.controlcenter.util.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.security.KeyPair;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class AccountBL implements BusinessLogic<Account, Account, Integer> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountBL.class);
    private static final String KEYS_FOLDER = ConfProperties.getString(Constants.KEYS_PAIRS_FOLDER, Constants.KEYS_PAIRS_FOLDER_DEFAULT);

    private final ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    @Override
    public UtilityBean<Account> clientValidation(RequestObject request) throws ClientException {
        if (request == null) {
            LOGGER.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (StringUtils.isEmpty(request.getBody())) {
            LOGGER.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String requestBody = request.getBody();
        Account account;

        try {
            account = objectMapper.readValue(requestBody, Account.class);
        } catch (IOException e) {
            LOGGER.error("Exception while parsing input request body. Details: {}", e.getMessage());
            throw new ClientException("Error while parsing input");
        }

        if (!account.validate()) {
            LOGGER.error("Validation failure of account details provided");
            throw new ClientException("Validation failure of account details");
        }

        account.setIdentifier(account.getIdentifier().toLowerCase());

        return UtilityBean.<com.appnomic.appsone.controlcenter.pojo.Account>builder()
                .accountIdentifier(account.getIdentifier())
                .authToken(authToken)
                .pojoObject(account)
                .build();
    }

    @Override
    public Account serverValidation(UtilityBean<Account> account) throws ServerException {
        boolean isAccountExists = AccountDataService.getAccountList(null)
                .stream()
                .anyMatch(a -> a.getIdentifier().equalsIgnoreCase(account.getAccountIdentifier().trim()));

        if (isAccountExists) {
            LOGGER.error("Account identifier already in use, kindly use other identifier {}.", account.getAccountIdentifier());
            throw new ServerException("Account identifier already in use, kindly use other identifier.");
        }

        String userId = ValidationUtils.getUserId(account.getAuthToken());

        if (userId == null) {
            LOGGER.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        Account accountPojoObject = account.getPojoObject();
        accountPojoObject.setUpdatedBy(userId);

        return accountPojoObject;
    }


    @Override
    public Integer process(Account account) throws DataProcessingException {
        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        try {
            return dbi.inTransaction((conn, status) -> addAccount(account, account.getUpdatedBy(), conn));
        } catch (Exception e) {
            LOGGER.error("Unable to create account: ", e);

            if (Throwables.getRootCause(e) instanceof ControlCenterException) {
                throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
            } else {
                throw e;
            }
        }
    }

    private int addAccount(Account account, String userId, Handle handle) throws ControlCenterException {
        int id = AccountDataService.getAccountByName(account.getAccountName(), handle);
        if (id > 0) {
            LOGGER.error("account name [{}] already exists", account.getAccountName());
            throw new ControlCenterException(String.format("account name [%s] already exists", account.getAccountName()));
        }

        try {
            File parentFolder = new File(KEYS_FOLDER);
            if (!parentFolder.exists()) {
                boolean mkdirs = parentFolder.mkdirs();
                LOGGER.trace("Parent folder directories :{}", mkdirs);
            }

            KeyPair keyPair = KeyGenerator.generateKeys();
            String privateKey = KeyGenerator.getPrivateKey(null, keyPair);
            String publicKey = KeyGenerator.getPublicKey(null, keyPair);

            AccountBean accountBean = new AccountBean();
            accountBean.setName(account.getAccountName());
            accountBean.setUserIdDetails(userId);
            accountBean.setIdentifier(account.getIdentifier());
            accountBean.setPublicKey(publicKey);
            accountBean.setPrivateKey(privateKey);
            java.sql.Timestamp date = new java.sql.Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
            accountBean.setCreatedTime(date);
            accountBean.setUpdatedTime(date);
            accountBean.setStatus(1);
            id = AccountDataService.addAccount(accountBean, handle);
            List<Tags> tagsList = account.getTags();

            List<com.heal.configuration.pojos.Tags> tags = new ArrayList<>();
            if (tagsList != null && !tagsList.isEmpty()) {
                TagsService tagsService = new TagsService();
                tags = tagsService.addTags(account.getTags(), id, id, "account", userId, handle);
            }
            Optional<Tags> timeZoneTag = account.getTags().stream().filter(tag -> tag.getName().equalsIgnoreCase("Timezone"))
                    .findAny();

            //update Account in Redis
            AccountRepo accountRepo = new AccountRepo();
            com.heal.configuration.pojos.Account newAccount = com.heal.configuration.pojos.Account.builder()
                    .accountId(id)
                    .id(id)
                    .name(accountBean.getName())
                    .createdTime(String.valueOf(accountBean.getCreatedTime()))
                    .updatedTime(String.valueOf(accountBean.getUpdatedTime()))
                    .status(accountBean.getStatus())
                    .privateKey(accountBean.getPrivateKey())
                    .publicKey(accountBean.getPublicKey())
                    .identifier(accountBean.getIdentifier())
                    .tags(tags)
                    .timezone(timeZoneTag.isPresent() ? timeZoneTag.get().getIdentifier() : null)
                    .lastModifiedBy(account.getUpdatedBy())
                    .build();
            accountRepo.updateAccount(account.getIdentifier(), newAccount);
            List<com.heal.configuration.pojos.Account> existingAccountList = accountRepo.getAccounts();
            if (existingAccountList.isEmpty()) {
                existingAccountList = new ArrayList<>();
            }
            existingAccountList.add(newAccount);
            accountRepo.updateAccounts(existingAccountList);

            new NotificationSettingsBL().addDefaultNotificationSettings(id, userId);

            LOGGER.info("Successfully added default Notification Settings for the account {}", account.getIdentifier());
        } catch (ParseException e) {
            LOGGER.error("ParseException encountered while adding account. Details: {}", e.getMessage());
            throw new ControlCenterException(e, "Error occurred while adding account");
        }
        return id;
    }

    public boolean isAccountKeysGenerated(int accountId) {
        String publicKey = AccountDataService.getAccountPublicKeyById(accountId, null);
        String privateKey = AccountDataService.getAccountPrivateKeyById(accountId, null);

        return !(publicKey == null || publicKey.trim().isEmpty() || privateKey == null || privateKey.trim().isEmpty());
    }

    public void updateAccountKeys(String privateKey, String publicKey, int accountId) {
        AccountDataService.updateAccountKeys(privateKey, publicKey, accountId, null);
    }

    public String getFileSignature(AccountBean account, byte[] fileBytes) {
        String privateKey = account.getPrivateKey();
        String signature = SignatureTool.generateSignature(fileBytes, privateKey);

        boolean isCorrect = SignatureTool.validateSignature(fileBytes, account.getPublicKey(), signature);

        if (isCorrect) {
            return signature;
        }
        return null;
    }
}
