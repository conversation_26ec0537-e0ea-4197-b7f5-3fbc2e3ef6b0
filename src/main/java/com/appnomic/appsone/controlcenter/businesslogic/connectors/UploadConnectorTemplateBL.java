package com.appnomic.appsone.controlcenter.businesslogic.connectors;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.ConnectorConstants;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.common.WorkerParameterStatement;
import com.appnomic.appsone.controlcenter.dao.mysql.connectors.AwsConnectorDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.connectors.ConnectorDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.connectors.DynatraceConnectorDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.*;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.connectors.*;
import com.appnomic.appsone.controlcenter.service.KeyCloakAuthService;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.appnomic.appsone.controlcenter.util.connector.UploadErrorUtil;
import com.appnomic.appsone.model.JWTData;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

public class UploadConnectorTemplateBL {

    private static final Logger log = LoggerFactory.getLogger(UploadConnectorTemplateBL.class);

    String schemaName;
    String userId;
    private static final String PERCONA_USERNAME = ConfProperties.getString(Constants.MYSQL_DB_USERNAME_PROPERTY_NAME, Constants.MYSQL_DB_USERNAME_PROPERTY_DEFAULT);
    private static final String PERCONA_PASSWORD = CommonUtils.getDecryptedData(ConfProperties.getString(Constants.MYSQL_DB_PASSWORD_PROPERTY_NAME, Constants.MYSQL_DB_PASSWORD_PROPERTY_DEFAULT));

    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty");
            throw new ClientException("Invalid account identifier");
        }

        return UtilityBean.builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .build();
    }

    public Integer serverValidation(UtilityBean<Object> utilityBean) throws ServerException {

        String accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        int accountId = account.getId();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        return accountId;
    }

    public Integer connectorIdValidation(Request request, int accountId) throws ServerException {
        int connectorId;
        try {
            connectorId = Integer.parseInt(request.params(":connectorId"));
        } catch (NumberFormatException ex) {
            log.error("Account identifier is invalid");
            throw new ServerException("Connector identifier is invalid");
        }
        ConnectorDataService connectorDataService = new ConnectorDataService();
        String connectorName = connectorDataService.getConnectorNameWithConnectorId(connectorId, accountId);
        if (connectorName == null) {
            log.error("Error while getting the connectorName using connectorId");
            throw new ServerException("Error while getting the connectorName using connectorId");
        }
        return connectorId;
    }

    public String fileValidation(Request request) throws Exception {

        String fileName;
        DiskFileItemFactory factory = new DiskFileItemFactory();
        ServletFileUpload fileUpload = new ServletFileUpload(factory);
        List<FileItem> items = fileUpload.parseRequest(request.raw());
        FileItem item = items.stream()
                .filter(e -> "file".equals(e.getFieldName()))
                .findFirst().orElse(null);

        if (item == null) {
            log.error("There is no file available For this account Id");
            throw new org.apache.commons.fileupload.FileUploadException("There is no file available For this account Id");
        } else {
            fileName = item.getName();
            Files.deleteIfExists(Paths.get(fileName));
            item.write(new File(fileName));
        }

        return fileName;
    }


    public void sapConnectorDataProcessing(File file, String schemaName, Integer accountId, Integer connectorId) throws SQLException, ParseException {
        SapConnectorBL sapConnectorBL = new SapConnectorBL();
        WorkerParameterStatement statement = new WorkerParameterStatement();
        ConnectorDataService connectorDataService = new ConnectorDataService();

        try {
            ConnectorDBData dbData = connectorDataService.getConnectorDBData(accountId, connectorId);
            if (dbData.getDbExists() == 0) {
                importSQL(connectorDataService.getConnectorDBData(accountId, connectorId));
                connectorDataService.updateDBExistsStatus(accountId, connectorId, 1);
            }
            addConnectorConfig(accountId, connectorId, this.userId, file);
            List<SapConnection> sapConnections = sapConnectorBL.getSapConnectionFromFile(file);
            List<SapHealKpi> sapHealKpiList = sapConnectorBL.getSapHealKpiDetails(file);
            List<SapInstanceDetails> instanceDetails = sapConnectorBL.getSapInstanceDetailsFromFile(file);
            List<HealAgentInstance> healAgentInstances = sapConnectorBL.getSourceHealInstanceMappingFromFile(file);
            Map<String, WorkerParameter> workerParameters = getWorkerParametersFromAdvanceSettingsWithMap(file);

            List<AdapterChainRepositoryData> adapterChainRepositoryData = AdapterChainRepositoryData.getSapRepositoryData();
            List<AdapterChainRepository> repositories = getAdapterChainRepository(adapterChainRepositoryData, schemaName);

            List<WorkerParameter> workerParameters1 = statement.createStatementForWorkerParameterForSapFromMap(workerParameters);
            workerParameters1.sort(WorkerParameter.comparator);
            if (!sapConnections.isEmpty()) sapConnectorBL.addSapConnectionDetails(sapConnections);
            if (!sapHealKpiList.isEmpty()) sapConnectorBL.addSapHealKpis(sapHealKpiList, schemaName);
            if (!instanceDetails.isEmpty()) sapConnectorBL.addSapInstanceDetails(instanceDetails);
            if (!repositories.isEmpty()) connectorDataService.addAdapterChainRepository(schemaName, repositories);
            if (!workerParameters1.isEmpty()) connectorDataService.addWorkerParameters(schemaName, workerParameters1);
            if (!healAgentInstances.isEmpty()) addHealInstanceMapper(schemaName, healAgentInstances);
        } catch (Exception e) {
            log.error("Exception in sapDataProcessing : ", e);
            UploadErrorUtil.updateError(e, connectorId, accountId);
        }
    }

    public void dynaTraceDataProcessing(File file, String schemaName, Integer accountId, Integer connectorId) throws ParseException, IOException, DataProcessingException {

        DynaTraceConnectorBL dynaTraceConnectorBL = new DynaTraceConnectorBL();
        DynatraceConnectorDataService dynatraceConnectorDataService = new DynatraceConnectorDataService();
        ConnectorDataService connectorDataService = new ConnectorDataService();
        WorkerParameterStatement statement = new WorkerParameterStatement();
        addConnectorConfig(accountId, connectorId, this.userId, file);
        try {
            ConnectorDBData dbData = connectorDataService.getConnectorDBData(accountId, connectorId);
            if (dbData.getDbExists() == 0) {
                importSQL(dbData);
                connectorDataService.updateDBExistsStatus(accountId, connectorId, 1);
            }
            List<DynaTraceEntity> dynaTraceEntities = dynaTraceConnectorBL.getDynatraceEntityFromFile(file);
            List<DynatraceHealMetric> fileMetrics = dynaTraceConnectorBL.getDynatraceHealMetricFromFile(file);
            dynaTraceConnectorBL.entityMappingbyType(dynaTraceEntities, fileMetrics);

            List<HealAgentInstance> agentInstances = dynaTraceConnectorBL.getSourceHealInstanceMappingFromFile(file);
            List<DynatraceEntityBean> dynatraceEntityBeans = dynaTraceConnectorBL.getDynatraceEntityBeanFromData(dynaTraceEntities);
            Map<String, WorkerParameter> workerParameters = getWorkerParametersFromFileWithMap(file);
            Map<String, WorkerParameter> workerParametersAdvanced = getWorkerParametersFromAdvanceSettingsWithMap(file);

            List<WorkerParameter> parameters = statement.createStatementWorkerParameterForDynatraceWithMap(workerParameters, workerParametersAdvanced);
            List<AdapterChainRepositoryData> adapterChainRepositoryData = AdapterChainRepositoryData.getDynatraceRepositoryData();
            List<AdapterChainRepository> repositories = getAdapterChainRepository(adapterChainRepositoryData, schemaName);
            addHealInstanceMapper(schemaName, agentInstances);
            dynatraceConnectorDataService.addDynatraceEntity(dynatraceEntityBeans);
            connectorDataService.addAdapterChainRepository(schemaName, repositories);
            connectorDataService.addWorkerParameters(schemaName, parameters);

            List<DynatraceMetricBean> dynatraceMetricBeans = dynaTraceConnectorBL.getDynatraceMetricData(fileMetrics);
            int[] srcKpiIds = dynaTraceConnectorBL.addDynatraceMetric(dynatraceMetricBeans);
            if (srcKpiIds != null && srcKpiIds.length > 0) {
                List<DomainToHealKpiMapping> domainToHealKpiMappings = dynaTraceConnectorBL.getDomainToHealKpiMappingFromData(fileMetrics, srcKpiIds);
                connectorDataService.addDomainToHealKpiMapping(schemaName, domainToHealKpiMappings);
                List<HealKpi> healKpiList = dynaTraceConnectorBL.getHealKpiFromData(fileMetrics);
                connectorDataService.addHealKpi(schemaName, healKpiList);
            }
        } catch (Exception e) {
            log.error("Exception in dynaTraceDataProcessing : ", e);
            UploadErrorUtil.updateError(e, connectorId, accountId);

        }
    }

    public void azureDataProcessing(File file, String schemaName, int accountId, int connectorId) throws ParseException, IOException, DataProcessingException {
        AzureConnectorBL azureConnectorBL = new AzureConnectorBL();
        ConnectorDataService connectorDataService = new ConnectorDataService();
        WorkerParameterStatement statement = new WorkerParameterStatement();
        addConnectorConfig(accountId, connectorId, this.userId, file);
        try {
            List<WorkerParameter> workerParametersAdvance = getWorkerParametersFromAdvanceSettings(file);
            ConnectorDBData dbData = connectorDataService.getConnectorDBData(accountId, connectorId);
            if (dbData.getDbExists() == 0) {
                importSQL(dbData);
                connectorDataService.updateDBExistsStatus(accountId, connectorId, 1);
            }
            List<AdapterChainRepositoryData> adapterChainRepositoryData = AdapterChainRepositoryData.getAzureRepositoryData();
            List<AdapterChainRepository> adapterChainRepositories = getAdapterChainRepository(adapterChainRepositoryData, schemaName);

            connectorDataService.addAdapterChainRepository(schemaName, adapterChainRepositories);
            azureConnectorBL.addAzureHealKpis(azureConnectorBL.getAzureHealKpiDetails(file));
            azureConnectorBL.addAzureApplicationDetails(azureConnectorBL.getAzureApplicationDetails(file));
            azureConnectorBL.addAzureResourceDetails(azureConnectorBL.getAzureResourceDetails(file));
            azureConnectorBL.addAzureTokenDetails(azureConnectorBL.getAzureTokenDetails(file));
            List<WorkerParameter> workerParametersForDB = statement.createStatementWorkerParametersForAzure(getWorkerParametersFromFile(file), workerParametersAdvance);
            connectorDataService.addWorkerParameters(schemaName, workerParametersForDB);
            addHealInstanceMapper(schemaName, azureConnectorBL.getSourceHealInstanceMappingFromFile(file));
        } catch (Exception e) {
            log.error("Exception in azureDataProcessing : ", e);
            UploadErrorUtil.updateError(e, connectorId, accountId);

        }
    }

    public void appDynamicsDataProcessing(File file, String schemaName, Integer accountId, Integer connectorId) throws ParseException {
        ConnectorDataService connectorDataService = new ConnectorDataService();
        try {
            List<WorkerParameter> workerParametersAdvance = getWorkerParametersFromAdvanceSettings(file);
            ConnectorDBData dbData = connectorDataService.getConnectorDBData(accountId, connectorId);
            if (dbData.getDbExists() == 0) {
                importSQL(dbData);
                connectorDataService.updateDBExistsStatus(accountId, connectorId, 1);
            }
            addConnectorConfig(accountId, connectorId, this.userId, file);
            AppDynamicsConnectorBL appDynamicsConnectorBL = new AppDynamicsConnectorBL();
            WorkerParameterStatement statement = new WorkerParameterStatement();
            List<AdapterChainRepositoryData> adapterChainRepositoryData = AdapterChainRepositoryData.getAppDynamicsRepositoryData();
            List<AdapterChainRepository> adapterChainRepositories = getAdapterChainRepository(adapterChainRepositoryData, schemaName);

            connectorDataService.addAdapterChainRepository(schemaName, adapterChainRepositories);
            appDynamicsConnectorBL.addAppDynamicsHealKpis(appDynamicsConnectorBL.getAppDynamicsKpiDetails(file));
            appDynamicsConnectorBL.addAppDynamicsApplication(appDynamicsConnectorBL.getAppDynamicsApplications(file));
            List<WorkerParameter> workerParametersDB = statement.createStatementWorkerParametersAppDynamics(getWorkerParametersFromFile(file), workerParametersAdvance);
            connectorDataService.addWorkerParameters(schemaName, workerParametersDB);
            addHealInstanceMapper(schemaName, appDynamicsConnectorBL.getSourceHealInstanceMappingFromFile(file));
        } catch (Exception e) {
            log.error("Exception in appDynamicDataProcessing : ", e);
            UploadErrorUtil.updateError(e, connectorId, accountId);

        }
    }

    public void awsDataProcessing(File file, String schemaName, Integer accountId, Integer connectorId) throws ParseException, IOException, DataProcessingException {
        AwsConnectorBL awsConnectorBL = new AwsConnectorBL();
        ConnectorDataService dataService = new ConnectorDataService();
        WorkerParameterStatement statement = new WorkerParameterStatement();
        AwsConnectorDataService connectorDataService = new AwsConnectorDataService();
        addConnectorConfig(accountId, connectorId, this.userId, file);
        try {
            ConnectorDBData dbData = dataService.getConnectorDBData(accountId, connectorId);
            if (dbData.getDbExists() == 0) {
                importSQL(dataService.getConnectorDBData(accountId, connectorId));
                dataService.updateDBExistsStatus(accountId, connectorId, 1);
            }
            Map<String, WorkerParameter> workerParameters = getWorkerParametersFromAdvanceSettingsWithMap(file);
            List<AwsCredentialDetail> awsCredentialDetails = awsConnectorBL.getAwsCredentialDetailsFromFile(file);
            List<AwsInstanceDetail> awsInstanceDetails = awsConnectorBL.getAwsInstanceDetailsFromFile(file);
            List<AwsHealKpiDetail> awsHealKpiDetails = awsConnectorBL.getAwsHealKpiDetailsFromFile(file);
            List<AwsLogsDetail> awsLogsDetails = awsConnectorBL.getAwsLogsDetailsFromFile(file);
            List<AwsLogKpiDetail> awsLogKpiDetails = awsConnectorBL.getAwsLogKpiDetailsFromFile(file);
            List<AwsDimensionDetail> awsDimensionDetails = awsConnectorBL.getAwsDimensionsDetailsFromFile(file);
            List<HealAgentInstance> healAgentInstances = awsConnectorBL.getHealAgentInstanceFromFile(file);

            List<AwsCredentialDetailBean> detailBeans = awsConnectorBL.getCredentialBean(awsCredentialDetails);

            List<AwsInstanceBean> instanceBeans = awsConnectorBL.getInstanceBean(awsInstanceDetails);
            List<AwsLogsInstanceMapping> logsInstanceMappings = awsConnectorBL.getLogsInstanceMapping(awsInstanceDetails);
            List<AwsCredentialInstanceMapping> credentialInstanceMappings = awsConnectorBL.getCredentialInstanceMapping(awsInstanceDetails);
            List<AwsLogKpiInstanceMapping> logKpiInstanceMappings = awsConnectorBL.getLogKpiInstanceMapping(awsInstanceDetails);

            List<AwsMetricBean> metricBeans = awsConnectorBL.getMetrics(awsHealKpiDetails);
            List<HealKpi> healKpiList = awsConnectorBL.getHealKpiList(awsHealKpiDetails);
            List<DomainToHealKpiMapping> domainToHealKpiMappings = awsConnectorBL.getDomainToHealKpiList(awsHealKpiDetails);

            List<AwsLogsBean> logsDetails = awsConnectorBL.getLogs(awsLogsDetails);
            List<AwsCredentialLogsMapping> credentialLogsMappings = awsConnectorBL.getCredentialLogsMapping(awsLogsDetails);

            List<AwsLogKpiBean> logKpiBeans = awsConnectorBL.getLogKpis(awsLogKpiDetails);
            List<AwsCredentialLogKpiMapping> credentialLogKpiMappings = awsConnectorBL.getCredentialLogKpiMapping(awsLogKpiDetails);

            List<AwsDimensionBean> dimensionBeans = awsConnectorBL.getDimensions(awsDimensionDetails);
            List<AwsMetricDimensionMapping> metricDimensionMappings = awsConnectorBL.getMetricDimensionMapping(awsDimensionDetails);

            List<WorkerParameter> parameters = statement.createStatementWorkerParametersAwsWithMap(workerParameters);
            parameters.sort(WorkerParameter.comparator);
            List<AdapterChainRepositoryData> adapterChainRepositoryData = AdapterChainRepositoryData.getAwsRepositoryData();
            List<AdapterChainRepository> adapterChainRepositories = getAdapterChainRepository(adapterChainRepositoryData, schemaName);
            connectorDataService.addAwsCredentials(detailBeans);
            int[] ids = connectorDataService.addAwsCredentialInstanceMapping(credentialInstanceMappings);
            List<AwsCredentialMetricMapping> mappingList = awsConnectorBL.getCredentialMetricMapping(ids);

            connectorDataService.addAwsCredentialMetricMapping(mappingList);
            connectorDataService.addAwsInstances(instanceBeans);
            connectorDataService.addAwsLogsInstanceMapping(logsInstanceMappings);

            connectorDataService.addAwsLogKpiInstanceMapping(logKpiInstanceMappings);
            connectorDataService.addAwsMetrics(metricBeans);

            connectorDataService.addAwsLogs(logsDetails);
            connectorDataService.addAwsCredentialLogsMapping(credentialLogsMappings);
            connectorDataService.addAwsLogKpis(logKpiBeans);
            connectorDataService.addAwsCredentialLogKpiMapping(credentialLogKpiMappings);
            connectorDataService.addAwsDimension(dimensionBeans);
            connectorDataService.addAwsMetricDimensionMapping(metricDimensionMappings);

            dataService.addHealKpi(schemaName, healKpiList);
            dataService.addDomainToHealKpiMapping(schemaName, domainToHealKpiMappings);
            dataService.addWorkerParameters(schemaName, parameters);
            dataService.addAdapterChainRepository(schemaName, adapterChainRepositories);
            addHealInstanceMapper(schemaName, healAgentInstances);
        } catch (Exception e) {
            log.error("Exception in awsDataProcessing : ", e);
            UploadErrorUtil.updateError(e, connectorId, accountId);

        }
    }

    public void kubernetesDataProcessing(File file, String schemaName, Integer accountId, Integer connectorId) throws ParseException {
        ConnectorDataService connectorDataService = new ConnectorDataService();
        List<WorkerParameter> workerParametersAdvance = getWorkerParametersFromAdvanceSettings(file);
        try {
            ConnectorDBData dbData = connectorDataService.getConnectorDBData(accountId, connectorId);
            if (dbData.getDbExists() == 0) {
                importSQL(dbData);
                connectorDataService.updateDBExistsStatus(accountId, connectorId, 1);
            }
            addConnectorConfig(accountId, connectorId, this.userId, file);
            KubernetesConnectorBL kubernetesConnectorBL = new KubernetesConnectorBL();
            WorkerParameterStatement statement = new WorkerParameterStatement();
            List<AdapterChainRepositoryData> adapterChainRepositoryData = AdapterChainRepositoryData.getKubernetesRepositoryData();
            List<AdapterChainRepository> adapterChainRepositories = getAdapterChainRepository(adapterChainRepositoryData, schemaName);

            connectorDataService.addAdapterChainRepository(schemaName, adapterChainRepositories);
            kubernetesConnectorBL.addKubernetesConfiguration(schemaName, kubernetesConnectorBL.getKubernetesConfiguration(file));
            kubernetesConnectorBL.addPrometheusKpiResponsePath(schemaName, kubernetesConnectorBL.getPrometheusKpiResponsePath(file));
            kubernetesConnectorBL.addPrometheusApplication(schemaName, kubernetesConnectorBL.getPrometheusApplication(file));
            kubernetesConnectorBL.addA1LogscanEndpt(schemaName, kubernetesConnectorBL.getA1LogscanEndpoint(file));
            kubernetesConnectorBL.addElasticSearchResponsePath(schemaName, kubernetesConnectorBL.getElasticSearchResponsePath(file));
            kubernetesConnectorBL.addKubernetesHealAccessToken(schemaName, kubernetesConnectorBL.getKubernetesHealAccessToken(file));
            kubernetesConnectorBL.addKubernetesHealInstanceCreationPayloads(schemaName, kubernetesConnectorBL.getKubernetesHealInstanceCreationPayload(file));
            List<WorkerParameter> workerParametersDB = statement.createStatementWorkerParametersKubernetes(getWorkerParametersFromFileWithMap(file), workerParametersAdvance);
            connectorDataService.addWorkerParameters(schemaName, workerParametersDB);
            kubernetesConnectorBL.addPrometheusHealKpis(schemaName, kubernetesConnectorBL.getPrometheusKpis(file));
        } catch (Exception e) {
            log.error("Exception in kubernetesDataProcessing :", e);
            UploadErrorUtil.updateError(e, connectorId, accountId);

        }
    }

    public String process(Request request, Integer accountId, File file, Integer connectorId)
            throws DataProcessingException, ControlCenterException, ParseException, FileUploadException, SQLException, IOException {

        String authKey = request.headers("Authorization");
        JWTData jwtData = KeyCloakAuthService.extractUserDetails(authKey);
        String userId = jwtData.getSub();
        this.userId = userId;

        ConnectorDataService connectorDataService = new ConnectorDataService();

        String connectorName = connectorDataService.getConnectorNameWithConnectorId(connectorId, accountId);

        schemaName = ConnectorConstants.SCHEMA_PREFIX + connectorName.toLowerCase();

        switch (connectorName) {
            case "Sap":
                sapConnectorDataProcessing(file, schemaName, accountId, connectorId);
                break;
            case "Dynatrace":
                dynaTraceDataProcessing(file, schemaName, accountId, connectorId);
                break;
            case "Azure":
                azureDataProcessing(file, schemaName, accountId, connectorId);
                break;
            case "Appdynamics":
                appDynamicsDataProcessing(file, schemaName, accountId, connectorId);
                break;
            case "Aws":
                awsDataProcessing(file, schemaName, accountId, connectorId);
                break;
            case "Kubernetes":
                kubernetesDataProcessing(file, schemaName, accountId, connectorId);
                break;
        }
        return connectorName;
    }

    public void addConnectorConfig(int accountId, int connectorId, String userId, File file) throws IOException, ParseException, DataProcessingException {
        ConnectorDataService connectorDataService = new ConnectorDataService();
        int connectorTemplateId = connectorDataService.getTheConnectorTemplateId(connectorId, accountId);
        String connectorName = connectorDataService.getConnectorNameWithConnectorId(connectorId, accountId);
        List<Integer> connectorConfigurationIds;
        List<ConnectorChainConfig> connectorChainConfigList = getConnectorChainConfigFromFile(file);
        if (connectorName.equalsIgnoreCase("Appdynamics"))
            connectorConfigurationIds = addConnectorConfiguration(schemaName, getConnectorChainConfigFromFileAppD(file));
        else
            connectorConfigurationIds = addConnectorConfiguration(schemaName, connectorChainConfigList);
        Timestamp date = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
        String encodedString = Base64.getEncoder().encodeToString(FileUtils.readFileToByteArray(file));
        ConnectorTemplateDataBean connectorTemplateDataBean = ConnectorTemplateDataBean.builder()
                .accountId(accountId)
                .templateDataContent(encodedString)
                .connectorDetailsId(connectorId)
                .connectorTemplateId(connectorTemplateId)
                .userDetailsId(userId)
                .status(1)
                .uploadStatus(UploadStatus.SUCCESS.getStatus())
                .createdTime(date)
                .updatedTime(date)
                .build();

        try {
            List<ConnectorTemplateDataBean> existingConnectorTemplateData = connectorDataService.getConnectorData(connectorId, accountId);
            Optional<ConnectorTemplateDataBean> connectorConfigToUpdate = existingConnectorTemplateData.parallelStream().filter(f -> f.getConnectorTemplateId() == connectorId).findFirst();
            if (connectorConfigToUpdate.isPresent()) {
                connectorTemplateDataBean.setTemplateDataContent(encodedString);
                connectorTemplateDataBean.setStatus(1);
                connectorTemplateDataBean.setUploadStatus(UploadStatus.SUCCESS.getStatus());
                connectorTemplateDataBean.setUserDetailsId(userId);
                connectorTemplateDataBean.setCreatedTime(date);
                connectorTemplateDataBean.setUpdatedTime(date);

                connectorDataService.updateConnectorConfigDataById(connectorTemplateDataBean);
            } else {
                connectorDataService.addConnectorDataTemplate(connectorTemplateDataBean);
            }
            for (Integer id : connectorConfigurationIds) {
                connectorDataService.updateConnectorTemplateDataIdInAdapterConfigTable(schemaName, connectorTemplateDataBean.getConnectorDetailsId(), id);
            }
        } catch (Exception ex) {
            log.debug("Connector configuration already exists. Details: ", ex);
            UploadErrorUtil.updateError(ex, connectorId, accountId);
            throw new DataProcessingException("Connector configuration already exists.");
        }
    }

    public List<ConnectorChainConfig> getConnectorChainConfigFromFile(File fileName) throws IOException {
        List<ConnectorChainConfig> connectorChainConfigList = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(1);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro = sheet.getRow(i);
                if (ro.getCell(ConnectorConstants.CHAIN_NAME_INDEX) == null) {
                    break;
                }
                ConnectorChainConfig connectorChainConfig = ConnectorChainConfig.builder()
                        .id(i - 2)
                        .chainName(String.valueOf(ro.getCell(ConnectorConstants.CHAIN_NAME_INDEX).getNumericCellValue()))
                        .sleepInterval((int) ro.getCell(ConnectorConstants.CHAIN_SLEEP_INTERVAL_INDEX).getNumericCellValue())
                        .pullHistoricData((int) ro.getCell(ConnectorConstants.CHAIN_PULL_HISTORICAL_DATA_INDEX).getNumericCellValue())
                        .historicStartDate((long) ro.getCell(ConnectorConstants.CHAIN_HISTORICAL_STARTDATE_INDEX).getNumericCellValue())
                        .historicEndDate((long) ro.getCell(ConnectorConstants.CHAIN_HISTORICAL_ENDDATE_INDEX).getNumericCellValue())
                        .historicInterval((int) ro.getCell(ConnectorConstants.CHAIN_HISTORICAL_INTERVAL_INDEX).getNumericCellValue())
                        .disableChain(0)
                        .extractorId(i - 2)
                        .build();
                connectorChainConfigList.add(connectorChainConfig);
            }
        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException(ex, "File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the Chain Configuration data from excel file {}", ex.getMessage());
            throw new IOException("Exception when reading the Chain Configuration data from excel file", ex);
        }
        return connectorChainConfigList;
    }

    public List<ConnectorChainConfig> getConnectorChainConfigFromFileAppD(File fileName) {
        List<ConnectorChainConfig> connectorChainConfigList = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(1);
            int[] extractorId = new int[]{1, 1, 4, 5};
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro = sheet.getRow(i);
                if (ro.getCell(ConnectorConstants.CHAIN_NAME_INDEX) == null) {
                    break;
                }
                ConnectorChainConfig connectorChainConfig = ConnectorChainConfig.builder()
                        .id(i - 2)
                        .chainName(String.valueOf(ro.getCell(ConnectorConstants.CHAIN_NAME_INDEX).getNumericCellValue()))
                        .sleepInterval((int) ro.getCell(ConnectorConstants.CHAIN_SLEEP_INTERVAL_INDEX).getNumericCellValue())
                        .pullHistoricData((int) ro.getCell(ConnectorConstants.CHAIN_PULL_HISTORICAL_DATA_INDEX).getNumericCellValue())
                        .historicStartDate((long) ro.getCell(ConnectorConstants.CHAIN_HISTORICAL_STARTDATE_INDEX).getNumericCellValue())
                        .historicEndDate((long) ro.getCell(ConnectorConstants.CHAIN_HISTORICAL_ENDDATE_INDEX).getNumericCellValue())
                        .historicInterval((int) ro.getCell(ConnectorConstants.CHAIN_HISTORICAL_INTERVAL_INDEX).getNumericCellValue())
                        .disableChain(0)
                        .extractorId(extractorId[i - 3])
                        .build();
                connectorChainConfigList.add(connectorChainConfig);
            }
        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the Chain Configuration data from excel file {}", ex.getMessage());
            throw new FileUploadException("Exception when reading the Chain Configuration data from excel file");
        }
        return connectorChainConfigList;
    }


    public List<WorkerParameter> getWorkerParametersFromFile(File fileName) {
        List<WorkerParameter> workerParameters = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro;
                if (sheet.getRow(i) != null) ro = sheet.getRow(i);
                else continue;
                if (ro.getCell(0) != null) {
                    WorkerParameter workerParameter = WorkerParameter.builder()
                            .name(String.valueOf(ro.getCell(ConnectorConstants.COMMON_WORKER_PARAMETER_KEY_INDEX).getNumericCellValue()))
                            .value(String.valueOf(ro.getCell(ConnectorConstants.COMMON_WORKER_PARAMETER_VALUE_INDEX).getNumericCellValue()))
                            .build();
                    workerParameters.add(workerParameter);
                }

            }
        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException("File is not found at given location please check and try ");
        } catch (IOException ex) {
            log.error("Exception when reading the data from excel file {}", ex.getMessage());
            throw new FileUploadException("Exception when reading the data from excel file");
        }

        return workerParameters;
    }

    public Map<String, WorkerParameter> getWorkerParametersFromFileWithMap(File fileName) {
        Map<String, WorkerParameter> workerParameters = new HashMap<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro;
                if (sheet.getRow(i) != null) ro = sheet.getRow(i);
                else continue;
                if (ro.getCell(0) != null) {
                    String key = String.valueOf(ro.getCell(ConnectorConstants.COMMON_WORKER_PARAMETER_KEY_INDEX).getNumericCellValue());
                    WorkerParameter workerParameter = WorkerParameter.builder()
                            .name(key)
                            .value(String.valueOf(ro.getCell(ConnectorConstants.COMMON_WORKER_PARAMETER_VALUE_INDEX).getNumericCellValue()))
                            .build();
                    workerParameters.put(key, workerParameter);
                }

            }
        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException("File is not found at given location please check and try ");
        } catch (IOException ex) {
            log.error("Exception when reading the data from excel file {}", ex.getMessage());
            throw new FileUploadException("Exception when reading the data from excel file");
        }

        return workerParameters;
    }

    public List<WorkerParameter> getWorkerParametersFromAdvanceSettings(File fileName) {
        List<WorkerParameter> workerParameters = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(ConnectorConstants.ADVANCE_SETTING_SHEET_INDEX);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro = sheet.getRow(i);
                if (ro.getCell(ConnectorConstants.ADVANCE_WORKER_PARAMETER_KEY_INDEX) == null) break;
                WorkerParameter workerParameter = WorkerParameter.builder()
                        .name(String.valueOf(ro.getCell(ConnectorConstants.ADVANCE_WORKER_PARAMETER_KEY_INDEX).getNumericCellValue()))
                        .value(String.valueOf(ro.getCell(ConnectorConstants.ADVANCE_WORKER_PARAMETER_VALUE_INDEX).getNumericCellValue()))
                        .build();
                workerParameters.add(workerParameter);

            }
        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException("Exception when reading excel file ");
        } catch (IOException ex) {
            log.error("Exception when reading the data from excel file {}", ex.getMessage());
            throw new FileUploadException("Exception when reading excel file ");
        }
        return workerParameters;
    }

    public Map<String, WorkerParameter> getWorkerParametersFromAdvanceSettingsWithMap(File fileName) {
        Map<String, WorkerParameter> workerParameters = new HashMap<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(ConnectorConstants.ADVANCE_SETTING_SHEET_INDEX);

            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row.getCell(ConnectorConstants.ADVANCE_WORKER_PARAMETER_KEY_INDEX) == null)
                    break;

                String key = String.valueOf(row.getCell(ConnectorConstants.ADVANCE_WORKER_PARAMETER_KEY_INDEX).getNumericCellValue());
                WorkerParameter workerParameter = WorkerParameter.builder()
                        .name(key)
                        .value(String.valueOf(row.getCell(ConnectorConstants.ADVANCE_WORKER_PARAMETER_VALUE_INDEX).getNumericCellValue()))
                        .build();
                workerParameters.put(key, workerParameter);
            }
            workerParameters.put("Heal-Cert", WorkerParameter.builder().name("Heal-Cert").value("").build());
        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException("Exception when reading excel file ");
        } catch (IOException ex) {
            log.error("Exception when reading the data from excel file {}", ex.getMessage());
            throw new FileUploadException("Exception when reading excel file ");
        }
        return workerParameters;
    }

    public List<Integer> addConnectorConfiguration(String schemaName, List<ConnectorChainConfig> connectorChainConfigList) {
        List<ConnectorSettingsEntity> connectorSettingsEntityList = connectorChainConfigList.stream()
                .map(this::getConnectorSettingDetails)
                .collect(Collectors.toList());
        ConnectorDataService connectorDataService = new ConnectorDataService();
        List<Integer> list = null;

        int[] ids = connectorDataService.addAdvanceConnectorSettings(schemaName, connectorSettingsEntityList);
        if (ids != null)
            list = Arrays.stream(ids)
                    .boxed()
                    .collect(Collectors.toList());
        return list;
    }

    public ConnectorSettingsEntity getConnectorSettingDetails(ConnectorChainConfig connectorChainConfig) {
        return ConnectorSettingsEntity.builder()
                .id(connectorChainConfig.getId())
                .chainName(connectorChainConfig.getChainName())
                .sleepInterval(connectorChainConfig.getSleepInterval())
                .pullHistoricData(connectorChainConfig.getPullHistoricData())
                .historicStartDate(connectorChainConfig.getHistoricStartDate())
                .historicEndDate(connectorChainConfig.getHistoricEndDate())
                .historicInterval(connectorChainConfig.getHistoricInterval())
                .disableChain(connectorChainConfig.getDisableChain())
                .disableChainReload(ConnectorConstants.CHAIN_DISABLE_CHAIN_RELOAD)
                .extractorId(connectorChainConfig.getExtractorId())
                .testStartDate(ConnectorConstants.CHAIN_TEST_START_DATE)
                .testEndDate(ConnectorConstants.CHAIN_TEST_END_DATE)
                .addSysLoader(ConnectorConstants.CHAIN_ADD_SYS_LOADER)
                .reloadInterval(ConnectorConstants.CHAIN_REALOAD_INTERVAL)
                .processorThreadPoolSize(ConnectorConstants.CHAIN_PROCESSOR_THREADPOOL_SIZE)
                .loaderThreadPoolSize(ConnectorConstants.CHAIN_LOADER_THREADPOOL_SIZE)
                .deltaInMinutes(ConnectorConstants.CHAIN_DELTA_IN_MINUTES)
                .backPressureMaxSize(ConnectorConstants.CHAIN_BACK_PRESSURE_MAX_SIZE)
                .backPressureStrategy(ConnectorConstants.CHAIN_BACK_PRESSURE_STRATEGY)
                .preProcessor(ConnectorConstants.CHAIN_PRE_PROCESSOR)
                .build();
    }

    public void addHealInstanceMapper(String schema, List<HealAgentInstance> healAgentInstances) {
        List<LogscanHealInstanceMapper> logScanHealInstanceMappers = healAgentInstances.stream()
                .map(this::getHealInstanceMapper)
                .collect(Collectors.toList());
        ConnectorDataService dataService = new ConnectorDataService();
        dataService.addLogscanHealInstanceMapper(schema, logScanHealInstanceMappers);
    }

    public LogscanHealInstanceMapper getHealInstanceMapper(HealAgentInstance agentInstance) {
        return LogscanHealInstanceMapper
                .builder().sourceInstanceName(agentInstance.getSourceInstanceName())
                .agentIdentifier(agentInstance.getAgentIdentifier())
                .healInstanceName(agentInstance.getHealInstanceName())
                .build();
    }

    public void importSQL(ConnectorDBData dbData) throws SQLException {
        Connection conn = MySQLConnectionManager.INSTANCE.getHandle().open().getConnection();
        String sql = dbData.getSqlData();
        try (Statement st = conn.createStatement()) {
            Scanner s = new Scanner(new ByteArrayInputStream(Base64.getDecoder().decode(sql.getBytes(StandardCharsets.UTF_8))));
            s.useDelimiter("(;(\r)?\n)|(--\n)");
            while (s.hasNext()) {
                String line = s.next();
                if (line.startsWith("/*!") && line.endsWith("*/")) {
                    int i = line.indexOf(' ');
                    line = line.substring(i + 1, line.length() - " */".length());
                }
                if (line.trim().length() > 0) {
                    st.execute(line);
                }
            }
        }
    }

    public List<AdapterChainRepository> getAdapterChainRepository(List<AdapterChainRepositoryData> adapterChainRepositoryData, String schemaName) {
        List<AdapterChainRepository> adapterChainRepositories = new ArrayList<>();
        for (AdapterChainRepositoryData acr : adapterChainRepositoryData) {
            if (acr.getName().equalsIgnoreCase("AppsoneRepository")) {
                adapterChainRepositories.add(AdapterChainRepository.builder()
                        .name(acr.getName())
                        .className(acr.getClassName())
                        .autoConnect(acr.getAutoConnect())
                        .poolMaxSize(acr.getPoolMaxSize())
                        .poolMinSize(acr.getPoolMinSize())
                        .username(PERCONA_USERNAME)
                        .password(PERCONA_PASSWORD)
                        .connectionUrl(ConfProperties.getString(ConnectorConstants.CONNECTOR_PERCONA_URL) + "appsone" + ConnectorConstants.CONNECTOR_DB_PARAMS)
                        .build());
            } else
                adapterChainRepositories.add(AdapterChainRepository.builder()
                        .name(acr.getName())
                        .className(acr.getClassName())
                        .autoConnect(acr.getAutoConnect())
                        .poolMaxSize(acr.getPoolMaxSize())
                        .poolMinSize(acr.getPoolMinSize())
                        .username(PERCONA_USERNAME)
                        .password(PERCONA_PASSWORD)
                        .connectionUrl(ConfProperties.getString(ConnectorConstants.CONNECTOR_PERCONA_URL) + schemaName + ConnectorConstants.CONNECTOR_DB_PARAMS)
                        .build());
        }
        return adapterChainRepositories;
    }

}
