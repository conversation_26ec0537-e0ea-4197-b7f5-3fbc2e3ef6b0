package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.RulesDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.HttpPatternDataBean;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.pojo.AddRulesPojo;
import com.appnomic.appsone.controlcenter.util.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;


public class RulesBL {

    private RulesBL() {
    }

    private static final ObjectMapper obj_mapper = CommonUtils.getObjectMapperWithHtmlEncoder();
    private static final Logger logger = LoggerFactory.getLogger(RulesBL.class);

    private static final String agentRuleType = ConfProperties.getString(Constants.AGENT_RULES_TYPE_NAME, Constants.AGENT_RULES_TYPE_REQUEST_DATA);
    private static final String segmentURIType = ConfProperties.getString(Constants.SEGMENT_URI_TYPE_NAME, Constants.SEGMENT_URI_TYPE_VALUE);
    private static final String twoSegmentValue = ConfProperties.getString(Constants.SEGMENT_VALUE_NAME, Constants.SEGMENT_VALUE);
    private static final String ruleNameTwoSegment = ConfProperties.getString(Constants.RULE_NAME, Constants.RULE_VALUE);
    private static final String payloadTypeId = ConfProperties.getString(Constants.PAYLOAD_TYPE_NAME, Constants.PAYLOAD_TYPE_VALUE);
    private static final String httpTypeId = ConfProperties.getString(Constants.HTTP_TYPE_NAME, Constants.HTTP_TYPE_VALUE);
    private static final String ruleNameSingleSegment = ConfProperties.getString(Constants.SINGLE_SEGMENT_RULE_NAME, Constants.SINGLE_SEGMENT_RULE_VALUE);
    private static final String singleSegmentValue = ConfProperties.getString(Constants.SINGLE_SEGMENT_VALUE_NAME, Constants.SINGLE_SEGMENT_VALUE);


    public static AddRulesPojo addClientValidations(Request request) throws RequestException {

        ValidationUtils.commonClientValidations(request);

        String serviceId = request.params(Constants.SERVICE_ID);

        if (StringUtils.isEmpty(serviceId)) {
            logger.error(UIMessages.EMPTY_SERVICE);
            throw new RequestException(UIMessages.EMPTY_SERVICE);
        }

        if (StringUtils.isEmpty(request.body())) {
            logger.error(UIMessages.REQUEST_NULL);
            throw new RequestException(UIMessages.REQUEST_NULL);
        }

        AddRulesPojo rulesPojo;

        try {
            rulesPojo = obj_mapper.readValue(request.body(),
                    new TypeReference<AddRulesPojo>() {
                    });

        } catch (IOException e) {
            logger.error(UIMessages.JSON_INVALID + " : {}",e.getMessage());
            throw new RequestException(UIMessages.JSON_INVALID);
        }

        rulesPojo.validate();
        if (!rulesPojo.getError().isEmpty()) {
            logger.error(rulesPojo.getError().toString());
            throw new RequestException(rulesPojo.getError().toString());
        }

        rulesPojo.setServiceId(serviceId);

        return  rulesPojo;

    }

    public static RulesBean addServerValidations(AddRulesPojo rulesPojo, String authToken, String accountIdentifier) throws RequestException, ParseException {
        UserAccountBean userAccBean = ValidationUtils.commonServerValidations(authToken, accountIdentifier);
        String userId = userAccBean.getUserId();
        int accountId = userAccBean.getAccount().getId();

        rulesPojo.setUserDetails(userId);
        rulesPojo.setAccountId(accountId);
        rulesPojo.setIsDefault(1);

        return addRule(rulesPojo);

    }


    public static RulesBean addRule(AddRulesPojo addRulesPojo) throws RequestException, ParseException {

        ViewTypes ruleType = MasterCache.getMstTypeForSubTypeName(Constants.RULES_TYPE, addRulesPojo.getRuleType());
        if (ruleType == null) {
            String err = "Sub type is not found for the given rule type: "+ addRulesPojo.getRuleType();
            logger.error(err);
            throw new RequestException(err);
        }

        TagDetailsBean controllerTag = MasterCache.getTagDetails(Constants.CONTROLLER_TAG);

        int ruleName = ValidationUtils.getRuleName(addRulesPojo.getAccountId(), Integer.parseInt(addRulesPojo.getServiceId()), controllerTag.getId(), addRulesPojo.getRuleName().trim());
        if (ruleName > 0) {
            String err = "Rule name : "+ addRulesPojo.getRuleName() + "  already exists";
            logger.error(err);
            throw new RequestException(err);
        }

        List<AddRulesPojo> ruleUniquePatterns = ValidationUtils.getCompletePattern(Constants.RULES_TABLE,addRulesPojo.getAccountId());
        AddRulesPojo existsRule = ruleUniquePatterns.stream()
                .filter(rulesPojo -> rulesPojo.getCompletePattern().equalsIgnoreCase(addRulesPojo.getUniquePatter()) && rulesPojo.getServiceId().equals(addRulesPojo.getServiceId()))
                .findAny()
                .orElse(null);
        if (existsRule != null) {
            String err = "Rule pattern : "+ addRulesPojo.getUniquePatter() + "  already exists";
            logger.error(err);
            throw new RequestException(err);
        }

        RulesBean rulesBean = new RulesBean();
        Timestamp date = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
        rulesBean.setCreatedTime(date);
        rulesBean.setUpdatedTime(date);
        rulesBean.setAccountId(addRulesPojo.getAccountId());
        rulesBean.setName(addRulesPojo.getRuleName().trim());
        rulesBean.setRuleTypeId(ruleType.getSubTypeId());
        rulesBean.setStatus(1);
        rulesBean.setIsDefault(addRulesPojo.getIsDefault());
        rulesBean.setUserDetails(addRulesPojo.getUserDetails());
        rulesBean.setMonitorEnabled(Integer.valueOf(Constants.RULES_MONITORING_ENABLED_DEFAULT));
        rulesBean.setDiscoveryEnabled(Integer.valueOf(Constants.RULES_DISCOVERY_ENABLED_DEFAULT));

        List<RuleDetailsBean> ruleDetailsBeans = RulesDataService.getRuleList(Integer.parseInt(addRulesPojo.getServiceId()), Constants.RULES_TABLE, controllerTag.getId(), addRulesPojo.getAccountId(), null);
        if (ruleDetailsBeans != null && !ruleDetailsBeans.isEmpty()) {
            rulesBean.setOrder(ruleDetailsBeans.size() + 1);
        } else if(addRulesPojo.getOrder() == 1 || addRulesPojo.getOrder() == 2) {
            //This else condition set's order when default rules are created with correct ordering.
            rulesBean.setOrder(addRulesPojo.getOrder());
        } else {
            rulesBean.setOrder(1);
        }

        return rulesBean;
    }

    public static HttpPatternDataBean addHttpPatterns(AddRulesPojo addRulesPojo) throws RequestException, ParseException {

        ViewTypes httpMethodType = MasterCache.getMstTypeForSubTypeName(Constants.HTTP_METHOD_TYPE, addRulesPojo.getHttpMethodType());
        if (httpMethodType == null) {
            String err = "Sub type is not found for the given http method type: "+ addRulesPojo.getHttpMethodType();
            logger.error(err);
            throw new RequestException(err);
        }

        ViewTypes payLoadType = MasterCache.getMstTypeForSubTypeName(Constants.PAY_LOAD_TYPE, addRulesPojo.getPayloadType());
        if (payLoadType == null) {
            String err = "Sub type is not found for the given  payload type: "+ addRulesPojo.getPayloadType();
            logger.error(err);
            throw new RequestException(err);
        }

        HttpPatternDataBean httpPatternDataBean = new HttpPatternDataBean();
        Timestamp date = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
        httpPatternDataBean.setCreatedTime(date);
        httpPatternDataBean.setUpdatedTime(date);
        httpPatternDataBean.setAccountId(addRulesPojo.getAccountId());
        httpPatternDataBean.setUserDetails(addRulesPojo.getUserDetails());
        httpPatternDataBean.setHttpMethodTypeId(httpMethodType.getSubTypeId());
        switch (addRulesPojo.getSegmentURIType()) {
            case "First":
                httpPatternDataBean.setFirstUriSegments(Integer.parseInt(addRulesPojo.getSegmentValue()));
                break;
            case "Last":
                httpPatternDataBean.setLastUriSegments(Integer.parseInt(addRulesPojo.getSegmentValue()));
                break;
            case "Complete":
                httpPatternDataBean.setCompleteURI(Boolean.parseBoolean(addRulesPojo.getSegmentValue()));
                break;
            case "Custom":
                httpPatternDataBean.setCustomSegments(addRulesPojo.getSegmentValue());
                break;
        }


        httpPatternDataBean.setCompletePattern(addRulesPojo.getUniquePatter());
        httpPatternDataBean.setPayloadTypeId(payLoadType.getSubTypeId());

        return httpPatternDataBean;

    }



    public static int[] addRule(int serviceId, String serviceIdentifier, int accountId, String userId, Handle handle)  throws Exception {
        int[] ruleIds = new int[2];

        List<AddRulesPojo> defaultRuleList = new ArrayList<>();
        defaultRuleList.add(getFirstDefaultRule(accountId, serviceId, userId));
        defaultRuleList.add(getSecondDefaultRule(accountId, serviceId, userId));

        int controllerId = MasterCache.getTagDetails(Constants.CONTROLLER_TAG).getId();
        for( AddRulesPojo addRulesPojo: defaultRuleList ) {

            HttpPatternDataBean httpPatternDataBean = addHttpPatterns(addRulesPojo);

            RulesBean rulesBean = addRule(addRulesPojo);
            ruleIds[0] = addRule(rulesBean, httpPatternDataBean, addRulesPojo.getServiceId(), serviceIdentifier, controllerId, handle);
            if(ruleIds[0] != -1){
                logger.info("Rule is added successfully :{}", addRulesPojo.getRuleName());
            }
            else{
                logger.error("Failed to add the Rule :{}", addRulesPojo.getRuleName());
                return null;
            }
        }

        ViewTypes ejbRuleType = MasterCache.getMstTypeForSubTypeName(
                Constants.RULES_TYPE, Constants.AGENT_RULES_TYPE_EJB_DATA);

        if (ejbRuleType != null && ejbRuleType.getSubTypeName().compareTo(agentRuleType) != 0) {

            for( AddRulesPojo addRulesPojo: defaultRuleList ) {
                addRulesPojo.setRuleType(ejbRuleType.getSubTypeName());
                addRulesPojo.setRuleName(ejbRuleType.getSubTypeName() + " Rule");

                HttpPatternDataBean httpPatternDataBean = addHttpPatterns(addRulesPojo);
                RulesBean rulesBean = addRule(addRulesPojo);
                ruleIds[1] = addRule(rulesBean, httpPatternDataBean, addRulesPojo.getServiceId(), serviceIdentifier, controllerId, handle);
                if(ruleIds[1] != -1){
                    logger.info("Rule is added successfully :{}", addRulesPojo.getRuleName());
                }
                else{
                    logger.error("Failed to add the Rule :{}", addRulesPojo.getRuleName());
                    return null;
                }
            }

        }

        return ruleIds;
    }

    private static AddRulesPojo getFirstDefaultRule(int accountId, int serviceId, String userId) {
        AddRulesPojo addRulesPojo = new AddRulesPojo();

        addRulesPojo.setRuleName(segmentURIType + " " + ruleNameSingleSegment + " " + "Segment");
        addRulesPojo.setRuleType(agentRuleType);
        addRulesPojo.setSegmentURIType(segmentURIType);
        addRulesPojo.setSegmentValue(singleSegmentValue);
        addRulesPojo.setCompletePattern(addRulesPojo.getUniquePatter());
        addRulesPojo.setServiceId(String.valueOf(serviceId));
        addRulesPojo.setUserDetails(userId);
        addRulesPojo.setAccountId(accountId);
        addRulesPojo.setIsDefault(1);
        addRulesPojo.setHttpMethodType(httpTypeId);
        addRulesPojo.setPayloadType(payloadTypeId);
        addRulesPojo.setOrder(2);
        addRulesPojo.setDiscoveryEnabled(Integer.valueOf(Constants.RULES_DISCOVERY_ENABLED_DEFAULT));
        addRulesPojo.setMonitorEnabled(Integer.valueOf(Constants.RULES_MONITORING_ENABLED_DEFAULT));
        return addRulesPojo;
    }

    private static AddRulesPojo getSecondDefaultRule(int accountId, int serviceId, String userId) {
        AddRulesPojo addRulesPojo = new AddRulesPojo();

        addRulesPojo.setRuleName(segmentURIType + " " + ruleNameTwoSegment + " " + "Segments");
        addRulesPojo.setRuleType(agentRuleType);
        addRulesPojo.setSegmentURIType(segmentURIType);
        addRulesPojo.setSegmentValue(twoSegmentValue);
        addRulesPojo.setCompletePattern(addRulesPojo.getUniquePatter());
        addRulesPojo.setServiceId(String.valueOf(serviceId));
        addRulesPojo.setUserDetails(userId);
        addRulesPojo.setAccountId(accountId);
        addRulesPojo.setIsDefault(1);
        addRulesPojo.setHttpMethodType(httpTypeId);
        addRulesPojo.setPayloadType(payloadTypeId);
        addRulesPojo.setOrder(1);
        addRulesPojo.setDiscoveryEnabled(Integer.valueOf(Constants.RULES_DISCOVERY_ENABLED_DEFAULT));
        addRulesPojo.setMonitorEnabled(Integer.valueOf(Constants.RULES_MONITORING_ENABLED_DEFAULT));
        return addRulesPojo;
    }

    private static int addRule(RulesBean rulesBean, HttpPatternDataBean httpPatternDataBean, String serviceId, String serviceIdentifier, int controllerId, Handle handle) {
        int ruleId = RulesDataService.addRule(rulesBean, handle);
        if (ruleId != -1) {
            int tagMappingId = TagMappingBL.addTagMapping(controllerId, ruleId, Constants.RULES_TABLE,
                    serviceId, serviceIdentifier, rulesBean.getUserDetails(), rulesBean.getAccountId(), handle);
            if (tagMappingId != -1) {
                logger.info("Tag mapping data is added successfully for rule :{}", rulesBean.getName());
            } else {
                logger.error("Failed to add the Tag mapping data for rule :{}", rulesBean.getName());
                return -1;
            }

            httpPatternDataBean.setRuleId(ruleId);
            int httpPatternId = RulesDataService.addHttpPatterns(httpPatternDataBean, handle);
            if (httpPatternId != -1) {
                logger.info("Http patterns data is added successfully for rule :{}", rulesBean.getName());
            } else {
                logger.error("Failed to add the Http patterns data for rule :{}", rulesBean.getName());
                return -1;
            }
        }

        return ruleId;
    }


}
