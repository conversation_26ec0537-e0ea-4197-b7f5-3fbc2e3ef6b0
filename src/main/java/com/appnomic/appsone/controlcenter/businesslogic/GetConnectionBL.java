package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.enums.DiscoveryStatus;
import com.appnomic.appsone.controlcenter.beans.ConnectionDetailsBean;
import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.AutoDiscoveryDiscoveredConnections;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.AutoDiscoveryDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ConnectionDetailsDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.GetConnection;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class GetConnectionBL implements BusinessLogic<Object, Integer, List<GetConnection>> {

    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty");
            throw new ClientException("Invalid account identifier");
        }

        return UtilityBean.builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .build();
    }

    public Integer serverValidation(UtilityBean<Object> utilityBean) throws ServerException {

        String accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        int accountId = account.getId();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }
        return accountId;
    }

    public List<GetConnection> process(Integer accountId) throws DataProcessingException {

        try {

            List<ControllerBean> controllerBeanList = new ControllerDataService().getServices(null);
            List<ConnectionDetailsBean> healConnectionDetailsBean = ConnectionDetailsDataService.getConnectionWithAccountId(accountId, null);
            List<AutoDiscoveryDiscoveredConnections> discoveredConnectionsBeanList = new AutoDiscoveryDataService().getUsefulDiscoveredConnections(null);

            /*
            Heal Side
             */
            List<GetConnection> healConnections = healConnectionDetailsBean.parallelStream()
                    .map(c -> GetConnection.builder()
                            .sourceServiceId(c.getSourceId())
                            .destinationServiceId(c.getDestinationId())
                            .process(c.getIsDiscovery() == 0 ? "Manual" : "Auto")
                            .status(DiscoveryStatus.ADDED_TO_SYSTEM)
                            .lastDiscoveryRunTime(DateTimeUtil.getGMTToEpochTime(String.valueOf(c.getUpdatedTime())))
                            .build())
                    .collect(Collectors.toList());

            healConnections.forEach(one -> controllerBeanList
                    .forEach(two -> {
                        if (one.getSourceServiceId() == two.getId()) {
                            one.setSourceServiceName(two.getName());
                            one.setSourceServiceIdentifier(two.getIdentifier());
                        }
                        if (one.getDestinationServiceId() == two.getId()) {
                            one.setDestinationServiceName(two.getName());
                            one.setDestinationServiceIdentifier(two.getIdentifier());
                        }
                    }));

            List<GetConnection> filteredHealConnections = healConnections.stream()
                    .filter(c -> c.getSourceServiceIdentifier() != null && c.getDestinationServiceIdentifier() != null)
                    .collect(Collectors.toList());

            healConnections.removeAll(filteredHealConnections);
            if (!healConnections.isEmpty()) {
                Set<Integer> serviceIds = new HashSet<>();
                healConnections.forEach(c -> {
                    if (c.getSourceServiceIdentifier() == null) {
                        serviceIds.add(c.getSourceServiceId());
                    }
                    if (c.getDestinationServiceIdentifier() == null) {
                        serviceIds.add(c.getDestinationServiceId());
                    }
                });

                log.warn("No Service details present in 'controller' table for service ids {} found in 'connection_details' table", serviceIds);
            }

            /*
            AutoDiscovery Side
             */
            List<GetConnection> autoDiscoConnections = discoveredConnectionsBeanList.parallelStream()
                    .map(c -> GetConnection.builder()
                            .sourceServiceIdentifier(c.getSourceIdentifier())
                            .destinationServiceIdentifier(c.getDestinationIdentifier())
                            .process("Discovered Not Added To System")
                            .status(c.getDiscoveryStatus() == null ? DiscoveryStatus.DISCOVERED_NOT_ADDED_TO_SYSTEM : c.getDiscoveryStatus())
                            .lastDiscoveryRunTime(c.getLastUpdatedTime())
                            .build())
                    .collect(Collectors.toList());

            autoDiscoConnections.forEach(one -> controllerBeanList
                    .forEach(two -> {
                        if (one.getSourceServiceIdentifier().equals(two.getIdentifier())) {
                            one.setSourceServiceName(two.getName());
                            one.setSourceServiceId(two.getId());
                        }
                        if (one.getDestinationServiceIdentifier().equals(two.getIdentifier())) {
                            one.setDestinationServiceName(two.getName());
                            one.setDestinationServiceId(two.getId());
                        }
                    }));

            List<GetConnection> filteredAutoDiscoConnections = autoDiscoConnections.stream()
                    .filter(c -> c.getSourceServiceIdentifier() != null && c.getDestinationServiceIdentifier() != null)
                    .collect(Collectors.toList());

            autoDiscoConnections.removeAll(filteredAutoDiscoConnections);
            if (!autoDiscoConnections.isEmpty()) {
                Set<Integer> serviceIds = new HashSet<>();
                autoDiscoConnections.forEach(c -> {
                    if (c.getSourceServiceIdentifier() == null) {
                        serviceIds.add(c.getSourceServiceId());
                    }
                    if (c.getDestinationServiceIdentifier() == null) {
                        serviceIds.add(c.getDestinationServiceId());
                    }
                });

                log.warn("No Service details present in 'controller' table for service ids {} found in 'autodisco_discovered_connections' table", serviceIds);
            }

            List<GetConnection> duplicates = new ArrayList<>();
            filteredHealConnections.forEach(one -> filteredAutoDiscoConnections
                    .forEach(two -> {
                        if (one.getSourceServiceIdentifier().equals(two.getSourceServiceIdentifier())
                                && one.getDestinationServiceIdentifier().equals(two.getDestinationServiceIdentifier())) {
                            duplicates.add(two);
                        }
                    }));
            filteredAutoDiscoConnections.removeAll(duplicates);
            filteredHealConnections.addAll(filteredAutoDiscoConnections);

            filteredHealConnections.sort(Comparator.comparing(GetConnection::getProcess)
                    .thenComparing(GetConnection::getLastDiscoveryRunTime, Comparator.reverseOrder()));

            return filteredHealConnections;

        } catch (Exception e) {
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }
}
