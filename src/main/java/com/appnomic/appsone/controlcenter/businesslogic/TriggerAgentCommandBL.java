package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import com.appnomic.appsone.controlcenter.beans.AgentBean;
import com.appnomic.appsone.controlcenter.beans.AgentCommandTriggerWrapperBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentStatusDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CommandDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.RulesDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandTriggerBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.PhysicalAgentBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.AgentCommandPojo;
import com.appnomic.appsone.controlcenter.pojo.AgentCommandTriggeredStatusPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.service.QueuePublisher;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class TriggerAgentCommandBL implements BusinessLogic<List<AgentCommandPojo>, List<AgentCommandPojo>,
        List<AgentCommandTriggeredStatusPojo>> {

    private String accountIdentifier;
    private String userId;

    @Override
    public UtilityBean<List<AgentCommandPojo>> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (StringUtils.isEmpty(requestObject.getBody())) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty");
            throw new ClientException("Invalid account identifier");
        }

        List<AgentCommandPojo> listOfStatusBean;
        try {
            listOfStatusBean = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestObject.getBody(),
                    new TypeReference<List<AgentCommandPojo>>() {
                    });
            
            listOfStatusBean = listOfStatusBean.parallelStream().distinct().collect(Collectors.toList());
            
            for (AgentCommandPojo agentCommand : listOfStatusBean) {
                if (!agentCommand.validate()) {
                    throw new ClientException("Input validation failure");
                }
            }
        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        return UtilityBean.<List<AgentCommandPojo>>builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .pojoObject(listOfStatusBean)
                .build();
    }

    @Override
    public List<AgentCommandPojo> serverValidation(UtilityBean<List<AgentCommandPojo>> utilityBean) throws ServerException {
        accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        return validateAndGetAgentCommandIds(utilityBean.getPojoObject());
    }

    private List<AgentCommandPojo> validateAndGetAgentCommandIds(List<AgentCommandPojo> agentCommandList) throws ServerException {
        List<PhysicalAgentBean> physicalAgentBeanList = AgentDataService.getAllPhysicalAgentDetails();
        Map<Integer, PhysicalAgentBean> physicalAgentBeanMap = physicalAgentBeanList.parallelStream().collect(Collectors.toMap(PhysicalAgentBean::getId, Function.identity()));

        for (AgentCommandPojo agentCommandPojo : agentCommandList) {
            int commandTypeId = agentCommandPojo.getCommandTypeId();

            int validCommandId = AgentStatusDataService.getValidCommandId(commandTypeId);
            if (validCommandId != 1) {
                log.error("Invalid commandTypeId [{}]. Reason: Provided commandTypeId is unavailable in command_details table.", commandTypeId);
                throw new ServerException(UIMessages.INVALID_COMMAND_ID);
            }

            int physicalAgentId = agentCommandPojo.getPhysicalAgentId();
            
            PhysicalAgentBean physicalAgentBean = physicalAgentBeanMap.getOrDefault(physicalAgentId, null);
            if (physicalAgentBean == null) {
                log.error("Invalid physicalAgentId. Reason: physicalAgentId [{}] not mapped to given service.", physicalAgentId);
                throw new ServerException(UIMessages.INVALID_PHYSICAL_AGENT_ID);
            }

            agentCommandPojo.setPhysicalAgentIdentifier(physicalAgentBean.getIdentifier());

            List<AgentBean> agentBeanList = AgentDataService.getAgentsListUsingPhysicalAgentId(agentCommandPojo.getPhysicalAgentId());

            if (agentBeanList == null || agentBeanList.isEmpty()) {
                log.error(UIMessages.AGENT_CONFIGURED_ERROR);
                throw new ServerException(UIMessages.AGENT_CONFIGURED_ERROR);
            }

            List<String> supervisorList = AgentStatusDataService.getSupervisorIdentifier(agentBeanList.get(0).getHostAddress());
            if (supervisorList.isEmpty()) {
                log.error("Supervisor is unavailable in host [{}]. Hence command will not be triggered for agent [{}]", agentBeanList.get(0).getHostAddress(), agentCommandPojo.getPhysicalAgentIdentifier());
                throw new ServerException("Supervisor is unavailable");
            }
        }
        return agentCommandList;
    }

    @Override
    public List<AgentCommandTriggeredStatusPojo> process(List<AgentCommandPojo> agentCommandList) throws DataProcessingException {
        try {
            List<CommandRequestProtos.CommandRequest> commandRequests = new ArrayList<>();
            List<AgentCommandTriggeredStatusPojo> agentCommandTriggeredStatusBeanList;

            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            try {
                agentCommandTriggeredStatusBeanList = dbi.inTransaction((conn, status) -> {
                    List<AgentCommandTriggeredStatusPojo> beansList = new ArrayList<>();

                    for (AgentCommandPojo agentCommandPojo : agentCommandList) {
                        AgentCommandTriggerWrapperBean wrapperBean = processCommand(agentCommandPojo, conn);
                        beansList.add(wrapperBean.getAgentCommandTriggeredStatus());
                        commandRequests.add(wrapperBean.getCommandRequest());
                    }
                    return beansList;
                });
            } catch (Exception e) {
                log.error("Error while triggering command. Reason: ", e);
                if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                    throw (DataProcessingException) Throwables.getRootCause(e);
                } else {
                    throw e;
                }
            }

            commandRequests.forEach(QueuePublisher::sendAgentCommandMessage);

            return agentCommandTriggeredStatusBeanList;
        } catch (DataProcessingException e) {
            log.error("CommandException encountered while inserting command details. Details: ", e);
            throw new DataProcessingException("CommandException encountered while adding command");
        }
    }

    private AgentCommandTriggerWrapperBean processCommand(AgentCommandPojo agentCommandPojo, Handle conn) throws DataProcessingException {
        List<AgentBean> agentBeanList = AgentDataService.getAgentsListUsingPhysicalAgentId(agentCommandPojo.getPhysicalAgentId());

        if (agentBeanList == null || agentBeanList.isEmpty()) {
            log.error(UIMessages.AGENT_CONFIGURED_ERROR);
            throw new DataProcessingException(UIMessages.AGENT_CONFIGURED_ERROR);
        }

        log.info("Processing command type ID [{}] triggered for agent [{}]", agentCommandPojo.getCommandTypeId(), agentCommandPojo.getPhysicalAgentId());

        CommandTriggerBean commandTriggerBean = new CommandTriggerBean();
        commandTriggerBean.setPhysicalAgentIdentifier(agentCommandPojo.getPhysicalAgentId());
        commandTriggerBean.setCommandId(agentCommandPojo.getCommandTypeId());
        commandTriggerBean.setTriggerTime(new Timestamp(new Date().getTime()));
        commandTriggerBean.setCommandStatus(1);
        commandTriggerBean.setUserDetailsId(userId);
        commandTriggerBean.setCommandJobId(String.valueOf(UUID.randomUUID()));

        String agentType = "Mixed";
        if (agentBeanList.stream().map(AgentBean::getAgentTypeId).distinct().count() == 1) {
            agentType = RulesDataService.getNameFromMSTSubType(agentBeanList.get(0).getAgentTypeId());
            agentType = agentType == null ? "Mixed" : agentType;
        }

        CommandDetailsBean commandDetailsBean = CommandDataService.getCommandDetail(agentCommandPojo.getCommandTypeId(), null);
        if (commandDetailsBean == null) {
            log.error("Default/Selected command not found for provided agentType [{}] to process the request.", agentType);
            throw new DataProcessingException("Default/Selected command not found to process the request.");
        }

        List<String> supervisorList = AgentStatusDataService.getSupervisorIdentifier(agentBeanList.get(0).getHostAddress());
        if (supervisorList.isEmpty()) {
            log.error("Supervisor is unavailable in host [{}]. Hence command [{}] will not be triggered for agent [{}]", agentBeanList.get(0).getHostAddress(),
                    commandDetailsBean.getCommandName(), agentCommandPojo.getPhysicalAgentIdentifier());
            throw new DataProcessingException("Supervisor is unavailable");
        }

        Map<String, String> argumentsMap = new HashMap<>();
        Map<String, String> metaData = new HashMap<>();
        argumentsMap.put("AgentMode", commandDetailsBean.getName());
        argumentsMap.put("Command", commandDetailsBean.getName());

        metaData.put("AccountId", accountIdentifier);
        metaData.put("CommandId", commandDetailsBean.getIdentifier());

        String desiredStatus = RulesDataService.getNameFromMSTSubType(commandDetailsBean.getActionId());
        metaData.put("DesiredState", desiredStatus);

        try {
            AgentStatusDataService.updateJobId(agentCommandPojo.getPhysicalAgentIdentifier(), commandTriggerBean.getCommandJobId(), userId, conn);
        } catch (ControlCenterException e) {
            throw new DataProcessingException("Exception encountered while updating commandJobID for agent");
        }

        ViewTypes commandTypeTypes = MasterCache.getMstTypeById(commandDetailsBean.getCommandTypeId());
        if (commandTypeTypes == null) {
            log.error("CommandType [{}] is invalid", commandDetailsBean.getCommandTypeId());
            throw new DataProcessingException("commandType is invalid");
        }

        String commandType = commandTypeTypes.getTypeName();
        String commandOutputType = RulesDataService.getNameFromMSTSubType(commandDetailsBean.getOutputTypeId());

        CommandRequestProtos.Command command =
                CommandRequestProtos.Command.newBuilder()
                        .setCommandJobId(commandTriggerBean.getCommandJobId())
                        .setCommand(commandDetailsBean.getCommandName())
                        .setCommandType(commandType)
                        .setCommandOutputType((null == commandOutputType) ? "" : commandOutputType)
                        .setCommandExecType("Execute")
                        .setRetryNumber(3)
                        .setCommandTimeout(commandDetailsBean.getTimeOutInSecs())
                        .setSupervisorCtrlTTL(300)
                        .putAllArguments(argumentsMap)
                        .build();

        CommandRequestProtos.CommandRequest commandDetails = CommandRequestProtos.CommandRequest.newBuilder().addAllSupervisorIdentifiers(supervisorList)
                .setAgentType(agentType)
                .setAgentIdentifier(agentCommandPojo.getPhysicalAgentIdentifier())
                .setTriggerSource("ControlCenter")
                .setUserDetailsID(commandDetailsBean.getUserDetails())
                .setTriggerTime(new Date().getTime())
                .setViolationTime(new Date().getTime())
                .putAllMetadata(metaData)
                .addCommands(command)
                .build();

        AgentStatusDataService.addCommandTrigger(commandTriggerBean, conn);

        AgentCommandTriggeredStatusPojo bean = new AgentCommandTriggeredStatusPojo();
        bean.setAgentId(commandTriggerBean.getPhysicalAgentIdentifier());
        bean.setCommandJobId(commandTriggerBean.getCommandJobId());
        bean.setAgentTypeId(agentBeanList.stream().map(AgentBean::getAgentTypeId).collect(Collectors.toSet()));

        log.info("Command details updated for agent [{}]", agentCommandPojo.getPhysicalAgentIdentifier());

        return new AgentCommandTriggerWrapperBean(bean, commandDetails);
    }
}
