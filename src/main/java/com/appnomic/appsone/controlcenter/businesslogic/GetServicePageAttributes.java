package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.AttributeSelectionType;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.ServicePageAttribute;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GetServicePageAttributes implements BusinessLogic<Integer, Integer, List<ServicePageAttribute>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(GetServicePageAttributes.class);

    ControllerDataService controllerDataService = new ControllerDataService();

    @Override
    public UtilityBean<Integer> clientValidation(RequestObject request) throws ClientException {

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            LOGGER.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }

        String serviceIdString = request.getParams().get(Constants.SERVICE_ID);

        if (serviceIdString == null || serviceIdString.trim().length() == 0) {
            LOGGER.error("serviceId is null or empty. {}", serviceIdString);
            throw new ClientException("serviceId is null or empty.");
        }

        int serviceId;
        try {
            serviceId = Integer.parseInt(serviceIdString.trim());
        } catch (NumberFormatException e) {
            LOGGER.error("Service Id [{}] is not an integer. ", serviceIdString);
            throw new ClientException("Service Id is not an integer.");
        }

        return UtilityBean.<Integer>builder()
                .accountIdentifier(identifier)
                .pojoObject(serviceId)
                .authToken(authToken)
                .build();
    }

    @Override
    public Integer serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }
        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            LOGGER.error(UIMessages.INVALID_ACCOUNT_MESSAGE);
            throw new ServerException(UIMessages.INVALID_ACCOUNT_MESSAGE);
        }
        int accountId = account.getId();

        ControllerBean service = null;
        if (utilityBean.getPojoObject() > 0) {
            service = controllerDataService.getControllerById(utilityBean.getPojoObject(), accountId, null);
            if (null == service || service.getControllerTypeId() != MasterCache.getMstTypeForSubTypeName
                    (Constants.CONTROLLER_TYPE_NAME_DEFAULT, Constants.SERVICES_CONTROLLER_TYPE).getSubTypeId()) {
                LOGGER.error(UIMessages.INVALID_SERVICE);
                throw new ServerException(UIMessages.INVALID_SERVICE);
            }
        }

        return service == null ? 0 : service.getId();
    }

    @Override
    public List<ServicePageAttribute> process(Integer serviceId) throws DataProcessingException {
        List<ServicePageAttribute> attributes = new ArrayList<>();
        try {
            int id = 1;
            attributes.add(new ServicePageAttribute(id++, null, "Name", "name", "name",
                    null, null, null, new ServicePageAttribute.AttributeProperties(1, 128,
                    null, "^[a-zA-Z0-9._-]+$", AttributeSelectionType.TextBox, 1, 0,
                    0, 1, null)));

            attributes.add(new ServicePageAttribute(id++, null, "Identifier", "identifier", "identifier",
                    null, null, null, new ServicePageAttribute
                    .AttributeProperties(1, 128, null, "^[a-zA-Z0-9._-]+$", AttributeSelectionType.TextBox,
                    0, serviceId == 0 ? 0 : 1, 0, 1, null)));

            attributes.add(new ServicePageAttribute(id++, null, "Application(s)", "appIdentifiers",
                    "application.identifier", "name", "identifier",
                    "accounts/{accountIdentifier}/applications", new ServicePageAttribute.AttributeProperties(0,
                    0, null, "", AttributeSelectionType.Dropdown, 1, 0, 1,
                    0, null)));

            attributes.add(new ServicePageAttribute(id++, null, "Layer", "layer", "layer",
                    null, null, null, new ServicePageAttribute.AttributeProperties(1, 128,
                    controllerDataService.getLayers(Constants.SERVICES_LAYER_TYPE), "^[a-zA-Z0-9._-]+$", AttributeSelectionType.Dropdown,
                    1, 0, 0, 0, null)));

            attributes.add(new ServicePageAttribute(id++, null, "Timezone", "timezone", "timezone",
                    "timeZoneId", "timeZoneId", "/timezones", new ServicePageAttribute.AttributeProperties(0,
                    0, null, "", AttributeSelectionType.Dropdown, 1, 0, 0,
                    0, null)));

            List<String> serviceType = new ArrayList<>();
            serviceType.add(Constants.KUBERNETES);
            attributes.add(new ServicePageAttribute(id++, null, "ServiceType", "serviceType", "serviceType",
                    null, null, null, new ServicePageAttribute.AttributeProperties(0,
                    0, serviceType,"^[a-zA-Z0-9._-]+$", AttributeSelectionType.Dropdown, 0, 0, 0,
                    0, null)));

            if (serviceId == 0) {
                Map<String, String> options = new HashMap<>();
                options.put("0", "No");
                options.put("1", "Yes");
                attributes.add(new ServicePageAttribute(id, 0, "Mark as Entry Point", "isEntryPointService",
                        "isEntryPoint", null, null, null, new ServicePageAttribute.
                        AttributeProperties(0, 0, options, "", AttributeSelectionType.Switch, 0,
                        0, 0, 0, null)));
            }

            return attributes;
        } catch (Exception e) {
            LOGGER.error("ERROR : ", e);
            throw new DataProcessingException(e.getMessage());
        }
    }
}
