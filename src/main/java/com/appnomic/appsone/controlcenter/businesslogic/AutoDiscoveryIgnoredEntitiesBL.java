package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.AutoDiscoveryDiscoveredAttributes;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.AutoDiscoveryIgnoredEntities;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.common.autodiscovery.Entity;
import com.appnomic.appsone.controlcenter.dao.mysql.AutoDiscoveryDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.AutoDiscoveryIgnoredEntitiesPojo;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class AutoDiscoveryIgnoredEntitiesBL implements BusinessLogic<UtilityBean, Object, List<AutoDiscoveryIgnoredEntitiesPojo>> {

    @Override
    public UtilityBean clientValidation(RequestObject requestObject) throws ClientException {

        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authToken == null || StringUtils.isEmpty(authToken)) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String accountIdentifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (accountIdentifier == null || StringUtils.isEmpty(accountIdentifier)) {
            log.error(UIMessages.ACCOUNT_NULL_OR_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_NULL_OR_EMPTY);
        }
        return UtilityBean.builder()
                .accountIdentifier(accountIdentifier)
                .authToken(authToken)
                .build();
    }

    @Override
    public Object serverValidation(UtilityBean utilityBean) throws ServerException {

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            log.error(UIMessages.INVALID_ACCOUNT_MESSAGE);
            throw new ServerException(UIMessages.INVALID_ACCOUNT_MESSAGE);
        }
        return null;
    }

    @Override
    public List<AutoDiscoveryIgnoredEntitiesPojo> process(Object bean) throws DataProcessingException {
        /**
         * fetches list of ignored entities, both hosts and component instances
         */
        AutoDiscoveryDataService autoDiscoveryDataService = new AutoDiscoveryDataService();
        List<AutoDiscoveryIgnoredEntities> ignoredHostsList = autoDiscoveryDataService.getIgnoredHosts();
        List<AutoDiscoveryDiscoveredAttributes> getDiscoveredAttributesList = autoDiscoveryDataService.getDiscoveredAttributesList(null);

        AutoDiscoveryIgnoredEntitiesPojo temp = new AutoDiscoveryIgnoredEntitiesPojo();
        List<AutoDiscoveryIgnoredEntitiesPojo> ignoredEntities = new ArrayList<>();
        ArrayList<String> hostIPs = new ArrayList<>();
        String prevHostIdentifier = "";

        //Get port list for each Host
        for (AutoDiscoveryIgnoredEntities ignoredEntity : ignoredHostsList) {
            if (!prevHostIdentifier.equals("") && !prevHostIdentifier.equals(ignoredEntity.getIdentifier())) {
                temp.setHostAddress(hostIPs);
                ignoredEntities.add(temp);
                hostIPs = new ArrayList<>();
                temp = new AutoDiscoveryIgnoredEntitiesPojo();
                temp.setName(ignoredEntity.getName());
                temp.setIdentifier(ignoredEntity.getIdentifier());
                temp.setEntityType(Entity.Host);
                temp.setIgnoredBy(ignoredEntity.getIgnoredBy());
                temp.setLastUpdatedTime(ignoredEntity.getLastUpdatedTime());
                temp.setLastDiscoveryRunTime(ignoredEntity.getLastDiscoveryRunTime());
                List<AutoDiscoveryDiscoveredAttributes> entityAttributes = getDiscoveredAttributesList.stream().filter(attr -> (attr.getEntityType().toString().equalsIgnoreCase("Host") &&
                        attr.getAttributeName().equalsIgnoreCase("SshPort") &&
                        attr.getAttributeName().equalsIgnoreCase("MonitorPort") &&
                        attr.getAttributeName().equalsIgnoreCase("JMXPort") &&
                        attr.getDiscoveredAttributesIdentifier().equals(ignoredEntity.getIdentifier())))
                        .collect(Collectors.toList());
                ArrayList<String> attributes = new ArrayList<>();
                for (AutoDiscoveryDiscoveredAttributes attr : entityAttributes) {
                    attributes.add(attr.getAttributeName() + ":" + attr.getAttributeValue());
                }
                temp.setDiscoveredEntities(attributes);
                hostIPs.add(ignoredEntity.getHostAddress());
            } else if (prevHostIdentifier.equals(ignoredEntity.getIdentifier())) {
                if (!hostIPs.contains(ignoredEntity.getHostAddress())) {
                    hostIPs.add(ignoredEntity.getHostAddress());
                } else continue;
            } else if (prevHostIdentifier.equals("")) {
                temp.setName(ignoredEntity.getName());
                temp.setIdentifier(ignoredEntity.getIdentifier());
                temp.setEntityType(Entity.Host);
                temp.setIgnoredBy(ignoredEntity.getIgnoredBy());
                temp.setLastUpdatedTime(ignoredEntity.getLastUpdatedTime());
                temp.setLastDiscoveryRunTime(ignoredEntity.getLastDiscoveryRunTime());
                List<AutoDiscoveryDiscoveredAttributes> entityAttributes = getDiscoveredAttributesList.stream().filter(attr -> (attr.getEntityType().toString().equalsIgnoreCase("Host") &&
                        attr.getAttributeName().equalsIgnoreCase("SshPort") &&
                        attr.getAttributeName().equalsIgnoreCase("MonitorPort") &&
                        attr.getAttributeName().equalsIgnoreCase("JMXPort") &&
                        attr.getDiscoveredAttributesIdentifier().equals(ignoredEntity.getIdentifier())))
                        .collect(Collectors.toList());
                ArrayList<String> attributes = new ArrayList<>();
                for (AutoDiscoveryDiscoveredAttributes attr : entityAttributes) {
                    attributes.add(attr.getAttributeName() + ":" + attr.getAttributeValue());
                }
                temp.setDiscoveredEntities(attributes);
                hostIPs.add(ignoredEntity.getHostAddress());
            }
            prevHostIdentifier = ignoredEntity.getIdentifier();
        }
        temp.setHostAddress(hostIPs);
        ignoredEntities.add(temp);

        // fetch ignored processes
        List<AutoDiscoveryIgnoredEntities> ignoredProcessesList = autoDiscoveryDataService.getIgnoredProcesses();
        for (AutoDiscoveryIgnoredEntities process: ignoredProcessesList) {
            temp = new AutoDiscoveryIgnoredEntitiesPojo();
            temp.setName(process.getName());
            temp.setIdentifier(process.getIdentifier());
            temp.setEntityType(Entity.CompInstance);
            temp.setIgnoredBy(process.getIgnoredBy());
            temp.setLastUpdatedTime(process.getLastUpdatedTime());
            temp.setLastDiscoveryRunTime(process.getLastDiscoveryRunTime());
            ignoredEntities.add(temp);
        }

        ignoredEntities.sort(Comparator.comparing(AutoDiscoveryIgnoredEntitiesPojo::getLastDiscoveryRunTime, Comparator.reverseOrder())
                .thenComparing(AutoDiscoveryIgnoredEntitiesPojo::getName, Comparator.comparingInt(o -> Character.toLowerCase(o.charAt(0)))));
        return ignoredEntities;
    }
}
