package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.util.Commons;
import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.MasterDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.UserAccessDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.UserDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.NotificationBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserAccessDetails;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserProfileBean;
import com.appnomic.appsone.controlcenter.dao.redis.AccountRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ApplicationRepo;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.pojo.Controller;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.UserDetails;
import com.appnomic.appsone.controlcenter.pojo.UserInfo;
import com.appnomic.appsone.controlcenter.service.NotificationPreferencesDataService;
import com.appnomic.appsone.controlcenter.util.AECSBouncyCastleUtil;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.keycloak.KeycloakConnectionManager;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.heal.configuration.pojos.Application;
import com.heal.configuration.pojos.BasicEntity;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.skife.jdbi.v2.Handle;
import spark.Request;

import java.io.IOException;
import java.lang.reflect.Type;
import java.security.Security;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class UsersBL {

    private static final ObjectMapper OBJECT_MAPPER = CommonUtils.getObjectMapperWithHtmlEncoder();

    private static final int USER_MANAGER = 3;
    private static final int SUPER_ADMIN = 1;

    private static final String SUPER_ADMIN_STRING = "Super Admin";

    private static final UserDataService userDataService = new UserDataService();

    public String clientValidationUser(Request request) {
        try {
            if (null == request) {
                log.error("Request object is NULL");
                throw new ControlCenterException("Request object is NULL");
            }
            return CommonUtils.getUserId(request);
        } catch (ControlCenterException e) {
            log.error("Invalid user identifier. Reason: {}", e.getMessage(), e);
        }
        return null;
    }

    /*Get all users present in appsone*/
    public static String getUsers() throws JsonProcessingException, InvalidCipherTextException, InvalidCipherTextException {

        List<UserDetailsBean> users = userDataService.getUsers();

        if (users.isEmpty()) {
            log.info("User list fetched from Appsone Schema is empty.");
            return "";
        }

        Map<String, UserNotificationDetails> userNotificationDetails = NotificationPreferencesDataService
                .getEmailSmsForensicNotificationStatusForUsers(null).parallelStream()
                .collect(Collectors.toMap(UserNotificationDetails::getApplicableUserId, u -> u));
        Map<String, String> usersMap = UserAccessDataService.getActiveUsers().parallelStream().collect(Collectors.toMap(IdPojo::getIdentifier, IdPojo::getName));
        List<UserDetails> userDetails = users.stream().filter(user -> !user.getRole().equals(SUPER_ADMIN_STRING))
                .sorted(Comparator.comparing(UserDetailsBean::getUpdatedOn))
                .map(user -> UserDetails.builder()
                        .userId(user.getId())
                        .userName(user.getUserName())
                        .status(user.getStatus())
                        .updatedBy(usersMap.getOrDefault(user.getUpdatedBy(), null))
                        .role(user.getRole())
                        .userProfile(user.getUserProfile())
                        .updatedOn(DateTimeUtil.getGMTToEpochTime(user.getUpdatedOn()))
                        .emailNotification(!userNotificationDetails.containsKey(user.getId()) ? 0 : userNotificationDetails.get(user.getId()).getEmailEnabled())
                        .smsNotification(!userNotificationDetails.containsKey(user.getId()) ? 0 : userNotificationDetails.get(user.getId()).getSmsEnabled())
                        .forensicNotification(!userNotificationDetails.containsKey(user.getId()) ? 0 : userNotificationDetails.get(user.getId()).getForensicEnabled())
                        .build())
                .collect(Collectors.toList());
        String userDetailsToJson = CommonUtils.getObjectMapper().writeValueAsString(userDetails);
        return new AECSBouncyCastleUtil().encrypt(userDetailsToJson);
    }

    public int validateRoleId(String roleId) throws RequestException, ControlCenterException {
        if (roleId == null || roleId.isEmpty()) {
            log.error("User role provided is invalid. Reason: It is NULL or empty.");
            throw new RequestException("roleId is null or empty.");
        }

        int id;
        try {
            id = Integer.parseInt(roleId);
        } catch (Exception e) {
            log.error("Error while converting roleId to integer. Reason: role id [{}] is invalid", roleId);
            throw new RequestException("Error while converting roleId to integer.");
        }

        if (UserAccessDataService.getRoles().parallelStream().noneMatch(r -> r.getId() == id)) {
            log.error("Role id [{}] is unavailable.", id);
            throw new RequestException("Invalid input provided for 'roleId'.");
        }

        return id;
    }

    public List<IdPojo> getProfilesForRoleId(int roleId) throws ControlCenterException {
        List<UserProfileBean> profiles = UserAccessDataService.getUserProfiles();

        if (profiles.isEmpty()) {
            log.debug("No profiles are present in Appsone Schema");
            return new ArrayList<>();
        }

        String role = UserAccessDataService.getRoles().parallelStream()
                .filter(r -> r.getId() == roleId)
                .map(IdPojo::getName)
                .findAny()
                .orElse(null);

        if (role == null) {
            log.error("Role Id provided is unavailable");
            throw new ControlCenterException("Role Id provided is unavailable");
        }

        return profiles.parallelStream().filter(p -> p.getRoleName().equals(role))
                .map(p -> IdPojo.builder().id(p.getId()).name(p.getName()).build())
                .collect(Collectors.toList());
    }

    public List<IdPojo> getRoles() throws ControlCenterException {
        List<IdPojo> data = UserAccessDataService.getRoles();
        if (data.isEmpty()) {
            log.info("roles data is empty.");
            return new ArrayList<>();
        }

        return data;
    }

    public static GenericResponse<String> validateUserName(String userName) throws RequestException, ControlCenterException, IOException, InvalidCipherTextException {
        GenericResponse<String> response = new GenericResponse<>();

        if (userName == null || userName.trim().isEmpty()) {
            log.error("Query parameter 'name' is invalid. Reason: It is NULL or empty");
            throw new RequestException("name should not be null or empty.");
        }

        if (userDataService.getUserDetails(userName) != null) {
            response.setResponseStatus(StatusResponse.FAILURE.name());
            response.setMessage("NOT AVAILABLE TO CREATE USER");
            return response;
        }

        String setup = userDataService.getSetup();
        if (setup == null) {
            log.error("Error while fetching integration mode.");
            throw new ControlCenterException("Unable to get Setup Mode.");
        }

        UserBean userBean = UserAccessDataService.getUserDetailsFromUsername(userName);

        if (setup.equalsIgnoreCase(Constants.SETUP_AD_INTEGRATION)) {
            if (userBean == null || userBean.getId().trim().isEmpty()) {
                response.setResponseStatus(StatusResponse.FAILURE.name());
                response.setMessage("NOT AVAILABLE : USER IS NOT PRESENT FOR MAPPING.");
                return response;
            }

            UserInfo user = UserInfo.builder()
                    .id(userBean.getId())
                    .userName(userBean.getUsername())
                    .firstName(userBean.getFirstName())
                    .lastName(userBean.getLastName())
                    .emailId(userBean.getEmail())
                    .build();

            if (userBean.isEnabled()) {
                user.setStatus(1);
            } else {
                user.setStatus(0);
            }

            response.setResponseStatus(StatusResponse.SUCCESS.name());
            response.setMessage("USER AVAILABLE TO MAPPING");
            String userToJson = CommonUtils.getObjectMapper().writeValueAsString(user);
            response.setData(new AECSBouncyCastleUtil().encrypt(userToJson));
        } else {
            if (userBean == null) {
                response.setResponseStatus(StatusResponse.SUCCESS.name());
                response.setMessage("AVAILABLE TO CREATE USER");
            } else {
                response.setResponseStatus(StatusResponse.FAILURE.name());
                response.setMessage("NOT AVAILABLE TO CREATE USER");
            }
        }
        return response;
    }

    public UserInfo getUserDetails(String userId) throws RequestException, IOException, ControlCenterException {
        Optional<UserDetailsBean> userDetail = userDataService.getUsers().parallelStream()
                .filter(u -> u.getId().equals(userId))
                .findAny();

        if (!userDetail.isPresent()) {
            log.error("User details is unavailable for user id [{}]", userId);
            throw new RequestException("User is not available.");
        }

        String userName = userDetail.get().getUserName();
        UserInfoBean userInfoBean = userDataService.getUserDetails(userName);

        if (userInfoBean == null) {
            log.error("Unable to fetch User details from db.");
            throw new ControlCenterException("User details is not available");
        }
        AccessDetailsBean accessDetailsBean = new AccessDetailsBean();
        try {
            accessDetailsBean = OBJECT_MAPPER.readValue(userInfoBean.getAccessDetailsJSON(),
                    new TypeReference<AccessDetailsBean>() {
                    });
        } catch (IOException e) {
            log.error("Error while parsing Access Details JSON.", e);
        }
        userInfoBean.setAccessDetails(accessDetailsBean);

        String setup = userDataService.getSetup();
        if (setup == null) {
            log.error("Error while fetching integration mode.");
            throw new ControlCenterException("Unable to get Setup Mode.");
        }

        UserInfo user = UserInfo.builder()
                .id(userInfoBean.getId())
                .status(userInfoBean.getStatus())
                .roleId(userInfoBean.getRoleId())
                .profileId(userInfoBean.getProfileId())
                .emailId(userInfoBean.getEmailId())
                .contactNumber(userInfoBean.getContactNumber())
                .userName(userInfoBean.getUserName())
                .mysqlId(userInfoBean.getMysqlId())
                .isTimezoneMychoice(userInfoBean.getIsTimezoneMychoice())
                .isNotificationsTimezoneMychoice(userInfoBean.getIsNotificationsTimezoneMychoice())
                .editInKeycloak(setup.equalsIgnoreCase(Constants.SETUP_AD_INTEGRATION) && userDataService.adEditStatusKeycloak())
                .build();

        UsersBL usersBL = new UsersBL();
        user.setAccessDetails(usersBL.populateAccessDetails(userInfoBean.getAccessDetails()));

        UserBean userBean = UserAccessDataService.getUserDetailsFromUsername(userName);

        if (userBean != null) {
            user.setFirstName(userBean.getFirstName());
            user.setLastName(userBean.getLastName());
        } else {
            log.error("Unable to fetch user details from keycloak.");
        }

        return user;
    }

    public List<UserInfo.AccessDetails> populateAccessDetails(AccessDetailsBean accessInfo) {
        List<UserInfo.AccessDetails> list = new ArrayList<>();
        UsersBL usersBL = new UsersBL();

        List<IdPojo> accounts = MasterCache.getAccounts().parallelStream()
                .map(a -> IdPojo.builder().id(a.getId()).identifier(a.getIdentifier()).build())
                .collect(Collectors.toList());
        Map<String, Integer> accountMap = new HashSet<>(accounts).parallelStream()
                .collect(Collectors.toMap(IdPojo::getIdentifier, IdPojo::getId));

        for (String acc : accessInfo.getAccounts()) {
            acc = acc.trim();
            UserInfo.AccessDetails accessDetails = new UserInfo.AccessDetails();

            if (acc.equals("*")) {
                accessDetails.setAccountId("*");
                list.add(accessDetails);
                break;
            } else {
                int accId = accountMap.get(acc);
                accessDetails.setAccountId(accId);

                if (accessInfo.getAccountMapping() != null) {
                    accessDetails.setApplications(usersBL.populateAccApps(accessInfo.getAccountMapping()
                            .get(acc).getApplications(), accId));

                }

                list.add(accessDetails);
            }
        }

        return list;

    }

    private List<UserInfo.AccessDetails.UserApp> populateAccApps(List<String> apps, int accId) {
        List<String> appIds = new ArrayList<>();

        int appTypeId = MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT,
                Constants.APPLICATION_CONTROLLER_TYPE).getSubTypeId();

        Map<String, String> appMap = MasterDataService.getControllerList(accId).parallelStream()
                .filter(a -> a.getControllerTypeId() == appTypeId)
                .collect(Collectors.toMap(Controller::getIdentifier, Controller::getAppId));


        for (String appName : apps) {
            if (appName.equals("*")) {
                appIds.add("*");
            } else {
                appIds.add(appMap.get(appName));
            }
        }

        List<UserInfo.AccessDetails.UserApp> userApp = new ArrayList<>();
        userApp.add(UserInfo.AccessDetails.UserApp.builder().ids(appIds).build());
        return userApp;
    }

    public UserInfo clientValidation(Request request) throws RequestException {
        UsersBL usersBL = new UsersBL();
        if (null == request.body() || (request.body().trim().isEmpty())) {
            log.error(UIMessages.REQUEST_NULL);
            throw new RequestException(UIMessages.REQUEST_NULL);
        }

        String userId = usersBL.clientValidationUser(request);
        if (userId == null) {
            log.error("Unable to extract userId from the request header");
            throw new RequestException("Invalid User.");
        }

        UserInfo userInfo;
        try {
            userInfo = OBJECT_MAPPER.readValue(request.body(), new TypeReference<UserInfo>() {
            });
            userInfo.setUserDetailsId(userId);
        } catch (IOException e) {
            log.error(Constants.JSON_PARSE_ERROR + " : {}", e.getMessage());
            throw new RequestException(Constants.JSON_PARSE_ERROR);
        }

        if (!userInfo.validate()) {
            throw new RequestException(UIMessages.INVALID_REQUEST);
        }
        return userInfo;
    }

    void validateUserDetailsId(String userDetailsId) throws RequestException {
        try {
            UserAttributesBean userAttributesBean = userDataService.getUserAttributes(userDetailsId);

            if (userAttributesBean == null || (userAttributesBean.getRoleId() != USER_MANAGER && userAttributesBean.getRoleId() != SUPER_ADMIN)) {
                log.error("User either doesn't exist or is unauthorized");
                throw new RequestException("User either doesn't exist or is unauthorized.");
            }
        } catch (ControlCenterException | RequestException e) {
            String err = "User Manager/SuperAdmin profile does not exist.";
            log.error(err);
            throw new RequestException(err);
        }
    }

    public void validateEmailId(List<UserBean> users, String emailId) throws RequestException {
        if (users.parallelStream().anyMatch(u -> u.getEmail() != null && u.getEmail().equalsIgnoreCase(emailId))) {
            log.error("Email address already exists");
            throw new RequestException("Email Address already exists");
        }
    }

    void validateRoleAndProfile(int roleId, int profileId) throws RequestException, ControlCenterException {
        UsersBL usersBL = new UsersBL();

        if (UserAccessDataService.getRoles().parallelStream().noneMatch(r -> r.getId() == roleId)) {
            log.error("Information related to role id [{}] is unavailable", roleId);
            throw new RequestException("Role Id requested is not present.");
        }

        if (usersBL.getProfilesForRoleId(roleId).parallelStream().noneMatch(p -> p.getId() == profileId)) {
            log.error("Information related to profile id [{}] is unavailable", profileId);
            throw new RequestException("Profile Id requested is not present for the role specified.");
        }
    }

    public AccessDetailsBean validateAndGetAccessDetailsUser(List<UserInfo.AccessDetails> accessDetails) throws RequestException {
        AccessDetailsBean accessDetail = new AccessDetailsBean();
        Set<String> accList = new HashSet<>();
        Map<String, AccessDetailsBean.Application> accMapping = new HashMap<>();

        if (accessDetails == null) {
            log.debug("accessDetails is null. Therefore, returning");
            return null;
        }

        Map<String, IdPojo> accountMap = new AccountRepo().getAccounts().parallelStream().filter(f -> f.getStatus() == 1)
                .map(a -> IdPojo.builder().id(a.getId()).identifier(a.getIdentifier()).build())
                .collect(Collectors.toMap(i -> String.valueOf(i.getId()), i -> i));

        for (UserInfo.AccessDetails access : accessDetails) {
            if (access.getAccountId().equals("*")) {
                accList.add("*");
                break;
            }

            if (!accountMap.containsKey(String.valueOf(access.getAccountId()))) {
                log.error("Information related to account id [{}] is unavailable : ", access.getAccountId());
                throw new RequestException("Account Id is not available : " + access.getAccountId());
            }

            IdPojo acc = accountMap.get(String.valueOf(access.getAccountId()));

            accList.add(acc.getIdentifier());
            if (!access.getApplications().isEmpty()) {
                if (access.getApplications().get(0).getIds().isEmpty()) {
                    log.error("No applications provided for account id [{}]", acc.getIdentifier());
                    throw new RequestException("No applications are present in accessDetails for the account. " + acc);
                }

                AccessDetailsBean.Application applications = new AccessDetailsBean.Application();
                applications.setApplications(getAccessibleApps(access.getApplications().get(0).getIds(), acc.getId(), acc.getIdentifier()));
                accMapping.put(acc.getIdentifier(), applications);
            }
        }

        accessDetail.setAccounts(new ArrayList<>(accList));
        accessDetail.setAccountMapping(accMapping);

        return accessDetail;
    }

    private List<String> getAccessibleApps(List<String> appIds, int accId,String accountIdentifier) throws RequestException {

        ApplicationRepo applicationRepo = new ApplicationRepo();
        Map<String, String> appMap = applicationRepo.getApplicationsForAccount(accountIdentifier).parallelStream()
                .filter(f-> f.getStatus() == 1).collect(Collectors.toMap(b -> String.valueOf(b.getId()), BasicEntity::getIdentifier));
        List<String> appIdentifiers = new ArrayList<>();

        for (String app : appIds) {
            if (app == null) {
                continue;
            }

            if (app.equals("*")) {
                appIdentifiers.add("*");
                break;
            }
            if (appMap.containsKey(app)) {
                appIdentifiers.add(appMap.get(app));
            } else {
                log.error("Application [{}] specified is unavailable for account [{}]", app, accId);
                throw new RequestException("Application Id specified is not available for the account : " + app);
            }
        }

        return appIdentifiers;
    }

    List<KeycloakUserBean.Credential> getDefaultCredentials() throws ControlCenterException {
        List<KeycloakUserBean.Credential> list = new ArrayList<>();
        String password = userDataService.getTemporaryPassword();

        if (password == null) {
            throw new ControlCenterException("Error while fetching temporary password");
        }

        String decryptValue;
        try {
            Security.removeProvider(Constants.BC_PROVIDER_NAME);
            decryptValue = Commons.decrypt(password);
        } catch (Exception e) {
            log.error("Exception encountered while decrypting the password. Details: {}", e.getMessage(), e);
            throw new ControlCenterException("Error while decrypting the password");
        }

        list.add(KeycloakUserBean.Credential.builder()
                .type("password")
                .value(decryptValue)
                .temporary("true").build());

        return list;
    }

    void updateNotificationPreferences(UserInfoBean user, Handle handle) throws ControlCenterException {
        BindInDataService bindInDataService = new BindInDataService();
        NotificationBL notificationBL = new NotificationBL();


        List<Integer> accIds = new ArrayList<>();
        for (Map.Entry<String, AccessDetailsBean.Application> entry : user.getAccessDetails().getAccountMapping().entrySet()) {

            List<Integer> appIds;
            List<String> values = entry.getValue().getApplications().parallelStream().map(String::valueOf).collect(Collectors.toList());

            if (values.isEmpty()) {
                log.error("There are no applications mapped to the account [{}] for user [{}]", entry.getKey(), user.getUserName());
                continue;
            }

            AccountRepo accountRepo = new AccountRepo();
            int accountId = accountRepo.getAccountWithAccountIdentifier(entry.getKey()).getId();
            accIds.add(accountId);

            ApplicationRepo applicationRepo = new ApplicationRepo();
            List<Application> applicationList = applicationRepo.getApplicationsForAccount(entry.getKey());
            appIds = values.contains("*")
                    ? applicationList.parallelStream().map(BasicEntity::getId).collect(Collectors.toList())
                    : applicationList.parallelStream()
                    .filter(application -> values.contains(application.getIdentifier()))
                    .map(BasicEntity::getId)
                    .collect(Collectors.toList());

            if (!appIds.isEmpty()) {
                bindInDataService.removeOldApplicationIdsForUserNotifications(accountId, user.getId(), handle, appIds);

                List<NotificationBean> applicationNotificationPreferences = bindInDataService
                        .getApplicationNotificationPreferences(appIds, accountId, handle);

                if (!applicationNotificationPreferences.isEmpty()) {
                    notificationBL.createDefaultNotificationsPreferences(user.getId(), applicationNotificationPreferences,
                            user.getUserDetailsId(), accountId, handle);
                }
                notificationBL.createDefaultForensicNotificationPreferences(user.getId(), appIds, accountId, user.getUserDetailsId(), handle);
            }
        }

        if (!accIds.isEmpty()) {
            bindInDataService.removeOldAccountsForUserNotifications(user.getId(), accIds, handle);
        }
        UserNotificationDetails userNotificationDetails = NotificationPreferencesDataService.getEmailAndSmsStatus(user.getId(), handle);

        if (Objects.isNull(userNotificationDetails)) {
            notificationBL.createDefaultNotificationsDetails(user.getId(), user.getUserDetailsId(), handle);
        }
    }

    public UserInfoBean getUserInfoBean(UserInfo user, AccessDetailsBean accessDetailsBean) throws ControlCenterException {
        try {
            return UserInfoBean.builder()
                    .userName(user.getUserName())
                    .firstName(user.getFirstName())
                    .lastName(user.getLastName())
                    .emailId(user.getEmailId())
                    .id(user.getId())
                    .userDetailsId(user.getUserDetailsId())
                    .contactNumber(user.getContactNumber())
                    .status(user.getStatus())
                    .roleId(user.getRoleId())
                    .profileId(user.getProfileId())
                    .accessDetailsJSON(accessDetailsBean != null ? OBJECT_MAPPER.writeValueAsString(accessDetailsBean): null)
                    .accessDetails(accessDetailsBean)
                    .profileChange(user.getProfileChange() == 1)
                    .editInKeycloak(user.isEditInKeycloak())
                    .build();
        } catch (JsonProcessingException j) {
            log.error("Could not build user info bean object",j);
            throw new ControlCenterException(j.getMessage());
        }
    }

    public KeycloakUserBean getKeycloakUserBean(UserInfoBean user) {
        return KeycloakUserBean.builder()
                .enabled(String.valueOf(user.getStatus() == 1))
                .email(user.getEmailId())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .username(user.getUserName()).build();
    }

    UserAccessDetails getUserAccessDetailsBean(UserInfoBean user) {
        return UserAccessDetails.builder()
                .userIdentifier(user.getId())
                .accessDetails(user.getAccessDetailsJSON())
                .userDetailsId(user.getUserDetailsId())
                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .build();
    }

    UserAttributesBean getUserAttributesBean(UserInfoBean user) {
        return UserAttributesBean.builder()
                .userIdentifier(user.getId())
                .userName(user.getUserName())
                .emailAddress(user.getEmailId())
                .contactNumber(user.getContactNumber())
                .status(user.getStatus())
                .roleId(user.getRoleId())
                .accessProfileId(user.getProfileId())
                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .userDetailsId(user.getUserDetailsId())
                .build();
    }

    void mapAdminRoleToUserManager(String userId) throws ControlCenterException {
        try {
            String userString = KeycloakConnectionManager.getRealms();

            Type userBeanType = new TypeToken<List<KeycloakRealmBean>>() {
            }.getType();

            List<KeycloakRealmBean> keycloakRealmBeans = CommonUtils.jsonToObject(userString, userBeanType);

            Optional<KeycloakRealmBean> bean = keycloakRealmBeans.stream().filter(b -> b.getName().equalsIgnoreCase("admin")).findAny();

            if (bean.isPresent()) {
                KeycloakRealmBean keycloakRealmBean = bean.get();

                KeycloakUserRealm userRealm = new KeycloakUserRealm(keycloakRealmBean.getId(), keycloakRealmBean.getName());

                List<KeycloakUserRealm> list = new ArrayList<>();
                list.add(userRealm);

                Gson gson = new GsonBuilder().create();
                KeycloakConnectionManager.mapAdminRoleToUser(userId, gson.toJson(list));
            }
        } catch (Exception e) {
            log.error("Error : ", e);
            throw new ControlCenterException("Error while mapping Admin Role to User Manager.");
        }
    }

    public  List<UserInfo> getUserDetailsList() {
        List<UserInfo> users = new ArrayList<>();

        try {
            for (UserDetailsBean user : userDataService.getUsers()) {
                users.add(getUserDetails(user.getId()));
            }

        } catch (Exception e) {
            log.error("Error loading user details in config details.", e);
        }
        return users;
    }

}