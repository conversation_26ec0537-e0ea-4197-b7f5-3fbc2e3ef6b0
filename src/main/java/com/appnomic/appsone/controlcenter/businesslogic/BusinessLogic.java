package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;

/**
 * <AUTHOR> on 28/7/20
 */
public interface BusinessLogic<T, V, R> {
    UtilityBean<T> clientValidation(RequestObject requestObject) throws ClientException;

    V serverValidation(UtilityBean<T> utilityBean) throws ServerException;

    R process(V bean) throws DataProcessingException;
}
