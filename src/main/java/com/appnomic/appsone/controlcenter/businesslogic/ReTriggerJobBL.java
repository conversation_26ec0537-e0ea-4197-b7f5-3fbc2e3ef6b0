package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.SchedulersDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.opensearch.OpenSearchRepo;
import com.appnomic.appsone.controlcenter.dao.opensearch.SchedulersOsRepo;
import com.appnomic.appsone.controlcenter.dao.redis.SchedulersRedisRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.service.QueuePublisher;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.entities.ScheduledJobDetails;
import com.heal.configuration.entities.SchedulerDetails;
import com.heal.configuration.enums.JobStatus;
import com.heal.configuration.pojos.ScheduleArguments;
import com.heal.configuration.pojos.TriggeredJob;
import com.heal.configuration.pojos.opensearch.TriggeredJobData;
import com.heal.configuration.protbuf.ScheduledJobProtos;
import com.heal.configuration.util.DateHelper;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ReTriggerJobBL implements BusinessLogic<List<TriggeredJob>, List<TriggeredJob>, String> {

    private String accountIdentifier;
    private String userId;

    SchedulersRedisRepo schedulersRedisRepo = new SchedulersRedisRepo();
    SchedulersOsRepo schedulersOsRepo = new SchedulersOsRepo();
    SchedulersDataService schedulersDataService = new SchedulersDataService();

    @Override
    public UtilityBean<List<TriggeredJob>> clientValidation(RequestObject request) throws ClientException {
        if (request == null) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (StringUtils.isEmpty(request.getBody())) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error(UIMessages.REQUEST_BODY_NULL_EMPTY);
            throw new ClientException(UIMessages.REQUEST_BODY_NULL_EMPTY);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (authToken == null || authToken.trim().isEmpty()) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty.");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error(UIMessages.ACCOUNT_NULL_OR_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_NULL_OR_EMPTY);
        }

        String actionType = request.getQueryParams().get("actionType")[0];
        if (actionType == null || actionType.trim().isEmpty()) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Action type is invalid. Reason: It is either NULL ot empty.");
            throw new ClientException("Action type is invalid");
        }

        String requestBody = request.getBody();
        List<TriggeredJob> triggeredJobs;
        try {
            triggeredJobs = CommonUtils.getObjectMapper().readValue(requestBody,
                    new TypeReference<List<TriggeredJob>>() {
                    });
        } catch (IOException e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        List<TriggeredJob> validatedTriggeredJobs = triggeredJobs.parallelStream().filter(t -> t.validate(actionType)).collect(Collectors.toList());
        if (validatedTriggeredJobs.size() != triggeredJobs.size()) {
            log.error("Triggered job(s) validation failed");
            throw new ClientException("Triggered job(s) validation failed");
        }

        log.debug("Client validated job(s) details: {}", validatedTriggeredJobs);

        return UtilityBean.<List<TriggeredJob>>builder()
                .authToken(authToken)
                .accountIdentifier(identifier)
                .pojoObject(validatedTriggeredJobs)
                .build();
    }

    @Override
    public List<TriggeredJob> serverValidation(UtilityBean<List<TriggeredJob>> utilityBean) throws ServerException {
        userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);
        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        List<SchedulerDetails> schedulerDetails = schedulersRedisRepo.getSchedulers(accountIdentifier);

        if (schedulerDetails.isEmpty()) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Scheduler details unavailable");
            throw new ServerException("Scheduler details unavailable");
        }

        List<Integer> scheduleIds = schedulerDetails.parallelStream().map(SchedulerDetails::getId).collect(Collectors.toList());
        List<Integer> scheduledJobIds = schedulerDetails.parallelStream()
                .filter(s -> scheduleIds.contains(s.getId()))
                .flatMap(s -> s.getScheduledJobDetails().stream())
                .map(ScheduledJobDetails::getId)
                .collect(Collectors.toList());

        List<TriggeredJob> triggeredJobs = utilityBean.getPojoObject();

        List<TriggeredJob> validTriggeredJobs = triggeredJobs.parallelStream()
                .filter(t -> scheduleIds.contains(t.getSchedulerDetailsId()))
                .filter(t -> scheduledJobIds.contains(t.getScheduledJobId()))
                .collect(Collectors.toList());

        if (validTriggeredJobs.size() != triggeredJobs.size()) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Some of the jobs in the request are invalid. Details: Valid triggered jobs are [{}]", validTriggeredJobs);
            throw new ServerException("Invalid request");
        }

        log.debug("Server validated job(s) details: {}", validTriggeredJobs);

        return validTriggeredJobs;
    }

    @Override
    public String process(List<TriggeredJob> triggeredJobs) throws DataProcessingException {
        for (TriggeredJob triggeredJob : triggeredJobs) {
            try {
                schedulersDataService.updateScheduledJobStatus(triggeredJob.getSchedulerDetailsId(), "SCHEDULED");
            } catch (ControlCenterException e) {
                throw new DataProcessingException(e.getMessage());
            }

            log.info("Updated rollup job details: {}", triggeredJob);

            Map<String, String> metadata = new HashMap<>();
            metadata.put("cronExpression", triggeredJob.getMetadata().get("cronExpression"));

            List<ScheduledJobProtos.Arguments> schedulerArguments = triggeredJob.getSchedulerArguments().parallelStream()
                    .map(s -> ScheduledJobProtos.Arguments.newBuilder()
                            .setName(s.getArgumentName())
                            .setValue(s.getArgumentValue())
                            .setDefaultValue(s.getDefaultValue())
                            .setPlaceholder(s.getPlaceholder())
                            .build())
                    .collect(Collectors.toList());

            List<ScheduledJobProtos.Arguments> scheduledJobArguments = triggeredJob.getScheduledJobArguments().parallelStream()
                    .map(s -> ScheduledJobProtos.Arguments.newBuilder()
                            .setName(s.getArgumentName())
                            .setValue(s.getArgumentValue())
                            .setDefaultValue(s.getDefaultValue())
                            .setPlaceholder(s.getPlaceholder())
                            .build())
                    .collect(Collectors.toList());

            List<ScheduledJobProtos.SupportedActions> supportedActions = triggeredJob.getSupportedActions().parallelStream()
                    .map(s -> ScheduledJobProtos.SupportedActions.newBuilder()
                            .setId(s.getActionId())
                            .setStatus(s.getStatus())
                            .setRetriedCount(s.getRetriedCount())
                            .setRetryLimit(s.getRetryLimit())
                            .build())
                    .collect(Collectors.toList());

            ScheduledJobProtos.ScheduledJob scheduledJob = ScheduledJobProtos.ScheduledJob.newBuilder()
                    .setAccountIdentifier(accountIdentifier)
                    .setSchedulerDetailsId(triggeredJob.getSchedulerDetailsId())
                    .setSchedulerType(triggeredJob.getIsRecurring() == 1 ? "Recurring" : "Non-Recurring")
                    .setTriggerSource("Control Center")
                    .setTriggerTime(Calendar.getInstance(TimeZone.getTimeZone("UTC")).getTimeInMillis())
                    .setScheduledJobId(triggeredJob.getScheduledJobId())
                    .setJobType(triggeredJob.getJobType())
                    .setJobImplementationType(triggeredJob.getImplementationType())
                    .setStartTime(triggeredJob.getStartTimeEpoch())
                    .setEndTime(triggeredJob.getEndTimeEpoch())
                    .addAllSchedulerArguments(schedulerArguments)
                    .addAllJobArguments(scheduledJobArguments)
                    .addAllSupportedActions(supportedActions)
                    .putAllMetadata(metadata)
                    .build();

            ScheduleArguments queue = triggeredJob.getSchedulerArguments().parallelStream()
                    .filter(q -> q.getArgumentName().equalsIgnoreCase("queue_name")).findFirst().orElse(null);
            String queueName = queue == null ? "collation-messages"
                    : queue.getArgumentValue() == null ? queue.getDefaultValue() : queue.getArgumentValue();

            QueuePublisher.sendScheduledJobMessages(queueName, scheduledJob);

            TriggeredJobData data = TriggeredJobData.builder()
                    .accountIdentifier(accountIdentifier)
                    .schedulerDetailsId(scheduledJob.getSchedulerDetailsId())
                    .schedulerType(scheduledJob.getSchedulerType())
                    .schedulerJobId(scheduledJob.getScheduledJobId())
                    .triggerSource("Control Center")
                    .jobType(scheduledJob.getJobType())
                    .jobImplementationType(scheduledJob.getJobImplementationType())
                    .startTimeEpoch(triggeredJob.getStartTimeEpoch())
                    .endTimeEpoch(triggeredJob.getEndTimeEpoch())
                    .triggerTime(System.currentTimeMillis())
                    .triggerSource(scheduledJob.getTriggerSource())
                    .userDetailsId(userId)
                    .status(JobStatus.getName(JobStatus.SCHEDULED.getStatus()))
                    .metadata(metadata)
                    .timestamp(DateHelper.getDate(System.currentTimeMillis()))
                    .build();

            try {
                String jobId = triggeredJob.getMetadata().get("jobId");
                String indexName = String.format(Constants.INDEX_PREFIX_HEAL_SCHEDULED_JOB + "_%s_%s", accountIdentifier, triggeredJob.getMetadata().get("weekYear"));

                Map<String, String> updatedMap = new HashMap<>();
                updatedMap.put("status", JobStatus.getName(JobStatus.RETRIGGERED.getStatus()));

                new OpenSearchRepo<Map<String, String>>().updateDocById(indexName, jobId, updatedMap, accountIdentifier, Constants.INDEX_PREFIX_HEAL_SCHEDULED_JOB);
            } catch (IOException e) {
                throw new DataProcessingException(e, e.getMessage());
            }

            schedulersOsRepo.insertRollupJobDetails(data);
        }

        return "Success";
    }
}