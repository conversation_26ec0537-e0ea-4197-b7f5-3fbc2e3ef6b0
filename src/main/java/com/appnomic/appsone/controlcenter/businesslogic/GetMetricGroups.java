package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ComponentInstanceBean;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.MetricDetailsDataService;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.MetricDetailsRequest;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class GetMetricGroups implements BusinessLogic<List<Integer>, MetricDetailsRequest, List<IdPojo>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(GetMetricGroups.class);
    MetricDetailsDataService dataService = new MetricDetailsDataService();

    @Override
    public UtilityBean<List<Integer>> clientValidation(RequestObject request) throws ClientException {

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }


        if (request.getQueryParams().get("instanceIds").length == 0) {
            LOGGER.error("InstanceIds are not specified in query parameters.");
            throw new ClientException("InstanceIds are not specified in queryParams");
        }
        String[] instanceIdsString = request.getQueryParams().get("instanceIds");

        List<Integer> instanceIds = new ArrayList<>();
        if (instanceIdsString == null || instanceIdsString.length == 0) {
            LOGGER.error("InstanceIds is null or empty.");
            throw new ClientException("InstanceIds is null or empty.");
        } else {
            try {
                for (String s : instanceIdsString[0].split(",")) {
                    Integer parseInt = Integer.parseInt(s.trim());
                    instanceIds.add(parseInt);
                }
            } catch (NumberFormatException e) {
                LOGGER.error("InstanceIds should be positive integers.");
                throw new ClientException("InstanceIds should be positive integers.");
            }
            if (instanceIds.parallelStream().anyMatch(i -> i < 1)) {
                LOGGER.error("InstanceIds cannot be less than 1.");
                throw new ClientException("InstanceIds cannot be less than 1.");
            }
        }

        return UtilityBean.<List<Integer>>builder()
                .accountIdentifier(identifier)
                .pojoObject(instanceIds)
                .authToken(authToken)
                .build();
    }

    @Override
    public MetricDetailsRequest serverValidation(UtilityBean<List<Integer>> utilityBean) throws ServerException {

        UserAccountBean userAccBean;
        try {
            userAccBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            throw new ServerException(e.getMessage());
        }

        int accountId = userAccBean.getAccount().getId();
        List<ComponentInstanceBean> instances = new ArrayList<>();
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
        Set<Integer> instanceIds = new HashSet<>(utilityBean.getPojoObject());
        for (int instanceId : instanceIds) {
            ComponentInstanceBean bean = compInstanceDataService.getComponentInstanceByIdAndAccount(instanceId, accountId);
            if (bean == null) {
                throw new ServerException("Invalid instance Id : " + instanceId);
            }
            instances.add(bean);
        }

        Set<Integer> componentIds = instances.parallelStream().map(ComponentInstanceBean::getMstComponentId)
                .collect(Collectors.toSet());
        if (componentIds.size() != 1) {
            LOGGER.error("InstanceIds specified are mapped to different components.");
            throw new ServerException("InstanceIds specified are mapped to different components.");
        }

        return MetricDetailsRequest.builder()
                .accountId(accountId)
                .componentId(new ArrayList<>(componentIds).get(0))
                .instanceIds(utilityBean.getPojoObject())
                .build();
    }

    @Override
    public List<IdPojo> process(MetricDetailsRequest bean) throws DataProcessingException {

        try {
            return dataService.getNonDiscoveredKPIGroupsForComponentId(bean.getComponentId(), bean.getAccountId(), null);
        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
    }
}
