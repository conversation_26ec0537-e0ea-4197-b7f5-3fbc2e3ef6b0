package com.appnomic.appsone.controlcenter.businesslogic.connectors;

import com.appnomic.appsone.controlcenter.common.ConnectorConstants;
import com.appnomic.appsone.controlcenter.dao.mysql.connectors.DynatraceConnectorDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.*;
import com.appnomic.appsone.controlcenter.exceptions.FileUploadException;
import com.appnomic.appsone.controlcenter.pojo.connectors.DynaTraceEntity;
import com.appnomic.appsone.controlcenter.pojo.connectors.DynatraceHealMetric;
import com.appnomic.appsone.controlcenter.pojo.connectors.HealAgentInstance;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
@Slf4j
public class DynaTraceConnectorBL {
    public static DynatraceConnectorDataService dynatraceConnectorDataService = new DynatraceConnectorDataService();

    public List<DynaTraceEntity> getDynatraceEntityFromFile(File fileName){
        List<DynaTraceEntity> entities = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)){
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(ConnectorConstants.COMMON_SETTING_SHEET_INDEX);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for(int i=sheet.getFirstRowNum()+3;i<=sheet.getLastRowNum();i++){
                Row ro=sheet.getRow(i);
//                ro.getCell(5).setCellType(CellType.STRING);
                if( ro.getCell(ConnectorConstants.DYNATRACE_ENTITY_ID_INDEX)==null)
                    break;
               // ro.getCell(ConnectorConstants.DYNATRACE_ENTITY_METRIC_IDS_INDEX).setCellType(Cell.CELL_TYPE_STRING);
                DynaTraceEntity entity = DynaTraceEntity.builder()
                                    .id((int)ro.getCell(ConnectorConstants.DYNATRACE_ENTITY_ID_INDEX).getNumericCellValue())
                                    .name(ro.getCell(ConnectorConstants.DYNATRACE_ENTITY_NAME_INDEX).getStringCellValue())
                                    .identifier(ro.getCell(ConnectorConstants.DYNATRACE_ENTITY_IDENTIFIER_INDEX).getStringCellValue())
                                    .entityType(ro.getCell(ConnectorConstants.DYNATRACE_ENTITY_TYPE).getStringCellValue())
                                    .build();
                if(entity.getId()!=0)
                    entities.add(entity);
            }

        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try : ",ex);
            throw new FileUploadException("File is not found at given location please check.");
        } catch (IOException ex) {
            log.error("Exception when reading the entity from excel file : ",ex);
            throw new FileUploadException("Exception when reading the entity from excel file.");
        }

        return entities;
    }
    public List<DynatraceHealMetric> getDynatraceHealMetricFromFile(File fileName){
        List<DynatraceHealMetric> dynatraceHealMetrics = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)){
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(ConnectorConstants.COMMON_SETTING_SHEET_INDEX);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for(int i=sheet.getFirstRowNum()+3;i<=sheet.getLastRowNum();i++){
                Row ro=sheet.getRow(i);
//                ro.getCell(5).setCellType(CellType.STRING);
                if(ro.getCell(ConnectorConstants.DYNATRACE_METRIC_ID_INDEX)==null)
                    break;
                DynatraceHealMetric dynatraceHealMetric = DynatraceHealMetric.builder()
                                    .id((int)ro.getCell(ConnectorConstants.DYNATRACE_METRIC_ID_INDEX).getNumericCellValue())
                                    .name(ro.getCell(ConnectorConstants.DYNATRACE_METRIC_NAME_INDEX).getStringCellValue())
                                    .identifier(ro.getCell(ConnectorConstants.DYNATRACE_METRIC_IDENTIFIER_INDEX).getStringCellValue())
                                    .resolution((int)ro.getCell(ConnectorConstants.DYNATRACE_METRIC_RESOLUTION_INDEX).getNumericCellValue())
                                    .healKpiId((int)ro.getCell(ConnectorConstants.DYNATRACE_METRIC_HEAL_ID_INDEX).getNumericCellValue())
                                    .healKpiName(ro.getCell(ConnectorConstants.DYNATRACE_METRIC_HEAL_NAME_INDEX).getStringCellValue())
                                    .healKpiIdentifier(ro.getCell(ConnectorConstants.DYNATRACE_METRIC_HEAL_IDENTIFIER_INDEX).getStringCellValue())
                                    .isGroupKpi(ro.getCell(ConnectorConstants.DYNATRACE_METRIC_IS_GROUP_INDEX).getNumericCellValue()==1)
                                    .healGroupName(ro.getCell(ConnectorConstants.DYNATRACE_METRIC_GROUP_NAME_INDEX).getStringCellValue())
                                    .entityType(ro.getCell(ConnectorConstants.DYNATRACE_METRIC_ENTITY_TYPE_INDEX).getStringCellValue())
                                    .build();
                if(dynatraceHealMetric.getId()!=0) dynatraceHealMetrics.add(dynatraceHealMetric);

            }

        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try : ",ex);
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the metric data from excel file : ",ex);
            throw new FileUploadException("Exception when reading the metric data from excel file");
        }

        return dynatraceHealMetrics;
    }

    public List<HealAgentInstance> getSourceHealInstanceMappingFromFile(File fileName){
        List<HealAgentInstance> healAgentInstances = new ArrayList<>();
        try ( FileInputStream file = new FileInputStream(fileName)){
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(ConnectorConstants.COMMON_SETTING_SHEET_INDEX);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for(int i=sheet.getFirstRowNum()+3;i<=sheet.getLastRowNum();i++){
                Row ro=sheet.getRow(i);

                if(ro.getCell(ConnectorConstants.DYNATRACE_AGENT_SOURCE_INSTANCE_NAME_INDEX)==null)
                    break;
                HealAgentInstance agentInstance = HealAgentInstance.builder()
                        .sourceInstanceName(ro.getCell(ConnectorConstants.DYNATRACE_AGENT_SOURCE_INSTANCE_NAME_INDEX).getStringCellValue())
                        .agentIdentifier(ro.getCell(ConnectorConstants.DYNATRACE_AGENT_NAME_INDEX).getStringCellValue())
                        .healInstanceName(ro.getCell(ConnectorConstants.DYNATRACE_AGENT_HEAL_INSTANCE_NAME_INDEX).getStringCellValue())
                        .build();
                if(!agentInstance.getHealInstanceName().equalsIgnoreCase(""))healAgentInstances.add(agentInstance);
            }

        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try : ",ex);
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the Sap Instance data from excel file : ",ex);
            throw new FileUploadException("Exception when reading the Sap Instance data from excel file");
        }
        return healAgentInstances;
    }

    public List<DynatraceEntityBean> getDynatraceEntityBeanFromData(List<DynaTraceEntity> dynaTraceEntities){
        List<DynatraceEntityBean> dynatraceEntityBeans = new ArrayList<>();
        for (DynaTraceEntity entity:dynaTraceEntities) {
            DynatraceEntityBean entityBean = DynatraceEntityBean.builder()
                    .id(entity.getId())
                    .name(entity.getName())
                    .identifier(entity.getIdentifier())
                    .build();
            dynatraceEntityBeans.add(entityBean);
        }
        return dynatraceEntityBeans;
    }

   /* public List<DynatraceEntityMetricMapping> getDynatraceEntityMetricMappingFromData(List<DynaTraceEntity> dynaTraceEntities, List<DynatraceHealMetric> metrics){
        List<DynatraceEntityMetricMapping> mappingList = new ArrayList<>();
        Map<Integer,String> identifierIdmapping = new HashMap<>();
        metrics.forEach(dynatraceHealMetric -> identifierIdmapping.put(dynatraceHealMetric.getId(),dynatraceHealMetric.getIdentifier()));
        for (DynaTraceEntity entity:dynaTraceEntities) {
            List<Integer> listOfIds = Arrays.stream(entity.getMetrixIds().split(",")).map(Integer::valueOf)
                    .collect(Collectors.toList());
            for (Integer metricId:listOfIds) {
                DynatraceEntityMetricMapping metricMapping = DynatraceEntityMetricMapping.builder()
                        .metricIdentifier(identifierIdmapping.get(metricId))
                        .entityIdentifier(entity.getIdentifier())
                        .build();
                mappingList.add(metricMapping);
            }

        }
        return mappingList;
    }*/

    public List<DynatraceMetricBean> getDynatraceMetricData(List<DynatraceHealMetric> dynatraceHealMetrics){
        List<DynatraceMetricBean> dynatraceMetricBeans =  new ArrayList<>();
        for (DynatraceHealMetric dynatraceHealMetric : dynatraceHealMetrics){
            DynatraceMetricBean metricBean = DynatraceMetricBean.builder()
                    .id(dynatraceHealMetric.getId())
                    .name(dynatraceHealMetric.getName())
                    .identifier(dynatraceHealMetric.getIdentifier())
                    .resolution(dynatraceHealMetric.getResolution())
                    .build();
            dynatraceMetricBeans.add(metricBean);
        }
        return dynatraceMetricBeans;
    }

    public List<HealKpi> getHealKpiFromData(List<DynatraceHealMetric> dynatraceHealMetrics){
        List<HealKpi> healKpiList = new ArrayList<>();
        for (DynatraceHealMetric dynatraceHealMatric:dynatraceHealMetrics) {
            HealKpi healKpi = HealKpi.builder()
                    .id(dynatraceHealMatric.getId())
                    .kpiId(dynatraceHealMatric.getHealKpiId())
                    .kpiName(dynatraceHealMatric.getHealKpiName())
                    .kpiIdentifier(dynatraceHealMatric.getHealKpiIdentifier())
                    .isGroupKpi(dynatraceHealMatric.isGroupKpi()?1:0)
                    .groupName(dynatraceHealMatric.getHealGroupName())
                    .build();
            healKpiList.add(healKpi);
        }
        return healKpiList;
    }

    public List<DomainToHealKpiMapping> getDomainToHealKpiMappingFromData(List<DynatraceHealMetric> dynatraceHealMatrics, int [] srcKpiIds){
        List<DomainToHealKpiMapping> healKpiList = new ArrayList<>();
        int i = 0;
        for (DynatraceHealMetric dynatraceHealMatric:dynatraceHealMatrics) {
            DomainToHealKpiMapping domainToHealKpiMapping = DomainToHealKpiMapping.builder()
                            .domainName(ConnectorConstants.DYNATRACE_DOMAIN_NAME)
                            .id(dynatraceHealMatric.getId())
                            .healIdentifier(dynatraceHealMatric.getHealKpiIdentifier())
                            .sourceId(srcKpiIds[i])
                            .build();
            healKpiList.add(domainToHealKpiMapping);
            i++;
        }
        return healKpiList;
    }
    public void entityMappingbyType(List<DynaTraceEntity> dynaTraceEntities, List<DynatraceHealMetric> fileMetrics)
    {
        deleteEntityMetricMapping();
        List<String> hostEntities = dynaTraceEntities.parallelStream()
                .filter(m -> m.getEntityType().equalsIgnoreCase("host"))
                .map(DynaTraceEntity::getIdentifier).collect(Collectors.toList());
        List<String> oobHostMetricsDB = dynatraceConnectorDataService.getDynatraceMetricList();
        List<String> hostMetricsFile = fileMetrics.parallelStream()
                .filter(m -> m.getEntityType().equalsIgnoreCase("host"))
                .map(DynatraceHealMetric::getIdentifier).collect(Collectors.toList());
        if(!hostMetricsFile.isEmpty())
            oobHostMetricsDB.addAll(hostMetricsFile);
        addEntityMetricMapping(hostEntities, oobHostMetricsDB);

        Set<String> serviceEntityTypes = dynaTraceEntities.stream()
                .filter(m->!m.getEntityType().equalsIgnoreCase("host"))
                .map(DynaTraceEntity::getEntityType).collect(Collectors.toSet());
        for(String serEntity : serviceEntityTypes) {
            List<String> serviceMetricsFile = fileMetrics.parallelStream()
                    .filter(m -> m.getEntityType().equalsIgnoreCase(serEntity)).map(DynatraceHealMetric::getIdentifier).collect(Collectors.toList());

            List<String> serviceEntities = dynaTraceEntities.parallelStream()
                    .filter(m -> m.getEntityType().equalsIgnoreCase(serEntity))
                    .map(DynaTraceEntity::getIdentifier).collect(Collectors.toList());
            addEntityMetricMapping(serviceEntities, serviceMetricsFile);
        }
    }
    public void addEntityMetricMapping(List<String> entityIdentifier, List<String> metricIdentifier){
        if(entityIdentifier.isEmpty() || metricIdentifier.isEmpty())
            return;
        List<DynatraceEntityMetricMapping> entityMetricMappings = new ArrayList<>();
        entityIdentifier.forEach(entity-> metricIdentifier.forEach(metric-> entityMetricMappings.add(DynatraceEntityMetricMapping.builder()
                .entityIdentifier(entity)
                .metricIdentifier(metric)
                .build())));
        dynatraceConnectorDataService.addDynatraceEntityMetricMappings(entityMetricMappings);
    }

    public int[] addDynatraceMetric(List<DynatraceMetricBean> dynatraceMetricBeans){
        return dynatraceConnectorDataService.addDynatraceMetrics(dynatraceMetricBeans);
    }

    public void deleteEntityMetricMapping(){
         dynatraceConnectorDataService.deleteDynatraceEntityMetricMappings();
    }
}
