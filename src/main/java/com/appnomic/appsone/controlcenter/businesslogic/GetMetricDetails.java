package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.KpiMaintenanceStatusBean;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.InstanceDetailsForKPI;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ProducerMapping;
import com.appnomic.appsone.controlcenter.dao.redis.ComponentRepo;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.configuration.pojos.ComponentCommonVersion;
import com.heal.configuration.pojos.ComponentKpiEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class GetMetricDetails implements BusinessLogic<List<Integer>, MetricDetailsRequest, List<MetricDetails>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(GetMetricDetails.class);

    @Override
    public UtilityBean<List<Integer>> clientValidation(RequestObject request) throws ClientException {

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }

        String serviceIdString = request.getParams().get(Constants.SERVICE_ID);
        if (StringUtils.isEmpty(serviceIdString)) {
            LOGGER.error("serviceId is null or empty. {}", serviceIdString);
            throw new ClientException("serviceId is null or empty.");
        }
        try {
            Integer.parseInt(serviceIdString.trim());
        } catch (NumberFormatException e) {
            LOGGER.error("Service Id [{}] is not an integer. ", serviceIdString);
            throw new ClientException("Service Id is not an integer.");
        }

        if (StringUtils.isEmpty(request.getBody())) {
            LOGGER.error("InstanceIds are not specified in the request body.");
            throw new ClientException("InstanceIds are not specified in the request body.");
        }
        MetricDetailsRequest metricDetailsRequest;
        try {
            metricDetailsRequest = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(request.getBody(),
                    new TypeReference<MetricDetailsRequest>() {
                    });
        } catch (IOException e) {
            LOGGER.error("InstanceIds are not specified in proper format in the request body.", e);
            throw new ClientException("InstanceIds are not specified in proper format in the request body.");
        }

        List<Integer> instanceIds = metricDetailsRequest.getInstanceIds();
        if (instanceIds == null || instanceIds.isEmpty()) {
            LOGGER.error("InstanceIds is null or empty.");
            throw new ClientException("InstanceIds is null or empty.");
        } else {
            if (instanceIds.parallelStream().anyMatch(i -> i < 1)) {
                LOGGER.error("InstanceIds cannot be less than 1.");
                throw new ClientException("InstanceIds cannot be less than 1.");
            }
        }

        return UtilityBean.<List<Integer>>builder()
                .accountIdentifier(identifier)
                .serviceId(serviceIdString.trim())
                .pojoObject(instanceIds)
                .authToken(authToken)
                .build();
    }

    @Override
    public MetricDetailsRequest serverValidation(UtilityBean<List<Integer>> utilityBean) throws ServerException {

        InstanceRepo instanceRepo = new InstanceRepo();
        UserAccountBean userAccBean;
        try {
            userAccBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            throw new ServerException(e.getMessage());
        }

        int accountId = userAccBean.getAccount().getId();
        int serviceId = Integer.parseInt(utilityBean.getServiceId());
        Controller serviceDetails = ValidationUtils.getServiceDetails(accountId, serviceId);
        if (Objects.isNull(serviceDetails)) {
            LOGGER.error("Invalid service id [{}]", serviceId);
            throw new ServerException("Invalid service Id. Reason: Service Id specified is unavailable for the account.");
        }

        Set<Integer> instanceIds = new HashSet<>(utilityBean.getPojoObject());
        Map<Integer, com.heal.configuration.pojos.CompInstClusterDetails> instancesMap = instanceRepo.getInstances(userAccBean.getAccount().getIdentifier())
                .parallelStream()
                .filter(i -> instanceIds.contains(i.getId()))
                .collect(Collectors.toMap(com.heal.configuration.pojos.CompInstClusterDetails::getId, Function.identity()));

        if (instanceIds.size() != instancesMap.size()) {
            LOGGER.error("Invalid instanceIds provided for the service.");
            throw new ServerException("Invalid instanceIds provided for the service.");
        }

        Set<Integer> componentIds = instancesMap.values().parallelStream().map(CompInstClusterDetails::getComponentId).collect(Collectors.toSet());
        if (componentIds.size() != 1) {
            LOGGER.error("InstanceIds specified are mapped to different components.");
            throw new ServerException("InstanceIds specified are mapped to different components.");
        }

        return MetricDetailsRequest.builder()
                .accountId(accountId)
                .accountIdentifier(userAccBean.getAccount().getIdentifier())
                .componentId(new ArrayList<>(componentIds).get(0))
                .compInstances(new ArrayList<>(instancesMap.values()))
                .instanceIds(utilityBean.getPojoObject())
                .build();
    }

    @Override
    public List<MetricDetails> process(MetricDetailsRequest bean) throws DataProcessingException {
        BindInDataService bindInDataService = new BindInDataService();
        InstanceRepo instanceRepo = new InstanceRepo();
        long st = System.currentTimeMillis();
        long start;
        List<MetricDetails> list;
        try {
            start = System.currentTimeMillis();
            int componentId = bean.getComponentId();
            Map<Integer, Map<Integer, List<InstanceDetailsForKPI>>> instKpiMap = bean.getCompInstances().parallelStream()
                    .collect(Collectors.toMap(CompInstClusterDetails::getId, i ->
                            instanceRepo.getInstanceWiseKpis(bean.getAccountIdentifier(), i.getIdentifier())
                                    .parallelStream()
                                    .collect(Collectors.groupingBy(CompInstKpiEntity::getId, Collectors.mapping(k -> InstanceDetailsForKPI.builder()
                                            .instanceKPIMappingId(k.getCompInstKpiId())
                                            .instanceId(i.getId())
                                            .instanceName(i.getName())
                                            .producerId(k.getDefaultProducerId())
                                            .producerName("")
                                            .mstProducerKPIMappingId(k.getProducerKpiMappingId())
                                            .isGroup(k.getIsGroup() ? 1: 0)
                                            .collectionInterval(k.getCollectionInterval())
                                            .status(k.getStatus())
                                            .severity( k.getKpiViolationConfig() != null && !k.getKpiViolationConfig().isEmpty()? k.getKpiViolationConfig().entrySet().iterator().next().getValue().getSeverity() : 0)
                                            .thresholdStatus(k.getKpiViolationConfig() != null && !k.getKpiViolationConfig().isEmpty()? k.getKpiViolationConfig().entrySet().iterator().next().getValue().getStatus() : 0)
                                            .build(), Collectors.toList())))));

            Map<Integer, Set<InstanceDetailsForKPI>> kpiInstsMap = new HashMap<>();
            instKpiMap.forEach((i, kp) -> kp.forEach((k, p) -> {
                Set<InstanceDetailsForKPI> kpiProducers = kpiInstsMap.getOrDefault(k, new HashSet<>());
                kpiProducers.add(p.get(0));

                kpiInstsMap.put(k, kpiProducers);
            }));

            LOGGER.debug("Time taken to get the KPI details for instances:{} is {} ms.", bean.getCompInstances().size(), System.currentTimeMillis() - start);
            start = System.currentTimeMillis();
            List<ComponentKpiEntity> kpiEntityList = new ComponentRepo().getComponentKpiDetails(bean.getAccountIdentifier(), bean.getCompInstances().get(0).getComponentName());
            Map<Integer, List<ProducerMapping>> kpiProducerMapping = bindInDataService.getProducerMapping(
                    bean.getCompInstances().parallelStream()
                        .map(CompInstClusterDetails::getComponentVersionId)
                        .distinct()
                        .collect(Collectors.toList()), bean.getComponentId(), bean.getAccountId())
                    .parallelStream().collect(Collectors.groupingBy(ProducerMapping::getKpiId));

            LOGGER.debug("Time taken to get the KPI details for component:is {} ms.", System.currentTimeMillis() - start);
            start = System.currentTimeMillis();

            list = kpiEntityList.parallelStream()
                    .filter(k -> kpiProducerMapping.containsKey(k.getId()))
                    .map(kpi -> {
                        try {
                            ProducerMapping defaultProducer = kpiProducerMapping.get(kpi.getId())
                                    .parallelStream()
                                    .filter(m -> m.getIsDefault() == 1)
                                    .findAny().orElse(null);

                            if (defaultProducer == null) {
                                LOGGER.warn("No default producer exists for kpi:{}, componentId:{}", kpi.getId(), bean.getComponentId());
                                return null;
                            }

                            List<KpiMaintenanceStatusBean> maintenanceList = bindInDataService
                                    .getMaintenanceStatusForInstAndKpi(bean.getInstanceIds(), kpi.getId(), null);

                            int commonVersionId = bean.getCompInstances().get(0).getCommonVersionId();
                            ComponentCommonVersion componentCommonVersion = kpi.getCommonVersionDetails().parallelStream().filter(f -> f.getCommonVersionId() == commonVersionId).findAny().orElse(null);

                            int collectionInterval = Constants.DEFAULT_COLLECTION_INTERVAL;
                            if (componentCommonVersion != null) {
                                collectionInterval = componentCommonVersion.getCollectionInterval();
                            }

                            return MetricDetails.builder()
                                    .metricId(kpi.getId())
                                    .metricName(kpi.getName())
                                    .metricType(kpi.getType())
                                    .metricIdentifier(kpi.getIdentifier())
                                    .metricGroupId(kpi.getGroupId())
                                    .metricGroup(kpi.getGroupName())
                                    .attributesDiscovered(kpi.getDiscovery())
                                    .producers(kpiProducerMapping.get(kpi.getId())
                                            .parallelStream()
                                            .map(m -> IdPojo.builder()
                                                    .id(m.getProducerId())
                                                    .name(m.getProducerName())
                                                    .identifier(m.getProducerName())
                                                    .build())
                                            .collect(Collectors.toList()))
                                    .componentLevel(MetricDetails.ComponentLevelDetails.builder()
                                            .componentId(componentId)
                                            .producerId(defaultProducer.getProducerId())
                                            .producerName(defaultProducer.getProducerName())
                                            .collectionInterval(collectionInterval)
                                            .build())
                                    .instances(kpiInstsMap.getOrDefault(kpi.getId(), new HashSet<>()))
                                    .thresholdExists(bindInDataService.getThresholdsCountForInstAndKpi(bean.getInstanceIds(), kpi.getId(), null) > 0 ? 1 : 0)
                                    .persistenceSuppressionExists(maintenanceList.parallelStream().anyMatch(m -> m.getSuppression() > 0 && m.getPersistence() > 0) ? 1 : 0)
                                    .dataType(kpi.getDataType())
                                    .unit(kpi.getUnit())
                                    .isMaintenanceExcluded(maintenanceList.parallelStream().anyMatch(m -> m.getIsMaintenanceExcluded() >= 0) ? 1 : 0)
                                    .build();
                        } catch (Exception e) {
                            LOGGER.error("Error occurred while populating metric details.", e);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new DataProcessingException(e, "Error while getting instance metric details." + bean);
        }
        LOGGER.debug("Time taken to populate KPI details is {} ms.", System.currentTimeMillis() - start);
        list.sort(Comparator.comparing(MetricDetails::getMetricName).thenComparing(MetricDetails::getMetricGroup, Comparator.nullsFirst(Comparator.naturalOrder())));
        LOGGER.debug("Time taken for get metrics API is {} ms.", System.currentTimeMillis() - st);
        return list;
    }
}
