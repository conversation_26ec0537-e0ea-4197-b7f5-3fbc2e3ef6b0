package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UserAttributesBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.UserDataService;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.OptInEnum;
import com.appnomic.appsone.controlcenter.pojo.OptInRequestPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.skife.jdbi.v2.DBI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.text.MessageFormat;

public class UpdateWhatsappOptInBL implements BusinessLogic<OptInRequestPojo, UtilityBean<OptInRequestPojo>, String> {
    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateWhatsappOptInBL.class);

    @Override
    public UtilityBean<OptInRequestPojo> clientValidation(RequestObject requestObject) throws ClientException {
        LOGGER.debug("Inside Client validation");

        OptInRequestPojo optInRequestPojo;

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            optInRequestPojo = objectMapper.readValue(requestObject.getBody(), new TypeReference<OptInRequestPojo>() {});
        } catch (IOException e) {
            throw new ClientException(e, Constants.REQUEST_BODY_INVALID_ERROR_MESSAGE);
        }
        optInRequestPojo.validate();

        optInRequestPojo.setOptInLastRequestTimeStr(DateTimeUtil.getTimeInGMT(optInRequestPojo.getOptInLastRequestTime()));
        optInRequestPojo.setContactNumber(optInRequestPojo.getContactNumber());

        return UtilityBean.<OptInRequestPojo>builder()
                .pojoObject(optInRequestPojo)
                .build();
    }

    @Override
    public UtilityBean<OptInRequestPojo> serverValidation(UtilityBean<OptInRequestPojo> bean) throws ServerException {
        LOGGER.debug("Inside Server validation");

        UserAttributesBean userAttributesBean;

        try {
            UserDataService userDataService = new UserDataService();
            userAttributesBean = userDataService.getUserAttributesByContact(bean.getPojoObject().getContactNumber());

            if (userAttributesBean == null) {
                LOGGER.error("Invalid User {}", bean.getPojoObject().getContactNumber());
                throw new ServerException(MessageFormat.format("Invalid User {0}", bean.getPojoObject().getContactNumber()));
            }
        } catch (ControlCenterException e) {
            LOGGER.error("Invalid User {}", bean.getPojoObject().getContactNumber());
            throw new ServerException(MessageFormat.format("Invalid User {0}", bean.getPojoObject().getContactNumber()));
        }
        bean.getPojoObject().setUserAttributesBean(userAttributesBean);
        return bean;
    }

    @Override
    public String process(UtilityBean<OptInRequestPojo> bean) throws DataProcessingException {
        LOGGER.debug("Inside Process");

        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        return dbi.inTransaction((conn, status) -> {
            try {
                new UserDataService().updateUserOptIn(bean.getPojoObject(), conn);
                return OptInEnum.values()[bean.getPojoObject().getOptInStatus()].getStatus();
            } catch (Exception e) {
                throw new DataProcessingException(e, "Failed to update whatsapp opt-in.");
            }
        });
    }
}
