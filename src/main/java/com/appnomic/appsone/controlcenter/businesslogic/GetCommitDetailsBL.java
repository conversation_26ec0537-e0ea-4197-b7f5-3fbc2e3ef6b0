package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.ClientValidations;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.CommitDetailsPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.heal.configuration.pojos.Service;
import com.heal.configuration.pojos.TransactionAutoAcceptance;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GetCommitDetailsBL implements BusinessLogic<String, UtilityBean<String>, CommitDetailsPojo> {

    @Override
    public UtilityBean<String> clientValidation(RequestObject request) throws ClientException {
        ClientValidations.requestNullCheck(request);

        String accountIdentifier = ClientValidations.accountNullCheck(request);
        String authToken = ClientValidations.authTokenNullCheck(request);
        String serviceId = ClientValidations.serviceNullCheck(request);

        return UtilityBean.<String>builder()
                .accountIdentifier(accountIdentifier)
                .serviceId(serviceId)
                .authToken(authToken)
                .pojoObject("")
                .build();
    }

    @Override
    public UtilityBean<String> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        UserAccountBean userAccountBean;
        try {
            userAccountBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(),
                    utilityBean.getAccountIdentifier());
            utilityBean.setUserId(userAccountBean.getUserId());
        } catch (RequestException e) {
            log.error("Error while validating user and account details", e);
            throw new ServerException("Error while validating user and account details");
        }

        ControllerBean serviceDetails = new ControllerDataService().getControllerById(Integer.parseInt(utilityBean.getServiceId()), userAccountBean.getAccount().getId(), null);
        if (serviceDetails == null) {
            log.error("Service details unavailable for serviceID [{}]", utilityBean.getServiceId());
            throw new ServerException("Service details unavailable for serviceID [{}]" + utilityBean.getServiceId());
        }

        utilityBean.setServiceIdentifier(serviceDetails.getIdentifier());

        return utilityBean;
    }

    @Override
    public CommitDetailsPojo process(UtilityBean<String> bean) throws DataProcessingException {
        // redis call for getting last commit details
        Service service = new ServiceRepo().getServiceConfigurationByIdentifier(bean.getAccountIdentifier(), bean.getServiceIdentifier());
        if (service == null) {
            log.error("Service is null from redis for serviceId : {}", bean.getServiceId());
            throw new DataProcessingException("Service is null from redis");
        }
        TransactionAutoAcceptance txnAutoAcceptance = service.getTransactionAutoAcceptance();
        if (txnAutoAcceptance == null) {
            log.error("Service.TransactionAutoAcceptance is null from redis for serviceId : {}", bean.getServiceId());
            throw new DataProcessingException("Transaction auto acceptance is null in service");
        }
        long lastCommitTimeEpoch = DateTimeUtil.getEpochTime(txnAutoAcceptance.getLastCommitTime());
        // considering auto commit duration is in hours
        long nextCommitTimeEpoch = lastCommitTimeEpoch + ((long) txnAutoAcceptance.getAutoCommitDuration() * 3600 * 1000);
        return CommitDetailsPojo.builder()
                .lastCommitOn(lastCommitTimeEpoch)
                .nextCommitOn(nextCommitTimeEpoch)
                .build();
    }

}
