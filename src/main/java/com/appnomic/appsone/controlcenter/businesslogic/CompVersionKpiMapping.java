package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommonVersionCompIdMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CompVersionKpiMappingBean;
import com.appnomic.appsone.controlcenter.exceptions.KpiException;
import com.appnomic.appsone.controlcenter.pojo.AddKpiRequest;
import com.appnomic.appsone.controlcenter.dao.mysql.MasterDataService;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

class CompVersionKpiMapping {

    private static final Logger LOGGER = LoggerFactory.getLogger(CompVersionKpiMapping.class);

    private CompVersionKpiMapping() {
        //Dummy constructor to hide implicit one
    }

    static int addCompVerKpiMappingBean(String userId, AddKpiRequest addKpiRequest, CommonVersionCompIdMapping compIdMapping,
                                        int componentTypeId, int kpiDetailsId, Handle handle) throws KpiException {
        if(null == compIdMapping) {
            LOGGER.error("Common version & component ID are invalid");
            throw new KpiException("Common version & component ID are invalid");
        }

        CompVersionKpiMappingBean compVersionKpiMapping;
        try {
            compVersionKpiMapping = CompVersionKpiMappingBean.builder()
                    .isCustom(addKpiRequest.getCustom())
                    .kpiDetailsId(kpiDetailsId)
                    .doAnalytics(addKpiRequest.getEnableAnalytics())
                    .commonVersionId(compIdMapping.getCommonVersionId())
                    .userDetailsId(userId)
                    .defaultCollectionInterval(addKpiRequest.getCollectionIntervalSeconds())
                    .status(1)
                    .componentId(compIdMapping.getComponentId())
                    .componentTypeId(componentTypeId)
                    .createdTime(DateTimeUtil.getCurrentTimestampInGMT())
                    .updatedTime(DateTimeUtil.getCurrentTimestampInGMT())
                    .build();
        } catch(Exception e) {
            LOGGER.error("Exception encountered while creating CompVersionKpiMappingBean. Reason: {}", e.getMessage());
            throw new KpiException("Error while creating CompVersionKpiMappingBean");
        }

        int id = MasterDataService.insertIntoComponentVersionKpiMapping(compVersionKpiMapping, handle);

        if(-1 == id) {
            throw new KpiException("Error while mapping KPI to Component version.");
        }

        LOGGER.info("KPI-Component version mapping successfully added");

        return id;
    }
}
