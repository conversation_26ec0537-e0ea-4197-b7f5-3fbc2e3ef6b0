package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.opensearch.SchedulersOsRepo;
import com.appnomic.appsone.controlcenter.dao.redis.MasterDataRepo;
import com.appnomic.appsone.controlcenter.dao.redis.SchedulersRedisRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.heal.configuration.entities.JobSelectionCriteria;
import com.heal.configuration.enums.JobStatus;
import com.heal.configuration.pojos.TriggeredJob;
import com.heal.configuration.pojos.ViewTypes;
import com.heal.configuration.pojos.opensearch.TriggeredJobData;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class GetTriggeredJobsBL implements BusinessLogic<JobSelectionCriteria, JobSelectionCriteria, List<TriggeredJob>> {
    private String accountIdentifier;
    private final int bulkOperationLimit = ConfProperties.getInt(Constants.SCHEDULER_BULK_OPERATION_LIMIT, Constants.SCHEDULER_BULK_OPERATION_LIMIT_DEFAULT);

    MasterDataRepo masterDataRepo = new MasterDataRepo();
    SchedulersRedisRepo schedulersRedisRepo = new SchedulersRedisRepo();
    SchedulersOsRepo schedulersOsRepo = new SchedulersOsRepo();

    @Override
    public UtilityBean<JobSelectionCriteria> clientValidation(RequestObject requestObject) throws ClientException {
        String authToken = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authToken == null || authToken.trim().isEmpty()) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty.");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error(UIMessages.ACCOUNT_NULL_OR_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_NULL_OR_EMPTY);
        }

        String fromTimeStr = requestObject.getQueryParams().get("fromTime")[0];
        if (fromTimeStr == null || fromTimeStr.trim().isEmpty()) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("fromTime is invalid. It is either NULL or empty.");
            throw new ClientException("fromTime is invalid");
        }

        long fromTime;
        try {
            fromTime = Long.parseLong(fromTimeStr);
        } catch (NumberFormatException e) {
            log.error("fromTime is invalid. Reason: It is not a valid integer. Details: ", e);
            throw new ClientException("fromTime is invalid");
        }

        String toTimeStr = requestObject.getQueryParams().get("toTime")[0];
        if (toTimeStr == null || toTimeStr.trim().isEmpty()) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("toTime is invalid. It is either NULL or empty.");
            throw new ClientException("toTime is invalid");
        }

        long toTime;
        try {
            toTime = Long.parseLong(toTimeStr);
        } catch (NumberFormatException e) {
            log.error("toTime is invalid. Reason: It is not a valid integer. Details: ", e);
            throw new ClientException("toTime is invalid");
        }

        int schedulerDetailsId = 0;
        String schedulerDetailsIdStr = requestObject.getQueryParams().get("schedulerDetailsId")[0];
        if (schedulerDetailsIdStr == null || schedulerDetailsIdStr.trim().isEmpty()) {
            log.info("Scheduler ID is not provided in the request. Jobs will not be filtered based on the scheduler ID.");
        } else {
            try {
                schedulerDetailsId = Integer.parseInt(schedulerDetailsIdStr);
            } catch (NumberFormatException e) {
                log.error("Scheduler ID is invalid. Reason: It is not a valid integer. Details: ", e);
                throw new ClientException("Scheduler ID is invalid");
            }
        }

        String jobStatus = requestObject.getQueryParams().get("jobStatus")[0];
        if (jobStatus == null || jobStatus.trim().isEmpty()) {
            log.info("Job status is not provided in the request. Jobs will not be filtered based on the status.");
            jobStatus = "ALL";
        }

        JobSelectionCriteria jobSelectionCriteria = JobSelectionCriteria.builder()
                .fromTime(fromTime)
                .toTime(toTime)
                .schedulerDetailsId(schedulerDetailsId)
                .jobStatus(jobStatus)
                .build();

        return UtilityBean.<JobSelectionCriteria>builder()
                .authToken(authToken)
                .accountIdentifier(identifier)
                .pojoObject(jobSelectionCriteria)
                .build();
    }

    @Override
    public JobSelectionCriteria serverValidation(UtilityBean<JobSelectionCriteria> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);
        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        JobSelectionCriteria criteria = utilityBean.getPojoObject();

        if (criteria.getSchedulerDetailsId() > 0) {
            boolean isSchedulerIdValid = schedulersRedisRepo.getSchedulers(accountIdentifier)
                    .parallelStream().anyMatch(s -> s.getId() == criteria.getSchedulerDetailsId());

            if (!isSchedulerIdValid) {
                CCCache.INSTANCE.updateCCErrors(1);
                log.error("Scheduler details ID [{}] is invalid for account [{}].", criteria.getSchedulerDetailsId(), accountIdentifier);
                throw new ServerException("Scheduler details unavailable");
            }
        }

        return criteria;
    }

    @Override
    public List<TriggeredJob> process(JobSelectionCriteria bean) throws DataProcessingException {
        Set<TriggeredJobData> triggeredJobDataSet = schedulersOsRepo.getTriggeredJobs(accountIdentifier, bean.getJobStatus(),
                        bean.getFromTime(), bean.getToTime()).parallelStream()
                .filter(t -> Objects.nonNull(t.getJobId()))
                .collect(Collectors.toSet());

        if (triggeredJobDataSet.isEmpty()) {
            log.info("No triggered job with status [{}] available from [{}] to [{}] for account [{}]",
                    bean.getJobStatus(), bean.getFromTime(), bean.getToTime(), accountIdentifier);

            return Collections.emptyList();
        }

        List<Long> schedulerTriggerTimes = triggeredJobDataSet.parallelStream().map(TriggeredJobData::getSchedulerTriggerTime).distinct().collect(Collectors.toList());

        Map<Long, Integer> reTriggerCount = schedulersOsRepo.getReTriggeredActionCount(accountIdentifier, JobStatus.RETRIGGERED.getStatus(), schedulerTriggerTimes);

        List<String> supportedActions = masterDataRepo.getTypes().parallelStream()
                .filter(t -> t.getTypeName().equalsIgnoreCase("MaintenanceCmds"))
                .map(ViewTypes::getSubTypeName)
                .collect(Collectors.toList());

        if (supportedActions.isEmpty()) {
            log.error("Supported actions for scheduled jobs unavailable. All job actions will be disabled.");
        }

        return triggeredJobDataSet.parallelStream().map(t -> {
                    t.getSupportedActions().forEach(a -> {
                        if ("ReTrigger".equalsIgnoreCase(a.getName())) {
                            a.setRetriedCount(reTriggerCount.getOrDefault(t.getSchedulerTriggerTime(), 0));

                            if (a.getRetryLimit() <= a.getRetriedCount()) {
                                a.setStatus(0);
                                a.setMessage(String.format("Retry limit of %d exceeded.", a.getRetryLimit()));

                                log.info("Retry limit [{}] reached for action [{}] for transformation job ID [{}]",
                                        a.getRetryLimit(), a.getActionId(), t.getJobId());
                            }
                        }
                    });

                    Map<String, String> metadata = new HashMap<>(t.getMetadata());
                    metadata.put("jobId", t.getJobId());

                    return TriggeredJob.builder()
                            .schedulerDetailsId(t.getSchedulerDetailsId())
                            .schedulerName(t.getSchedulerName())
                            .scheduledJobId(t.getSchedulerJobId())
                            .jobName(t.getScheduledJobName())
                            .failureReason(t.getFailureReason())
                            .triggerTime(t.getTriggerTime())
                            .triggerSource(t.getTriggerSource())
                            .startTimeEpoch(t.getStartTimeEpoch())
                            .endTimeEpoch(t.getEndTimeEpoch())
                            .isRecurring(t.getSchedulerType().equalsIgnoreCase("Recurring") ? 1 : 0)
                            .implementationType(t.getJobImplementationType())
                            .jobType(t.getJobType())
                            .status(JobStatus.getName(t.getStatus()))
                            .schedulerArguments(t.getSchedulerArguments())
                            .scheduledJobArguments(t.getScheduledJobArguments())
                            .supportedActions(t.getSupportedActions())
                            .metadata(metadata)
                            .bulkOperationLimit(bulkOperationLimit)
                            .build();
                }
        ).collect(Collectors.toList());
    }
}
