package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.ViewApplicationServiceMappingBean;
import com.appnomic.appsone.controlcenter.beans.ViewClusterServicesBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.ServiceClusterPojo;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class GetServicesClustersBL implements BusinessLogic<String, String, List<ServiceClusterPojo>> {

    private int accId;

    public UtilityBean<String> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty");
            throw new ClientException("Invalid account identifier");
        }

        String[] applicationId = requestObject.getQueryParams().get(Constants.SERVICE_CLUSTER_APPLICATION_KEY);
        if (applicationId == null || applicationId.length != 1 || StringUtils.isEmpty(applicationId[0])) {
            log.error("Invalid query parameter value found. Reason: 'applicationId' is either NULL or empty");
            throw new ClientException("Invalid query parameter value found.");
        }
        String appId = "";
        try {
            if (applicationId[0].equals("*")) {
                appId = applicationId[0];
            } else if ((Integer) Integer.parseInt(applicationId[0]) instanceof Integer) {
                appId = applicationId[0];
            }
        } catch (Exception e) {
            log.error("Invalid query parameter value found. Reason: 'applicationId' is either NULL or empty");
            throw new ClientException("Invalid query parameter value found.");
        }


        return UtilityBean.<String>builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .pojoObject(appId)
                .build();
    }

    public String serverValidation(UtilityBean<String> utilityBean) throws ServerException {

        String accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        accId = account.getId();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }
        String applicationId = utilityBean.getPojoObject();

        if (!applicationId.equals("*")) {
            ControllerBean controllerBean =
                    new ControllerDataService().getApplicationsById(Integer.parseInt(applicationId), accId, null);
            if (controllerBean == null) {
                log.error("Application id [{}] is unavailable", applicationId);
                throw new ServerException(String.format("Application id [%s] is unavailable", applicationId));
            }
        }

        return applicationId;
    }

    public List<ServiceClusterPojo> process(String applicationId) throws DataProcessingException {

        try {
            ControllerDataService controllerDataService = new ControllerDataService();

            List<ViewApplicationServiceMappingBean> applicationServiceMappingBeanList;
            if (applicationId.equals("*")) {
                applicationServiceMappingBeanList = controllerDataService.getApplicationServiceMapping(accId);
            } else {
                applicationServiceMappingBeanList =
                        controllerDataService.getApplicationServiceMappingWithAppId(accId, Integer.parseInt(applicationId), null);
            }
            List<ViewClusterServicesBean> clusterServicesBeanList = controllerDataService.getClusterServiceMapping(null);

            return applicationServiceMappingBeanList.parallelStream()
                    .map(c -> ServiceClusterPojo.builder()
                            .id(c.getServiceId())
                            .serviceName(c.getServiceName())
                            .serviceIdentifier(c.getServiceIdentifier())
                            .clusters(clusterServicesBeanList.parallelStream()
                                    .filter(d -> d.getServiceId() == c.getServiceId())
                                    .map(d -> IdPojo.builder()
                                            .id(d.getClusterId())
                                            .name(d.getClusterName())
                                            .identifier(d.getClusterIdentifier())
                                            .build())
                                    .collect(Collectors.toList()))
                            .build()
                    ).collect(Collectors.toList());

        } catch (Exception e) {
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }

}
