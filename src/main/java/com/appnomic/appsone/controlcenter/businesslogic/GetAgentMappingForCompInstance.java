package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.AgentCompInstMappingBean;
import com.appnomic.appsone.controlcenter.beans.ComponentInstanceBean;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.AgentInstanceMappingDetails;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.collect.Sets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Kudva on 07/10/2021
 */

public class GetAgentMappingForCompInstance implements BusinessLogic<List<Integer>, List<Integer>, Set<AgentInstanceMappingDetails>> {
    private static final Logger LOGGER = LoggerFactory.getLogger(GetAgentMappingForCompInstance.class);
    private static final CompInstanceDataService COMP_INST_DATA_SERVICE = new CompInstanceDataService();

    @Override
    public UtilityBean<List<Integer>> clientValidation(RequestObject request) throws ClientException {

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }

        String[] instanceIdsString = request.getQueryParams().get("instanceIds");

        List<Integer> instanceIds = new ArrayList<>();
        if (instanceIdsString == null || instanceIdsString.length == 0) {
            LOGGER.error("InstanceIds is null or empty.");
            throw new ClientException("InstanceIds is null or empty.");
        } else {
            try {
                for (String s : instanceIdsString[0].split(",")) {
                    Integer parseInt = Integer.parseInt(s.trim());
                    instanceIds.add(parseInt);
                }
            } catch (NumberFormatException e) {
                LOGGER.error("InstanceIds should be positive integers. Details: {}", String.join(", ", instanceIdsString), e);
                throw new ClientException("InstanceIds should be positive integers.");
            }
            List<Integer> invalidInstIds = instanceIds.parallelStream()
                    .filter(i -> i < 1)
                    .collect(Collectors.toList());
            if (invalidInstIds.size() > 0) {
                LOGGER.error("InstanceId cannot be less than 1. Ids: {}", invalidInstIds);
                throw new ClientException("InstanceIds cannot be less than 1.");
            }
        }

        Set<Integer> instanceIdsSet = instanceIds.parallelStream().collect(Collectors.toSet());
        if(instanceIdsSet.size() != instanceIds.size()) {
            Set<Integer> duplicateInstanceIds = Sets.difference(instanceIdsSet, Sets.newHashSet(instanceIds));
            LOGGER.error("List of instance Ids provided has duplicate Ids {}", duplicateInstanceIds);
            throw new ClientException("List of instance Ids provided has duplicate Ids");
        }

        return UtilityBean.<List<Integer>>builder()
                .accountIdentifier(identifier)
                .pojoObject(instanceIds)
                .authToken(authToken)
                .build();
    }

    @Override
    public List<Integer> serverValidation(UtilityBean<List<Integer>> utilityBean) throws ServerException {
        UserAccountBean userAccBean;
        try {
            userAccBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            throw new ServerException(e.getMessage());
        }

        int accountId = userAccBean.getAccount().getId();
        List<Integer> instanceIds = utilityBean.getPojoObject();
        List<Integer> invalidInstIds = new ArrayList<>();
        instanceIds.parallelStream()
            .forEach(instId -> {
                ComponentInstanceBean componentInstanceBean = COMP_INST_DATA_SERVICE.getComponentInstanceByIdAndAccount(instId, accountId);
                if(componentInstanceBean == null) {
                    invalidInstIds.add(instId);
                }
            });

        if(invalidInstIds.size() > 0) {
           LOGGER.error("Invalid instance Ids: {}", invalidInstIds);
            throw new ServerException(String.format("Invalid instance Ids: %s", invalidInstIds));
        }

        return instanceIds;
    }

    @Override
    public Set<AgentInstanceMappingDetails> process(List<Integer> instanceIds) throws DataProcessingException {
        try {
            Set<AgentInstanceMappingDetails> result = new HashSet<>();
            Map<Integer, List<Integer>> compInstIdsByAgentId = new HashMap<>();

            instanceIds.parallelStream()
                .forEach(instId -> {
                    List<AgentCompInstMappingBean> agentCompInstMappingBeans = AgentDataService.getAgentMappingByCompInstanceId(instId);
                    result.addAll(agentCompInstMappingBeans.parallelStream().map(a -> {
                        compInstIdsByAgentId.putIfAbsent(a.getAgentId(), new ArrayList<>());
                        compInstIdsByAgentId.get(a.getAgentId()).add(instId);
                        return AgentInstanceMappingDetails.builder()
                         .agentId(a.getAgentId())
                         .agentTypeId(a.getAgentTypeId())
                         .build();
                        })
                        .collect(Collectors.toList()));
                });

            result.parallelStream()
                .forEach(res -> res.setCompInstanceIds(compInstIdsByAgentId.get(res.getAgentId())));
            return result;
        } catch (Exception e) {
            throw new DataProcessingException(e.getMessage());
        }
    }
}