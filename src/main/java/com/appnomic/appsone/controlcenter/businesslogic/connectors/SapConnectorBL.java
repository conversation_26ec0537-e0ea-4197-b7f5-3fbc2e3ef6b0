package com.appnomic.appsone.controlcenter.businesslogic.connectors;

import com.appnomic.appsone.controlcenter.common.ConnectorConstants;
import com.appnomic.appsone.controlcenter.dao.mysql.connectors.ConnectorDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.connectors.SapConnectorDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.*;
import com.appnomic.appsone.controlcenter.exceptions.FileUploadException;
import com.appnomic.appsone.controlcenter.pojo.connectors.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class SapConnectorBL {
    public void addSapInstanceDetails(List<SapInstanceDetails> sapInstanceDetails){
        List<SapInstanceFunctionalModuleMapping> sapInstanceFunctionalModuleMappings = new ArrayList<>();
        List<SapInstanceBean> instanceBeans = new ArrayList<>();
        for (SapInstanceDetails sapInstanceDetails1:sapInstanceDetails) {
            List<SapInstanceFunctionalModuleMapping> mapping =
                    getFunctionalModuleFunctionalModuleMapping(sapInstanceDetails1);
            sapInstanceFunctionalModuleMappings.addAll(mapping);
            instanceBeans.add(SapInstanceBean.builder()
                            .id(sapInstanceDetails1.getId())
                            .instanceName(sapInstanceDetails1.getSapInstanceName())
                            .healName(sapInstanceDetails1.getHealInstanceName())
                            .fileName(sapInstanceDetails1.getFileName())
                    .build());
        }
        SapConnectorDataService sapConnectorDataService = new SapConnectorDataService();
        sapConnectorDataService.addSapInstanceDetails(instanceBeans);
        sapConnectorDataService.addSapInstanceFunctionalModuleMapping(sapInstanceFunctionalModuleMappings);
    }


    public void addSapHealKpis(List<SapHealKpi> sapHealKpiList, String schemaName){

        SapConnectorDataService sapConnectorDataService = new SapConnectorDataService();
        ConnectorDataService connectorDataService = new ConnectorDataService();
        List<SapKpi> sapKpiDB = sapConnectorDataService.getSapKpi();
        List<String> kpiNameDB = sapKpiDB.stream().map(SapKpi::getKpiName).collect(Collectors.toList());
        List<String> kpiNameFile = sapHealKpiList.stream().map(SapHealKpi::getKpiName).collect(Collectors.toList());

        List<SapKpi> updateKpis = sapKpiDB.stream().filter(x -> !kpiNameFile.contains(x.getKpiName())).collect(Collectors.toList());
        List<SapHealKpi> newKpis = sapHealKpiList.stream().filter(x -> !kpiNameDB.contains(x.getKpiName())).collect(Collectors.toList());

        List<SapKpi> sapKpiList = newKpis.stream()
                .map(this::getSapKpiDetails)
                .collect(Collectors.toList());
        int[] srcIds = sapConnectorDataService.addSapKpi(sapKpiList);

        if(srcIds.length > 0) {
            List<HealKpi> healKpiList = newKpis.stream()
                    .map(this::getHealKpiDetails)
                    .collect(Collectors.toList());
            int[] conHealKpiIds = connectorDataService.addHealKpi(schemaName, healKpiList);

            List<DomainToHealKpiMapping> domainToHealKpiMappings = new ArrayList<>();
            if (srcIds.length == conHealKpiIds.length && conHealKpiIds.length == newKpis.size()) {
                for (SapHealKpi sapKpi : newKpis) {
                    domainToHealKpiMappings.add(DomainToHealKpiMapping.builder()
                            .domainName("sap")
                            .healIdentifier(sapKpi.getHealKpiIdentifier())
                            .sourceId(srcIds[sapKpi.getId() - 1])
                            .build());
                }
            }
            connectorDataService.addDomainToHealKpiMapping(schemaName, domainToHealKpiMappings);
        }
        for(SapKpi kpi : updateKpis)
        {
            sapConnectorDataService.updateSapKpi(kpi);
            Optional<SapHealKpi> kpiDetail = sapHealKpiList.parallelStream()
                    .filter(x -> x.getKpiName().equals(kpi.getKpiName())).findAny();
            Optional<DomainToHealKpiMapping> domainToHeal = connectorDataService.getDomainToHealKpiMapping(schemaName).parallelStream()
                    .filter(x -> x.getSourceId() == kpi.getId()).findAny();
            if(kpiDetail.isPresent() && domainToHeal.isPresent())
            {
                sapConnectorDataService.updateDomainToHealKpiMapping(kpiDetail.get().getHealKpiIdentifier(),domainToHeal.get().getSourceId());
                sapConnectorDataService.updateHealKpi(kpiDetail.get().getHealKpiName(), kpiDetail.get().getHealKpiIdentifier(), domainToHeal.get().getHealIdentifier());
            }
        }

    }

    public void addSapConnectionDetails(List<SapConnection> sapConnections){
        List<SapConnectionBean> sapConnectionBeanList = sapConnections.stream()
                .map(this::convertToSapConnectionEntity)
                .collect(Collectors.toList());
        SapConnectorDataService dataService = new SapConnectorDataService();

        int[] result = dataService.addSapConnectionDetails(sapConnectionBeanList);
        log.info("this is result : ",result);
    }


    public List<SapConnection> getSapConnectionFromFile(File fileName){
        List<SapConnection> sapConnections = new ArrayList<>();
        try ( FileInputStream file = new FileInputStream(fileName)){
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(ConnectorConstants.COMMON_SETTING_SHEET_INDEX);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for(int i=sheet.getFirstRowNum()+3;i<=sheet.getLastRowNum();i++){
                Row row=sheet.getRow(i);

                if (row.getCell(ConnectorConstants.SAP_CONNECTION_VALUE_INDEX)==null)
                    break;
                 SapConnection sapConnection= SapConnection.builder()
                        .id((int)row.getCell(ConnectorConstants.SAP_CONNECTION_ID_INDEX).getNumericCellValue())
                        .fileName(row.getCell(ConnectorConstants.SAP_CONNECTION_FILE_NAME_INDEX).getStringCellValue())
                        .name(row.getCell(ConnectorConstants.SAP_CONNECTION_NAME_INDEX).getStringCellValue())
                        .value(String.valueOf(row.getCell(ConnectorConstants.SAP_CONNECTION_VALUE_INDEX).getNumericCellValue()))
                        .build();
                 if (sapConnection.getId()!=0) sapConnections.add(sapConnection);

            }

        } catch (FileNotFoundException ex) {
            log.error("File not found at given location please check and try {}",ex.getMessage());
            throw new FileUploadException("Exception when reading excel file ");
        } catch (Exception ex) {
            log.error("Exception while reading the Sap Connection Data from excel file {}",ex.getMessage());
            throw new FileUploadException("Exception when reading the Sap Connection Data from excel file {}");
        }

        return sapConnections;
    }

    public List<SapHealKpi> getSapHealKpiDetails(File fileName){
        List<SapHealKpi> sapHealKpis = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)){
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(ConnectorConstants.COMMON_SETTING_SHEET_INDEX);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for(int i=sheet.getFirstRowNum()+3;i<=sheet.getLastRowNum();i++){
                Row ro=sheet.getRow(i);
//                ro.getCell(5).setCellType(CellType.STRING);
                if(ro.getCell(ConnectorConstants.SAP_HEAL_KPI_ID_INDEX)==null)
                    break;

                SapHealKpi sapHealKpi = SapHealKpi.builder()
                            .id((int) ro.getCell(ConnectorConstants.SAP_HEAL_KPI_ID_INDEX).getNumericCellValue())
                            .kpiName(ro.getCell(ConnectorConstants.SAP_HEAL_KPI_NAME_INDEX).getStringCellValue())
                            .kpiType(ro.getCell(ConnectorConstants.SAP_HEAL_KPI_TYPE_INDEX).getStringCellValue())
                            .kpiAggregator(ro.getCell(ConnectorConstants.SAP_HEAL_KPI_AGGREGATOR_INDEX).getStringCellValue())
                            .healKpiId((int) ro.getCell(ConnectorConstants.SAP_HEAL_KPI_HEAL_ID_INDEX).getNumericCellValue())
                            .healKpiName(ro.getCell(ConnectorConstants.SAP_HEAL_KPI_HEAL_NAME_INDEX).getStringCellValue())
                            .healKpiIdentifier(ro.getCell(ConnectorConstants.SAP_HEAL_KPI_HEAL_IDENTIFIER_INDEX).getStringCellValue())
                            .isGroupKpi(ro.getCell(ConnectorConstants.SAP_HEAL_KPI_IS_GROUP_INDEX).getNumericCellValue() == 1)
                            .healGroupName(ro.getCell(ConnectorConstants.SAP_HEAL_KPI_GROUP_NAME_INDEX).getStringCellValue())
                            .build();
                if (sapHealKpi.getId()!=0) sapHealKpis.add(sapHealKpi);

            }

        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try : ",ex);
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the Sap Kpi Data from excel file : ",ex);
            throw new FileUploadException("Exception when reading the Sap Kpi Data from excel file");
        }

        return sapHealKpis;
    }

    public List<SapInstanceDetails> getSapInstanceDetailsFromFile(File fileName){
        List<SapInstanceDetails> sapInstanceDetailsList = new ArrayList<>();
        try ( FileInputStream file = new FileInputStream(fileName)){
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(ConnectorConstants.COMMON_SETTING_SHEET_INDEX);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for(int i=sheet.getFirstRowNum()+3;i<=sheet.getLastRowNum();i++){
                Row ro=sheet.getRow(i);

                if(ro.getCell(ConnectorConstants.SAP_INSTANCE_ID_INDEX)==null)
                    break;
                SapInstanceDetails sapInstanceDetails = SapInstanceDetails.builder()
                            .id((int) ro.getCell(ConnectorConstants.SAP_INSTANCE_ID_INDEX).getNumericCellValue())
                            .sapInstanceName(ro.getCell(ConnectorConstants.SAP_INSTANCE_NAME_INDEX).getStringCellValue())
                            .healInstanceName(ro.getCell(ConnectorConstants.SAP_INSTANCE_HEAL_NAME_INDEX).getStringCellValue())
                            .fileName(ro.getCell(ConnectorConstants.SAP_INSTANCE_FILE_NAME_INDEX).getStringCellValue())
                            .build();
                if (sapInstanceDetails.getId()!=0)sapInstanceDetailsList.add(sapInstanceDetails);

            }

        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try : ",ex);
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the Sap Instance data from excel file : ",ex);
            throw new FileUploadException("Exception when reading the Sap Instance data from excel file");
        }
        return sapInstanceDetailsList;
    }

    public List<HealAgentInstance> getSourceHealInstanceMappingFromFile(File fileName){
        List<HealAgentInstance> healAgentInstances = new ArrayList<>();
        try ( FileInputStream file = new FileInputStream(fileName)){
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(ConnectorConstants.COMMON_SETTING_SHEET_INDEX);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for(int i=sheet.getFirstRowNum()+3;i<=sheet.getLastRowNum();i++){
                Row ro=sheet.getRow(i);
//                ro.getCell(5).setCellType(CellType.STRING);
                if(ro.getCell(ConnectorConstants.SAP_AGENT_SAP_INSTANCE_NAME_INDEX)==null)
                    break;
                HealAgentInstance agentInstance = HealAgentInstance.builder()
                        .sourceInstanceName(ro.getCell(ConnectorConstants.SAP_AGENT_SAP_INSTANCE_NAME_INDEX).getStringCellValue())
                        .agentIdentifier(ro.getCell(ConnectorConstants.SAP_AGENT_NAME_INDEX).getStringCellValue())
                        .healInstanceName(ro.getCell(ConnectorConstants.SAP_AGENT_HEAL_INSTANCE_NAME_INDEX).getStringCellValue())
                        .build();
                if(!agentInstance.getHealInstanceName().equalsIgnoreCase(""))healAgentInstances.add(agentInstance);
            }

        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try : ",ex);
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the Sap Instance data from excel file : ",ex);
            throw new FileUploadException("Exception when reading the Sap Instance data from excel file");
        }
        return healAgentInstances;
    }

    public SapConnectionBean convertToSapConnectionEntity(SapConnection sapConnection){
        return SapConnectionBean
                .builder().id(sapConnection.getId())
                .name(sapConnection.getName())
                .fileName(sapConnection.getFileName())
                .value(sapConnection.getValue())
                .build();
    }

    public SapKpi getSapKpiDetails(SapHealKpi sapHealKpi){
        return SapKpi
                .builder().id(sapHealKpi.getId())
                .kpiName(sapHealKpi.getKpiName())
                .kpiType(sapHealKpi.getKpiType())
                .kpiAggregator(sapHealKpi.getKpiAggregator())
                .build();
    }

    public HealKpi getHealKpiDetails(SapHealKpi sapHealKpi){
        return HealKpi.builder()
                .id(sapHealKpi.getId())
                .kpiId(sapHealKpi.getHealKpiId())
                .kpiName(sapHealKpi.getHealKpiName())
                .kpiIdentifier(Objects.equals(sapHealKpi.getHealKpiIdentifier(), "null") ?null:sapHealKpi.getHealKpiIdentifier())
                .groupName(sapHealKpi.getHealGroupName())
                .isGroupKpi(sapHealKpi.isGroupKpi()?1:0)
                .build();
    }

    public List<SapInstanceFunctionalModuleMapping> getFunctionalModuleFunctionalModuleMapping(SapInstanceDetails sapInstanceDetails){
        List<SapInstanceFunctionalModuleMapping> mappings = new ArrayList<>();
        ConnectorConstants.SAP_FUNCTIONAL_MODULE_IDS.forEach(fm -> mappings.add(
                SapInstanceFunctionalModuleMapping.builder()
                        .sapInstanceId(sapInstanceDetails.getId())
                        .functionalModuleId(fm)
                        .build()
        ));
        return mappings;
    }

}
