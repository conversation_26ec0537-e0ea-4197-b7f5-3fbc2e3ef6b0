package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.InstancesBean;
import com.appnomic.appsone.controlcenter.beans.UserAccessDetails;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.*;
import com.appnomic.appsone.controlcenter.dao.mysql.connectors.ConnectorDetailsDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.redis.SchedulersRedisRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.CompInstClusterDetails;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.SetupTypes;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.UserValidationUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GetEntityCountBL implements BusinessLogic<InstancesBean, InstancesBean, Map<String, Object>> {
    private static final Logger LOGGER = LoggerFactory.getLogger(GetEntityCountBL.class);

    @Override
    public UtilityBean<InstancesBean> clientValidation(RequestObject request) throws ClientException {
        if (request == null) {
            LOGGER.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }
        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            LOGGER.error(UIMessages.AUTH_KEY_EMPTY);
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }
        String accountIdentifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(accountIdentifier)) {
            LOGGER.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String[] typeName = request.getQueryParams().get("type");
        if (typeName == null || StringUtils.isEmpty(typeName[0])) {
            LOGGER.error("Type should not be empty or null");
            throw new ClientException("Invalid type name.");
        }

        InstancesBean instancesBean = new InstancesBean();
        instancesBean.setTypeName(typeName[0]);
        return UtilityBean.<InstancesBean>builder()
                .accountIdentifier(accountIdentifier)
                .authToken(authToken)
                .pojoObject(instancesBean).build();

    }

    @Override
    public InstancesBean serverValidation(UtilityBean<InstancesBean> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }
        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        //Check account details
        if (account == null) {
            LOGGER.error(UIMessages.INVALID_ACCOUNT_MESSAGE);
            throw new ServerException(UIMessages.INVALID_ACCOUNT_MESSAGE);
        }
        SetupTypes types;
        try {
            types = SetupTypes.valueOf(utilityBean.getPojoObject().getTypeName().trim());
        } catch (IllegalArgumentException ie) {
            LOGGER.error("Invalid type name");
            throw new ServerException("Invalid type name.");
        }
        InstancesBean instancesBean = utilityBean.getPojoObject();
        instancesBean.setAccountIdentifier(account.getIdentifier());
        instancesBean.setAccountId(account.getId());
        instancesBean.setUser(userId);
        instancesBean.setType(types);
        return instancesBean;
    }

    @Override
    public Map<String, Object> process(InstancesBean bean) throws DataProcessingException {
        Map<String, Object> data = new HashMap<>();
        long st = System.currentTimeMillis();
        if (bean.getType().compareTo(SetupTypes.HOST) == 0) {
            List<CompInstClusterDetails> details = MasterDataService.getCompInstanceDetails(bean.getAccountId());
            long count = details.stream().filter(i -> i.getComponentTypeName().equalsIgnoreCase(SetupTypes.HOST.name())).count();
            data.put(Constants.TOTAL, count);
        } else if (bean.getType().compareTo(SetupTypes.SERVICE) == 0) {
            UserAccessDetails userAccessDetails = UserValidationUtil.getUserAccessDetails(bean.getUser(), bean.getAccountIdentifier());
            if (userAccessDetails == null) {
                data.put(Constants.TOTAL, 0);
                return data;
            }
            data.put(Constants.TOTAL, userAccessDetails.getServiceIds().size());
        } else if (bean.getType().compareTo(SetupTypes.APPLICATION) == 0) {
            UserAccessDetails userAccessDetails = UserValidationUtil.getUserAccessDetails(bean.getUser(), bean.getAccountIdentifier());
            if (userAccessDetails == null) {
                data.put(Constants.TOTAL, 0);
                return data;
            }
            data.put(Constants.TOTAL, userAccessDetails.getApplicationIdentifiers().size());
        } else if (bean.getType().compareTo(SetupTypes.METRICES) == 0) {
            long count = new KPIDataService().addKpiCount(bean.getAccountId(), null);
            data.put(Constants.TOTAL, count);
        } else if (bean.getType().compareTo(SetupTypes.CATEGORY) == 0) {
            data.put(Constants.TOTAL, new CategoryDataService().getCategoryCountForAccount(bean.getAccountId()));
        } else if (bean.getType().compareTo(SetupTypes.PRODUCER) == 0) {
            data.put(Constants.TOTAL, ProducerDataService.getProducerCount(bean.getAccountId()));
        } else if (bean.getType().compareTo(SetupTypes.FORENSIC) == 0) {
            data.put(Constants.TOTAL, new ActionScriptDataService().getForensicCountForAccount(bean.getAccountId()));
        } else if (bean.getType().compareTo(SetupTypes.AGENT) == 0) {
            int size  = 0;
            UserAccessDetails userAccessDetails = UserValidationUtil.getUserAccessDetails(bean.getUser(), bean.getAccountIdentifier());
            if (userAccessDetails == null) {
                LOGGER.error("User access details is invalid for user [{}] and account [{}]", bean.getUser(), bean.getAccountIdentifier());
                data.put(Constants.TOTAL, 0);
                return data;
            }

            data.put(Constants.TOTAL, userAccessDetails.getAgents().size());
        } else if (bean.getType().compareTo(SetupTypes.CONNECTOR) == 0) {
            data.put(Constants.TOTAL, ConnectorDetailsDataService.getConnectorCountForAccount(bean.getAccountId()));
        } else if (bean.getType().compareTo(SetupTypes.SCHEDULERS) == 0) {
            data.put(Constants.TOTAL, new SchedulersRedisRepo().getSchedulers(bean.getAccountIdentifier()).size());
        }

        LOGGER.info("Time taken in {} ms.", System.currentTimeMillis() - st);
        return data;

    }
}
