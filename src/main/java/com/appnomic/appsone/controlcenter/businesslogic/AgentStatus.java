package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.AgentBean;
import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.*;
import com.appnomic.appsone.controlcenter.dao.opensearch.AgentHealthStatusRepo;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.heal.configuration.pojos.opensearch.AgentCommandsData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

public class AgentStatus {

    private static final Logger LOGGER = LoggerFactory.getLogger(AgentStatus.class);

    public ValidatedRequestKey clientValidation(RequestObject requestObject) throws ControlCenterException {
        String accountIdString = requestObject.getParams().get(UIMessages.ACCOUNT_IDENTIFIER.toLowerCase());
        String serviceId = "0";

        if(requestObject.getQueryParams() != null && !requestObject.getQueryParams().isEmpty()) {
            serviceId = requestObject.getQueryParams().get(Constants.AUDIT_PARAM_SERVICE_NAME)[0];
        }

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdString);
        if (account == null) {
            LOGGER.error("Invalid account identifier: {}", accountIdString);
            throw new ControlCenterException(UIMessages.INVALID_ACCOUNT_MESSAGE);
        }

        if (serviceId == null || serviceId.trim().length() == 0) {
            LOGGER.error(UIMessages.SERVICE_EMPTY_ERROR_MESSAGE, serviceId);
            throw new ControlCenterException(UIMessages.SERVICE_EMPTY_ERROR);
        }

        int servId;
        try {
            servId = Integer.parseInt(serviceId);
        } catch(Exception e) {
            LOGGER.error("ServiceID is invalid. It is not an integer.");
            throw new ControlCenterException("ServiceId is not an integer");
        }

        return new ValidatedRequestKey(account, servId);
    }

    public List<CompInstClusterDetails> serverValidation(ValidatedRequestKey requestKey) throws ControlCenterException {
        List<CompInstClusterDetails> compInstClusterDetails = new ArrayList<>();
        ControllerDataService controllerDataService = new ControllerDataService();

        if(requestKey.getServiceId() == 0) {
            List<ControllerBean> servicesList = controllerDataService.getServicesForAccount(requestKey.getAccount().getId(), null);

            if(servicesList == null || servicesList.isEmpty()) {
                LOGGER.warn("Services unavailable for account [{}]", requestKey.getAccount().getId());
            } else {
                servicesList.forEach(service -> {
                    List<CompInstClusterDetails> tempList = new CompInstanceDataService().getCompInstanceDetailsForService(service.getId(),
                            requestKey.getAccount().getId(), null);
                    compInstClusterDetails.addAll(tempList);
                });
            }
        } else {
            ControllerBean service = new ControllerDataService().getControllerById(requestKey.getServiceId(), requestKey.getAccount().getId(), null);

            if (null == service) {
                LOGGER.error("Invalid service identifier. Reason: No mapping found in controller table for the provided serviceId [{}]", requestKey.getServiceId());
                throw new ControlCenterException(UIMessages.INVALID_SERVICE);
            }

            compInstClusterDetails.addAll(new CompInstanceDataService().getCompInstanceDetailsForService(requestKey.getServiceId(),
                    requestKey.getAccount().getId(), null));
        }

        return compInstClusterDetails;
    }

    public List<AgentStatusPojo> getAgentStatusDetails(List<CompInstClusterDetails> compInstDetails, String accountIdentifier) {
        List<AgentStatusPojo> instancesData = new ArrayList<>();
        Map<Integer, List<CommandDetailsBean>> commandDetailsMap = new HashMap<>();
        long time = System.currentTimeMillis();
        compInstDetails.forEach(ca -> {
            List<AgentBean> agentCompList = AgentStatusDataService.getAgentCompDetails(ca.getInstanceId());
            Map<Integer, Set<AgentBean>> typeWiseAgents = agentCompList.stream()
                    .collect(Collectors.groupingBy(AgentBean::getAgentTypeId, Collectors.toSet()));

            typeWiseAgents.forEach((k, v) -> {
                if (!k.equals(Integer.valueOf(Constants.JIM_AGENT_TYPE_ID)) && !k.equals(Integer.valueOf(Constants.FORENSIC_AGENT_TYPE_ID))) {
                    AgentStatusPojo agentStatusBean = new AgentStatusPojo();
                    agentStatusBean.setInstanceId(ca.getInstanceId());
                    agentStatusBean.setAgentTypeId(k);

                    if (!commandDetailsMap.containsKey(k)) {
                        commandDetailsMap.put(k, CommandDataService.getCommandDetailsByAgentType(k, null));
                    }

                    List<CommandDetailsBean> detailsBeans = commandDetailsMap.get(k);
                    agentStatusBean.setCommands(detailsBeans.stream()
                            .map(c -> new CommandDetailsPojo(c.getId(), c.getName()))
                            .collect(Collectors.toList())
                    );

                    updateAgentDetails(accountIdentifier, v, agentStatusBean);
                    instancesData.add(agentStatusBean);
                }
            });
        });

        LOGGER.error("Time taken to process: {}", System.currentTimeMillis()-time);

        return instancesData;
    }


    private void updateAgentDetails(String accountIdentifier, Set<AgentBean> agentBeans, AgentStatusPojo agentStatusBean) {
        agentStatusBean.setAgentDetails(agentBeans.parallelStream().map(agentBean -> {
            PhysicalAgentBean physicalAgentBean = AgentDataService.getPhysicalAgentDetailsUsingId(agentBean.getPhysicalAgentId());

            if(null != physicalAgentBean) {
                AgentDetailsBeansPojo agentDetailsBeansPojo = new AgentDetailsBeansPojo(agentBean, physicalAgentBean);
                getMetaData(agentDetailsBeansPojo, accountIdentifier, physicalAgentBean.getIdentifier());

                getAgentDataDetails(agentDetailsBeansPojo, agentBean, physicalAgentBean);

                if (null != physicalAgentBean.getLastStatusId() && physicalAgentBean.getLastStatusId() > 0) {
                    agentDetailsBeansPojo.setCurrentStatus(RulesDataService.getNameFromMSTSubType(physicalAgentBean.getLastStatusId()));
                }
                agentDetailsBeansPojo.setName(agentBean.getName());
                agentDetailsBeansPojo.setIdentifier(physicalAgentBean.getIdentifier());
                agentDetailsBeansPojo.setHostAddress(agentBean.getHostAddress());
                agentDetailsBeansPojo.setInstalledOn(agentBean.getCreatedTime());
                agentDetailsBeansPojo.setAgentType(RulesDataService.getNameFromMSTSubType(agentBean.getAgentTypeId()));
                agentDetailsBeansPojo.setIsCommandComplete(physicalAgentBean.getLastCommandExecuted());
                agentDetailsBeansPojo.setVersion(agentBean.getVersion());
                return agentDetailsBeansPojo;
            } else {
                return null;
            }
        }).collect(Collectors.toList()));
    }

    private void getAgentDataDetails(AgentDetailsBeansPojo agentDetailsBeansPojo, AgentBean agentBean, PhysicalAgentBean physicalAgentBean) {
        try {
            if (!agentDetailsBeansPojo.getCommandJobId().equals("")) {
                CommandTriggerBean commandDetails = AgentStatusDataService.getAgentCommandTriggerStatus(agentDetailsBeansPojo.getPhysicalAgentId(), agentDetailsBeansPojo.getCommandJobId());
                if (Objects.nonNull(commandDetails)) {
                    agentDetailsBeansPojo.setLastCommandName(commandDetails.getLastCommandName());
                    agentDetailsBeansPojo.setDesiredStatus(commandDetails.getDesiredStat());
                    agentDetailsBeansPojo.setNoOfCmds(commandDetails.getNoOfCmds());

                    String lastDesired = AgentDataService.getLastDesiredStatus(agentDetailsBeansPojo.getPhysicalAgentId());
                    if (lastDesired != null) {
                        agentDetailsBeansPojo.setLastDesiredStatus(lastDesired);
                    }

                    if (physicalAgentBean.getLastCommandExecuted() == 0) {
                        long fromEpochTime = DateTimeUtil.getGMTToEpochTime(String.valueOf(commandDetails.getTriggerTime()));
                        long toEpochTime = DateTimeUtil.getGMTToEpochTime(String.valueOf(new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime())));
                        long diffInMilli = toEpochTime - fromEpochTime;
                        long secondsPassed = (diffInMilli / 1000) / 60;
                        long timeInMinute = commandDetails.getTimeoutInSecs() / 60;
                        if (secondsPassed > timeInMinute) {
                            CommandDataService.updateCommandFailureStatus(physicalAgentBean.getIdentifier(),
                                    physicalAgentBean.getLastJobId(), physicalAgentBean.getUserDetailsId());
                        }
                    }
                }
            }
        } catch (ControlCenterException e) {
            LOGGER.error("no data available for update agent:{agentId}" + agentBean.getId(), e);
        } catch (ParseException e) {
            LOGGER.error("no data available for agent:{agentId}" + agentBean.getId(), e);
        }
    }

    private void getMetaData(AgentDetailsBeansPojo agentDetailsBeans, String accountIdentifier, String agentIdentifier) {
        AgentCommandsData agentHealthStatus = new AgentHealthStatusRepo().getAgentCommandDetails(accountIdentifier, agentIdentifier, agentDetailsBeans.getCommandJobId());
        if (Objects.nonNull(agentHealthStatus)) {
            agentDetailsBeans.setLastCommandTime(agentHealthStatus.getLastDataReceivedTimeInGMT());
            Map<String, String> dataMap = agentHealthStatus.getMetadata();
            if (Objects.nonNull(dataMap)) {
                agentDetailsBeans.setMetaData(dataMap);
            }
        }
    }
}
