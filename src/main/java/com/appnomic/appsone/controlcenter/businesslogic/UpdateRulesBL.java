package com.appnomic.appsone.controlcenter.businesslogic;


import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.RulesBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.xpt.UpdateBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.RulesDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.UpdateObjectReferenceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.TagRequestPojo;
import com.appnomic.appsone.controlcenter.pojo.UpdatedValue;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.Rule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class UpdateRulesBL implements BusinessLogic<List<UpdatedValue>, List<UpdateBean>, String> {

    private AccountBean account;
    private int serviceId;
    BasicEntity serviceDetail = new BasicEntity();
    ServiceRepo serviceRepo = new ServiceRepo();

    private static List<UpdateBean> getUpdateBeans(List<UpdatedValue> updatedValueList, Map<Integer, RulesBean> idMap, Map<String, RulesBean> nameMap,
                                                   String userId, String timestamp) {
        return updatedValueList.parallelStream().map(updatedValue -> {
                    int id = updatedValue.getId();
                    RulesBean rulesBean = idMap.get(id);

                    if (rulesBean == null) {
                        log.error("Rule id {}" + " is not present in rules table.", id);
                        return null;
                    }

                    if (updatedValue.getAddDiscoveryTags().isEmpty() && updatedValue.getRemoveDiscoveryTags().isEmpty() && updatedValue.getMonitorEnabled() == null && updatedValue.getDiscoveryEnabled() == null) {

                        if (updatedValue.getName() == null && updatedValue.getStatus() == null) {
                            log.error("No changes in edit request for RuleId {}", updatedValue.getId());
                            return null;
                        }
                        if (updatedValue.getName() != null && nameMap.containsKey(updatedValue.getName())) {
                            log.info("Rule with same name already exists.");
                            return null;
                        }
                    }
                    UpdateBean updateBean = new UpdateBean();
                    updateBean.setId(updatedValue.getId());

                    if (com.appnomic.appsone.controlcenter.util.StringUtils.isEmpty(updatedValue.getName()) || nameMap.containsKey(updatedValue.getName())) {
                        updateBean.setName(rulesBean.getName());
                    } else {
                        updateBean.setName(updatedValue.getName());
                    }
                    updateBean.setStatus(updatedValue.getStatus() == null ? (rulesBean.getStatus() == 1) : updatedValue.getStatus());
                    updateBean.setUpdateTime(timestamp);
                    updateBean.setUserDetailsId(userId);
                    if (updatedValue.getMonitorEnabled() == null) {
                        updateBean.setMonitorEnabled(Integer.parseInt(Constants.RULES_MONITORING_ENABLED_DEFAULT));
                    } else {
                        updateBean.setMonitorEnabled(updatedValue.getMonitorEnabled());
                    }
                    if (updatedValue.getDiscoveryEnabled() == null) {
                        updateBean.setDiscoveryEnabled(Integer.parseInt(Constants.RULES_DISCOVERY_ENABLED_DEFAULT));
                    } else {
                        updateBean.setDiscoveryEnabled(updatedValue.getDiscoveryEnabled());
                    }
                    updateBean.setAddDiscoveryTags(updatedValue.getAddDiscoveryTags());
                    updateBean.setRemoveDiscoveryTags(updatedValue.getRemoveDiscoveryTags());

                    return updateBean;

                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public UtilityBean<List<UpdatedValue>> clientValidation(RequestObject requestObject) throws ClientException {
        log.debug("Inside Client validation");
        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        String serviceId = requestObject.getParams().get(Constants.SERVICE_ID);

        List<UpdatedValue> updatedValueList;

        if (identifier == null || StringUtils.isEmpty(identifier)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        if (serviceId == null || StringUtils.isEmpty(serviceId)) {
            log.error(UIMessages.SERVICE_EMPTY_ERROR_MESSAGE, serviceId);
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR_MESSAGE);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authKey)) {
            log.error(UIMessages.AUTH_KEY_EMPTY);
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }

        ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();
        try {
            updatedValueList = objectMapper.readValue(requestObject.getBody(), new TypeReference<List<UpdatedValue>>() {
            });
        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID + " Details: {}", e.getMessage());
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        for (UpdatedValue updatedValue : updatedValueList) {
            TagRequestPojo tagRequestPojo = new TagRequestPojo();
            tagRequestPojo.getAddDiscoveryTags().addAll(updatedValue.getAddDiscoveryTags());
            tagRequestPojo.getRemoveDiscoveryTags().addAll(updatedValue.getRemoveDiscoveryTags());
            tagRequestPojo.getObjectIds().add(updatedValue.getId());
            tagRequestPojo.validate();
            if (tagRequestPojo.getError().size() > 0) {
                log.error(UIMessages.JSON_INVALID + " Details: {}", tagRequestPojo.getError().toString());
                throw new ClientException(UIMessages.JSON_INVALID);
            }
        }

        return UtilityBean.<List<UpdatedValue>>builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .serviceId(serviceId)
                .pojoObject(updatedValueList)
                .build();
    }

    @Override
    public String process(List<UpdateBean> bean) throws DataProcessingException {
        StringBuilder stringBuilder = new StringBuilder();

        //we are accessing 0 index because only one rule can be updated at a time
        Set<String> addDiscoveryTags = bean.get(0).getAddDiscoveryTags();
        Set<String> removeDiscoveryTags = bean.get(0).getRemoveDiscoveryTags();
        if (removeDiscoveryTags.isEmpty() && addDiscoveryTags.isEmpty()) {
            stringBuilder.append(updateNameStatus(bean));
        } else {
            stringBuilder.append(updateNameDiscoveryTags(bean));
        }

        log.info(stringBuilder.toString());

        return stringBuilder.toString();
    }

    @Override
    public List<UpdateBean> serverValidation(UtilityBean<List<UpdatedValue>> utilityBean) throws ServerException {

        log.debug("Inside Server validation");
        serviceId = Integer.parseInt(utilityBean.getServiceId());
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            log.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }

        ControllerBean serviceDetails = new ControllerDataService().getControllerById(serviceId, account.getId(), null);
        if (serviceDetails == null) {
            log.error(UIMessages.INVALID_SERVICE, utilityBean.getServiceId());
            throw new ServerException(UIMessages.INVALID_SERVICE);
        }

        serviceDetail.setIdentifier(serviceDetails.getIdentifier());

        if (!utilityBean.getPojoObject().get(0).getAddDiscoveryTags().isEmpty() || !utilityBean.getPojoObject().get(0).getRemoveDiscoveryTags().isEmpty()) {
            TagRequestPojo tagRequestPojo = new TagRequestPojo();
            tagRequestPojo.getAddDiscoveryTags().addAll(utilityBean.getPojoObject().get(0).getAddDiscoveryTags());
            tagRequestPojo.getRemoveDiscoveryTags().addAll(utilityBean.getPojoObject().get(0).getRemoveDiscoveryTags());
            tagRequestPojo.getObjectIds().add(utilityBean.getPojoObject().get(0).getId());
            tagRequestPojo.setServiceId(utilityBean.getServiceId());
            try {
                TagRequestDetailsBL.removeAddTagValidation(account.getId(), tagRequestPojo, Constants.RULES_TABLE);
            } catch (RequestException e) {
                log.error("Error occurred in validation of removing and adding tag:", e);
                throw new ServerException("Error occurred in server validation.");
            }
        }

        List<RulesBean> rulesBeanList = RulesDataService.getRules(utilityBean.getPojoObject().parallelStream().map(UpdatedValue::getId).collect(Collectors.toList()));

        if (rulesBeanList == null) {
            log.error("Rules not available for the account {}", account.getId());
            throw new ServerException("Rules not available for the service " + account.getId());
        }

        Map<Integer, RulesBean> idMap = new HashMap<>();
        Map<String, RulesBean> nameMap = new HashMap<>();
        rulesBeanList.forEach(rule -> {
                    idMap.put(rule.getId(), rule);
                    nameMap.put(rule.getName(), rule);
                }
        );

        String timestamp = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
        List<UpdateBean> updateBeanList = getUpdateBeans(utilityBean.getPojoObject(), idMap, nameMap, userId, timestamp);
        if (updateBeanList.size() != utilityBean.getPojoObject().size()) {
            log.error("Invalid data ");
        }

        return updateBeanList;
    }

    private String updateNameStatus(List<UpdateBean> updateBeanList) throws DataProcessingException {
        long st = System.currentTimeMillis();
        new UpdateObjectReferenceDataService().updateRules(updateBeanList, null);
        log.info("TIME TAKEN UPDATE NAME: {}", System.currentTimeMillis() - st);

        List<Rule> serviceWiseRules = serviceRepo.getServiceRules(account.getIdentifier(), serviceDetail.getIdentifier());
        boolean updatedRules = false;
        for (UpdateBean updateBean : updateBeanList) {
            Rule rule = serviceWiseRules.parallelStream().filter(f -> f.getId() == updateBean.getId()).findAny().orElse(null);
            if (rule != null) {
                rule.setMonitoringEnabled(updateBean.getStatus());
                rule.setName(updateBean.getName());
                rule.setMonitoringEnabled(updateBean.getMonitorEnabled().equals(1));
                rule.setDiscoveryEnabled(updateBean.getDiscoveryEnabled().equals(1));
                updatedRules = true;
            }
        }

        if (updatedRules) {
            serviceRepo.updateServiceRules(account.getIdentifier(), serviceDetail.getIdentifier(), serviceWiseRules);
        }

        return "Rule updated successfully.";

    }

    private String updateNameDiscoveryTags(List<UpdateBean> updateBeanList) {
        StringBuilder stringBuilder = new StringBuilder();

        MySQLConnectionManager.getInstance().getHandle().inTransaction((conn, status) -> {
            if (!updateBeanList.get(0).getRemoveDiscoveryTags().isEmpty() || !updateBeanList.get(0).getAddDiscoveryTags().isEmpty()) {
                long st = System.currentTimeMillis();
                TagRequestPojo tagRequest = new TagRequestPojo();
                tagRequest.getObjectIds().add(updateBeanList.get(0).getId());
                tagRequest.setRemoveDiscoveryTags(updateBeanList.get(0).getRemoveDiscoveryTags());
                tagRequest.setAddDiscoveryTags(updateBeanList.get(0).getAddDiscoveryTags());
                tagRequest.setServiceId(String.valueOf(serviceId));

                String result = TagRequestDetailsBL.discoveryTagsBL(account, tagRequest, Constants.RULES_TABLE, conn);
                stringBuilder.append(result);

                log.info("TIME TAKEN TO ADD REMOVE DISCOVERY TAGS: {}", System.currentTimeMillis() - st);
            }

            new UpdateObjectReferenceDataService().updateRules(updateBeanList, conn);

            stringBuilder.append("Rule name updated successfully.");
            return "";
        });

        List<Rule> serviceWiseRules = serviceRepo.getServiceRules(account.getIdentifier(), serviceDetail.getIdentifier());
        boolean updatedRules = false;
        for (UpdateBean updateBean : updateBeanList) {
            Rule rule = serviceWiseRules.parallelStream().filter(f -> f.getId() == updateBean.getId()).findAny().orElse(null);
            if (rule != null) {
                rule.setName(updateBean.getName());
                rule.setMonitoringEnabled(updateBean.getMonitorEnabled().equals(1));
                rule.setDiscoveryEnabled(updateBean.getDiscoveryEnabled().equals(1));
                updatedRules = true;
            }
        }
        long st = System.currentTimeMillis();

        if (updatedRules) {
            serviceRepo.updateServiceRules(account.getIdentifier(), serviceDetail.getIdentifier(), serviceWiseRules);
            log.debug("TIME TAKEN UPDATE NAME: {}", System.currentTimeMillis() - st);
        }

        return stringBuilder.toString();
    }
}
