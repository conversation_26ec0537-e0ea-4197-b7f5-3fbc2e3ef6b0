package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.ObjPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class GetEnvironmentDetailsBL implements BusinessLogic<Object, Integer, List<ObjPojo>> {
    CompInstanceDataService compInstanceDataService = new CompInstanceDataService();

    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty");
            throw new ClientException("Invalid account identifier");
        }

        return UtilityBean.builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .build();
    }

    @Override
    public Integer serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

        if (account == null) {
            String errMsg = String.format("Account identifier %s is invalid", accountIdentifier);
            log.error(errMsg);
            throw new ServerException(errMsg);
        }

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        int environmentTypeIdFromTypeName = compInstanceDataService.getEnvTypeIdFromTypeName(Constants.ENVIRONMENT_TYPE_NAME, null);

        if(environmentTypeIdFromTypeName == 0) {
            String errMsg = String.format("Could not find the type id for the type name %s in Data source.", Constants.ENVIRONMENT_TYPE_NAME);
            log.error(errMsg);
            throw new ServerException(errMsg);
        }
        return environmentTypeIdFromTypeName;
    }

    @Override
    public List<ObjPojo> process(Integer typeId) throws DataProcessingException {
        log.debug("Retrieving subType details for the type Id {} of type {}", typeId, Constants.ENVIRONMENT_TYPE_NAME);

        List<ObjPojo> environmentSubTypeDetails = compInstanceDataService.getEnvSubTypeDetails(typeId, null);

        if(environmentSubTypeDetails.isEmpty()) {
            String errMsg = String.format("Obtained empty result from data source when queried for sub type details for the type id %s of type %s", typeId, Constants.ENVIRONMENT_TYPE_NAME);
            log.error(errMsg);
            throw new DataProcessingException(errMsg);
        }

        return environmentSubTypeDetails;
    }
}
