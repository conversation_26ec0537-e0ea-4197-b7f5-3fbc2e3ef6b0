package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.InstancesBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.CompInstClusterDetails;
import com.appnomic.appsone.controlcenter.pojo.CompInstancePojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.stream.Collectors;

public class InstanceBL implements BusinessLogic<CompInstancePojo, InstancesBean, List<CompInstancePojo>> {
    private static final Logger LOGGER = LoggerFactory.getLogger(InstanceBL.class);

    @Override
    public UtilityBean<CompInstancePojo> clientValidation(RequestObject requestObject) throws ClientException {
        String accountIdString = requestObject.getParams().get(UIMessages.ACCOUNT_IDENTIFIER);

        if (StringUtils.isEmpty(accountIdString)) {
            LOGGER.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String serviceId = requestObject.getParams().get(UIMessages.SERVICE_IDENTIFIER.toLowerCase());

        if (serviceId == null || serviceId.trim().length() == 0) {
            LOGGER.error(UIMessages.SERVICE_EMPTY_ERROR_MESSAGE, serviceId);
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR);
        }

        try {
            Integer.parseInt(serviceId);
        } catch(NumberFormatException e) {
            LOGGER.error("Service Id [{}] is not an integer", serviceId);
            throw new ClientException("Service Id is not an integer");
        }

        return UtilityBean.<CompInstancePojo>builder()
                .accountIdentifier(accountIdString)
                .serviceId(serviceId)
                .build();
    }

    @Override
    public InstancesBean serverValidation(UtilityBean<CompInstancePojo> utilityBean) throws ServerException {
        int accountId = ValidationUtils.validAndGetIdentifier(utilityBean.getAccountIdentifier());
        if (accountId == -1) {
            LOGGER.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }

        ControllerBean service = new ControllerDataService().getControllerById(Integer.parseInt(utilityBean.getServiceId()), accountId, null);

        if(null == service) {
            LOGGER.error(UIMessages.INVALID_SERVICE);
            throw new ServerException(UIMessages.INVALID_SERVICE);
        }

        InstancesBean instancesBean = new InstancesBean();
        instancesBean.setAccountId(accountId);
        instancesBean.setServiceId(utilityBean.getServiceId());

        return instancesBean;
    }

    @Override
    public List<CompInstancePojo> process(InstancesBean bean) {

        List<CompInstClusterDetails> compHostDetails = new CompInstanceDataService()
                .getCompInstanceDetailsForService(Integer.parseInt(bean.getServiceId()), bean.getAccountId(),null);

        return compHostDetails.parallelStream().map(c -> CompInstancePojo.builder()
                .id(c.getInstanceId()).name(c.getInstanceName()).hostAddress(c.getHostAddress()).build())
                .collect(Collectors.toList());
    }
}
