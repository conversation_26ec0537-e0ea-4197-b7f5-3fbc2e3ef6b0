package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.CategoryDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.redis.CategoryRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.Category;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class DeleteCategoryBL implements BusinessLogic<Integer, UtilityBean<Integer>, String> {

    CategoryDataService categoryDataService = new CategoryDataService();

    @Override
    public UtilityBean<Integer> clientValidation(RequestObject request) throws ClientException {
        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }

        String categoryIdString = request.getParams().get(Constants.CATEGORY_ID);
        if (categoryIdString == null || categoryIdString.trim().isEmpty()) {
            log.error("Category Id is null or empty.");
            throw new ClientException("Category Id is null or empty.");
        }

        int categoryId;
        try {
            categoryId = Integer.parseInt(categoryIdString);
        } catch (NumberFormatException e) {
            log.error("Category Id should be a positive integer.");
            throw new ClientException("Category Id should be a positive integer.");
        }


        return UtilityBean.<Integer>builder()
                .authToken(authToken)
                .accountIdentifier(identifier)
                .pojoObject(categoryId)
                .build();
    }

    @Override
    public UtilityBean<Integer> serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be " +
                    "invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        int accountId = account.getId();
        int categoryId = utilityBean.getPojoObject();

        int categoryCount;
        try {
            categoryCount = categoryDataService.getCategoryById(categoryId, accountId, null);
        } catch (ControlCenterException e) {
            throw new ServerException(e.getMessage());
        }

        if (categoryCount == 0) {
            log.error("Category is not present for the specified account.");
            throw new ServerException("Category is not present in the specified account.");
        }

        int kpiCount;
        try {
            kpiCount = categoryDataService.getKpiCountForCategory(categoryId);
        } catch (ControlCenterException e) {
            throw new ServerException(e.getSimpleMessage());
        }

        if (kpiCount != 0) {
            log.error("Category can not delete because its mapped with some other KPI.");
            throw new ServerException("Category cant delete because its mapped with KPI.");
        }

        return utilityBean;
    }

    @Override
    public String process(UtilityBean<Integer> categoryDetail) throws DataProcessingException {
        try {
            int categoryId = categoryDetail.getPojoObject();
            CategoryRepo categoryRepo = new CategoryRepo();
            int updatedRowId = categoryDataService.deleteCategoryById(categoryId, null);
            if (updatedRowId == categoryId) {
                log.error("Error in deleting category with ID [{}]", categoryId);
                throw new DataProcessingException("Error in deleting category");
            }
            List<Category> categoryDetails = categoryRepo.getCategoryDetails(categoryDetail.getAccountIdentifier());
            Category category = categoryDetails.parallelStream().filter(f -> f.getId() == categoryId).findAny().orElse(null);
            if (category == null) {
                log.error("Could not find category object detail which is to be deleted");
                return "";
            }
            String categoryIdentifier = category.getIdentifier();
            categoryRepo.deleteCategoryInRedis(categoryDetail.getAccountIdentifier(), categoryIdentifier);
            categoryDetails.remove(category);
            categoryRepo.updateCategoryDetails(categoryDetail.getAccountIdentifier(), categoryDetails);
            return "Category is deleted successfully";
        } catch (Exception e) {
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }
}