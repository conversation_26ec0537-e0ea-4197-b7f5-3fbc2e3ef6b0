package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandTriggerBean;
import com.appnomic.appsone.controlcenter.dao.redis.ConnectionsRepo;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.service.ControlCenterService;
import com.appnomic.appsone.controlcenter.util.*;
import com.heal.configuration.pojos.MaintenanceDetails;
import lombok.extern.slf4j.Slf4j;
import spark.Request;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ServiceDetailsBL {

    private static final String FAILED = "failed";

    public AccountBean clientValidation(Request request) throws ControlCenterException, RequestException {
        String accountIdString = request.params(Constants.ACCOUNT_IDENTIFIER);
        AccountBean account = ValidationUtils.validAndGetAccount(accountIdString);
        if (null == account) {
            throw new ControlCenterException("Invalid account identifier " + accountIdString);
        }

        List<Controller> controllerService = CommonUtils.getControllersByTypeBypassCache(Constants.SERVICES_CONTROLLER_TYPE, account.getId());
        if (controllerService == null || controllerService.isEmpty()) {
            log.warn("No services available for account identifier: " + account.getIdentifier());
        }

        return account;
    }

    public ServiceDetailsKey serverValidation(int accountId) throws ControlCenterException {
        ViewTypes serviceType = MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT, Constants.SERVICES_CONTROLLER_TYPE);
        if (serviceType == null) {
            log.error("Unable to fetch Services for ControllerType.");
            throw new ControlCenterException("Unable to fetch Services for ControllerType.");
        }

        TagDetailsBean controllerTag = MasterCache.getTagDetails(Constants.CONTROLLER_TAG);
        if (controllerTag == null) {
            throw new ControlCenterException("Tag detail is not found for given tag name and account id " + accountId);
        }

        return new ServiceDetailsKey(accountId, controllerTag);
    }

    public List<ServiceListPage> getServiceProcessList(AccountBean account, boolean agentDetailRequired, TagDetailsBean controllerTag, String userId) throws ControlCenterException {
        long time = System.currentTimeMillis();
        int accountId = account.getId();

        Map<Integer, CompInstClusterDetails> componentInstanceMap = MasterDataService.getCompInstanceDetails(accountId)
                .parallelStream()
                .filter(c -> c.getStatus() == 1)
                .filter(c -> c.getIsCluster() == 0)
                .collect(Collectors.toMap(CompInstClusterDetails::getInstanceId, t -> t));
        log.info("Time taken for componentInstanceMap:{} ms", (System.currentTimeMillis() - time));
        time = System.currentTimeMillis();

        Map<Integer, Set<Integer>> clusterInstsMapping = MasterDataService.getClusterInstanceMapping(accountId)
                .parallelStream().collect(Collectors.groupingBy(ClusterInstanceMapping::getClusterId,
                        Collectors.mapping(ClusterInstanceMapping::getCompInstanceId, Collectors.toSet())));
        log.info("Time taken for clusterInstsMapping:{} ms", (System.currentTimeMillis() - time));
        time = System.currentTimeMillis();

        //Should have only clusters
        Map<String, List<TagMappingDetails>> serviceToClusterTagMappings;
        if (!clusterInstsMapping.isEmpty()) {
            serviceToClusterTagMappings = MasterDataService.getTagMappingDetailsForCompInstanceClusters(accountId, controllerTag.getId(), new ArrayList<>(clusterInstsMapping.keySet()))
                    .parallelStream()
                    .collect(Collectors.groupingBy(TagMappingDetails::getTagKey));
            log.info("Time taken for serviceToClusterTagMappings:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();
        } else {
            serviceToClusterTagMappings = new HashMap<>();
        }

        List<ViewApplicationServiceMappingBean> serviceAppMapping = UserValidationUtil.getUserAccessibleApplicationsServices(userId, account.getIdentifier());
        if (serviceAppMapping.isEmpty()) {
            log.warn("Access details unavailable for user:{}, Account: {}", userId, account.getIdentifier());
            return Collections.emptyList();
        }
        log.info("Time taken for serviceAppMapping:{} ms", (System.currentTimeMillis() - time));
        time = System.currentTimeMillis();

        Map<Integer, List<ViewApplicationServiceMappingBean>> servicesMap = serviceAppMapping.parallelStream().filter(sa -> sa.getServiceId() != null).collect(Collectors.groupingBy(ViewApplicationServiceMappingBean::getServiceId));
        log.debug("Access Details: ServiceIds size:{}", servicesMap.size());
        log.info("Time taken for accessibleServiceList:{} ms", (System.currentTimeMillis() - time));
        time = System.currentTimeMillis();


        List<ConnectionDetails> connectionDetailsList = new ArrayList<>();
        Map<Integer, String> timezone = new HashMap<>();
        Map<Integer, Integer> timezoneDetails = new HashMap<>();
        Map<Integer, String> layerDetails = new HashMap<>();
        Map<Integer, List<IdValuePojo>> instanceAgentMap = new HashMap<>();
        Map<Integer, List<AgentPhysicalAgentPojo>> agentPhysicalAgentMap = new HashMap<>();
        Map<Integer, Integer> allTransactionMap = new HashMap<>();

        if (!agentDetailRequired) {
            connectionDetailsList = MasterDataService.getConnectionDetails(accountId);
            log.info("Time taken for connectionDetailsList:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            timezone = MasterCache.getTimezones().parallelStream().collect(Collectors
                    .toMap(TimezoneDetail::getId, TimezoneDetail::getTimeZoneId));
            log.info("Time taken for timezone:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            TagDetailsBean timezoneTagDetails = MasterCache.getTagDetails(Constants.TIME_ZONE_TAG);
            if (timezoneTagDetails == null) {
                log.error("Tag details unavailable for 'Timezone' tag");
                throw new ControlCenterException("Tag details unavailable for 'Timezone' tag");
            }
            log.info("Time taken for timezoneTagDetails:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            int timeZoneTagId = timezoneTagDetails.getId();
            timezoneDetails = TagsDataService.getTagValue(timeZoneTagId, Constants.CONTROLLER, accountId).parallelStream()
                    .collect(Collectors.toMap(IdPojo::getId, t -> Integer.parseInt(t.getIdentifier())));
            log.info("Time taken for timezoneDetails:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            TagDetailsBean layerTagDetails = MasterCache.getTagDetails(Constants.LAYER_TAG);
            if (layerTagDetails == null) {
                log.error("Tag details unavailable for 'Layer' tag");
                throw new ControlCenterException("Tag details unavailable for 'Layer' tag");
            }
            log.info("Time taken for layerTagDetails:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            int layerTagId = layerTagDetails.getId();
            layerDetails = TagsDataService.getTagValue(layerTagId, Constants.CONTROLLER, accountId).parallelStream()
                    .collect(Collectors.toMap(IdPojo::getId, IdPojo::getName, (val1, val2) -> {
                        log.warn("Duplicate tags of tag_key 'Type' found for service. Choosing the first entry [{}] available instead of [{}] in tag_mapping.", val1, val2);
                        return val1;
                    }));
            log.info("Time taken for layerDetails:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            allTransactionMap = ControlCenterService.getAllTransaction(Constants.TXN_TABLE,
                            controllerTag.getId(), accountId).parallelStream()
                    .collect(Collectors.toMap(IdValuePojo::getId, IdValuePojo::getValue));
            log.info("Time taken for allTransactionMap:{} ms", (System.currentTimeMillis() - time));
        } else {
            instanceAgentMap = AgentDataService.getAllAgentIdOfCompInstId()
                    .parallelStream().collect(Collectors.groupingBy(IdValuePojo::getId));
            log.info("Time taken for instanceAgentMap:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            agentPhysicalAgentMap = AgentDataService.getPhysicalAgentIdsFromAgentId()
                    .parallelStream().collect(Collectors.groupingBy(AgentPhysicalAgentPojo::getAgentId));
            log.info("Time taken for agentPhysicalAgentMap:{} ms", (System.currentTimeMillis() - time));
        }
        time = System.currentTimeMillis();

        Map<Integer, String> finalTimezone = timezone;
        Map<Integer, Integer> finalTimezoneDetails = timezoneDetails;
        Map<Integer, String> finalLayerDetails = layerDetails;
        List<ConnectionDetails> finalConnectionDetailsList = connectionDetailsList;
        Map<Integer, List<IdValuePojo>> finalInstanceAgentMap = instanceAgentMap;
        Map<Integer, List<AgentPhysicalAgentPojo>> finalAgentPhysicalAgentMap = agentPhysicalAgentMap;
        Map<Integer, Integer> finalAllTransactionMap = allTransactionMap;
        ControllerDataService dataService = new ControllerDataService();

        List<ControllerBean> serviceList = dataService.getAllServicesByAccountId(accountId, null);
        Map<Integer, ControllerBean> serviceListMap = serviceList.parallelStream().collect(Collectors.toMap(ControllerBean::getId, c -> c));
        log.info("Time taken for serviceList:{} ms", (System.currentTimeMillis() - time));
        time = System.currentTimeMillis();

        String maintenanceCheckEnabled = InstallationAttributesDataService.checkForServiceMaintenanceFlag(null);

        TagDetailsBean entryTagDetail = MasterCache.getTagDetails(Constants.ENTRY_POINT);
        List<IdPojo> entryPointTagMappingDetails = new ArrayList<>();
        if (entryTagDetail == null) {
            log.error("Entry Tag {} does not exist in database.", Constants.ENTRY_POINT);
        } else {
            entryPointTagMappingDetails = TagsDataService.getTagValue(entryTagDetail.getId(), Constants.CONTROLLER, accountId);
        }

        List<IdPojo> entryPointTagMappings = entryPointTagMappingDetails;
        List<ServiceListPage> services = servicesMap.entrySet().parallelStream()
                .map(entry -> {
                    ServiceListPage bean = new ServiceListPage();

                    ControllerBean service = serviceListMap.getOrDefault(entry.getKey(), null);
                    if (service == null) {
                        return null;
                    }
                    bean.setId(entry.getKey());
                    bean.setName(service.getName());
                    bean.setIdentifier(service.getIdentifier());

                    List<TagMappingDetails> clusterTagMappingDetails = serviceToClusterTagMappings.getOrDefault(String.valueOf(bean.getId()), new ArrayList<>());
                    List<CompInstClusterDetails> compInstClusterDetailsList = clusterTagMappingDetails.parallelStream()
                            .map(t -> clusterInstsMapping.getOrDefault(t.getObjectId(), new HashSet<>()))
                            .flatMap(Collection::parallelStream)
                            .map(componentInstanceMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

                    if (agentDetailRequired) {
                        String agentRunningStatus = getAgentRunningStatus(compInstClusterDetailsList,
                                finalInstanceAgentMap, finalAgentPhysicalAgentMap);
                        bean.setAgentStatus(agentRunningStatus);
                    } else {
                        bean.setLayer(finalLayerDetails.getOrDefault(entry.getKey(), ""));
                        bean.setTimezone(finalTimezone.getOrDefault(finalTimezoneDetails.get(entry.getKey()), ""));
                        bean.setStatus(String.valueOf(service.getStatus()));
                        bean.setCreatedOn(DateTimeUtil.getGMTToEpochTime(service.getCreatedTime()));
                        bean.setLastModifiedOn(DateTimeUtil.getGMTToEpochTime(service.getUpdatedTime()));

                        KeyCloakUserDetails keyCloakUserDetails = MasterCache.getKeycloakUserDetails(service.getUserDetailsId());
                        if (keyCloakUserDetails != null) {
                            bean.setCreatedBy(keyCloakUserDetails.getUsername());
                            bean.setLastModifiedBy(keyCloakUserDetails.getUsername());
                        }

                        bean.setTxnCount(finalAllTransactionMap.getOrDefault(bean.getId(), 0));

                        updateComponentInstanceList(bean, compInstClusterDetailsList);

                        updateServiceAndComponentType(bean, compInstClusterDetailsList);

                        Set<IdPojo> serviceApps = entry.getValue().parallelStream().map(c -> IdPojo.builder()
                                        .id(c.getApplicationId())
                                        .name(c.getApplicationName())
                                        .identifier(c.getApplicationIdentifier())
                                        .build())
                                .collect(Collectors.toSet());

                        bean.setApplication(serviceApps);

                        updateInboundOutBoundServices(finalConnectionDetailsList, entry.getKey(), bean, serviceList);

                        if (entryTagDetail != null)
                            updateServiceEntryTagDetails(entryPointTagMappings, service.getId(), entryTagDetail, bean);

                        if (StringUtils.isEmpty(maintenanceCheckEnabled) || !maintenanceCheckEnabled.equalsIgnoreCase("false")) {
                            bean.setMaintenance(getServiceMaintenanceStatus(service.getIdentifier(), account.getIdentifier()));
                        }
                    }
                    return bean;
                })
                .collect(Collectors.toList());

        log.warn("After access able services size {}.", services.size());
        log.info("Time take for services population is {} ms.", System.currentTimeMillis() - time);
        return services;
    }


    private static void updateServiceEntryTagDetails(List<IdPojo> entryPointTagMapping, int serviceId, TagDetailsBean entryTagDetail, ServiceListPage bean) {
        boolean entryPointTag = entryPointTagMapping.parallelStream().anyMatch(p -> p.getId() == serviceId);
        if (entryPointTag) {
            bean.setIsEntryPoint(1);
            bean.setTags(Collections.singletonList(entryTagDetail.getName()));
        }
    }

    private static void updateServiceAndComponentType(ServiceListPage bean, List<CompInstClusterDetails> compInstClusterDetailsList) {
        String checkIsKubernetes = TagsDataService.getTagValueByKey(Constants.KUBERNETES_TAG_ID, bean.getId(), Constants.DEFAULT_TAG_VALUE, Constants.CONTROLLER);
        if (checkIsKubernetes != null && !checkIsKubernetes.trim().isEmpty()) {
            if (checkIsKubernetes.equalsIgnoreCase(Constants.KUBERNETES)) {
                bean.setServiceType(Constants.KUBERNETES);
            } else {
                bean.setServiceType("");
            }
        }
        String type = compInstClusterDetailsList.parallelStream()
                .map(CompInstClusterDetails::getComponentTypeName)
                .filter(componentTypeName -> !Constants.HOST.equalsIgnoreCase(componentTypeName))
                .distinct()
                .collect(Collectors.joining(", "));
        bean.setComponentType(type);
    }

    private static void updateComponentInstanceList(ServiceListPage bean, List<CompInstClusterDetails> compInstClusterDetailsList) {
        Set<String> compNameList = new HashSet<>();
        Set<Integer> compIdList = new HashSet<>();
        compInstClusterDetailsList
                .forEach(c -> {
                    compNameList.add(c.getInstanceName());
                    compIdList.add(c.getInstanceId());
                });

        // get the Component Name and Count
        Map<String, Object> compList = new HashMap<>();
        if (!compNameList.isEmpty()) {
            compList.put(Constants.COUNT, compNameList.size());
            compList.put(Constants.NAME, compNameList);
            compList.put(Constants.ID, compIdList);
        }
        bean.setComponentInstance(compList);
    }

    private static void updateInboundOutBoundServices(List<ConnectionDetails> connectionDetailsList, Integer serviceId, ServiceListPage bean, List<ControllerBean> serviceList) {
        if (Objects.nonNull(connectionDetailsList)) {
            Map<String, Object> map = new HashMap<>();

            final List<Integer> serviceConnectionList = connectionDetailsList.parallelStream()
                    .filter(c -> c.getDestinationId() == serviceId)
                    .map(ConnectionDetails::getSourceId)
                    .collect(Collectors.toList());

            List<String> srcServiceNameList = serviceList.parallelStream()
                    .filter(a -> serviceConnectionList.contains(a.getId()))
                    .map(ControllerBean::getName)
                    .collect(Collectors.toList());

            map.put(Constants.COUNT, srcServiceNameList.size());
            map.put(Constants.NAME, srcServiceNameList);

            bean.setInbound(map);

            map = new HashMap<>();
            final List<Integer> destServiceConnList = connectionDetailsList.parallelStream()
                    .filter(c -> c.getSourceId() == serviceId)
                    .map(ConnectionDetails::getDestinationId)
                    .collect(Collectors.toList());

            List<String> destServiceNameList = serviceList.parallelStream()
                    .filter(a -> destServiceConnList.contains(a.getId()))
                    .map(ControllerBean::getName)
                    .collect(Collectors.toList());

            map.put(Constants.COUNT, destServiceNameList.size());
            map.put(Constants.NAME, destServiceNameList);

            bean.setOutbound(map);
        }
    }

    private static String getAgentRunningStatus(List<CompInstClusterDetails> compInstClusterDetails,
                                                Map<Integer, List<IdValuePojo>> instanceAgentMap,
                                                Map<Integer, List<AgentPhysicalAgentPojo>> agentPhysicalAgentMap) {
        String overallStatus = "";
        List<Integer> instanceIds = compInstClusterDetails.stream().map(CompInstClusterDetails::getInstanceId).collect(Collectors.toList());
        if (instanceIds.isEmpty()) {
            return overallStatus;
        }

        List<Integer> agentIds = instanceIds.parallelStream().map(c ->
                        instanceAgentMap.getOrDefault(c, new ArrayList<>()))
                .flatMap(Collection::stream)
                .map(IdValuePojo::getValue).distinct().collect(Collectors.toList());

        if (!agentIds.isEmpty()) {
            List<AgentPhysicalAgentPojo> physicalAgentBeans = agentIds.parallelStream()
                    .map(c -> agentPhysicalAgentMap.getOrDefault(c, new ArrayList<>()))
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());

            for (AgentPhysicalAgentPojo physicalAgentBean : physicalAgentBeans) {
                String agentStatus = getAgentStatus(physicalAgentBean);
                if (FAILED.equalsIgnoreCase(agentStatus)) {
                    return agentStatus;
                }
                if (Constants.AGENT_STATUS_STOP.equalsIgnoreCase(agentStatus)) {
                    overallStatus = agentStatus;
                }
            }
        }

        return overallStatus;
    }

    private static String getAgentStatus(AgentPhysicalAgentPojo physicalAgentBean) {
        String overallStatus = null;
        if (null != physicalAgentBean.getLastJobId()
                && !physicalAgentBean.getLastJobId().trim().isEmpty()) {
            CommandTriggerBean commandDetails = AgentStatusDataService
                    .getAgentCommandTriggerStatus(physicalAgentBean.getPhysicalAgentId(),
                            physicalAgentBean.getLastJobId());

            String lastStatus = "";
            if (null != physicalAgentBean.getLastStatusId()) {
                lastStatus = RulesDataService
                        .getNameFromMSTSubType(physicalAgentBean.getLastStatusId());
            }

            if (null != commandDetails) {
                if ((null == lastStatus || lastStatus.trim().isEmpty())
                        && (commandDetails.getNoOfCmds() > 1
                        || physicalAgentBean.getLastCommandExecuted() == 1)) {
                    return FAILED;
                }

                if (null != lastStatus && !lastStatus.trim().isEmpty()) {
                    if (Constants.AGENT_STATUS_STOP.equalsIgnoreCase(commandDetails.getDesiredStat())
                            && lastStatus.equalsIgnoreCase(commandDetails.getDesiredStat())) {
                        overallStatus = Constants.AGENT_STATUS_STOP;
                    } else if (Constants.AGENT_STATUS_RUNNING.equalsIgnoreCase(commandDetails.getDesiredStat())
                            && !lastStatus.equalsIgnoreCase(commandDetails.getDesiredStat())) {
                        return FAILED;
                    }
                }
            }
        }
        return overallStatus;
    }

    private boolean getServiceMaintenanceStatus(String serviceIdentifier, String accountIdentifier) {
        List<MaintenanceDetails> maintenanceDetails = new ConnectionsRepo().getMaintenanceDetails(accountIdentifier, serviceIdentifier);
        if (maintenanceDetails.isEmpty()) {
            return false;
        }

        boolean isUnderMaintenance = false;

        for (MaintenanceDetails maintenanceDetail : maintenanceDetails) {
            if (maintenanceDetail.isOngoing()) {
                isUnderMaintenance = true;
                break;
            }
        }
        return isUnderMaintenance;
    }
}
