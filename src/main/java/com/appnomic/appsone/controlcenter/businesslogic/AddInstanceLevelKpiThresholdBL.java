package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.KPIDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.InstanceKpiAttributeThresholdBean;
import com.appnomic.appsone.controlcenter.dao.opensearch.InstanceKpiThresholdRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.InstanceKpiThresholdDetails;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.KpiAttributeLevelThresholdUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.Handle;

import java.util.List;
import java.util.Map;

@Slf4j
public class AddInstanceLevelKpiThresholdBL implements BusinessLogic<InstanceKpiThresholdDetails, List<InstanceKpiAttributeThresholdBean>, String> {

    private static final KpiAttributeLevelThresholdUtil KPI_UTIL = new KpiAttributeLevelThresholdUtil();

    @Override
    public UtilityBean<InstanceKpiThresholdDetails> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (StringUtils.isEmpty(requestObject.getBody())) {
            log.error("Invalid request body. Reason: Request body is either NULL or empty.");
            throw new ClientException("Invalid request body. Reason: Request body is either NULL or empty.");
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);

        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Account identifier is invalid");
            throw new ClientException("Account identifier is invalid");
        }

        InstanceKpiThresholdDetails kpiAttributeThresholds = KPI_UTIL.getInstanceKpiThresholdDetails(requestObject, true);

        return UtilityBean.<InstanceKpiThresholdDetails>builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .pojoObject(kpiAttributeThresholds)
                .build();
    }

    @Override
    public List<InstanceKpiAttributeThresholdBean> serverValidation(UtilityBean<InstanceKpiThresholdDetails> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        String accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);
        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        InstanceKpiThresholdDetails details = utilityBean.getPojoObject();
        int accountId = account.getId();

        Map<Integer, String> compInstIdToIdentifierMap = KPI_UTIL.verifyInstanceIdKpiAndGroupKpi(details, accountId);

        return KPI_UTIL.kpiAndCompInstanceValidation(details, compInstIdToIdentifierMap, userId, accountId, accountIdentifier, true);
    }

    @Override
    public String process(List<InstanceKpiAttributeThresholdBean> thresholdBeans) throws DataProcessingException {
        String result;
        try {
            result = MySQLConnectionManager.getInstance().getHandle()
                    .inTransaction((conn, status) -> insertThresholdData(thresholdBeans, conn));

            //Inserting threshold details in redis cache
            KPI_UTIL.addInstanceKpiAttributeLevelThresholdsInRedis(thresholdBeans);
        } catch (Exception e) {
            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
            } else {
                throw e;
            }
        }

        return result;
    }

    private String insertThresholdData(List<InstanceKpiAttributeThresholdBean> thresholdBeans, Handle handle) throws DataProcessingException {
        //inserting threshold detail in percona
        int[] ids = new KPIDataService().addInstanceKpiAttributeLevelThresholds(thresholdBeans, handle);
        if (ids == null || ids.length == 0) {
            log.error("Error while adding attribute level thresholds");
            throw new DataProcessingException("Error while adding attribute level thresholds");
        }

        //inserting threshold detail in opensearch
        try {
            new InstanceKpiThresholdRepo().createThreshold(thresholdBeans);
        } catch (ControlCenterException e) {
            log.error("Error while adding the thresholds to OpenSearch");
            throw new DataProcessingException("Error while adding the thresholds to OpenSearch");
        }

        return "Attribute thresholds added successfully";
    }
}

