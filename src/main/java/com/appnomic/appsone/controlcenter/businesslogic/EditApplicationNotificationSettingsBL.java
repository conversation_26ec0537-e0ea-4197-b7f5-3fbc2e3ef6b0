package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.AccessDetailsBean;
import com.appnomic.appsone.controlcenter.beans.UserAccessBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.NotificationSettingsDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.UserAccessDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.NotificationSettings;
import com.appnomic.appsone.controlcenter.dao.redis.AccountRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ApplicationRepo;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.pojo.ApplicationNotificationSettingsPojo;
import com.appnomic.appsone.controlcenter.pojo.NotificationSettingsPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.service.KeyCloakAuthService;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.model.JWTData;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.reflect.TypeToken;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.EscalationSettings;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class EditApplicationNotificationSettingsBL implements BusinessLogic<ApplicationNotificationSettingsPojo, UtilityBean<ApplicationNotificationSettingsPojo>, String>{

    private static final ObjectMapper obj_mapper = CommonUtils.getObjectMapperWithHtmlEncoder();
    @Override
    public UtilityBean<ApplicationNotificationSettingsPojo> clientValidation(RequestObject requestObject) throws ClientException {

        List<NotificationSettingsPojo> settings;

        if (StringUtils.isEmpty(requestObject.getBody())) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        try {
            settings = obj_mapper.readValue(requestObject.getBody(),
                    new TypeReference<List<NotificationSettingsPojo>>() {
                    });

        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID + "err:{}", e.getMessage());
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        if (settings.isEmpty()) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }


        Set<NotificationSettingsPojo> settingsSet = new HashSet<>(settings);
        if (settingsSet.size() < settings.size()) {
            log.error(UIMessages.DUPLICATE_NOTIFICATION_SETTING);
            throw new ClientException(UIMessages.DUPLICATE_NOTIFICATION_SETTING);
        }

        for (NotificationSettingsPojo setting : settings) {
            setting.validate();
            if (!setting.getError().isEmpty()) {
                String err = setting.getError().toString();
                log.error(err);
                throw new ClientException(err);
            }
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String accountIdentifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(accountIdentifier)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }


        String appIdsString = requestObject.getQueryParams().get(Constants.SERVICE_CLUSTER_APPLICATION_KEY)[0];
        if (appIdsString == null || StringUtils.isEmpty(appIdsString)) {
            log.error("Invalid query parameter value found. Reason: 'applicationId' is either NULL or empty");
            throw new ClientException("Invalid query parameter value found.");
        }

        String[] appIds = appIdsString.split(",");

        List<Integer> applicationIds = new ArrayList<>();

        try {
            for (String appId : appIds) {
                applicationIds.add(Integer.parseInt(appId));
            }
        } catch (Exception e) {
            log.error(UIMessages.APPLICATION_ID_IS_NOT_NUMBER, e);
            throw new ClientException(UIMessages.APPLICATION_ID_IS_NOT_NUMBER);
        }

        return UtilityBean.<ApplicationNotificationSettingsPojo>builder()
                .pojoObject(ApplicationNotificationSettingsPojo.builder()
                        .notificationSettingsPojos(settings)
                        .applicationIds(applicationIds)
                        .build())
                .accountIdentifier(accountIdentifier)
                .authToken(authKey)
                .build();

    }

    @Override
    public UtilityBean<ApplicationNotificationSettingsPojo> serverValidation(UtilityBean<ApplicationNotificationSettingsPojo> utilityBean) throws ServerException {

        JWTData jwtData;

        try {
            jwtData = KeyCloakAuthService.extractUserDetails(utilityBean.getAuthToken());
        } catch (ControlCenterException e) {
            log.error("Exception encountered while fetching the userIdentifier. Details: {}", e.getMessage());
            throw new ServerException("Error while extracting user identifier");
        }

        UserAccessBean accessDetails;
        try {
            accessDetails = UserAccessDataService.getUserAccessDetails(jwtData.getSub().trim());
        } catch (ControlCenterException e) {
            log.error(e.getMessage());
            throw new ServerException(e.getMessage());
        }

        Type userBeanType = new TypeToken<AccessDetailsBean>() {
        }.getType();

        AccessDetailsBean accessDetailsBean = CommonUtils.jsonToObject(accessDetails.getAccessDetailsJson(), userBeanType);
        if (accessDetailsBean == null) {
            log.error(UIMessages.INVALID_USER_ACCESS_DETAILS);
            throw new ServerException(UIMessages.INVALID_USER_ACCESS_DETAILS);
        }
        AccountRepo accountRepo = new AccountRepo();

        Account account = accountRepo.getAccountWithAccountIdentifier(utilityBean.getAccountIdentifier());

        if (account == null) {
            log.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }

        ApplicationRepo applicationRepo = new ApplicationRepo();

        List<Integer> applicationIdsFromRequest = utilityBean.getPojoObject().getApplicationIds();

        List<Integer> appIdsFromDB = applicationRepo.getApplicationsForAccount(utilityBean.getAccountIdentifier()).parallelStream()
                .map(BasicEntity::getId)
                .filter(applicationIdsFromRequest::contains)
                .collect(Collectors.toList());

        if (appIdsFromDB.size() != applicationIdsFromRequest.size()) {
            List<Integer> invalidAppIds = applicationIdsFromRequest.parallelStream().filter(app -> !appIdsFromDB.contains(app))
                    .collect(Collectors.toList());
            log.error("Invalid application Ids provided: {}", invalidAppIds);
            throw new ServerException("Invalid application Ids provided: "+ invalidAppIds);
        }

        utilityBean.setUserId(jwtData.getSub());
        AccountBean accountBean = new AccountBean();
        accountBean.setId(account.getId());
        utilityBean.setAccount(accountBean);
        return utilityBean;


    }

    @Override
    public String process(UtilityBean<ApplicationNotificationSettingsPojo> bean) throws DataProcessingException {

        ApplicationNotificationSettingsPojo pojoObject = bean.getPojoObject();

        List<NotificationSettingsPojo> notificationSettingsPojos = pojoObject.getNotificationSettingsPojos();
        List<Integer> applicationIds = pojoObject.getApplicationIds();

        List<NotificationSettings> settings = applicationIds.parallelStream()
                .flatMap(app -> notificationSettingsPojos.parallelStream()
                        .map(appNotification -> NotificationSettings.builder()
                                .userId(bean.getUserId())
                                .typeId(appNotification.getTypeId())
                                .durationInMin((int) appNotification.getDurationInMin())
                                .updatedTime(DateTimeUtil.getTimeInGMT())
                                .accountId(bean.getAccount().getId())
                                .applicationId(app)
                                .build()))
                .collect(Collectors.toList());

        NotificationSettingsDataService.updateApplicationNotificationSettings(settings);

        updateIntoRedis(bean.getAccountIdentifier(), pojoObject);

        return "Application notification settings updated successfully";
    }

    private void updateIntoRedis(String accountIdentifier, ApplicationNotificationSettingsPojo pojoObject) {

        ApplicationRepo applicationRepo = new ApplicationRepo();
        List<EscalationSettings> settings = pojoObject.getNotificationSettingsPojos().parallelStream()
                .map( notificationSettingsPojo -> EscalationSettings.builder()
                        .id(notificationSettingsPojo.getTypeId())
                        .name(notificationSettingsPojo.getTypeName())
                        .noOfMinutes((int) notificationSettingsPojo.getDurationInMin())
                        .build()).collect(Collectors.toList());

        List<Integer> applicationIds = pojoObject.getApplicationIds();

        List<String> appIdentifiers = applicationRepo.getApplicationsForAccount(accountIdentifier).parallelStream()
                .filter(app -> applicationIds.contains(app.getId()))
                .map(BasicEntity::getIdentifier)
                .collect(Collectors.toList());


        appIdentifiers.forEach(appIdentifier -> applicationRepo.updateApplicationEscalationSettings(accountIdentifier, appIdentifier, settings));
    }
}
