package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.ResponseObject;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.MasterDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.PhysicalAgentBean;
import com.appnomic.appsone.controlcenter.dao.redis.MasterDataRepo;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.service.ComponentAgentService;
import com.appnomic.appsone.controlcenter.service.TagsService;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.ViewTypes;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
public class AddAgent {

    public void addAgentDetails(String jsonString, String userId) throws Exception {
        ResponseObject<String> responseObject = new ResponseObject<>();

        List<Agent> agentList;
        try {
            agentList = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(jsonString, new TypeReference<List<Agent>>() {
            });
        } catch (IOException e) {
            log.error(Constants.JSON_PARSE_ERROR + " : {}", e.getMessage());
            throw new RequestException("Error in parsing JSON request");
        }

        for (Agent agent : agentList) {
            agent.validate();
        }

        List<ViewTypes> allViewType = new MasterDataRepo().getTypes();

        ViewTypes serviceType = allViewType
                .stream()
                .filter(a -> a.getTypeName().equalsIgnoreCase(Constants.CONTROLLER_TYPE_NAME_DEFAULT))
                .filter(a -> a.getSubTypeName().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE))
                .findFirst().orElse(null);
        if (serviceType == null) {
            log.error("Type details unavailable for type [{}] and subtype [{}]", Constants.CONTROLLER_TYPE_NAME_DEFAULT, Constants.SERVICES_CONTROLLER_TYPE);
            throw new RequestException("Type details unavailable for type " + Constants.CONTROLLER_TYPE_NAME_DEFAULT + " and subtype " + Constants.SERVICES_CONTROLLER_TYPE);
        }

        int typeId = serviceType.getSubTypeId();

        //accountId, <ServiceIdentifier, service IdPojo>
        Map<Integer, Map<String, IdPojo>> controllerMap = MasterDataService.getAllControllerList().parallelStream()
                .filter(c -> c.getControllerTypeId() == typeId)
                .collect(Collectors.groupingBy(Controller::getAccountId,
                        Collectors.toMap(Controller::getIdentifier, c -> IdPojo.builder()
                                .id(Integer.parseInt(c.getAppId()))
                                .identifier(c.getIdentifier())
                                .name(c.getName())
                                .build())));

        List<ViewTypes> agentViewTypes = allViewType.stream().filter(v -> v.getTypeName().equalsIgnoreCase(Constants.AGENT_TYPE)).collect(Collectors.toList());

        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        dbi.inTransaction((conn, status) -> {
            //This set of physical agent identifier are needed to ensure that
            // duplicate physical agent identifiers are not inserted into physical_agent
            // table when multiple agents are added in one call.
            Map<String, Integer> physicalAgentIdentifiers = new HashMap<>();

            for (Agent agent : agentList) {
                addAgent(agent, userId, conn, physicalAgentIdentifiers, controllerMap, agentViewTypes);
            }
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());

            return responseObject;
        });
    }

    private void addAgent(Agent agent, String userId, Handle handle, Map<String, Integer> physicalAgentIdentifiers,
                          Map<Integer, Map<String, IdPojo>> controllers, List<ViewTypes> viewTypes)
            throws ControlCenterException, ParseException {
        AgentBean agentBean = isAgentAlreadyAvailable(agent);

        String uniqueToken = UUID.randomUUID().toString();

        if (agent.getUniqueToken() == null) {
            agent.setUniqueToken(uniqueToken);
        }
        if (agent.getPhysicalAgentIdentifier() == null) {
            agent.setPhysicalAgentIdentifier(uniqueToken + "_Physical");
        }

        int agentId;
        int mappingId = -1;
        if (agentBean == null) {
            PhysicalAgentBean physicalAgentBean = AgentDataService.getPhysicalAgentDetailsUsingIdentifier(agent.getPhysicalAgentIdentifier());

            if (null == physicalAgentBean && !physicalAgentIdentifiers.containsKey(agent.getPhysicalAgentIdentifier())) {
                log.info("physicalAgentIdentifier [{}] provided in the addAgent request is unavailable " +
                        "in physical_agent table. Hence a new entry will be added in physical_agent table.", agent.getPhysicalAgentIdentifier());
                physicalAgentIdentifiers.putIfAbsent(agent.getPhysicalAgentIdentifier(), addPhysicalAgent(agent, userId, handle).getId());
            }
            int physicalIdentifierId = (null == physicalAgentBean) ? physicalAgentIdentifiers.get(agent.getPhysicalAgentIdentifier()) : physicalAgentBean.getId();

            agentBean = addAgent(agent, userId, physicalIdentifierId, viewTypes, handle);
            agentId = agentBean.getId();

            List<AgentAccountMappings> accountMappings = agent.getAccountMappings();
            if (accountMappings != null && !accountMappings.isEmpty()) {
                mappingId = addAccountMappingDetails(agentId, accountMappings, controllers, userId, handle);
            }
        } else if (agentBean.getStatus() == 0 && agentBean.getUniqueToken().equals(agent.getUniqueToken())) {
            log.info("uniqueToken- {} is already available with status 0, so reactivating..", agent.getUniqueToken());
            reActivateAgent(agent, agentBean, userId, handle);
            agentId = agentBean.getId();
            mappingId = AgentDataService.getAgentAccountMappingByAgentId(agentBean.getUniqueToken()).get(0).getId();
        } else {
            log.info("uniqueToken- {}  or agent name - {} is already available.", agent.getUniqueToken(), agent.getName());
            agentId = agentBean.getId();
            mappingId = AgentDataService.getAgentAccountMappingByAgentId(agentBean.getUniqueToken()).get(0).getId();
        }
        if (agent.getCompInstIdentifiers() != null && !agent.getCompInstIdentifiers().isEmpty()) {
            addAgentCompInstMappingDetails(agent, agentBean, agentId, mappingId, userId, handle);
        }
    }

    private AgentBean isAgentAlreadyAvailable(Agent agent) {
        AgentBean agentBean;
        if (agent.getUniqueToken() != null) {
            agentBean = MasterCache.getAgentBean(agent.getUniqueToken());
            if (agentBean != null) {
                return agentBean;
            }
        }
        agentBean = MasterCache.getAgentBeanForName(agent.getName());

        return agentBean;
    }

    private PhysicalAgentBean addPhysicalAgent(Agent agent, String userId, Handle handle) throws ParseException, ControlCenterException {

        Timestamp timestamp = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
        PhysicalAgentBean physicalAgentBean = PhysicalAgentBean.builder()
                .identifier(agent.getPhysicalAgentIdentifier())
                .userDetailsId(userId)
                .createdTime(timestamp)
                .updatedTime(timestamp)
                .lastCommandExecuted(1)
                .lastJobId(null)
                .lastStatusId(null)
                .build();

        int physicalAgentId = AgentDataService.addPhysicalAgentDetails(physicalAgentBean, handle);

        if (physicalAgentId < 0) {
            log.error("Physical agent insertion failed.");
            throw new ControlCenterException("Physical agent insertion failed.");
        }

        physicalAgentBean.setId(physicalAgentId);
        return physicalAgentBean;
    }

    private AgentBean addAgent(Agent agent, String userId, int physicalIdentifierId, List<ViewTypes> viewTypes, Handle handle) throws ParseException, ControlCenterException {
        ViewTypes subTypeBean = viewTypes.parallelStream().filter(v -> v.getSubTypeName().equalsIgnoreCase(agent.getSubType())).findFirst().orElse(null);
        if(subTypeBean == null) {
            log.error("Type details unavailable for Agent type and [{}] subtype", agent.getSubType());
            throw new ControlCenterException("Type details unavailable for type Agent and subtype " + agent.getSubType());
        }

        String agentIdentifier = agent.getUniqueToken();

        log.debug("agent identifier [{}] will be created.", agentIdentifier);

        Timestamp timestamp = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());

        AgentBean agentBean = new AgentBean();
        agentBean.setUniqueToken(agentIdentifier);
        agentBean.setPhysicalAgentId(physicalIdentifierId);
        agentBean.setName(agent.getName());
        agentBean.setAgentTypeId(subTypeBean.getSubTypeId());
        agentBean.setCreatedTime(timestamp);
        agentBean.setUpdatedTime(timestamp);
        agentBean.setUserDetailsId(userId);
        agentBean.setStatus(agent.getStatus());
        agentBean.setHostAddress(agent.getHostAddress());
        agentBean.setDescription(agent.getDescription());
        agentBean.setMode(agent.getMode());

        int agentId = AgentDataService.addAgent(agentBean, handle);

        if (agentId == 0) {
            throw new ControlCenterException("Unable to add agent data into DB. for agent name-" + agent.getName() + " and agent identifier-" + agentIdentifier);
        }
        agentBean.setId(agentId);
        return agentBean;
    }

    private int addAccountMappingDetails(int agentId, List<AgentAccountMappings> accountMappings, Map<Integer, Map<String, IdPojo>> controllers, String userId, Handle handle)
            throws ControlCenterException, ParseException {

        int mappingId = -1;

        for (AgentAccountMappings accountMapping : accountMappings) {
            int accountId = ValidationUtils.validAndGetIdentifier(accountMapping.getAccountIdentifier());
            if (accountId != -1) mappingId = addAgentAccountMapping(agentId, accountId, userId, handle);
            else throw new ControlCenterException("Error while adding agent account mapping.");

            if (accountMapping.getTags() == null || accountMapping.getTags().isEmpty()) continue;

            for (Tags t : accountMapping.getTags()) {
                addAccountTag(t, agentId, accountId, userId, controllers, handle);
            }
        }
        return mappingId;
    }

    private void addAccountTag(Tags t, int agentId, int accountId, String userId, Map<Integer, Map<String,
            IdPojo>> controllers, Handle handle) throws ControlCenterException {

        int tagId = MasterCache.getTagDetails(Constants.CONTROLLER_TAG).getId();
        IdPojo c;
        if (controllers.containsKey(accountId) && controllers.get(accountId).containsKey(t.getIdentifier()))
            c = controllers.get(accountId).get(t.getIdentifier());
        else {
            c = AddServiceBL.addService(ServiceBean.builder()
                    .accountId(accountId)
                    .userId(userId)
                    .identifier(t.getIdentifier())
                    .name(t.getIdentifier())
                    .build(), handle);

            if (!controllers.containsKey(accountId))
                controllers.put(accountId, new HashMap<>());
            controllers.get(accountId).put(t.getIdentifier(), c);
        }
        TagMappingBL.addTagMapping(tagId, agentId, Constants.AGENT_TABLE, String.valueOf(c.getId()),
                c.getIdentifier(), userId, accountId, handle);
    }

    private int addAgentAccountMapping(int agentId, int accountId, String userDetailsId, Handle handle) throws ParseException {
        AgentAccountMappingBean agentAccountMappingBean = new AgentAccountMappingBean();
        agentAccountMappingBean.setAgentId(agentId);
        agentAccountMappingBean.setAccountId(accountId);
        agentAccountMappingBean.setUserDetailsId(userDetailsId);
        agentAccountMappingBean.setCreatedTime(new java.sql.Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime()));
        int id = new CompInstanceDataService().addAgentAccountMapping(agentAccountMappingBean, handle);
        if (id == -1) {
            log.error("Error while adding agent account mapping.");
            return -1;
        }
        log.info("Agent is mapped for a given account and agent id {},{}-", accountId, agentId);
        return id;
    }

    private void addAgentCompInstMappingDetails(Agent agent, AgentBean agentBean, int agentId, int mappingId,
                                                String userId, Handle handle) throws ParseException, ControlCenterException {

        List<AgentCompInstMappingDetails> compInstIds = agent.getCompInstIdentifiers();
        for (AgentCompInstMappingDetails compInstId : compInstIds) {
            if (compInstId.getAccountId() == null || compInstId.getAccountId().isEmpty() ||
                    ValidationUtils.validAndGetIdentifier(compInstId.getAccountId()) < 1) {
                continue;
            }
            int accountId = ValidationUtils.validAndGetIdentifier(compInstId.getAccountId());

            if (mappingId == -1) {
                addAgentAccountMapping(agentId, accountId, userId, handle);
            }

            List<String> idList = compInstId.getCompInstIds();
            addCompInstMapping(idList, agentId, accountId, handle);
            List<Tags> tagsList = compInstId.getTags();
            if (tagsList != null && !tagsList.isEmpty()) {
                new TagsService().addTags(tagsList, accountId, agentId, "agent", userId, handle);
            }

        }

        ComponentAgent agentMappingDetails = agent.getAgentMappingDetails();
        if (agentMappingDetails != null) {
            agentMappingDetails.setAgentIdentifier(agentBean.getUniqueToken());
            ComponentAgentService componentAgentService = new ComponentAgentService();
            componentAgentService.addComponentAgent(agentMappingDetails, userId, agentBean, handle);
        }
    }

    private void addCompInstMapping(List<String> idList, int agentId, int accountId, Handle handle) throws ControlCenterException {
        if (idList != null && !idList.isEmpty()) {
            for (String compId : idList) {
                ComponentInstanceBean componentInstanceBean = MasterCache.getCompInstUsingAccountIdAndInstName(compId, accountId);
                if (componentInstanceBean == null) {
                    log.warn("component instance name - {} is not found in data.", compId);
                    continue;
                }
                addAgentCompInstMapping(componentInstanceBean.getId(), agentId, handle);
            }
        }
    }

    public void addAgentCompInstMapping(int compInstId, int agentId, Handle handle) throws ControlCenterException {

        AgentCompInstMappingBean agentCompInstMappingBean = new AgentCompInstMappingBean();
        agentCompInstMappingBean.setCompInstanceId(compInstId);
        agentCompInstMappingBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        agentCompInstMappingBean.setAgentId(agentId);
        int id = new CompInstanceDataService().addAgentCompInstMapping(agentCompInstMappingBean, handle);
        if (id == -1) {
            log.error("Error while adding component instance agent mapping.");
            throw new ControlCenterException("Exception occurred when adding component instance agent mapping");
        }
        log.info("Component instance and agent mapping are done successfully for compInstId-{} and agent id- {}", compInstId, agentId);
    }

    private void reActivateAgent(Agent agent, AgentBean agentBean, String userId, Handle handle) throws ParseException, ControlCenterException {

        Timestamp timestamp = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());

        agentBean.setName(agent.getName());
        agentBean.setUpdatedTime(timestamp);
        agentBean.setUserDetailsId(userId);
        agentBean.setStatus(1);
        agentBean.setHostAddress(agent.getHostAddress());
        agentBean.setDescription(agent.getDescription());

        int res = AgentDataService.updateAgent(agentBean, handle);

        if (res == -1) {
            String err = "Unable to update agent data for agent identifier-" + agentBean.getUniqueToken();
            log.error(err);
            throw new ControlCenterException(err);
        }
    }

}
