package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.UserAccessDataService;
import com.appnomic.appsone.controlcenter.dao.redis.AccountRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ApplicationRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.service.KeyCloakAuthService;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.model.JWTData;
import com.google.gson.reflect.TypeToken;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Application;
import com.heal.configuration.pojos.EscalationSettings;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Type;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class GetApplicationNotificationSettingsBL implements BusinessLogic<String, UtilityBean<String>, List<NotificationSettingsBean>>{
    private static final int MIN_OPEN_FOR_LONG = ConfProperties.getInt(Constants.MIN_OPEN_FOR_LONG, Constants.MIN_OPEN_FOR_LONG_DEFAULT);
    private static final int MIN_OPEN_FOR_TOO_LONG = ConfProperties.getInt(Constants.MIN_OPEN_FOR_TOO_LONG, Constants.MIN_OPEN_FOR_TOO_LONG_DEFAULT);
    private static final int MAX_OPEN_FOR_LONG = ConfProperties.getInt(Constants.MAX_OPEN_FOR_LONG, Constants.MAX_OPEN_FOR_LONG_DEFAULT);
    private static final int MAX_OPEN_FOR_TOO_LONG = ConfProperties.getInt(Constants.MAX_OPEN_FOR_TOO_LONG, Constants.MAX_OPEN_FOR_TOO_LONG_DEFAULT);

    @Override
    public UtilityBean<String> clientValidation(RequestObject requestObject) throws ClientException {
        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String accountIdentifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(accountIdentifier)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }
        String applicationId = requestObject.getParams().get(Constants.APPLICATION_ID);
        if (StringUtils.isEmpty(applicationId)) {
            log.error(UIMessages.APPLICATION_ID_EMPTY);
            throw new ClientException(UIMessages.INSTANCE_EMPTY);
        }

        if (!StringUtils.isNumber(applicationId)) {
            log.error(UIMessages.APPLICATION_ID_IS_NOT_NUMBER);
            throw new ClientException(UIMessages.APPLICATION_ID_IS_NOT_NUMBER);
        }
        return UtilityBean.<String>builder()
                .pojoObject(applicationId)
                .authToken(authKey)
                .accountIdentifier(accountIdentifier)
                .build();
    }

    @Override
    public UtilityBean<String> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        JWTData jwtData;

        try {
            jwtData = KeyCloakAuthService.extractUserDetails(utilityBean.getAuthToken());
        } catch (ControlCenterException e) {
            log.error("Exception encountered while fetching the userIdentifier. Details: {}", e.getMessage());
            throw new ServerException("Error while extracting user identifier");
        }

        UserAccessBean accessDetails;
        try {
            accessDetails = UserAccessDataService.getUserAccessDetails(jwtData.getSub().trim());
        } catch (ControlCenterException e) {
            log.error(e.getMessage());
            throw new ServerException(e.getMessage());
        }

        Type userBeanType = new TypeToken<AccessDetailsBean>() {
        }.getType();

        AccessDetailsBean accessDetailsBean = CommonUtils.jsonToObject(accessDetails.getAccessDetailsJson(), userBeanType);
        if (accessDetailsBean == null) {
            log.error(UIMessages.INVALID_USER_ACCESS_DETAILS);
            throw new ServerException(UIMessages.INVALID_USER_ACCESS_DETAILS);
        }
        AccountRepo accountRepo = new AccountRepo();

        Account account = accountRepo.getAccountWithAccountIdentifier(utilityBean.getAccountIdentifier());

        if (account == null) {
            log.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }

        ApplicationRepo applicationRepo = new ApplicationRepo();
        String appIdString = utilityBean.getPojoObject();
        int appId = Integer.parseInt(appIdString);
        Application application = applicationRepo.getApplicationsForAccount(utilityBean.getAccountIdentifier())
                .parallelStream()
                .filter(app -> app.getId() == appId)
                .findFirst().orElse(null);

        if (application == null) {
            log.error(MessageFormat.format(UIMessages.ERROR_INVALID_APPLICATION_ID, appId));
            throw new ServerException(MessageFormat.format(UIMessages.ERROR_INVALID_APPLICATION_ID, appId));
        }

        utilityBean.setApplicationId(application.getIdentifier());

        return utilityBean;
    }

    @Override
    public List<NotificationSettingsBean> process(UtilityBean<String> bean) throws DataProcessingException {

        String applicationIdentifier = bean.getApplicationId();
        String accountIdentifier = bean.getAccountIdentifier();

        ApplicationRepo applicationRepo = new ApplicationRepo();
        List<EscalationSettings> applicationEscalationSettings = applicationRepo
                .getApplicationEscalationSettings(accountIdentifier, applicationIdentifier);


        return applicationEscalationSettings.parallelStream().map(setting -> {
            Map<String, Integer> properties = new HashMap<>();
            if (Constants.LONG.equalsIgnoreCase(setting.getName())) {
                properties.put("min", MIN_OPEN_FOR_LONG);
                properties.put("max", MAX_OPEN_FOR_LONG);
            } else if (Constants.TOO_LONG.equalsIgnoreCase(setting.getName())) {
                properties.put("min", MIN_OPEN_FOR_TOO_LONG);
                properties.put("max", MAX_OPEN_FOR_TOO_LONG);
            }

            return NotificationSettingsBean.builder()
                    .typeId(setting.getId())
                    .durationInMin(setting.getNoOfMinutes())
                    .typeName(setting.getName())
                    .properties(properties)
                    .build();
        }).collect(Collectors.toList());
    }
}