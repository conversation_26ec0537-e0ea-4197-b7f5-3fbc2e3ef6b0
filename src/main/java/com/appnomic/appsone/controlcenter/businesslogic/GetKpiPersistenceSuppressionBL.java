package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.CompInstanceKpiGroupDetailsBean;
import com.appnomic.appsone.controlcenter.beans.ComponentInstanceBean;
import com.appnomic.appsone.controlcenter.beans.InstancesKpisBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.KPIDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ThresholdDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.*;
import com.appnomic.appsone.controlcenter.dao.opensearch.KPIGroupAttributesRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.KpiAttrConfigInfo;
import com.appnomic.appsone.controlcenter.pojo.KpiAttributePersistSuppressInfo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class GetKpiPersistenceSuppressionBL implements BusinessLogic<InstancesKpisBean, InstancesKpisBean, KpiAttrConfigInfo> {
    
    private int accountId;
    private String accountIdentifier;
    private final Map<Integer, String> compInstanceIdVsIdentifier = new HashMap<>();
    private static final KPIDataService KPI_DATA_SERVICE = new KPIDataService();
    private int COMP_INST_RANGE = ConfProperties.getInt(Constants.COMP_INST_RANGE, Constants.COMP_INST_RANGE_DEFAULT_VALUE);

    @Override
    public UtilityBean<InstancesKpisBean> clientValidation(RequestObject requestObject) throws ClientException {
        log.debug("GetKpiPersistenceSuppressionBL:clientValidation: BEGIN");

        CommonUtils.basicRequestValidation(requestObject);

        List<Integer> instanceIds = new ArrayList<>();
        int kpiId = 0;

        if (requestObject.getQueryParams() != null && !requestObject.getQueryParams().isEmpty()) {
            if (requestObject.getQueryParams().containsKey("instanceIds")) {
                instanceIds = Arrays.stream(requestObject.getQueryParams().get("instanceIds")[0].split(","))
                        .map(c -> Integer.parseInt(c.trim())).collect(Collectors.toList());
            }
            if (requestObject.getQueryParams().containsKey("kpiId")) {
                kpiId = Integer.parseInt(requestObject.getQueryParams().get("kpiId")[0]);
            }
        }

        if(instanceIds.isEmpty() || instanceIds.contains(0)) {
            log.error("Invalid instance ID. All the instance IDs should be a non-zero integer");
            throw new ClientException("Invalid instance ID. All the instance IDs should be a non-zero integer");
        }

        if(kpiId == 0) {
            log.error("Invalid KPI ID. It should be a non-zero integer");
            throw new ClientException("Invalid KPI ID. It should be a non-zero integer");
        }

        InstancesKpisBean bean = InstancesKpisBean.builder()
                .instanceIds(instanceIds)
                .kpiId(kpiId)
                .build();

        log.debug("GetKpiPersistenceSuppressionBL:clientValidation: END with instancesKpiBean: {}", bean);

        return UtilityBean.<InstancesKpisBean>builder()
                .accountIdentifier(requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER))
                .authToken(requestObject.getHeaders().get(Constants.AUTHORIZATION))
                .pojoObject(bean)
                .build();
    }

    @Override
    public InstancesKpisBean serverValidation(UtilityBean<InstancesKpisBean> utilityBean) throws ServerException {
        log.debug("GetKpiPersistenceSuppressionBL:serverValidation: BEGIN");
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);
        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        InstancesKpisBean instancesKpisBean = utilityBean.getPojoObject();
        List<Integer> instanceIds = instancesKpisBean.getInstanceIds();
        int kpiId = instancesKpisBean.getKpiId();
        accountId = account.getId();

        KpiBean kpiBean = KPI_DATA_SERVICE.fetchKpiUsingKpiId(kpiId, accountId, null);
        if (null == kpiBean) {
            log.error("KPI with ID [{}] is unavailable", kpiId);
            throw new ServerException(String.format("KPI with ID [%d] is unavailable", kpiId));
        }

        int discoveryFlag;
        try {
            discoveryFlag = new KPIDataService().getGroupKpiDiscovery(kpiBean.getGroupKpiId(), null);
        } catch (ControlCenterException e) {
            throw new ServerException(e.getMessage());
        }

        instancesKpisBean.setGroupKpiId(kpiBean.getGroupKpiId());
        instancesKpisBean.setDiscovery(discoveryFlag);

        for (int instanceId : instanceIds) {
            ComponentInstanceBean compInst = new CompInstanceDataService().getComponentInstanceByIdAndAccount(instanceId, accountId);

            if (compInst == null) {
                log.error("Component instance with ID [{}] with account ID [{}] is unavailable", instanceId, accountId);
                throw new ServerException("Invalid instanceId provided");
            }

            compInstanceIdVsIdentifier.put(instanceId, compInst.getIdentifier());
        }

        log.debug("GetKpiPersistenceSuppressionBL:serverValidation: END with instancesKpiBean: {}", instancesKpisBean);

        return instancesKpisBean;
    }

    @Override
    public KpiAttrConfigInfo process(InstancesKpisBean instancesKpisBean) throws DataProcessingException {
        log.debug("GetKpiPersistenceSuppressionBL:process: BEGIN");
        Long range = COMP_INST_RANGE * 60 * 1000L;
        List<CompInstanceKpiGroupDetailsBean> mappedKpis;
        Set<String> attributes = new HashSet<>();
        List<InstKpiAttrPersistenceSuppressionBean> configDetailsList = new ArrayList<>();
        Map<KpiIdVsAttribute, List<InstKpiAttrPersistenceSuppressionBean>> configBeans = new HashMap<>();
        Set<KpiIdVsAttribute> instanceConfigNotPresent = new HashSet<>();

        List<Integer> instanceIds = instancesKpisBean.getInstanceIds();
        int kpiId = instancesKpisBean.getKpiId();
        int groupKpiId = instancesKpisBean.getGroupKpiId();

        for (int instanceId : instanceIds) {
            try {
                if (groupKpiId == 0) {
                    mappedKpis = KPI_DATA_SERVICE.getNonGroupKpiListForCompInstance(instanceId, kpiId);
                } else {
                    mappedKpis = KPI_DATA_SERVICE.getGroupKpiListForCompInstance(instanceId, groupKpiId);

                    if(1 == instancesKpisBean.getDiscovery()) {
                        try {
                            attributes.addAll(new KPIGroupAttributesRepo()
                                    .getGroupKpiAttributesWithDataCollected(accountIdentifier, compInstanceIdVsIdentifier.get(instanceId), range, new HashSet<Integer>() {{
                                        add(kpiId);
                                    }}));
                        }catch (ControlCenterException cce){
                            log.error("No kpi group attributes found for the instances - {} - ",compInstanceIdVsIdentifier.get(instanceId), cce);
                        }
                    }
                }

                List<InstKpiAttrPersistenceSuppressionBean> list = KPI_DATA_SERVICE.fetchCompInstanceKpiPerSupValuesForCompInstanceKpi(instanceId, kpiId, accountId);
                configDetailsList.addAll(list);

                mappedKpis.forEach(c -> {
                    attributes.add(c.getAttributeValue());
                    boolean attrNotPresent = list.parallelStream().noneMatch(i -> c.getAttributeValue().equals(i.getAttributeValue()));
                    if(attrNotPresent) {
                        instanceConfigNotPresent.add(new KpiIdVsAttribute(kpiId, c.getAttributeValue()));
                    }
                });

                List<InstanceKpiAttributeThresholdBean> thresholdBeanList = ThresholdDataService.getCompInstanceThresholdDetail(accountId, instanceId, kpiId);
                attributes.addAll(thresholdBeanList.parallelStream().map(InstanceKpiAttributeThresholdBean::getAttributeValue).collect(Collectors.toList()));

                configBeans.putAll(configDetailsList.parallelStream()
                        .collect(Collectors.groupingBy(c -> new KpiIdVsAttribute(c.getKpiId(), c.getAttributeValue()))));

            } catch (ControlCenterException e) {
                throw new DataProcessingException(e.getMessage());
            }
        }

        Set<KpiAttributePersistSuppressInfo> output = configDetailsList.parallelStream().map(c -> {
            attributes.add(c.getAttributeValue());

            if (instanceIds.size() == 1) {
                KpiAttributePersistSuppressInfo.Persistence persistence = KpiAttributePersistSuppressInfo.Persistence.builder()
                        .value(c.getPersistence()).common(1).build();
                KpiAttributePersistSuppressInfo.Suppression suppression = KpiAttributePersistSuppressInfo.Suppression.builder()
                        .value(c.getSuppression()).common(1).build();

                return KpiAttributePersistSuppressInfo.builder()
                        .kpiId(c.getKpiId())
                        .groupKpiId(c.getKpiGroupId())
                        .attributeValue(c.getAttributeValue())
                        .persistence(persistence)
                        .suppression(suppression)
                        .build();
            }

            boolean commonConfig = !instanceConfigNotPresent.contains(new KpiIdVsAttribute(c.getKpiId(), c.getAttributeValue()));

            List<InstKpiAttrPersistenceSuppressionBean> beans = configBeans.get(new KpiIdVsAttribute(c.getKpiId(), c.getAttributeValue()));
            InstKpiAttrPersistenceSuppressionBean firstBean = beans.get(0);

            KpiAttributePersistSuppressInfo.Persistence persistence;
            KpiAttributePersistSuppressInfo.Suppression suppression;

            boolean samePersistence = beans.stream().allMatch(x -> x.getPersistence().equals(firstBean.getPersistence()));
            if (samePersistence && commonConfig) {
                persistence = KpiAttributePersistSuppressInfo.Persistence.builder()
                        .value(firstBean.getPersistence()).common(1).build();
            } else {
                persistence = KpiAttributePersistSuppressInfo.Persistence.builder()
                        .value(0).common(0).build();
            }

            boolean sameSuppression = beans.stream().allMatch(x -> x.getSuppression().equals(firstBean.getSuppression()));
            if (sameSuppression && commonConfig) {
                suppression = KpiAttributePersistSuppressInfo.Suppression.builder()
                        .value(firstBean.getSuppression()).common(1).build();
            } else {
                suppression = KpiAttributePersistSuppressInfo.Suppression.builder()
                        .value(0).common(0).build();
            }

            return KpiAttributePersistSuppressInfo.builder()
                    .kpiId(c.getKpiId())
                    .groupKpiId(c.getKpiGroupId())
                    .attributeValue(c.getAttributeValue())
                    .persistence(persistence)
                    .suppression(suppression)
                    .build();

        }).filter(Objects::nonNull)
                .collect(Collectors.toSet());

        KpiAttrConfigInfo kpiAttrConfigInfo = KpiAttrConfigInfo.builder()
                .attributes(attributes)
                .persistenceSuppressionConfig(output).build();

        log.debug("GetKpiPersistenceSuppressionBL:process: END with attributes list [{}] and persistence-suppression configuration list size [{}]", attributes.size(), output.size());

        return kpiAttrConfigInfo;
    }
}
