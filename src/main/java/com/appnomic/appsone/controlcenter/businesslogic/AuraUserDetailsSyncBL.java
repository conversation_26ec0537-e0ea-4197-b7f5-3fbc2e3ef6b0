package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.AccessDetailsBean;
import com.appnomic.appsone.controlcenter.beans.TagDetailsBean;
import com.appnomic.appsone.controlcenter.beans.UserBean;
import com.appnomic.appsone.controlcenter.beans.UserInfoBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserProfileBean;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

public class AuraUserDetailsSyncBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(AuraUserDetailsSyncBL.class);
    private static final UserDataService userDataService = new UserDataService();
    private static final ObjectMapper OBJECT_MAPPER = CommonUtils.getObjectMapperWithHtmlEncoder();
    private static UsersBL usersBL = new UsersBL();

    public void syncUserDetails(HealUserEntitlement healUserEntitlement) throws ControlCenterException {
        String auraAccountName;
        try {
            auraAccountName = ConfProperties.getString(Constants.AURA_ACCOUNT_ID_KEY);
        } catch (Exception e) {
            LOGGER.error("Unable to load account id for AURA setup from conf file.");
            throw new ControlCenterException("Unable to load account id from conf file.");
        }

        AccountBean accountBean = AccountDataService.getAccountDetailsByName(auraAccountName, null);
        if (accountBean == null) {
            LOGGER.error("Invalid account id: {} configured in AURA account id.", auraAccountName);
            throw new ControlCenterException("Invalid account received in AURA configuration.");
        }

        String setup = userDataService.getSetup();
        if (!setup.equalsIgnoreCase(Constants.SETUP_AD_INTEGRATION)) {
            LOGGER.error("The setup is not AD integrated, AURA sync is not supported on non AD setup.");
            throw new ControlCenterException("Unsupported setup.");
        }

        String userName = healUserEntitlement.getUserName();

        List<String> applicationList = new ArrayList<>();
        List<Controller> apps = getApplicationDetails(accountBean.getId(), healUserEntitlement.getApplicationAccessList());
        if (apps == null || apps.isEmpty()) {
            LOGGER.debug("Aura ICTO numbers are not available in tag mapping. User:{}, ICTOs:{}", userName, healUserEntitlement.getApplicationAccessList());
        } else {
            applicationList.addAll(apps.parallelStream().map(Controller::getAppId).collect(Collectors.toList()));
        }

        UserInfo userInfo;
        try {
            userInfo = getUserInfo(userName, setup);
        } catch (Exception e) {
            LOGGER.error("Error occurred while fetching user details for user id: {}.", userName);
            throw new ControlCenterException(e.getMessage());
        }

        boolean isNewUser = false;

        if (userInfo == null) {
            isNewUser = true;

            userInfo = new UserInfo();
            userInfo.setEditInKeycloak(false);

            UserBean userBean = UserAccessDataService.getUserDetailsFromUsername(healUserEntitlement.getUserName());
            if (userBean == null) {
                throw new ControlCenterException("UserBean is not available for userName: {}", userInfo.getUserName());
            }

            userInfo.setUserDetailsId(userBean.getId());
            userInfo.setId(userBean.getId());
            userInfo.setEmailId(userBean.getEmail());
            userInfo.setFirstName(userBean.getFirstName());
            userInfo.setLastName(userBean.getLastName());
            userInfo.setUserName(healUserEntitlement.getUserName());
            userInfo.setStatus(1);
        } else {
            UserInfo.AccessDetails accessDetails = userInfo.getAccessDetails().parallelStream()
                    .filter(a -> a.getAccountId().equals(accountBean.getId())).findAny()
                    .orElse(null);

            if(accessDetails == null) {
                LOGGER.error("Access details unavailable for user [{}] mapped to account [{}]", userName, accountBean.getName());
                throw new ControlCenterException(String.format("Access details unavailable for user [%s] mapped to account [%s]", userName, accountBean.getName()));
            }

            applicationList.addAll(accessDetails.getApplications().get(0).getIds());
        }

        LOGGER.debug("Details fetched for username: {} -> {}.", userName, userInfo);

        //Updates the role and profile information.
        UserInfo roleInfoUpdated = updateRoleAndProfileMapping(userInfo, healUserEntitlement);

        List<UserInfo.AccessDetails> updatedAccessDetails = generateAccessDetails(accountBean.getId(), roleInfoUpdated, applicationList);
        userInfo.setAccessDetails(updatedAccessDetails);
        userInfo.setUserDetailsId(userInfo.getId());

        AccessDetailsBean accessDetailsBean;
        try {
            accessDetailsBean = usersBL.validateAndGetAccessDetailsUser(updatedAccessDetails);
        } catch (RequestException e) {
            LOGGER.error("Error occurred while validating access details.", e);
            throw new ControlCenterException(e.getSimpleMessage());
        }

        UserInfoBean userInfoBean = usersBL.getUserInfoBean(roleInfoUpdated, accessDetailsBean);

        try {
            if (isNewUser) {
                AddUserBL addUserBL = new AddUserBL();
                addUserBL.process(userInfoBean);
            } else {
                EditUserBL editUserBL = new EditUserBL();
                editUserBL.process(userInfoBean);
            }
        } catch (DataProcessingException e) {
            LOGGER.error("Error occurred while syncing user: {},  into database.", userName, e);
            throw new ControlCenterException("Unable to sync user to DB");
        }
        LOGGER.info("Successfully synced user id: {} to DB.", userInfoBean.getId());
    }

    private UserInfo updateRoleAndProfileMapping(UserInfo userInfo, HealUserEntitlement userEntitlement) throws ControlCenterException {
        LOGGER.trace("{} updateRoleAndProfileMapping. PARAMS -> {}, {}.", Constants.INVOKED_METHOD, userInfo, userEntitlement);

        String auraProfile = userEntitlement.getRole();
        if (auraProfile == null) {
            LOGGER.error("Invalid aura role received: {}.", auraProfile);
            throw new ControlCenterException("Invalid role received from AURA");
        }

        List<UserProfileBean> allProfiles = UserAccessDataService.getUserProfiles();
        UserProfileBean requiredProfile = allProfiles.stream()
                .filter(profile -> (profile.getName().equalsIgnoreCase(auraProfile)))
                .findAny()
                .orElse(null);
        if (requiredProfile == null) {
            LOGGER.error("Invalid profile name received as part of role in for from AURA: {}.", auraProfile);
            throw new ControlCenterException("Invalid profile info received from AURA.");
        }

        List<IdPojo> allRolesList = UserAccessDataService.getRoles();
        IdPojo requiredRole = allRolesList.stream()
                .filter(role -> role.getName().equalsIgnoreCase(requiredProfile.getRoleName()))
                .findAny()
                .orElse(null);
        if (requiredRole == null) {
            LOGGER.error("Invalid role name received from AURA: {}.", requiredProfile.getRoleName());
            throw new ControlCenterException("Invalid role name received from AURA");
        }

        LOGGER.debug("Successfully fetched role: {} and profile: {} info.", requiredRole, requiredProfile);

        if (userInfo.getRoleId() == requiredRole.getId() && userInfo.getProfileId() == requiredProfile.getId()) {
            LOGGER.debug("Role and profile for user: {} are same hence no change required.", userInfo.getId());
        } else {
            LOGGER.debug("Role/Profile data requires change as they are different, current role: {}, profile:{}",
                    userInfo.getRoleId(), userInfo.getProfileId());

            userInfo.setRoleId(requiredRole.getId());
            userInfo.setProfileId(requiredProfile.getId());
            userInfo.setProfileChange(1);
        }

        LOGGER.debug("Updated role id: {}, profile id: {}.", userInfo.getRoleId(), userInfo.getProfileId());

        return userInfo;
    }

    private List<UserInfo.AccessDetails> generateAccessDetails(int accountId, UserInfo userDetails, List<String> appList) throws ControlCenterException {
        LOGGER.trace("{} generateAccessDetails(), PARAMS -> acc: {}, user: {}, appList: {}.", Constants.INVOKED_METHOD, accountId, userDetails, appList);
        UserInfo.AccessDetails updatedAccessDetails;

        List<UserInfo.AccessDetails> currentDetailsList = userDetails.getAccessDetails();

        if (currentDetailsList == null || currentDetailsList.isEmpty()) {
            LOGGER.info("User is not yet mapped in HEAL, generating initial access mapping...");
            updatedAccessDetails = generateInitialAccessListForNewUser(accountId, userDetails.getRoleId(), appList);
        } else {
            //TODO: To check if first account verification is needed.
            if (Integer.parseInt(currentDetailsList.get(0).getAccountId().toString()) != accountId) {
                throw new ControlCenterException("Invalid account mapping");
            }
            UserInfo.AccessDetails userAccessDetails = currentDetailsList.get(0);
            updatedAccessDetails = generateUpdatedAccessDetails(userAccessDetails, appList);
        }

        return Collections.singletonList(updatedAccessDetails);
    }

    private UserInfo.AccessDetails generateUpdatedAccessDetails(UserInfo.AccessDetails currentState, List<String> requiredAppList) {
        UserInfo.AccessDetails result = new UserInfo.AccessDetails();
        result.setAccountId(currentState.getAccountId());
        result.setAction("edit");

        UserInfo.AccessDetails.UserApp userAppMappingAdd = new UserInfo.AccessDetails.UserApp();
        userAppMappingAdd.setAction("add");
        userAppMappingAdd.setIds(requiredAppList);

        List<UserInfo.AccessDetails.UserApp> userAppList = new ArrayList<>();
        userAppList.add(userAppMappingAdd);
        result.setApplications(userAppList);
        LOGGER.debug("Generated updated Access list for user: {}.", result);
        return result;
    }

    private UserInfo.AccessDetails generateInitialAccessListForNewUser(int accountId, int roleId, List<String> requiredAppList) throws ControlCenterException {
        UserInfo.AccessDetails result = new UserInfo.AccessDetails();
        result.setAccountId(accountId);
        result.setAction("add");
        UserInfo.AccessDetails.UserApp userAppMappingAdd = new UserInfo.AccessDetails.UserApp();

        userAppMappingAdd.setAction("add");

        if(roleId == new UserAccessDataService().getProfileId("Heal Admin")
                || roleId == new UserAccessDataService().getProfileId("User Manager")) {
            List<String> appIds = new ArrayList<>();
            appIds.add("*");
            userAppMappingAdd.setIds(appIds);
        } else {
            userAppMappingAdd.setIds(requiredAppList);
        }

        result.setApplications(Collections.singletonList(userAppMappingAdd));

        LOGGER.debug("Generated updated Access list for user: {}.", result);

        return result;
    }

    private List<Controller> getApplicationDetails(int accountId, List<String> externalIdList) throws ControlCenterException {
        TagDetailsBean externalTagDetail = MasterDataService.getTagDetails(Constants.EXTERNAL_TAGS_LITERAL);
        if (externalTagDetail == null) {
            LOGGER.debug("Unable to find {} tag details.", Constants.EXTERNAL_TAGS_LITERAL);
            throw new ControlCenterException("Unable to find necessary tag.");
        }

        List<IdPojo> allICTOList = TagsDataService.getTagValue(externalTagDetail.getId(), Constants.CONTROLLER_TAG, accountId);
        if (allICTOList.isEmpty()) {
            return new ArrayList<>();
        }

        List<Controller> controllerList = new ControllerDataService().getApplicationsList(accountId);
        if (controllerList == null || controllerList.isEmpty()) {
            return new ArrayList<>();
        }

        Map<String, Controller> ictoApplicationMapper = new HashMap<>();
        allICTOList.forEach(icto -> {
            Controller cur = controllerList.stream()
                    .filter(app -> Integer.toString(icto.getId()).equalsIgnoreCase(app.getAppId()))
                    .findAny()
                    .orElse(null);
            if (cur != null) {
                LOGGER.debug("Found app details for ICTO: {}.", icto.getName());
                ictoApplicationMapper.put(icto.getName(), cur);
            }
        });

        return externalIdList.parallelStream()
                .map(ictoApplicationMapper::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private UserInfo getUserInfo(String userName, String setup) throws ControlCenterException {
        UserInfoBean userInfoBean = userDataService.getUserDetails(userName);

        if (userInfoBean == null) {
            LOGGER.error("Unable to fetch User details from db.");
            return null;
        }

        AccessDetailsBean accessDetailsBean;
        try {
            accessDetailsBean = OBJECT_MAPPER.readValue(userInfoBean.getAccessDetailsJSON(),
                    new TypeReference<AccessDetailsBean>() {
                    });
        } catch (IOException e) {
            LOGGER.error("Error while parsing Access Details JSON.", e);
            throw new ControlCenterException("Error while parsing Access Details JSON");
        }

        userInfoBean.setAccessDetails(accessDetailsBean);

        UserInfo user = UserInfo.builder()
                .id(userInfoBean.getId())
                .status(userInfoBean.getStatus())
                .roleId(userInfoBean.getRoleId())
                .profileId(userInfoBean.getProfileId())
                .emailId(userInfoBean.getEmailId())
                .contactNumber(userInfoBean.getContactNumber())
                .userName(userInfoBean.getUserName())
                .mysqlId(userInfoBean.getMysqlId())
                .isTimezoneMychoice(userInfoBean.getIsTimezoneMychoice())
                .isNotificationsTimezoneMychoice(userInfoBean.getIsNotificationsTimezoneMychoice())
                .editInKeycloak(setup.equalsIgnoreCase(Constants.SETUP_AD_INTEGRATION) && userDataService.adEditStatusKeycloak())
                .build();

        user.setAccessDetails(usersBL.populateAccessDetails(userInfoBean.getAccessDetails()));

        UserBean userBean = UserAccessDataService.getUserDetailsFromUsername(userName);

        if (userBean != null) {
            user.setFirstName(userBean.getFirstName());
            user.setLastName(userBean.getLastName());
        } else {
            LOGGER.error("Unable to fetch user details from keycloak.");
        }

        return user;
    }
}
