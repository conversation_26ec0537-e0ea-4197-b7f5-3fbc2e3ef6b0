package com.appnomic.appsone.controlcenter.businesslogic;


import com.appnomic.appsone.controlcenter.beans.ComponentInstanceBean;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.common.Actions;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.KPIDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.MetricDetailsDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CompInstanceKPIDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.InstKpiAttrPersistenceSuppressionBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.InstanceKpiAttributeThresholdBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.KPIProducerDetailsBean;
import com.appnomic.appsone.controlcenter.dao.redis.ComponentRepo;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.MetricDetailsRequest;
import com.appnomic.appsone.controlcenter.pojo.MetricGroupAttribute;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.*;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class UpdateMetricGroupAttributes implements BusinessLogic<MetricDetailsRequest, MetricDetailsRequest, String> {

    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateMetricGroupAttributes.class);
    MetricDetailsDataService dataService = new MetricDetailsDataService();
    CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
    ComponentRepo componentRepo = new ComponentRepo();
    InstanceRepo instanceRepo = new InstanceRepo();

    @Override
    public UtilityBean<MetricDetailsRequest> clientValidation(RequestObject request) throws ClientException {

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }

        String groupIdString = request.getParams().get(Constants.GROUP_ID);
        if (StringUtils.isEmpty(groupIdString)) {
            LOGGER.error("groupId is null or empty. {}", groupIdString);
            throw new ClientException("groupId is null or empty.");
        }
        int groupId;
        try {
            groupId = Integer.parseInt(groupIdString.trim());
        } catch (NumberFormatException e) {
            LOGGER.error("groupId [{}] is not an integer. ", groupIdString);
            throw new ClientException("groupId is not an integer.");
        }

        if (StringUtils.isEmpty(request.getBody())) {
            LOGGER.error("InstanceIds are not specified in the request body.");
            throw new ClientException("InstanceIds are not specified in the request body.");
        }
        LOGGER.info(request.getBody());
        MetricDetailsRequest metricDetailsRequest;
        try {
            metricDetailsRequest = new ObjectMapper().readValue(request.getBody(),
                    new TypeReference<MetricDetailsRequest>() {
                    });
            LOGGER.info(metricDetailsRequest.toString());
        } catch (IOException e) {
            LOGGER.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        metricDetailsRequest.setGroupId(groupId);
        if (!metricDetailsRequest.validateGroupAttributes()) {
            LOGGER.error("Request validation failure.");
            throw new ClientException("Request validation failure. Kindly check the logs.");
        }

        return UtilityBean.<MetricDetailsRequest>builder()
                .accountIdentifier(identifier)
                .pojoObject(metricDetailsRequest)
                .authToken(authToken)
                .build();
    }

    @Override
    public MetricDetailsRequest serverValidation(UtilityBean<MetricDetailsRequest> utilityBean) throws ServerException {
        UserAccountBean userAccBean;
        try {
            userAccBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            throw new ServerException(e.getMessage());
        }

        int accountId = userAccBean.getAccount().getId();
        List<ComponentInstanceBean> instances = new ArrayList<>();
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
        MetricDetailsRequest metricDetailsRequest = utilityBean.getPojoObject();

        Set<Integer> instanceIds = new HashSet<>(metricDetailsRequest.getInstanceIds());
        for (int instanceId : instanceIds) {
            ComponentInstanceBean bean = compInstanceDataService.getComponentInstanceByIdAndAccount(instanceId, accountId);
            if (bean == null) {
                throw new ServerException("Invalid instance Id : " + instanceId);
            }
            instances.add(bean);
        }

        Set<Integer> componentIds = instances.parallelStream().map(ComponentInstanceBean::getMstComponentId)
                .collect(Collectors.toSet());
        if (componentIds.size() != 1) {
            LOGGER.error("InstanceIds specified are mapped to different components.");
            throw new ServerException("InstanceIds specified are mapped to different components.");
        }

        int compId = new ArrayList<>(componentIds).get(0);
        Set<Integer> groups;
        try {
            groups = dataService.getNonDiscoveredKPIGroupsForComponentId
                    (compId, accountId, null).parallelStream().map(IdPojo::getId).collect(Collectors.toSet());
        } catch (ControlCenterException e) {
            throw new ServerException(e.getMessage());
        }
        if (!groups.contains(metricDetailsRequest.getGroupId())) {
            LOGGER.error("Invalid groupId.");
            throw new ServerException("Invalid groupId.");
        }

        metricDetailsRequest.setAccountId(accountId);
        metricDetailsRequest.setAccountIdentifier(userAccBean.getAccount().getIdentifier());
        metricDetailsRequest.setComponentId(compId);
        metricDetailsRequest.setInstances(instances.parallelStream().map(instance -> IdPojo.builder()
                .id(instance.getId())
                .identifier(instance.getIdentifier())
                .name(instance.getName())
                .build())
                .collect(Collectors.toList()));
        metricDetailsRequest.setUserId(userAccBean.getUserId());

        try {
            Set<String> commonAttributes = new GetMetricGroupAttributes().process(metricDetailsRequest).parallelStream()
                    .filter(attr -> attr.getCommon() == 1).map(MetricGroupAttribute::getValue).collect(Collectors.toSet());
            List<MetricDetailsRequest.GroupAttributes> groupAttributes = metricDetailsRequest.getGroupAttributes();

            if (groupAttributes.parallelStream().anyMatch(attr -> !StringUtils.isEmpty(attr.getOldValue()) &&
                    !commonAttributes.contains(attr.getOldValue()))) {
                LOGGER.error("attributes specified are not available to be updated or deleted.");
                throw new ServerException("attributes specified are not available to be updated or deleted.");
            }

            Set<String> instanceAttributes = new HashSet<>();
            for (Integer instanceId : instanceIds) {
                instanceAttributes.addAll(dataService.getGroupAttributesForInstance(instanceId, metricDetailsRequest.getGroupId(),
                        null).parallelStream().map(MetricGroupAttribute::getValue).collect(Collectors.toSet()));
            }
            if (groupAttributes.parallelStream().anyMatch(attr -> !StringUtils.isEmpty(attr.getValue()) &&
                    instanceAttributes.contains(attr.getValue()))) {
                LOGGER.error("attributes specified are already present for the group hence it can't be added.");
                throw new ServerException("attributes specified are already present for the group hence it can't be added.");
            }
        } catch (Exception e) {
            throw new ServerException("Server validation failure : " + e.getMessage());
        }
        return metricDetailsRequest;
    }

    @Override
    public String process(MetricDetailsRequest bean) throws DataProcessingException {
        try {
            return MySQLConnectionManager.getInstance().getHandle().inTransaction((conn, status) ->
                    updateMetricGroupAttributes(bean, conn));
        } catch (Exception e) {
            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw (DataProcessingException) Throwables.getRootCause(e);
            } else {
                throw e;
            }
        }
    }

    public String updateMetricGroupAttributes(MetricDetailsRequest bean, Handle conn) throws ControlCenterException {
        int groupId = bean.getGroupId();
        List<Integer> instanceIds = bean.getInstanceIds();
        List<MetricDetailsRequest.GroupAttributes> groupAttributes = bean.getGroupAttributes();
        String userId = bean.getUserId();
        String timestamp = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());

        Set<CompInstanceKPIDetailsBean> addGroupAttributes = new HashSet<>();

        for (MetricDetailsRequest.GroupAttributes attribute : groupAttributes) {
            switch (attribute.getAction()) {
                case ADD:
                    int componentId = bean.getComponentId();
                    int accountId = bean.getAccountId();
                    for (Integer id : instanceIds) {
                        List<Integer> collectionIntervals = dataService.getCollectionIntervalForGroup(id, groupId, conn);
                        if (collectionIntervals.size() > 1) {
                            LOGGER.error("For selected instances: {}, {} different collection interval found for InstanceId: {}, GroupId: {}.",instanceIds, collectionIntervals.size(), id, groupId);
                            throw new ControlCenterException("For selected instances: "+instanceIds+", all collection Interval are not same for groupId: "+groupId+" and instanceId: "+id);
                        }

                        List<KPIProducerDetailsBean> KPIs = dataService.getKPIsForGroupAndForComponent(componentId, id, groupId, accountId, conn);
                        for (KPIProducerDetailsBean kpi : KPIs) {
                            addGroupAttributes.add(CompInstanceKPIDetailsBean.builder()
                                    .componentId(componentId)
                                    .instanceId(id)
                                    .mstProducerKPIMappingId(kpi.getMstKpiProducerMappingId())
                                    .attributeStatus(attribute.getStatus())
                                    .collectionInterval(!collectionIntervals.isEmpty() ? collectionIntervals.get(0) : kpi.getCollectionInterval())
                                    .status(Constants.STATUS_ACTIVE)
                                    .createdTime(timestamp)
                                    .updatedTime(timestamp)
                                    .userId(userId)
                                    .kpiId(kpi.getKpiId())
                                    .producerId(kpi.getProducerId())
                                    .attribute(attribute.getValue())
                                    .discovery(kpi.getDiscovery())
                                    .groupName(kpi.getKpiGroup())
                                    .isGroup(1)
                                    .groupKpiId(groupId)
                                    .aliasName(attribute.getAliasName())
                                    .accountId(bean.getAccountId())
                                    .build());
                        }
                    }
                    break;
                case UPDATE:
                    for (Integer instanceId : instanceIds) {
                        List<IdPojo> idPojos = bean.getInstances().parallelStream().filter(t -> t.getId() == instanceId).collect(Collectors.toList());
                        String instanceIdentifier = idPojos.get(0).getIdentifier();
                        dataService.updateGroupAttributeForInstances(instanceId, groupId, attribute.getOldValue(),
                                attribute.getValue(), attribute.getAliasName(), attribute.getStatus(), timestamp, userId, conn);
                        InstanceKpiAttributeThresholdBean kpiAttribThresholdBean = InstanceKpiAttributeThresholdBean.builder()
                                .attributeValue(attribute.getValue() == null ? attribute.getOldValue() : attribute.getValue())
                                .attributeOldValue(attribute.getOldValue())
                                .compInstanceId(instanceId)
                                .kpiGroupId(groupId)
                                .build();
                        new KPIDataService().updateInstanceKpiAttributeName(kpiAttribThresholdBean, conn);
                        InstKpiAttrPersistenceSuppressionBean instKpiAttrPersistenceSuppressionBean = InstKpiAttrPersistenceSuppressionBean.builder()
                                .attributeValue(attribute.getValue() == null ? attribute.getOldValue() : attribute.getValue())
                                .attributeOldValue(attribute.getOldValue())
                                .compInstanceId(instanceId)
                                .kpiGroupId(groupId)
                                .accountId(bean.getAccountId())
                                .build();
                        new KPIDataService().updateInstanceKpiAttributeValueInPersistSuppress(instKpiAttrPersistenceSuppressionBean, conn);

                        //updating into the redis service
                        if (attribute.getStatus() == 1){
                            List<CompInstKpiEntity> instanceWiseKpis = instanceRepo.getInstanceWiseKpis(bean.getAccountIdentifier(), instanceIdentifier);
                        if (!instanceWiseKpis.isEmpty()) {
                            if (instanceWiseKpis.parallelStream().noneMatch(m -> m.getGroupId() == groupId)) {
                                LOGGER.error("The KPI details not found for the group kpi: [{}]", groupId);
                            } else {
                                updateGroupKpiAttributeDetail(bean, groupId, attribute, instanceIdentifier, instanceWiseKpis);
                            }
                        }
                    }
                    }
                    break;
                case DELETE:
                    for (Integer id : instanceIds) {
                        List<IdPojo> idPojos = bean.getInstances().parallelStream().filter(t -> t.getId() == id).collect(Collectors.toList());
                        String instanceIdentifier = idPojos.get(0).getIdentifier();
                        dataService.deleteGroupAttributeForInstances(id, groupId, attribute.getOldValue(), conn);
                        InstanceKpiAttributeThresholdBean kpiAttribThresholdBean = InstanceKpiAttributeThresholdBean.builder()
                                .attributeValue(attribute.getValue())
                                .attributeOldValue(attribute.getOldValue())
                                .compInstanceId(id)
                                .kpiGroupId(groupId)
                                .build();
                        new KPIDataService().deleteInstanceKpiAttributeName(kpiAttribThresholdBean, conn);
                        InstKpiAttrPersistenceSuppressionBean instKpiAttrPersistenceSuppressionBean = InstKpiAttrPersistenceSuppressionBean.builder()
                                .attributeValue(attribute.getOldValue())
                                .compInstanceId(id)
                                .kpiGroupId(groupId)
                                .accountId(bean.getAccountId())
                                .build();
                        new KPIDataService().deleteInstanceKpiAttributeLevelPersistSuppressByGroupID(instKpiAttrPersistenceSuppressionBean, conn);
                        List<CompInstKpiEntity> instanceWiseKpis = instanceRepo.getInstanceWiseKpis(bean.getAccountIdentifier(), instanceIdentifier);
                        String attributeValue = attribute.getOldValue();
                        if (!instanceWiseKpis.isEmpty()) {
                            if (instanceWiseKpis.parallelStream().noneMatch(m -> m.getGroupId() == groupId && m.getAttributeValues() != null && m.getAttributeValues().containsKey(attributeValue))) {
                                LOGGER.debug("The attributes details not found for the given group kpi: [{}] and attribute: [{}]", groupId, attributeValue);
                            } else {
                                deleteKpiAttributeDetail(bean, groupId, instanceIdentifier, instanceWiseKpis, attributeValue);
                            }

                        }
                    }
            }
        }

        if (!addGroupAttributes.isEmpty()) {
           dataService.addInstanceProducerMappingDetailsForGroupKPI(new ArrayList<>(addGroupAttributes), conn);
            //adding the attribute details in the redis service
            addKpiAttributeDetails(bean.getAccountIdentifier(), addGroupAttributes, conn);
        } else if (groupAttributes.parallelStream().anyMatch(attribute -> attribute.getAction().equals(Actions.ADD))) {
            throw new ControlCenterException("Attributes cannot be added because the KPIs for groupId [" + groupId + "] are not mapped to any producer.");
        }
        LOGGER.info("Attributes updated for KPI group {} and instances {}.",groupId,instanceIds);
        return "Attributes updated for KPI group [" + groupId + "] and instances [" + instanceIds + "].";
    }

    private void deleteKpiAttributeDetail(MetricDetailsRequest bean, int groupId, String instanceIdentifier, List<CompInstKpiEntity> instanceWiseKpis, String attributeValue) {
        instanceWiseKpis.forEach(kpiList ->
        {
            if(kpiList.getGroupId() == groupId && kpiList.getAttributeValues() != null && kpiList.getAttributeValues().containsKey(attributeValue))
            {
                kpiList.getAttributeValues().remove(attributeValue);
                //remove thresholds details
                if (kpiList.getKpiViolationConfig() != null) {
                    kpiList.getKpiViolationConfig().remove(attributeValue);
                }
                instanceRepo.updateKpiDetailsForKpiId(bean.getAccountIdentifier(), instanceIdentifier, kpiList);
                instanceRepo.updateKpiDetailsForKpiIdentifier(bean.getAccountIdentifier(), instanceIdentifier, kpiList);
                instanceRepo.updateKpiDetails(bean.getAccountIdentifier(), instanceIdentifier, instanceWiseKpis);
            }
        });
    }

    private void addKpiAttributeDetails(String accountIdentifier, Set<CompInstanceKPIDetailsBean> instanceGroupAttributes, Handle conn) {
        InstanceRepo instanceRepo = new InstanceRepo();
        Map<Integer, CompInstClusterDetails> instClusterDetailMap = instanceRepo.getInstances(accountIdentifier)
                .stream()
                .collect(Collectors.toMap(CompInstClusterDetails::getId, Function.identity()));
        instanceGroupAttributes
                .stream()
                .filter(a -> a.getStatus() == 1)
                .forEach(attributeDetails -> {
                    try {
                        CompInstClusterDetails instClusterDetail = instClusterDetailMap.get(attributeDetails.getInstanceId());
                        if(instClusterDetail == null) {
                            LOGGER.error("Could not find the instance details for redis cache. AccountId:{}, instanceId:{}", accountIdentifier, attributeDetails.getInstanceId());
                            return;
                        }
                        List<CompInstKpiEntity> instanceWiseKpis = instanceRepo.getInstanceWiseKpis(accountIdentifier, instClusterDetail.getIdentifier());
                        if(instanceWiseKpis.isEmpty()){
                            instanceWiseKpis = new ArrayList<>();
                        }

                        CompInstKpiEntity kpiEntity = instanceWiseKpis.stream().filter(k -> k.getId() == attributeDetails.getKpiId() && k.getGroupId() == attributeDetails.getGroupKpiId()).findAny().orElse(null);
                        Map<String, String> attributeValuesDetails = new HashMap<>();
                        Map<String, String> inactiveAttributeValuesDetails = new HashMap<>();

                        if (attributeDetails.getAttributeStatus() == 1) {
                            attributeValuesDetails.put(attributeDetails.getAttribute(), attributeDetails.getAliasName());
                        } else {
                            inactiveAttributeValuesDetails.put(attributeDetails.getAttribute(), attributeDetails.getAliasName());
                        }

                        if(kpiEntity == null) {
                            Component componentDetails = componentRepo.getComponentDetails(accountIdentifier).
                                    parallelStream()
                                    .filter(f -> f.getId() == attributeDetails.getComponentId())
                                    .findAny()
                                    .orElse(null);
                            if(componentDetails == null) {
                                LOGGER.error("Could not find the component details for redis cache. AccountId:{}", accountIdentifier);
                                return;
                            }
                            List<ComponentKpiEntity> componentKpiDetails = componentRepo.getComponentKpiDetails(accountIdentifier, componentDetails.getName());
                            BasicKpiEntity componentKpi = componentKpiDetails.parallelStream()
                                    .filter(f -> f.getId() == attributeDetails.getKpiId() && f.getGroupId() == attributeDetails.getGroupKpiId())
                                    .findAny()
                                    .orElse(null);
                            if(componentKpi == null) {
                                CCCache.INSTANCE.updateCCErrors(1);
                                LOGGER.error("The kpi details not found for the given Component: [{}] from redis cache", componentDetails.getName());
                                return;
                            }
                            int comInsKpiId;
                            try {
                                comInsKpiId = compInstanceDataService.getCompInsGroupKpiIdUsingKpiId(componentKpi.getId(), componentKpi.getGroupId(), instClusterDetail.getId(), conn);
                            } catch (ControlCenterException e) {
                                LOGGER.error("Could not found the group kpi attribute  details for kpiId:{}", attributeDetails.getKpiId());
                                return;
                            }
                            kpiEntity = CompInstKpiEntity.builder()
                                    .id(componentKpi.getId())
                                    .name(componentKpi.getName())
                                    .categoryDetails(componentKpi.getCategoryDetails())
                                    .identifier(componentKpi.getIdentifier())
                                    .custom(componentKpi.getCustom())
                                    .unit(componentKpi.getUnit())
                                    .aggOperation(componentKpi.getAggOperation())
                                    .type(componentKpi.getType())
                                    .rollupOperation(componentKpi.getRollupOperation())
                                    .clusterAggType(componentKpi.getClusterAggType())
                                    .instanceAggType(componentKpi.getInstanceAggType())
                                    .valueType(componentKpi.getValueType())
                                    .dataType(componentKpi.getDataType())
                                    .isInfo(componentKpi.getIsInfo())
                                    .resetDeltaValue(componentKpi.getResetDeltaValue())
                                    .deltaPerSec(componentKpi.getDeltaPerSec())
                                    .cronExpression(componentKpi.getCronExpression())
                                    .categoryDetails(componentKpi.getCategoryDetails())
                                    .defaultProducerId(attributeDetails.getMstProducerKPIMappingId())
                                    .collectionInterval(attributeDetails.getCollectionInterval())
                                    .status(attributeDetails.getStatus())
                                    .notification(attributeDetails.getNotification())
                                    .groupId(componentKpi.getGroupId())
                                    .groupName(componentKpi.getGroupName())
                                    .groupIdentifier(componentKpi.getGroupIdentifier())
                                    .groupStatus(componentKpi.getGroupStatus())
                                    .isGroup(componentKpi.getIsGroup())
                                    .discovery(componentKpi.getDiscovery())
                                    .isMaintenanceExcluded(0)
                                    .compInstKpiId(comInsKpiId)
                                    .attributeValues(attributeValuesDetails)
                                    .inactiveAttributeValues(inactiveAttributeValuesDetails)
                                    .build();
                            instanceWiseKpis.add(kpiEntity);
                        } else {
                            if (!attributeValuesDetails.isEmpty()){
                                Map<String, String> attributeDetailsNew = kpiEntity.getAttributeValues() == null ? new HashMap<>() : kpiEntity.getAttributeValues();
                                attributeDetailsNew.put(attributeDetails.getAttribute(), attributeDetails.getAliasName());
                                kpiEntity.setAttributeValues(attributeDetailsNew);
                            } else {
                                Map<String, String> attributeDetailsNew = kpiEntity.getInactiveAttributeValues() == null ? new HashMap<>() : kpiEntity.getInactiveAttributeValues();
                                attributeDetailsNew.put(attributeDetails.getAttribute(), attributeDetails.getAliasName());
                                kpiEntity.setInactiveAttributeValues(attributeDetailsNew);
                            }
                        }

                        instanceRepo.updateKpiDetailsForKpiId(accountIdentifier, instClusterDetail.getIdentifier(), kpiEntity);
                        instanceRepo.updateKpiDetailsForKpiIdentifier(accountIdentifier, instClusterDetail.getIdentifier(), kpiEntity);
                        instanceRepo.updateKpiDetails(accountIdentifier, instClusterDetail.getIdentifier(), instanceWiseKpis);

                    } catch (Exception e) {
                        LOGGER.error("Error occurred while updating attribute status. Details: ", e);
                    }
                });
    }

    private void updateGroupKpiAttributeDetail(MetricDetailsRequest bean, int groupId, MetricDetailsRequest.GroupAttributes attribute, String instanceIdentifier, List<CompInstKpiEntity> instanceWiseKpis) {
        instanceWiseKpis.stream()
                .filter(a -> a.getGroupId() == groupId)
                .forEach(kpiDetails -> {
                    String oldValue = attribute.getOldValue();

                    if (attribute.getStatus() == 1) {
                        if (kpiDetails.getAttributeValues() != null && kpiDetails.getAttributeValues().containsKey(oldValue)) {
                            kpiDetails.updateAttributeValues(getAttributes(attribute, kpiDetails.getAttributeValues()));
                        }
                        else if (kpiDetails.getInactiveAttributeValues() != null && kpiDetails.getInactiveAttributeValues().containsKey(oldValue)) {
                            kpiDetails.updateAttributeValues(getAttributes(attribute, kpiDetails.getInactiveAttributeValues()));
                        }

                    }
                    else {
                        if (kpiDetails.getInactiveAttributeValues() != null && kpiDetails.getInactiveAttributeValues().containsKey(oldValue)) {
                            kpiDetails.updateInactiveAttributeValues(getAttributes(attribute, kpiDetails.getInactiveAttributeValues()));
                        }
                        else if (kpiDetails.getAttributeValues() != null && kpiDetails.getAttributeValues().containsKey(oldValue)) {
                            kpiDetails.updateInactiveAttributeValues(getAttributes(attribute, kpiDetails.getAttributeValues()));
                        }
                    }

                    if (kpiDetails.getKpiViolationConfig() != null) {
                        if (kpiDetails.getKpiViolationConfig().containsKey(oldValue)) {
                            kpiDetails.getKpiViolationConfig().get(oldValue).setAttributeValue(attribute.getValue());
                            Map<String, KpiViolationConfig> kpiViolationConfigMap = new HashMap<>();
                            kpiViolationConfigMap.put(attribute.getValue(), kpiDetails.getKpiViolationConfig().get(attribute.getOldValue()));
                            kpiDetails.getKpiViolationConfig().remove(oldValue);
                            kpiDetails.getKpiViolationConfig().putAll(kpiViolationConfigMap);
                        } else {
                            LOGGER.debug("The thresholds details not found for the given attribute details");
                        }
                    }
                    instanceRepo.updateKpiDetailsForKpiId(bean.getAccountIdentifier(), instanceIdentifier, kpiDetails);
                    instanceRepo.updateKpiDetailsForKpiIdentifier(bean.getAccountIdentifier(), instanceIdentifier, kpiDetails);
                    instanceRepo.updateKpiDetails(bean.getAccountIdentifier(), instanceIdentifier, instanceWiseKpis);
                });
    }
    private Map<String, String> getAttributes(MetricDetailsRequest.GroupAttributes attribute, Map<String, String> attributeValues){
        Map<String, String> attributeDetails = new HashMap<>();
        String oldValue = attribute.getOldValue();
        String value = attribute.getValue();
        String aliasName = attribute.getAliasName();
        if (aliasName == null && value == null) {
            attributeDetails.put(oldValue, attributeValues.get(oldValue));
        }
        else if (aliasName == null) {
            attributeDetails.put(value, attributeValues.get(oldValue));
        } else if (value == null) {
            attributeDetails.put(oldValue, aliasName);
        } else {
            attributeDetails.put(value, aliasName);
        }
        attributeValues.remove(oldValue);
        return attributeDetails;

    }

}
