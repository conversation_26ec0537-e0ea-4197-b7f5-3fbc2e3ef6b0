package com.appnomic.appsone.controlcenter.businesslogic.connectors;

import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.businesslogic.BusinessLogic;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.ConnectorConstants;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.CommandDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.RulesDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.connectors.ConnectorDetailsDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandDetailsBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.connectors.ConnectorCommandPojo;
import com.appnomic.appsone.controlcenter.pojo.connectors.ConnectorDetails;
import com.appnomic.appsone.controlcenter.service.QueuePublisher;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.util.*;

@Slf4j
public class ConnectorControllerBL implements BusinessLogic<Object, ConnectorDetails.ConnectorRequest, String> {
    private static final ObjectMapper OBJECT_MAPPER = CommonUtils.getObjectMapperWithHtmlEncoder();

    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        CommonUtils.basicRequestValidation(requestObject);

        ConnectorDetails.ConnectorRequest connectorRequest = null;
        try {
            connectorRequest = OBJECT_MAPPER.readValue(requestObject.getBody(), new TypeReference<ConnectorDetails.ConnectorRequest>() {
            });
        } catch (JsonMappingException e) {
            log.error("Error in mapping JSON Object. Details: ", e);
        } catch (JsonProcessingException e) {
            log.error("Error in parsing JSON Object. Details: ", e);
        }

        if (connectorRequest == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        int connectorId;
        try {
            connectorId = Integer.parseInt(requestObject.getParams().get(":connectorId"));
        } catch (NumberFormatException e) {
            log.error("Connector ID is an invalid integer [{}]", requestObject.getParams().get(":connectorId"));
            throw new ClientException("Invalid connector ID");
        }

        connectorRequest.setConnectorId(connectorId);
        if (connectorRequest.getConnectorId() <= 0) {
            log.error("Invalid connector id. Reason: It is not a positive integer.");
            throw new ClientException("Invalid template id");
        }

        int commandId;
        try {
            commandId = connectorRequest.getCommandId();
        } catch (NumberFormatException ex) {
            throw new ClientException("Invalid command id. Reason: It is not a positive integer.");
        }
        if (commandId <= 0) {
            log.error("Invalid command id.");
            throw new ClientException("Invalid command id");
        }

        return UtilityBean.builder()
                .accountIdentifier(requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER))
                .authToken(requestObject.getHeaders().get(Constants.AUTHORIZATION))
                .pojoObject(connectorRequest)
                .build();
    }

    @Override
    public ConnectorDetails.ConnectorRequest serverValidation(UtilityBean<Object> utilityBean) throws ServerException {

        String accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);
        ConnectorDetails.ConnectorRequest connectorRequest = (ConnectorDetails.ConnectorRequest) utilityBean.getPojoObject();
        int connectorId = connectorRequest.getConnectorId();

        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        if (connectorId <= 0) {
            log.error("Connector Id is invalid");
            throw new ServerException("Connector Id is invalid");
        }

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        connectorRequest.setUserId(userId);
        return connectorRequest;
    }

    public String process(ConnectorDetails.ConnectorRequest connectorRequest) throws DataProcessingException {
        CommandRequestProtos.Command command;
        CommandRequestProtos.CommandRequest commandDetails;
        CommandDetailsBean commandDetailsBean = CommandDataService.getConnectorCommandDetails(connectorRequest.getCommandId());
        if (commandDetailsBean == null) {
            log.error("Command Details for command id: {} not found.", connectorRequest.getCommandId());
            throw new DataProcessingException("Command details for command id: not found.");
        }
        String commandOutputType = RulesDataService.getNameFromMSTSubType(commandDetailsBean.getOutputTypeId());
        String triggerTime = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
        long fromEpochTime = DateTimeUtil.getGMTToEpochTime(triggerTime);
        ViewTypes viewTypes = MasterCache.getMstTypeById(commandDetailsBean.getCommandTypeId());
        if (viewTypes == null) {
            log.error("Error while fetching view types for command type id: {}", commandDetailsBean.getCommandTypeId());
            throw new DataProcessingException("Error while fetching view types for command type id");
        }
        List<ConnectorDetails.CommandArgs> commandArgs = ConnectorDetailsDataService.getConnectorCommandArguments(connectorRequest.getCommandId(), connectorRequest.getConnectorId(), null);
        if (commandArgs == null) {
            log.error("Error while fetching command arguments for command id: {}, connector id: {}", connectorRequest.getCommandId(), connectorRequest.getConnectorId());
            throw new DataProcessingException("Error while fetching command arguments for command id");
        }
        Map<String, String> commandArgsInMap = new HashMap<>();
        ConnectorDetails.CommandArgsValues commandArgsValues = ConnectorDetailsDataService.getConnectorCommandArgumentsValues(connectorRequest.getConnectorId(), connectorRequest.getCommandId(), null);
        if (commandArgsValues == null) {
            log.error("Error while fetching command arguments values for command id: {}, connector id: {}", connectorRequest.getCommandId(), connectorRequest.getConnectorId());
            throw new DataProcessingException("Error while fetching command arguments values for command id");
        }
        for (ConnectorDetails.CommandArgs ca : commandArgs) {
            if (ca.getKey().equalsIgnoreCase(ConnectorConstants.CONNECTOR_COMMAND_CONNECTOR_LOGS) && ca.getIsPlaceHolder() == 1) {
                ca.setValue(commandArgsValues.getConnector_logs());
                commandArgsInMap.put(ca.getKey(), ca.getValue());
            }
            if (ca.getKey().equalsIgnoreCase(ConnectorConstants.CONNECTOR_COMMAND_CONNECTOR_CONFIG) && ca.getIsPlaceHolder() == 1) {
                ca.setValue(commandArgsValues.getConnector_config());
                commandArgsInMap.put(ca.getKey(), ca.getValue());
            }
            if (ca.getKey().equalsIgnoreCase(ConnectorConstants.CONNECTOR_COMMAND_JOB_NAME) && ca.getIsPlaceHolder() == 1) {
                ca.setValue(commandArgsValues.getJob_name());
                commandArgsInMap.put(ca.getKey(), ca.getValue());
            }
        }

        String commandJobId = String.valueOf(UUID.randomUUID());
        command = CommandRequestProtos.Command.newBuilder()
                .setCommandJobId(commandJobId)
                .setCommand(commandDetailsBean.getIdentifier())
                .setCommandType(viewTypes.getTypeName())
                .setCommandOutputType((null == commandOutputType) ? "" : commandOutputType)
                .setCommandExecType(ConnectorConstants.CONNECTOR_COMMAND_EXEC_TYPE)
                .setRetryNumber(ConnectorConstants.CONNECTOR_COMMAND_RETRY_COUNT)
                .setCommandTimeout(commandDetailsBean.getTimeOutInSecs())
                .setSupervisorCtrlTTL(commandDetailsBean.getTimeOutInSecs())
                .putAllArguments(commandArgsInMap)
                .build();
        List<String> supervisorIdentifiers = new ArrayList<>();
        supervisorIdentifiers.add(ConnectorConstants.CONNECTOR_SUPERVISOR_IDENTIFIER);
        try {
            commandDetails = CommandRequestProtos.CommandRequest.newBuilder().addAllSupervisorIdentifiers(supervisorIdentifiers)
                    .setAgentType(ConnectorConstants.CONNECTOR_AGENT_TYPE)
                    .setAgentIdentifier(ConnectorConstants.CONNECTOR_SUPERVISOR_IDENTIFIER)
                    .setTriggerSource(ConnectorConstants.CONNECTOR_COMMAND_TRIGGER_SOURCE)
                    .setUserDetailsID(connectorRequest.getUserId())
                    .setTriggerTime(fromEpochTime)
                    .setViolationTime(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime())
                    .addCommands(command)
                    .putMetadata(ConnectorConstants.CONNECTOR_SUPERVISOR_MODE_KEY, ConnectorConstants.CONNECTOR_SUPERVISOR_MODE_VAL)
                    .putMetadata(ConnectorConstants.CONNECTOR_PRODUCER_TYPE_KEY, ConnectorConstants.CONNECTOR_PRODUCER_TYPE_VAL)
                    .build();
        } catch (ParseException e) {
            log.error("Error building CommandRequestProtos. Details: ", e);
            throw new DataProcessingException("Error building CommandRequestProtos");
        }
        try {
            QueuePublisher.sendAgentCommandMessage(commandDetails);
            ConnectorCommandPojo connectorCommandPojo = ConnectorCommandPojo.builder()
                    .connectorId(connectorRequest.getConnectorId())
                    .commandId(connectorRequest.getCommandId())
                    .triggerTime(DateTimeUtil.getCurrentTimestampInGMT())
                    .commandJobId(commandJobId)
                    .commandStatus(1)
                    .userDetailsId(connectorRequest.getUserId())
                    .build();
            ConnectorDetailsDataService.addConnectorCommandsTriggered(connectorCommandPojo);
        } catch (Exception e) {
            log.error("Error sending command message to queue. Details: ", e);
            throw new DataProcessingException("Error sending command message to queue. Details: ");
        }
        return "";
    }

}