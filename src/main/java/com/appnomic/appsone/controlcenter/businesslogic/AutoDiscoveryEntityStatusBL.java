package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.enums.DiscoveryStatus;
import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.AutoDiscoveryServiceMapping;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.Host;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.common.autodiscovery.Entity;
import com.appnomic.appsone.controlcenter.dao.mysql.AutoDiscoveryDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AutoDiscoveryIgnoreBean;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.AutoDiscoveryEntityStatusPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class AutoDiscoveryEntityStatusBL implements BusinessLogic<AutoDiscoveryEntityStatusPojo, AutoDiscoveryEntityStatusPojo, List<String>> {
    private static final String NO_NODEIDS_STRING = "Received empty list of host identifier's!";

    @Override
    public UtilityBean<AutoDiscoveryEntityStatusPojo> clientValidation(RequestObject request) throws ClientException {

        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (authToken == null || StringUtils.isEmpty(authToken)) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String accountIdentifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (accountIdentifier == null || StringUtils.isEmpty(accountIdentifier)) {
            log.error(UIMessages.ACCOUNT_NULL_OR_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_NULL_OR_EMPTY);
        }

        if (StringUtils.isEmpty(request.getBody())) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        AutoDiscoveryEntityStatusPojo autoDiscoveryEntityStatusPojo;
        try {
            autoDiscoveryEntityStatusPojo = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(request.getBody(),
                    new TypeReference<AutoDiscoveryEntityStatusPojo>() {
                    });
            if (autoDiscoveryEntityStatusPojo != null && !autoDiscoveryEntityStatusPojo.validateMandatoryFields()) {
                    log.error("Input Validation failure for Update Entity Status.");
                    throw new ClientException("Input Validation failure for Update Entity Status.");
                }

        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        return UtilityBean.<AutoDiscoveryEntityStatusPojo>builder()
                .accountIdentifier(accountIdentifier)
                .authToken(authToken)
                .pojoObject(autoDiscoveryEntityStatusPojo)
                .build();
    }

    @Override
    public AutoDiscoveryEntityStatusPojo serverValidation(UtilityBean<AutoDiscoveryEntityStatusPojo> utilityBean) throws ServerException {

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            log.error(UIMessages.INVALID_ACCOUNT_MESSAGE);
            throw new ServerException(UIMessages.INVALID_ACCOUNT_MESSAGE);
        }

        AutoDiscoveryEntityStatusPojo autoDiscoveryEntityStatusPojo = utilityBean.getPojoObject();
        return AutoDiscoveryEntityStatusPojo.builder()
                .isIgnored(autoDiscoveryEntityStatusPojo.getIsIgnored())
                .identifiers(autoDiscoveryEntityStatusPojo.getIdentifiers())
                .entityType(autoDiscoveryEntityStatusPojo.getEntityType())
                .ignoredBy(account.getName())
                .build();
    }

    @Override
    public List<String> process(AutoDiscoveryEntityStatusPojo adEntityStatusPojo) throws DataProcessingException {
        /*
         * sets is_ignored value to 0 or 1 for discovered entities
         */
        List<String> invalidJsonObjects;

        try {
            invalidJsonObjects = invalidIDs(adEntityStatusPojo.getIsIgnored(), adEntityStatusPojo.getIdentifiers(),
                    adEntityStatusPojo.getIgnoredBy(), adEntityStatusPojo.getEntityType());
            return invalidJsonObjects;
        } catch (Exception e) {
            throw new DataProcessingException(e.getMessage());
        }
    }

    public List<String> invalidIDs(Integer isIgnored, String[] ids, String ignoredBy, Entity entityType) throws DataProcessingException {

        AutoDiscoveryEntityStatusPojo adHosts = new AutoDiscoveryEntityStatusPojo();
        List<String> validEntityIDs = Arrays.asList(ids);
        List<String> invalidEntityIDs = new ArrayList<>();
        if (ids.length == 0) {
            log.error(NO_NODEIDS_STRING);
            throw new DataProcessingException(NO_NODEIDS_STRING);
        }

        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        dbi.inTransaction((conn, status) -> {
            try {
                setStatusOfIDs(validEntityIDs, isIgnored, ignoredBy, entityType, adHosts.getErrorMessages(), conn, invalidEntityIDs);
            } catch (DataProcessingException e) {
                throw new DataProcessingException(e.getMessage());
            }
            return "Nothing to return";
        });
        // identifiers which are not present in db are added to invalid entities here
        for (String ID : validEntityIDs) {
            if ((adHosts.getErrorMessages().get(ID) != null)) invalidEntityIDs.add(ID);
        }
        return invalidEntityIDs;
    }

    private void setStatusOfIDs(List<String> ids, Integer isIgnored, String ignoredBy, Entity entityType, Map<String, String> errorMessages,
                                Handle handle, List<String> invalidEntityIDs) throws DataProcessingException {

        AutoDiscoveryDataService autoDiscoveryDataService = new AutoDiscoveryDataService();
        List<Process> processList = autoDiscoveryDataService.getProcessList(null);
        List<com.appnomic.appsone.controlcenter.beans.autodiscovery.Host> hostsList = autoDiscoveryDataService.getHostList(null);

        List<AutoDiscoveryIgnoreBean> validIDsList = new ArrayList<>();
        List<String> hostIDsList = new ArrayList<>();
        List<String> compInstancesIDsList = new ArrayList<>();

        if (entityType == null) {
            for (String id : ids) {
                com.appnomic.appsone.controlcenter.beans.autodiscovery.Host h = hostsList.stream().filter(host -> host.getHostIdentifier().equals(id)).findFirst().orElse(null);
                if (h != null) {
                    hostIDsList.add(id);
                } else {
                    compInstancesIDsList.add(id);
                }
            }
        } else {
            validIDsList = validEntityBean(ids, isIgnored, ignoredBy, entityType, errorMessages, handle, hostsList, processList, invalidEntityIDs);
            if (validIDsList.isEmpty()) return;
        }

        // update is_ignored for valid identifiers
        if (entityType == Entity.Host) {
            autoDiscoveryDataService.setHostIDIsIgnore(validIDsList, handle);
            // when a host is ignored then all of its component instances should also be ignored
            for (AutoDiscoveryIgnoreBean validID: validIDsList) {
                List<Process> processToIgnore = processList.stream().filter(process -> process.getHostIdentifier().equals(validID.getIdentifier())).collect(Collectors.toList());
                List<String> processIds = new ArrayList<>();
                for (Process process: processToIgnore) {
                    processIds.add(process.getProcessIdentifier());
                }
                List<AutoDiscoveryIgnoreBean> validProcessIDsList = validEntityBean(processIds, isIgnored, ignoredBy, Entity.CompInstance, errorMessages, handle, hostsList, processList, invalidEntityIDs);
                autoDiscoveryDataService.setProcessIDIsIgnore(validProcessIDsList, handle);
            }
            log.info("The is_ignored value for the host(s) have been modified successfully.");
        } else if (entityType == Entity.CompInstance) {
            autoDiscoveryDataService.setProcessIDIsIgnore(validIDsList, handle);
            log.info("The is_ignored value for the process(s) have been modified successfully.");
        } else if (entityType == null) {
            List<AutoDiscoveryIgnoreBean> validHostIDsList;
            List<AutoDiscoveryIgnoreBean> validCompInstancesIDsList;

            validHostIDsList = validEntityBean(hostIDsList, isIgnored, ignoredBy, Entity.Host, errorMessages, handle, hostsList, processList, invalidEntityIDs);
            autoDiscoveryDataService.setHostIDIsIgnore(validHostIDsList, handle);
            // when a host is ignored then all of its component instances should also be ignored
            for (AutoDiscoveryIgnoreBean validID: validHostIDsList) {
                List<Process> processToIgnore = processList.stream().filter(process -> process.getHostIdentifier().equals(validID.getIdentifier())).collect(Collectors.toList());
                List<String> processIds = new ArrayList<>();
                for (Process process: processToIgnore) {
                    processIds.add(process.getProcessIdentifier());
                }
                List<AutoDiscoveryIgnoreBean> validProcessIDsList = validEntityBean(processIds, isIgnored, ignoredBy, Entity.CompInstance, errorMessages, handle, hostsList, processList, invalidEntityIDs);
                autoDiscoveryDataService.setProcessIDIsIgnore(validProcessIDsList, handle);
            }
            log.info("The is_ignored value for the host(s) have been modified successfully.");
            validCompInstancesIDsList = validEntityBean(compInstancesIDsList, isIgnored, ignoredBy, Entity.CompInstance, errorMessages, handle, hostsList, processList, invalidEntityIDs);
            autoDiscoveryDataService.setProcessIDIsIgnore(validCompInstancesIDsList, handle);
            log.info("The is_ignored value for the process(s) have been modified successfully.");
        }
    }

    private List<AutoDiscoveryIgnoreBean> validEntityBean(List<String> ids, Integer isIgnored, String ignoredBy,
                                                          Entity entityType, Map<String, String> errorMessages,
                                                          Handle handle, List<com.appnomic.appsone.controlcenter.beans.autodiscovery.Host> hostsList, List<Process> processList,
                                                          List<String> invalidEntityIDs) {

        List<AutoDiscoveryIgnoreBean> validIDsList = new ArrayList<>();
        AutoDiscoveryDataService adDataService = new AutoDiscoveryDataService();
        if (entityType.equals(Entity.Host)) {
            validateIdentifiers(ids, entityType, isIgnored, errorMessages, processList, handle, invalidEntityIDs);
            for (String x : ids) {
                AutoDiscoveryIgnoreBean adIgnoreHostBean = new AutoDiscoveryIgnoreBean();
                if (errorMessages.get(x) == null) {
                    adIgnoreHostBean.setIdentifier(x);
                    adIgnoreHostBean.setIsIgnored(isIgnored);
                    adIgnoreHostBean.setIgnoredBy(ignoredBy);
                    com.appnomic.appsone.controlcenter.beans.autodiscovery.Host host = hostsList.stream()
                            .filter(h -> h.getHostIdentifier().equals(x))
                            .findFirst().orElse(null);
                    if (isIgnored == 0) {
                        if (host != null) {
                            adIgnoreHostBean.setDiscoveryStatus(DiscoveryStatus.DISCOVERED_NOT_ADDED_TO_SYSTEM);
                        }
                    } else if (isIgnored == 1) {
                        // if host is already added to system it cant be ignored
                        if (host != null) {
                            if (host.getDiscoveryStatus().equals(DiscoveryStatus.ADDED_TO_SYSTEM)) continue;
                            adIgnoreHostBean.setDiscoveryStatus(DiscoveryStatus.IGNORED);
                        }
                    }
                    long time = System.currentTimeMillis();
                    adIgnoreHostBean.setLastUpdatedTime(time);
                    validIDsList.add(adIgnoreHostBean);
                }
            }
        } else if (entityType.equals(Entity.CompInstance)) {
            validateIdentifiers(ids, entityType, isIgnored, errorMessages, processList, handle, invalidEntityIDs);
            for (String x : ids) {
                AutoDiscoveryIgnoreBean adIgnoreProcessBean = new AutoDiscoveryIgnoreBean();
                if (errorMessages.get(x) == null) {
                    adIgnoreProcessBean.setIdentifier(x);
                    adIgnoreProcessBean.setIsIgnored(isIgnored);
                    adIgnoreProcessBean.setIgnoredBy(ignoredBy);
                    Process process = processList.stream()
                            .filter(h -> h.getProcessIdentifier().equals(x))
                            .findFirst().orElse(null);
                    if (isIgnored == 0) {
                        if (process != null) {
                            adIgnoreProcessBean.setDiscoveryStatus(DiscoveryStatus.DISCOVERED_NOT_ADDED_TO_SYSTEM);
                        }
                    } else if (isIgnored == 1) {
                        // if host is already added to system it cant be ignored
                        if (process != null) {
                            if (process.getDiscoveryStatus().equals(DiscoveryStatus.ADDED_TO_SYSTEM)) continue;
                            // remove the service mappings and connections of ignored component instances
                            List<String> mappings = new ArrayList<>();
                            List<AutoDiscoveryServiceMapping> serviceMappingList = adDataService.getServiceMappingList(null);
                            List<AutoDiscoveryServiceMapping> existingCompMappings = serviceMappingList.stream().filter(z -> z.getServiceMappingIdentifier().equals(x)).collect(Collectors.toList());
                            if (!existingCompMappings.isEmpty()) {
                                for (AutoDiscoveryServiceMapping mapping : existingCompMappings) {
                                    if (mappings.contains(mapping.getServiceIdentifier())) continue;
                                    mappings.add(mapping.getServiceIdentifier());
                                }
                                adDataService.deleteExistingMappingsAndConnections(x, mappings, process.getHostIdentifier(), handle);
                            }
                            adIgnoreProcessBean.setDiscoveryStatus(DiscoveryStatus.IGNORED);
                        }
                    }
                    long time = System.currentTimeMillis();
                    adIgnoreProcessBean.setLastUpdatedTime(time);
                    validIDsList.add(adIgnoreProcessBean);
                }
            }
        }
        return validIDsList;
    }

    public void validateIdentifiers(List<String> ids, Entity entityType, Integer isIgnored, Map<String, String> errorMessages,
                                    List<Process> processList, Handle handle, List<String> invalidEntityIDs) {
        AutoDiscoveryDataService adDataService = new AutoDiscoveryDataService();
        // fetching updated hosts list here because hosts status might be changed in the same transaction
        List<com.appnomic.appsone.controlcenter.beans.autodiscovery.Host> hostsList = adDataService.getHostList(handle);

        if(hostsList.isEmpty()) {
            String logMsg = "Host components details unavailable";
            errorMessages.put("Hosts", logMsg);
            log.error("Host components details unavailable");
            return;
        }

        if (entityType.equals(Entity.Host))  {
            Host hostBean;
            for (String hostID : ids) {
                hostBean = hostsList.stream().filter(host -> host.getHostIdentifier().equals(hostID)).findAny().orElse(null);
                if (hostBean == null) {
                    String logMsg = "Host identifier not found in table. Identifier- " + hostID;
                    errorMessages.put(hostID, logMsg);
                    log.error(logMsg);
                }
            }
        } else if (entityType.equals(Entity.CompInstance)) {
            Process processBean;
            for (String processID : ids) {
                processBean = processList.stream().filter(process -> process.getProcessIdentifier().equals(processID)).findAny().orElse(null);
                if (processBean == null) {
                    String logMsg = "Process identifier not found in table. Identifier- " + processID;
                    errorMessages.put(processID, logMsg);
                    log.error(logMsg);
                } else {
                    Process finalProcessBean = processBean;
                    if (hostsList.stream().filter(host -> host.getHostIdentifier().equals(finalProcessBean.getHostIdentifier()))
                            .findFirst().get().getDiscoveryStatus().equals(DiscoveryStatus.IGNORED) && isIgnored.equals(0)) {
                        String logMsg = "The host of this entity is ignored. Please undo ignore on the host first.";
                        errorMessages.put(processID, logMsg);
                        invalidEntityIDs.add("CANNOT-UNIGNORE");
                        log.error(logMsg);
                    }
                }
            }
        }
    }
}
