package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import com.appnomic.appsone.controlcenter.beans.AgentBean;
import com.appnomic.appsone.controlcenter.beans.TagDetailsBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.MasterDataService;
import com.appnomic.appsone.controlcenter.exceptions.CommandException;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AgentModeConfigBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandArgumentBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ServiceCommandArgument;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.service.QueuePublisher;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentModeConfigDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CommandDataService;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.google.common.base.Throwables;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

import static com.appnomic.appsone.controlcenter.common.Constants.*;

/**
 * <AUTHOR> Prasad on 19/12/19
 */
public class AgentCommands {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentCommands.class);
    private static final String SNAPSHOT_COUNT = "snapshot-count";
    private static final String SNAPSHOT_DURATION = "snapshot-duration";
    private static final String IS_EXCEPTION_ENABLED = "exceptions";
    private static final String SILENT_WINDOW = "silent-window";
    private static final String JVM_CPU = "jvm_cpu";
    private static final String JVM_MEM = "jvm_mem";
    private static final String COLLECTION_MODE = "collection-mode";


    private Map<String, String> getCommandArguments(AgentCommand agentCommand, String commandName) {
        Map<String, String> map = new HashMap<>();

        AgentCommandArguments commandArguments = agentCommand.getArguments();
        if(AGENT_MODE_AUTO.equalsIgnoreCase(commandName)) {
            map.put(JVM_CPU, String.valueOf(commandArguments.getJvmCpuUtil()));
            map.put(JVM_MEM, String.valueOf(commandArguments.getJvmMemUtil()));
            map.put(COLLECTION_MODE, String.valueOf(commandArguments.getAutoSnapshotCollectionLevel()));
            map.put(SNAPSHOT_COUNT, String.valueOf(commandArguments.getAutoSnapshotCount()));
            map.put(SNAPSHOT_DURATION, String.valueOf(commandArguments.getAutoSnapshotDuration()));
            map.put(IS_EXCEPTION_ENABLED, String.valueOf(commandArguments.isAutoSnapshotForException()));
            map.put(SILENT_WINDOW, String.valueOf(commandArguments.getAutoSnapshotSilentWindow()));
        } else if(AGENT_MODE_VERBOSE.equalsIgnoreCase(commandName)) {
            map.put(SNAPSHOT_COUNT, String.valueOf(commandArguments.getVerboseSnapshotCount()));
            map.put(SNAPSHOT_DURATION, String.valueOf(commandArguments.getVerboseSnapshotDuration()));
            map.put(SILENT_WINDOW, String.valueOf(commandArguments.getVerboseSnapshotSilentWindow()));
        }
        return map;
    }

    public static CommandDetailsBean getSelectedCommand(String agentType, String commandName) throws CommandException {

        List<CommandDetailsBean> commandDetails = getCommandsByAgentType(agentType);
        if(commandDetails.isEmpty()) {
            LOGGER.error("No commands found to process the request. commandName:{}, agentType:{}", commandName, agentType);
            return null;
        }

        CommandDetailsBean commandSelected = null;

        if (commandName != null) {
            commandSelected = commandDetails.stream()
                    .filter(c -> c.getName().equalsIgnoreCase(commandName))
                    .findAny()
                    .orElse(null);
        }

        if(commandSelected == null) {
            commandSelected = commandDetails.stream()
                    .filter(c -> c.getIsDefault() == 1)
                    .findAny()
                    .orElse(null);
        }
        return commandSelected;

    }
    public void process(AgentCommand agentCommand, String userId, int serviceId, String agentType, int accountId) throws CommandException {
        ViewTypes jimAgentType = MasterCache.getMstTypeForSubTypeName(Constants.AGENT_TYPE, agentType);
        AgentModeConfigBean configBean = AgentModeConfigDataService.getAgentModeConfig(serviceId,
                accountId, jimAgentType.getSubTypeId(), null);

        CommandDetailsBean commandSelected = getSelectedCommand(agentType, agentCommand.getCommandMode());
        if(commandSelected == null) {
            LOGGER.error("Default/Selected command not found to process the request. {}, {}", agentCommand, agentType);
            throw new CommandException("Default/Selected command not found to process the request.");
        }

        saveCommandToDB(agentCommand, userId,accountId, serviceId, agentType, configBean, jimAgentType.getSubTypeId(), commandSelected);

        List<String> jimAgentIdList = getJimAgents(serviceId, accountId, jimAgentType.getSubTypeId());

        if(jimAgentIdList.isEmpty()){
            LOGGER.warn("No jim agent is setup for this service hence not sending the data to MQ");
            return;
        }

        List<CommandArgumentBean> serviceCmdArgs = AgentModeConfigDataService.getServiceCommandArguments(serviceId,
                jimAgentType.getSubTypeId(), commandSelected.getId(), null);
        for(int i=0;i< serviceCmdArgs.size();i++){
            if(serviceCmdArgs.get(i).getArgumentKey().equalsIgnoreCase("switch-mode")){
                serviceCmdArgs.remove(i);
                break;
            }
        }

        sendCommandDetails(commandSelected, serviceCmdArgs, jimAgentType.getSubTypeId(), jimAgentIdList);
    }

    private void saveCommandToDB(AgentCommand agentCommand, String userId,int accountId, int serviceId, String agentType, AgentModeConfigBean configBean, int agentTypeID, CommandDetailsBean commandDetailsBean) throws CommandException {
        ViewTypes jimAgentType = MasterCache.getMstTypeForSubTypeName(Constants.AGENT_TYPE, agentType);

        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        try {
            dbi.inTransaction((handle, status) -> {
                if(agentCommand.getCommandMode().equalsIgnoreCase("Auto")) {
                    AgentSnapshotLevelDetails agentSnapshotLevelDetails = AgentModeConfigDataService.
                            getAgentSnapshotDetails((agentCommand.getArguments().getAutoSnapshotCollectionLevel()),handle);
                    agentCommand.getArguments().setAutoSnapshotCount(agentSnapshotLevelDetails.getSnapshotCount());
                    agentCommand.getArguments().setAutoSnapshotDuration(agentSnapshotLevelDetails.getSnapshotDuration());
                    agentCommand.getArguments().setAutoSnapshotSilentWindow(agentSnapshotLevelDetails.getSilentWindow());
                }
                if (configBean != null) {
                    configBean.setCommandId(commandDetailsBean.getId());
                    configBean.setUpdatedTime(DateTimeUtil.getCurrentTimestampInGMT());
                    AgentModeConfigDataService.updateAgentModeConfig(configBean, handle);
                } else {
                    AgentModeConfigBean configBean1 = new AgentModeConfigBean();
                    configBean1.setServiceId(serviceId);
                    configBean1.setAgentTypeId(agentTypeID);
                    configBean1.setUserDetailsId(userId);
                    configBean1.setAccountId(accountId);
                    configBean1.setCommandId(commandDetailsBean.getId());
                    configBean1.setCreatedTime(DateTimeUtil.getCurrentTimestampInGMT());
                    configBean1.setUpdatedTime(DateTimeUtil.getCurrentTimestampInGMT());
                    AgentModeConfigDataService.addAgentModeConfig(configBean1, handle);
                }

                updateCommandArguments(agentCommand, jimAgentType, serviceId, userId, handle);
                return "nothing to return";
            });

        } catch (Exception e){
            if(Throwables.getRootCause(e) instanceof CommandException){
                throw (CommandException) Throwables.getRootCause(e);
            }else{
                throw e;
            }
        }
    }

    private void updateCommandArguments(AgentCommand agentCommand, ViewTypes jimAgentType, int serviceId, String userId, Handle handle) throws CommandException {
        List<CommandDetailsBean> commandDetails = CommandDataService.getCommandDetailsByAgentType(jimAgentType.getSubTypeId(), null);

        for (CommandDetailsBean cmdArgBean : commandDetails) {
            Map<String, String> argumentMap = getCommandArguments(agentCommand, cmdArgBean.getName());
            List<CommandArgumentBean> serviceCmdArgs = AgentModeConfigDataService.getServiceCommandArguments(serviceId,
                    jimAgentType.getSubTypeId(), cmdArgBean.getId(), handle);
            for(int i=0;i< serviceCmdArgs.size();i++){
                if(serviceCmdArgs.get(i).getArgumentKey().equalsIgnoreCase("switch-mode")){
                       serviceCmdArgs.remove(i);
                       break;
                }
            }

            List<ServiceCommandArgument> updateCmdArgs = new ArrayList<>();
            List<ServiceCommandArgument> insertCmdArgs = new ArrayList<>();
            for (CommandArgumentBean svcCmdArg : serviceCmdArgs) {
                if (null != svcCmdArg.getValue()) {
                    updateCmdArgs.add(ServiceCommandArgument
                            .builder()
                            .id(svcCmdArg.getId())
                            .serviceId(serviceId)
                            .agentTypeId(jimAgentType.getSubTypeId())
                            .argumentValue(argumentMap.get(svcCmdArg.getArgumentKey()))
                            .userDetailsId(userId)
                            .updatedTime(DateTimeUtil.getCurrentTimestampInGMT())
                            .build());
                } else{
                    insertCmdArgs.add(ServiceCommandArgument
                            .builder()
                            .commandArgumentId(svcCmdArg.getId())
                            .serviceId(serviceId)
                            .agentTypeId(jimAgentType.getSubTypeId())
                            .commandId(cmdArgBean.getId())
                            .argumentValue(argumentMap.get(svcCmdArg.getArgumentKey()))
                            .userDetailsId(userId)
                            .updatedTime(DateTimeUtil.getCurrentTimestampInGMT())
                            .createdTime(DateTimeUtil.getCurrentTimestampInGMT())
                            .build());
                }
            }

            if (!updateCmdArgs.isEmpty()) {
                AgentModeConfigDataService.updateServiceCommandArguments(updateCmdArgs, handle);
            }

            if (!insertCmdArgs.isEmpty()) {
                AgentModeConfigDataService.addServiceCommandArguments(insertCmdArgs, handle);
            }

        }
    }

    private void sendCommandDetails(CommandDetailsBean commandDetailsBean, List<CommandArgumentBean> serviceCmdArgs,
                                           int agentTypeId, List<String> jimAgentIdList) {

        Map<String, String> argumentsMap = new HashMap<>();
        Map<String, String> envArgs = new HashMap<>();

        Map<String, String> commandArguments = serviceCmdArgs.stream().collect(Collectors.toMap(CommandArgumentBean::getArgumentKey,
                CommandArgumentBean::getValue));


        argumentsMap.put("AgentMode", commandDetailsBean.getName());
        argumentsMap.put("Command", commandDetailsBean.getName());
        if (AGENT_MODE_VERBOSE.equalsIgnoreCase(commandDetailsBean.getName())) {
            argumentsMap.put("SnapshotCollectionDuration", commandArguments.get(SNAPSHOT_DURATION));
            argumentsMap.put("SnapshotsPerMin", commandArguments.get(SNAPSHOT_COUNT));
            argumentsMap.put("SilentWindow", commandArguments.getOrDefault(SILENT_WINDOW,Constants.AGENT_SIILENT_WINDOW));
        } else if (AGENT_MODE_AUTO.equalsIgnoreCase(commandDetailsBean.getName())) {
            argumentsMap.put("SnapshotCollectionDuration", commandArguments.get(SNAPSHOT_DURATION));
            argumentsMap.put("SnapshotsPerMin", commandArguments.get(SNAPSHOT_COUNT));
            argumentsMap.put("SilentWindow", commandArguments.getOrDefault(SILENT_WINDOW,Constants.AGENT_SIILENT_WINDOW));

            envArgs.put("JVMCPUUtilizationThreshold", commandArguments.get(JVM_CPU));
            envArgs.put("JVMMemUtilizationThreshold", commandArguments.get(JVM_MEM));
            envArgs.put("SnapshotsCollectionForFailures", commandArguments.get(IS_EXCEPTION_ENABLED));
        }

        String agentType = MasterCache.getMstSubTypeForSubTypeId(agentTypeId).getSubTypeName();
        pushCommandRequestToQueue(commandDetailsBean, argumentsMap, envArgs, jimAgentIdList, agentType, "dummy");

    }

    public void pushCommandRequestToQueue(CommandDetailsBean commandDetailsBean, Map<String, String> commandArgsInMap,
                                          Map<String, String> commandEnvInMap, List<String> superviserIdentifiers,
                                          String agentType, String agentIdentifier) {

        String commandType =
                MasterCache.getMstTypeById(commandDetailsBean.getCommandTypeId()).getTypeName();
        String commandOutputType =
                MasterCache.getMstSubTypeForSubTypeId(commandDetailsBean.getOutputTypeId()).getSubTypeName();

        CommandRequestProtos.Command command =
                CommandRequestProtos.Command.newBuilder()
                        .setCommandJobId(String.valueOf(UUID.randomUUID()))
                        .setCommand(commandDetailsBean.getName())
                        .setCommandType(commandType)
                        .setCommandOutputType(commandOutputType)
                        .setCommandExecType("Execute")
                        .setRetryNumber(3)
                        .setCommandTimeout(commandDetailsBean.getTimeOutInSecs())
                        .setSupervisorCtrlTTL(300)
                        .putAllArguments(commandArgsInMap)
                        .putAllEnvArgs(commandEnvInMap)
                        .build();

        CommandRequestProtos.CommandRequest commandDetails =
                CommandRequestProtos.CommandRequest.newBuilder().addAllSupervisorIdentifiers(superviserIdentifiers)
                        .setAgentType(agentType)
                        .setAgentIdentifier(agentIdentifier)
                        .setTriggerSource("ControlCenter")
                        .setUserDetailsID(commandDetailsBean.getUserDetails())
                        .setTriggerTime(new Date().getTime())
                        .setViolationTime(new Date().getTime())
                        .addCommands(command)
                        .build();

        QueuePublisher.sendAgentCommandMessage(commandDetails);
    }

    public static List<String> getJimAgents(int serviceId, int accountId, int jimAgentTypeId){
        int tagId = MasterDataService.getTagDetailsForAccount(accountId).stream()
                .filter(tagDetailsBean -> tagDetailsBean.getName().equalsIgnoreCase(Constants.CONTROLLER_TAG))
                .findFirst()
                .map(TagDetailsBean::getId)
                .orElse(0);

        Set<Integer> agentIds = MasterDataService.getTagMappingDetails(accountId).stream()
                .filter(tag -> tag.getTagId() == tagId &&
                        tag.getObjectRefTable().equalsIgnoreCase(Constants.AGENT_TABLE) &&
                        tag.getTagKey().equalsIgnoreCase(serviceId + ""))
                .map(TagMappingDetails::getObjectId)
                .collect(Collectors.toSet());

        List<AgentBean> agentBeans = MasterCache.getAgentList("agent").stream()
                .filter(agentBean -> agentIds.contains(agentBean.getId())
                        && agentBean.getAgentTypeId() == jimAgentTypeId)
                .collect(Collectors.toList());

        return agentBeans.stream().map(AgentBean::getUniqueToken).collect(Collectors.toList());
    }

    private static List<CommandDetailsBean> getCommandsByAgentType(String agentType) throws CommandException {

        ViewTypes jimAgentType = MasterCache.getMstTypeForSubTypeName(Constants.AGENT_TYPE, agentType);

        List<CommandDetailsBean> commandDetails = CommandDataService.getCommandDetailsByAgentType(jimAgentType.getSubTypeId(), null);
        if(commandDetails == null || commandDetails.isEmpty()) {
            LOGGER.error("No commands found to process the request. agentType:{}", agentType);
            throw new CommandException("No commands found to process the request.");
        }

        return commandDetails;

    }

    public AgentCommand getAgentCommand(int serviceId, int accountId, String agentType) throws CommandException {
        AgentCommand agentCommand = new AgentCommand();
        ViewTypes jimAgentType = MasterCache.getMstTypeForSubTypeName(Constants.AGENT_TYPE, agentType);

        List<CommandDetailsBean> commandDetails = getCommandsByAgentType(agentType);
        if(commandDetails.isEmpty()) {
            LOGGER.error("No commands found to process the request, agentType:{}", agentType);
            throw  new CommandException("No commands found to process the request.");
        }

        AgentCommandArguments agentCommandArguments = new AgentCommandArguments();
        for (CommandDetailsBean command : commandDetails) {
            List<CommandArgumentBean> serviceCmdArgs = AgentModeConfigDataService.getServiceCommandArguments(serviceId,
                    jimAgentType.getSubTypeId(), command.getId(), null);

            Map<String, String> cmdsMap = serviceCmdArgs.stream()
                    .collect(Collectors.toMap(CommandArgumentBean::getArgumentKey, CommandArgumentBean::getDefaultValue));
            getAgentCommandArgs(agentCommandArguments, cmdsMap, command.getName());
        }


        AgentModeConfigBean configBean = AgentModeConfigDataService.getAgentModeConfig(serviceId, accountId,
                jimAgentType.getSubTypeId(), null);

        CommandDetailsBean commandBean = null;
        if(configBean == null) {
            commandBean = getSelectedCommand(agentType, null);
        } else {
            commandBean = commandDetails.stream()
                    .filter(c -> c.getId() == configBean.getCommandId())
                    .findAny()
                    .orElse(null);
            agentCommand.setLastUpdatedTime(configBean.getUpdatedTime());
        }

        if (commandBean == null) {
            agentCommand.setCommandMode(AGENT_MODE_AUTO);
            agentCommand.setCommandName(agentType);
        } else {
            agentCommand.setCommandMode(commandBean.getName());
            agentCommand.setCommandName(agentType);
            agentCommand.setIdentifier("JIMAgentIdentifier");
        }

        agentCommand.setServerTime(DateTimeUtil.getCurrentTimestampInGMT());
        agentCommand.setArguments(agentCommandArguments);
        return agentCommand;
    }

    private void getAgentCommandArgs(AgentCommandArguments agentCommandArguments, Map<String, String> cmdArgsMap, String commandName) {

        if(AGENT_MODE_AUTO.equalsIgnoreCase(commandName)) {
            agentCommandArguments.setAutoSnapshotDuration(Integer.valueOf(cmdArgsMap.get(SNAPSHOT_DURATION)));
            agentCommandArguments.setAutoSnapshotCount(Integer.valueOf(cmdArgsMap.get(SNAPSHOT_COUNT)));
            agentCommandArguments.setAutoSnapshotForException(Boolean.valueOf(cmdArgsMap.get(IS_EXCEPTION_ENABLED)));
            agentCommandArguments.setAutoSnapshotSilentWindow(Integer.valueOf(cmdArgsMap.getOrDefault(SILENT_WINDOW,Constants.AGENT_SIILENT_WINDOW)));
            agentCommandArguments.setJvmCpuUtil(Integer.valueOf(cmdArgsMap.get(JVM_CPU)));
            agentCommandArguments.setJvmMemUtil(Integer.valueOf(cmdArgsMap.get(JVM_MEM)));
            agentCommandArguments.setAutoSnapshotCollectionLevel(cmdArgsMap.get(COLLECTION_MODE));
        } else if(AGENT_MODE_VERBOSE.equalsIgnoreCase(commandName)) {
            agentCommandArguments.setVerboseSnapshotCount(Integer.valueOf(cmdArgsMap.get(SNAPSHOT_COUNT)));
            agentCommandArguments.setVerboseSnapshotDuration(Integer.valueOf(cmdArgsMap.get(SNAPSHOT_DURATION)));
            agentCommandArguments.setVerboseSnapshotSilentWindow(Integer.valueOf(cmdArgsMap.getOrDefault(SILENT_WINDOW,Constants.AGENT_SIILENT_WINDOW)));
        }
    }
}
