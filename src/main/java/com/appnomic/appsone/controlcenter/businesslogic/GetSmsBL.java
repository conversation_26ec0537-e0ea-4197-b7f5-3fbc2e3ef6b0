package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.NotificationDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.SMSDetailsBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.SmsDetails;
import com.appnomic.appsone.controlcenter.util.AECSBouncyCastleUtil;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GetSmsBL implements BusinessLogic<Integer, Integer, SmsDetails> {
    private static final Logger LOGGER = LoggerFactory.getLogger(GetSmsBL.class);

    @Override
    public UtilityBean<Integer> clientValidation(RequestObject requestObject) throws ClientException {
        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authKey)) {
            authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION.toLowerCase());
            if (StringUtils.isEmpty(authKey)) {
                LOGGER.error(UIMessages.AUTH_KEY_EMPTY);
                throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
            }
        }
        return UtilityBean.<Integer>builder().accountIdentifier(identifier).authToken(authKey).build();
    }

    @Override
    public Integer serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            LOGGER.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }
        return account.getId();
    }

    @Override
    public SmsDetails process(Integer accountId) throws DataProcessingException {
        SMSDetailsBean smsDetails = new NotificationDataService().getSMSDetails(accountId, null);
        if (smsDetails != null) {
            String plainTxt = "";
            try {
                if(smsDetails.getPassword() != null && !smsDetails.getPassword().trim().isEmpty()){
                    plainTxt = CommonUtils.decryptInBCEC(smsDetails.getPassword());
                    smsDetails.setPassword(new AECSBouncyCastleUtil().encrypt(plainTxt));
                }
                return CommonUtils.getSMSDetails(smsDetails);
            } catch (Exception ex){
                LOGGER.error("Exception encountered while decrypting/encrypting the password. AccountId: {}, smsDetails:{}", accountId, smsDetails, ex);
                throw new DataProcessingException(ex, "Exception encountered while decrypting/encrypting the password. AccountId: "+accountId+", smsDetails:"+ smsDetails);
            }
        }
        return null;
    }
}

