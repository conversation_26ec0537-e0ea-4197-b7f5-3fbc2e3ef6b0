package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.CronExpressionUtil;
import com.appnomic.appsone.controlcenter.dao.mysql.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ApplicationPercentilesBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.DefaultNotificationPreferences;
import com.appnomic.appsone.controlcenter.dao.redis.AccountRepo;
import com.appnomic.appsone.controlcenter.dao.redis.MasterDataRepo;
import com.appnomic.appsone.controlcenter.dao.redis.SchedulersRedisRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.pojo.TimezoneDetail;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.heal.configuration.entities.ScheduledJobArguments;
import com.heal.configuration.entities.ScheduledJobDetails;
import com.heal.configuration.entities.SchedulerArguments;
import com.heal.configuration.entities.SchedulerDetails;
import com.heal.configuration.pojos.*;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


public class ControllerBL {

    private static final Logger logger = LoggerFactory.getLogger(ControllerBL.class);
    private static final ControllerDataService CONTROLLER_DATA_SERVICE = new ControllerDataService();

    private static final int SERVICE_START_HOUR = ConfProperties.getInt(Constants.SERVICE_START_WITHIN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_START_TIME_WITHIN_AN_HOUR);
    private static final int SERVICE_END_HOUR = ConfProperties.getInt(Constants.SERVICE_END_WITHIN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_END_TIME_WITHIN_AN_HOUR);
    private static final int SERVICE_START = ConfProperties.getInt(Constants.SERVICE_START_MORE_THAN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_START_TIME_MORE_THAN_AN_HOUR);
    private static final int SERVICE_END = ConfProperties.getInt(Constants.SERVICE_END_MORE_THAN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_END_TIME_MORE_THAN_AN_HOUR);


    private static final int SOR_PERSISTENCE_HOUR = ConfProperties.getInt(Constants.SERVICE_SOR_PERSISTENCE_WITHIN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_SOR_PERSISTENCE_WITHIN_AN_HOUR);
    private static final int SOR_SUPPRESSION_HOUR = ConfProperties.getInt(Constants.SERVICE_SOR_SUPPRESSION_WITHIN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_SOR_SUPPRESSION_WITHIN_AN_HOUR);
    private static final int SOR_PERSISTENCE = ConfProperties.getInt(Constants.SERVICE_SOR_PERSISTENCE_MORE_THAN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_SOR_PERSISTENCE_MORE_THAN_AN_HOUR);
    private static final int SOR_SUPPRESSION = ConfProperties.getInt(Constants.SERVICE_SOR_SUPPRESSION_MORE_THAN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_SOR_SUPPRESSION_MORE_THAN_AN_HOUR);

    private static final int NOR_PERSISTENCE_HOUR = ConfProperties.getInt(Constants.SERVICE_NOR_PERSISTENCE_WITHIN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_NOR_PERSISTENCE_WITHIN_AN_HOUR);
    private static final int NOR_SUPPRESSION_HOUR = ConfProperties.getInt(Constants.SERVICE_NOR_SUPPRESSION_WITHIN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_NOR_SUPPRESSION_WITHIN_AN_HOUR);
    private static final int NOR_PERSISTENCE = ConfProperties.getInt(Constants.SERVICE_NOR_PERSISTENCE_MORE_THAN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_NOR_PERSISTENCE_MORE_THAN_AN_HOUR);
    private static final int NOR_SUPPRESSION = ConfProperties.getInt(Constants.SERVICE_NOR_SUPPRESSION_MORE_THAN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_NOR_SUPPRESSION_MORE_THAN_AN_HOUR);


    private static final int SERVICE_TRANSACTION_MIN_VOLUME_COUNT = ConfProperties.getInt(Constants.DEFAULT_MIN_REQ_COUNT, Constants.DEFAULT_MIN_REQ_COUNT_DEFAULT_VAL);
    private static final int SERVICE_MAX_AUTO_ACCEPTED_REQS_LIMIT = ConfProperties.getInt(Constants.DEFAULT_MAX_AUTO_ACCEPTED_REQS, Constants.DEFAULT_MAX_AUTO_ACCEPTED_REQS_DEFAULT_VAL);
    private static final int SERVICE_TRANSACTION_DEFAULT_AUTO_COMMIT_DURATION = ConfProperties.getInt(Constants.DEFAULT_HOLD_DURATION, Constants.DEFAULT_HOLD_DURATION_DEFAULT_VAL);

    public static ControllerBean addController(ServiceBean bean, int controllerTypeId, int isUpdate, Handle handle) {
        int id;

        String name = bean.getName();
        String identifier = bean.getIdentifier();
        int accountId = bean.getAccountId();
        String userId = bean.getUserId();
        String layer = bean.getLayer();
        List<Integer> appIds = bean.getAppIds();
        Service.ServiceBuilder<?, ?> serviceConfiguration = Service.builder();
        List<Tags> tags = new ArrayList<>();
        String currentTime = String.valueOf(DateTimeUtil.getCurrentTimestampInGMT());

        ControllerBean controllerBean = new ControllerBean();
        controllerBean.setName(name);
        controllerBean.setIdentifier(identifier);
        controllerBean.setAccountId(accountId);
        controllerBean.setUserDetailsId(userId);
        controllerBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        controllerBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        controllerBean.setControllerTypeId(controllerTypeId);
        controllerBean.setStatus(bean.getStatus());

        ViewTypes applicationType = MasterCache.
                getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT, Constants.APPLICATION_CONTROLLER_TYPE);

        try {
            if (isUpdate == 0) {
                id = CONTROLLER_DATA_SERVICE.addController(controllerBean, handle);
            } else {
                id = CONTROLLER_DATA_SERVICE.updateController(controllerBean, handle);
            }
            controllerBean.setId(id);
            if (id < 0) {
                logger.error("Failed to add controller name:{} identifier:{} for acc id:{}", name, identifier, accountId);
                return controllerBean;
            } else {
                logger.debug("Successfully created/updated controller id:{}", id);
            }

            ViewTypes subType = MasterCache.getMstTypeForSubTypeName(
                    Constants.CONTROLLER_TYPE_NAME_DEFAULT, Constants.SERVICES_CONTROLLER_TYPE);

            if (isUpdate == 0) {
                if (controllerBean.getControllerTypeId() == subType.getSubTypeId()) {
                    int[] svcConfigId = ServiceConfigurationBL.addServiceConfiguration(id, accountId, userId, handle);

                    if (svcConfigId == null) {
                        logger.error("Failed to add service configuration for service name:{} identifier:{}  acc id:{}", name, identifier, accountId);
                        controllerBean.setId(-1);
                        return controllerBean;
                    } else {
                        logger.debug("Successfully created service config ids: {} {} for service id:{}", svcConfigId[0], svcConfigId[1], id);
                    }

                    // Add new entry into Scheduler_Details table
                    MasterDataRepo masterDataRepo = new MasterDataRepo();
                    com.heal.configuration.pojos.ViewTypes schedulerRecurringViewType = masterDataRepo.getSubType(Constants.VIEW_TYPE_TYPE_NAME_SCHEDULER_TYPE, Constants.MST_SUB_TYPE_RECURRING);
                    com.heal.configuration.pojos.ViewTypes jobTypeTransactionCommitViewType = masterDataRepo.getSubType(Constants.VIEW_TYPE_TYPE_NAME_JOB_TYPES, Constants.VIEW_TYPE_SUB_TYPE_NAME_TRANSACTION_COMMIT);
                    com.heal.configuration.pojos.ViewTypes jobTypeRMQViewType = masterDataRepo.getSubType(Constants.VIEW_TYPE_TYPE_NAME_SINK_TYPE, Constants.VIEW_TYPE_SUBTYPE_NAME_RMQ);

                    String cron_expression = CronExpressionUtil.generateCronExpression(SERVICE_TRANSACTION_DEFAULT_AUTO_COMMIT_DURATION, DateTimeUtil.getHourTime(currentTime));
                    SchedulerDetails schedulerDetails = SchedulerDetails.builder()
                            .name(name + " " + Constants.DEFAULT_SERVICE_SCHEDULER_NAME)
                            .typeId(schedulerRecurringViewType.getSubTypeId())
                            .sinkTypeId(jobTypeRMQViewType.getSubTypeId())
                            .startTime(currentTime)
                            .cronExpression(cron_expression)
                            .createdTime(currentTime)
                            .updatedTime(currentTime)
                            .userDetailsId(userId)
                            .accountId(accountId)
                            .status(1)
                            .build();


                    // Updating Scheduler Details to percona
                    SchedulersDataService schedulersDataService = new SchedulersDataService();
                    int schedulerDetailsId = schedulersDataService.insertSchedulerDetails(schedulerDetails, handle);
                    schedulerDetails.setId(schedulerDetailsId);


                    // Add Entry in Scheduler_Job_Details Table
                    ScheduledJobDetails scheduledJobDetails = ScheduledJobDetails.builder()
                            .name(name + " " + Constants.DEFAULT_SERVICE_SCHEDULER_NAME)
                            .schedulerId(schedulerDetailsId)
                            .implementationId(jobTypeTransactionCommitViewType.getSubTypeId())
                            .createdTime(currentTime)
                            .updatedTime(currentTime)
                            .userDetailsId(userId)
                            .status(1)
                            .jobStatus(Constants.DEFAULT_SCHEDULER_JOB_STATUS)
                            .build();

                    // Percona update
                    int schedulerJobId = schedulersDataService.insertSchedulerJobDetails(scheduledJobDetails, handle);

                    // Add Entry in Scheduler_Arguments Table
                    SchedulerArguments schedulerArguments = SchedulerArguments.builder()
                            .argumentName(Constants.SCHEDULER_ARGUMENT_NAME)
                            .argumentValue(Constants.SCHEDULER_ARGUMENT_VALUE)
                            .defaultValue(Constants.SCHEDULER_ARGUMENT_DEFAULT_VALUE)
                            .placeholder(0)
                            .schedulerId(schedulerDetailsId)
                            .createdTime(currentTime)
                            .updatedTime(currentTime)
                            .userDetailsId(userId)
                            .build();

                    // Percona update
                    schedulersDataService.insertSchedulerArguments(schedulerArguments, handle);

                    // Add Entry in Scheduler_Job_Arguments Table
                    ScheduledJobArguments scheduledJobArguments = ScheduledJobArguments.builder()
                            .argumentName(Constants.SCHEDULER_JOB_ARGUMENT_DEFAULT_ARGUMENT_NAME)
                            .argumentValue(bean.getIdentifier())
                            .defaultValue(bean.getIdentifier())
                            .placeholder(0)
                            .schedulerJobId(schedulerJobId)
                            .createdTime(currentTime)
                            .updatedTime(currentTime)
                            .userDetailsId(userId)
                            .build();


                    // Percona update
                    schedulersDataService.insertSchedulerJobArguments(scheduledJobArguments, handle);

                    // Adding ScheduledJobDetails to SchedulerDetails
                    List<ScheduledJobDetails> scheduledJobDetailsList = schedulerDetails.getScheduledJobDetails();
                    if (scheduledJobDetailsList == null) {
                        scheduledJobDetailsList = new ArrayList<>();
                    }
                    scheduledJobDetailsList.add(scheduledJobDetails);
                    schedulerDetails.setScheduledJobDetails(scheduledJobDetailsList);

                    // Adding SchedulerArguments to SchedulerDetails
                    List<SchedulerArguments> schedulerArgumentsList = schedulerDetails.getSchedulerArguments();
                    if (schedulerArgumentsList == null) {
                        schedulerArgumentsList = new ArrayList<>();
                    }
                    schedulerArgumentsList.add(schedulerArguments);
                    schedulerDetails.setSchedulerArguments(schedulerArgumentsList);

                    // Updating redis key
                    SchedulersRedisRepo schedulersRedisRepo = new SchedulersRedisRepo();
                    List<SchedulerDetails> existingSchedulerDetailsList = schedulersRedisRepo.getSchedulerDetails(bean.getAccountIdentifier());
                    if (existingSchedulerDetailsList.isEmpty()) {
                        existingSchedulerDetailsList = new ArrayList<>();
                    }
                    existingSchedulerDetailsList.add(schedulerDetails);
                    schedulersRedisRepo.updateSchedulerDetails(existingSchedulerDetailsList, bean.getAccountIdentifier());
                    logger.info("Scheduler details updated for the Redis key accounts/{}/schedulers for account: {}", bean.getAccountIdentifier(), accountId);

                    ServiceTransactionSettingBean serviceTransactionSettingBean = ServiceTransactionSettingBean.builder()
                            .accountId(accountId)
                            .serviceId(controllerBean.getId())
                            .lastCommitedTime(currentTime)
                            .lastCommitedTransactions(0)
                            .maxVolumeCount(SERVICE_TRANSACTION_MIN_VOLUME_COUNT)
                            .maxTransactionsLimit(SERVICE_MAX_AUTO_ACCEPTED_REQS_LIMIT)
                            .createdTime(currentTime)
                            .updatedTime(currentTime)
                            .userDetailsId(userId)
                            .autoCommitDuration(SERVICE_TRANSACTION_DEFAULT_AUTO_COMMIT_DURATION)
                            .schedulerDetailsId(schedulerDetailsId)
                            .build();

                    // Percona Call
                    ServiceDataService serviceDataService = new ServiceDataService();
                    serviceDataService.insertServiceTransactionDetails(serviceTransactionSettingBean, handle);

                    int[] rulesId = RulesBL.addRule(id, identifier, accountId, userId, handle);
                    if (rulesId == null) {
                        logger.error("Failed to add rules for service name:{} identifier:{}  acc id:{}", name, identifier, accountId);
                        controllerBean.setId(-1);
                        return controllerBean;
                    } else {
                        logger.debug("Successfully created rules ids: {} {} for service id:{}", rulesId[0], rulesId[1], id);
                    }

                    if (!StringUtils.isEmpty(layer)) {
                        TagDetailsBean tagDetailsBean = MasterCache.getTagDetails(Constants.LAYER_TAG);
                        if (tagDetailsBean == null) {
                            logger.error("Tag detail is not found for given tag name-" + Constants.LAYER_TAG + " and account id-" + accountId);
                            controllerBean.setId(-1);
                            return controllerBean;
                        }
                        int tagMappingId = TagMappingBL.addTagMapping(tagDetailsBean.getId(), id, Constants.CONTROLLER,
                                Constants.LAYER_DEFAULT, layer, userId, accountId, handle);
                        tags.add(Tags.builder()
                                .key(Constants.LAYER_DEFAULT)
                                .value(layer)
                                .type(Constants.LAYER_TAG)
                                .build());

                        if (tagMappingId != -1) {
                            logger.info("Tag mapping data is added successfully for layer :{}", layer);
                        } else {
                            logger.error("Failed to add the Tag mapping data for layer :{}", layer);
                            controllerBean.setId(-1);
                            return controllerBean;
                        }
                    }

                    if (appIds != null && !appIds.isEmpty()) {
                        TagDetailsBean tagDetailsBean = MasterCache.getTagDetails(Constants.CONTROLLER_TAG);
                        if (tagDetailsBean == null) {
                            logger.error("Tag detail is not found for given tag name-" + Constants.CONTROLLER_TAG + " and account id-" + accountId);
                            controllerBean.setId(-1);
                            return controllerBean;
                        }
                        for (int appId : appIds) {
                            int tagMappingId = TagMappingBL.addTagMapping(tagDetailsBean.getId(), appId, Constants.CONTROLLER,
                                    String.valueOf(id), identifier, userId, accountId, handle);
                            if (tagMappingId != -1) {
                                logger.info("Tag mapping data is added successfully for {}", identifier);
                                tags.add(Tags.builder()
                                        .key(String.valueOf(id))
                                        .value(identifier)
                                        .type(Constants.CONTROLLER_TAG)
                                        .build());
                            } else {
                                logger.error("Failed to add the Tag mapping data for {}", identifier);
                                controllerBean.setId(-1);
                                return controllerBean;
                            }
                        }
                    }

                    if (!StringUtils.isEmpty(bean.getType())) {
                        String appIdentifier = bean.getAppIdentifier();
                        int appId = bean.getAppId();

                        TagDetailsBean controllerTagDetailsBean = MasterCache.getTagDetails(Constants.CONTROLLER_TAG);
                        if (controllerTagDetailsBean == null) {
                            logger.error("Tag detail is not found for given tag name-" + Constants.CONTROLLER_TAG + " and account id-" + accountId);
                            controllerBean.setId(-1);
                            return controllerBean;
                        }
                        if (!StringUtils.isEmpty(appIdentifier)) {
                            int tagMappingId = TagMappingBL.addTagMapping(controllerTagDetailsBean.getId(), appId, Constants.CONTROLLER,
                                    String.valueOf(id), identifier, userId, accountId, handle);
                            if (tagMappingId != -1) {
                                logger.info("Tag mapping data is added successfully for {}", identifier);
                                tags.add(Tags.builder()
                                        .key(String.valueOf(id))
                                        .value(identifier)
                                        .type(Constants.CONTROLLER_TAG)
                                        .build());
                            } else {
                                logger.error("Failed to add the Tag mapping data for {}", identifier);
                                controllerBean.setId(-1);
                                return controllerBean;
                            }
                        }

                        TagDetailsBean tagDetailsBean = MasterCache.getTagDetails(Constants.SERVICE_TYPE_TAG);
                        if (tagDetailsBean == null) {
                            logger.error("Tag detail is not found for given tag name-" + Constants.SERVICE_TYPE_TAG + " and account id-" + accountId);
                            controllerBean.setId(-1);
                            return controllerBean;
                        }
                        int tagMappingId = TagMappingBL.addTagMapping(tagDetailsBean.getId(), id, Constants.CONTROLLER,
                                Constants.SERVICE_TYPE_DEFAULT, bean.getType(), userId, accountId, handle);
                        if (tagMappingId != -1) {
                            logger.info("Tag mapping data is added successfully for type :{}", bean.getType());
                            tags.add(Tags.builder()
                                    .key(Constants.SERVICE_TYPE_DEFAULT)
                                    .value(bean.getType())
                                    .type(Constants.SERVICE_TYPE_TAG)
                                    .build());
                        } else {
                            logger.error("Failed to add the Tag mapping data for layer :{}", bean.getType());
                            controllerBean.setId(-1);
                            return controllerBean;
                        }
                    }
                    if (!StringUtils.isEmpty(bean.getTimezoneId())) {
                        TagDetailsBean tagDetailsBean = MasterCache.getTagDetails(Constants.TIME_ZONE_TAG);
                        if (tagDetailsBean == null) {
                            logger.error("Tag detail is not found for given tag name-" + Constants.TIME_ZONE_TAG);
                            controllerBean.setId(-1);
                            return controllerBean;
                        }
                        TimezoneDetail timezoneDetail = MasterDataService.getTimezonesById(bean.getTimezoneId());
                        if (null == timezoneDetail) {
                            logger.error("Invalid timezone id: {}, Failed to add the Tag mapping data for controller :{}", bean.getTimezoneId(), identifier);
                            controllerBean.setId(-1);
                            return controllerBean;
                        }
                        int tagMappingId = TagMappingBL.addTagMapping(tagDetailsBean.getId(), id, Constants.CONTROLLER,
                                String.valueOf(timezoneDetail.getId()), String.valueOf(timezoneDetail.getOffset()), userId, accountId, handle);
                        if (tagMappingId != -1) {
                            logger.info("Tag mapping data is added successfully for controller :{}", identifier);
                            tags.add(Tags.builder()
                                    .key(String.valueOf(timezoneDetail.getId()))
                                    .value(String.valueOf(timezoneDetail.getOffset()))
                                    .type(Constants.TIME_ZONE_TAG)
                                    .build());
                        } else {
                            logger.error("Failed to add the Tag mapping data for controller :{}", identifier);
                            controllerBean.setId(-1);
                            return controllerBean;
                        }
                    }
                }
                if (controllerBean.getControllerTypeId() == applicationType.getSubTypeId()) {
                    try {
                        List<DefaultNotificationPreferences> list = generateDefaultNotificationList(id, accountId, userId);
                        new NotificationPreferencesDataService().addDefaultNotificationPreferences(list, handle);
                    } catch (Exception e) {
                        logger.error("Failed to add default notification preferences for controller name:{} identifier:{} for acc id:{}", name, identifier, accountId, e);
                    }

                    List<ApplicationPercentilesBean> beanList = createApplicationPercentiles(accountId, id, userId);
                    new ApplicationPercentilesDataService().addApplicationPercentiles(beanList, handle);
                }
            }

            if (id > 0) {
                logger.info("Controller:{} created successfully for account id: {}", identifier, accountId);
                // Redis call done here......

                List<ServiceConfiguration> serviceConfigurationlist = new ArrayList<>();

                serviceConfigurationlist.add(ServiceConfiguration.builder()
                        .lastModifiedBy(userId)
                        .createdTime(currentTime)
                        .updatedTime(currentTime)
                        .startCollectionInterval(SERVICE_START_HOUR)
                        .endCollectionInterval(SERVICE_END_HOUR)
                        .sorPersistence(SOR_PERSISTENCE_HOUR)
                        .sorSuppression(SOR_SUPPRESSION_HOUR)
                        .norPersistence(NOR_PERSISTENCE_HOUR)
                        .norSuppression(NOR_SUPPRESSION_HOUR)
                        .build());

                serviceConfigurationlist.add(ServiceConfiguration.builder()
                        .lastModifiedBy(userId)
                        .createdTime(currentTime)
                        .updatedTime(currentTime)
                        .startCollectionInterval(SERVICE_START)
                        .endCollectionInterval(SERVICE_END)
                        .sorPersistence(SOR_PERSISTENCE)
                        .sorSuppression(SOR_SUPPRESSION)
                        .norPersistence(NOR_PERSISTENCE)
                        .norSuppression(NOR_SUPPRESSION)
                        .build());

                TransactionAutoAcceptance transactionAutoAcceptance = TransactionAutoAcceptance.builder()
                        .lastCommitTime(String.valueOf(DateTimeUtil.getCurrentTimestampInGMT()))
                        .lastAcceptedTxnCount(0)
                        .minVolumeCount(SERVICE_TRANSACTION_MIN_VOLUME_COUNT)
                        .maxTxnLimit(SERVICE_MAX_AUTO_ACCEPTED_REQS_LIMIT)
                        .createdTime(String.valueOf(DateTimeUtil.getCurrentTimestampInGMT()))
                        .updatedTime(String.valueOf(DateTimeUtil.getCurrentTimestampInGMT()))
                        .lastModifiedBy(userId)
                        .autoCommitDuration(SERVICE_TRANSACTION_DEFAULT_AUTO_COMMIT_DURATION)
                        .build();

                serviceConfiguration.id(controllerBean.getId())
                        .accountId(accountId)
                        .serviceConfigurations(serviceConfigurationlist)
                        .transactionAutoAcceptance(transactionAutoAcceptance)
                        .tags(tags)
                        .createdTime(String.valueOf(DateTimeUtil.getCurrentTimestampInGMT()))
                        .updatedTime(String.valueOf(DateTimeUtil.getCurrentTimestampInGMT()))
                        .lastModifiedBy(userId);

                ServiceRepo serviceRepo = new ServiceRepo();
                Account accounts = new AccountRepo().getAccounts().stream().filter(account -> account.getId() == accountId).findAny().orElse(null);

                if (accounts == null) {
                    logger.error("No account details found for accountId {}", accountId);
                    throw new DataProcessingException("No account details found for accountId");
                }
                String accountIdentifier = accounts.getIdentifier();
                serviceRepo.updateServiceConfigurationByServiceIdentifier(accountIdentifier, identifier, serviceConfiguration.build());
                logger.info("Details updated for the Redis key /accounts/{}/services/{}", accountIdentifier, identifier);
            }

        } catch (Exception e) {
            logger.error("Error occurred while adding new controller:{} for account id:{}", identifier, accountId, e);
            controllerBean.setId(-1);
        }
        return controllerBean;
    }


    public static int remController(int accountId, String identifier, String userId, Handle handle) {
        int res = CONTROLLER_DATA_SERVICE.remController(accountId, identifier, userId, handle);

        if (res < 0) {
            logger.error("Failed to remove controller identifier:{} for acc id:{}", identifier, accountId);
            return -1;
        } else
            logger.debug("Successfully removed identifier:{} for acc id:{}", identifier, accountId);

        return 0;
    }

    public static List<ApplicationPercentilesBean> createApplicationPercentiles(int accountId, int applicationId, String userId) {
        String percentileKpisSuffix = ConfProperties.getString(Constants.PERCENTILE_KPIS_IDENTIFIER_SUFFIX, Constants.PERCENTILE_KPIS_IDENTIFIER_SUFFIX_DEFAULT);
        String defaultPercentiles = ConfProperties.getString(Constants.APPLICATION_PERCENTILES_DEFAULT_CONFIGURATION, Constants.APPLICATION_PERCENTILES_DEFAULT_VALUES);
        String[] percentileKpisIdentifiers = defaultPercentiles.split(",");

        return Arrays.stream(percentileKpisIdentifiers)
                .map(percentileKpiIdentifier -> {
                    int kpiId = new KPIDataService().checkForKpiIdUsingIdentifier(percentileKpiIdentifier.trim(), null);
                    if (kpiId > 0) {
                        String[] percentileKpiIdentifierArr = percentileKpiIdentifier.trim().split("_");
                        if (percentileKpiIdentifierArr[percentileKpiIdentifierArr.length - 1].equals(percentileKpisSuffix)) {
                            return ApplicationPercentilesBean.builder()
                                    .applicationId(applicationId)
                                    .accountId(accountId)
                                    .kpiIdentifier(percentileKpiIdentifier.trim())
                                    .kpiId(kpiId)
                                    .userDetailsId(userId)
                                    .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                                    .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                                    .build();
                        } else {
                            logger.warn("Percentiles kpi {} suffix is not matching with {}.", percentileKpiIdentifier, percentileKpisSuffix);
                            return null;
                        }
                    } else {
                        logger.warn("No kpi details found for kpi identifier {} in percona.", percentileKpiIdentifier);
                        return null;
                    }
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static List<DefaultNotificationPreferences> generateDefaultNotificationList(int id, int accountId, String userId) {
        List<SignalTypeSeverityMapping> preferencesType = getDefaultPreferencesType();

        int defaultNotificationTypeId = MasterCache.getMstTypeForSubTypeName(Constants.NOTIFICATION_TYPE_LITERAL, Constants.IMMEDIATELY).getSubTypeId();

        return preferencesType.parallelStream().map(m -> {
            DefaultNotificationPreferences defaultNotificationPreferences = new DefaultNotificationPreferences();
            defaultNotificationPreferences.setApplicationId(id);

            defaultNotificationPreferences.setNotificationTypeId(defaultNotificationTypeId);
            defaultNotificationPreferences.setSignalTypeId(m.getSignalTypeId());
            defaultNotificationPreferences.setSignalSeverityId(m.getSignalSeverityId());
            defaultNotificationPreferences.setAccountId(accountId);
            defaultNotificationPreferences.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
            defaultNotificationPreferences.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
            defaultNotificationPreferences.setUserDetailsId(userId);

            return defaultNotificationPreferences;
        }).collect(Collectors.toList());
    }

    private static List<SignalTypeSeverityMapping> getDefaultPreferencesType() {
        List<SignalTypeSeverityMapping> signalTypeSeverityMappingList = new ArrayList<>();

        int problemTypeId = MasterCache.getMstTypeForSubTypeName(Constants.SIGNAL_TYPE_LITERAL, Constants.PROBLEM).getSubTypeId();
        int earlyWarningTypeId = MasterCache.getMstTypeForSubTypeName(Constants.SIGNAL_TYPE_LITERAL, Constants.EARLY_WARNING).getSubTypeId();
        int infoTypeId = MasterCache.getMstTypeForSubTypeName(Constants.SIGNAL_TYPE_LITERAL, Constants.INFO).getSubTypeId();
        int batchTypeId = MasterCache.getMstTypeForSubTypeName(Constants.SIGNAL_TYPE_LITERAL, Constants.BATCH).getSubTypeId();

        int severeTypeId = MasterCache.getMstTypeForSubTypeName(Constants.SIGNAL_SEVERITY_TYPE_LITERAL, Constants.SEVERE).getSubTypeId();
        int defaultTypeId = MasterCache.getMstTypeForSubTypeName(Constants.SIGNAL_SEVERITY_TYPE_LITERAL, Constants.DEFAULT).getSubTypeId();

        signalTypeSeverityMappingList.add(new SignalTypeSeverityMapping(problemTypeId, severeTypeId));
        signalTypeSeverityMappingList.add(new SignalTypeSeverityMapping(problemTypeId, defaultTypeId));
        signalTypeSeverityMappingList.add(new SignalTypeSeverityMapping(earlyWarningTypeId, severeTypeId));
        signalTypeSeverityMappingList.add(new SignalTypeSeverityMapping(earlyWarningTypeId, defaultTypeId));
        signalTypeSeverityMappingList.add(new SignalTypeSeverityMapping(infoTypeId, severeTypeId));
        signalTypeSeverityMappingList.add(new SignalTypeSeverityMapping(infoTypeId, defaultTypeId));
        signalTypeSeverityMappingList.add(new SignalTypeSeverityMapping(batchTypeId, severeTypeId));
        signalTypeSeverityMappingList.add(new SignalTypeSeverityMapping(batchTypeId, defaultTypeId));

        return signalTypeSeverityMappingList;
    }
}
