package com.appnomic.appsone.controlcenter.businesslogic.connectors;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.connectors.ConnectorTemplate;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import spark.Request;

import java.util.ArrayList;
import java.util.List;

import static com.appnomic.appsone.controlcenter.dao.mysql.connectors.ConnectorDetailsDataService.getConnectorTemplateConfig;

@Slf4j
public class GetConnectorsTemplateConfigBL {

    public UtilityBean<Object> clientValidation(RequestObject requestObject, Request request) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String token = request.cookie("token");
        /*String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }*/

        if (token == null || token.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty");
            throw new ClientException("Invalid account identifier");
        }

        Integer connectorId;
        try {
            connectorId = Integer.valueOf(requestObject.getParams().get(":connectorid"));
        } catch (NumberFormatException ex) {
            throw new ClientException("Invalid template id");
        }

        return UtilityBean.builder()
                .accountIdentifier(identifier)
                .authToken(token)
                .pojoObject(connectorId)
                .build();
    }

    public List<Integer> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {

        String accountIdentifier = utilityBean.getAccountIdentifier();
        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);
        String connectorId = utilityBean.getPojoObject().toString();
        List<Integer> object = new ArrayList<>();

        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        if (connectorId == null || connectorId.equals("")) {
            log.error("Connector Id is invalid");
            throw new ServerException("Connector Id is invalid");
        }
        object.add(account.getId());
        object.add(Integer.valueOf(connectorId));

        return object;
    }

    public ConnectorTemplate process(List<Integer> object) throws DataProcessingException {
        try {
            ConnectorTemplate template = getConnectorTemplateConfig(null, object.get(0), object.get(1));
            if (template == null)
                throw new DataProcessingException("Template corrupted or doesn't exist.");
            return template;
        } catch (Exception e) {
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }
}