package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.AccessDetailsBean;
import com.appnomic.appsone.controlcenter.beans.MasterTimezoneBean;
import com.appnomic.appsone.controlcenter.beans.UserAccessBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.AccountDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.UserAccessDataService;
import com.appnomic.appsone.controlcenter.dao.redis.AccountRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.Account;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.service.KeyCloakAuthService;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.model.JWTData;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Type;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class AccountListBL implements BusinessLogic<com.appnomic.appsone.controlcenter.pojo.Account, AccessDetailsBean, List<Account>> {
    
    @Override
    public UtilityBean<Account> clientValidation(RequestObject request) throws ClientException {
        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        return UtilityBean.
                <com.appnomic.appsone.controlcenter.pojo.Account>builder()
                .authToken(authToken)
                .build();
    }

    @Override
    public AccessDetailsBean serverValidation(UtilityBean<Account> utilityBean) throws ServerException {
        JWTData jwtData;
        try {
            jwtData = KeyCloakAuthService.extractUserDetails(utilityBean.getAuthToken());
        } catch (ControlCenterException e) {
            log.error("Exception encountered while fetching the userIdentifier. Details: {}", e.getMessage());
            throw new ServerException("Error while extracting user identifier");
        }

        UserAccessBean accessDetails;
        try {
            accessDetails = UserAccessDataService.getUserAccessDetails(jwtData.getSub().trim());
        } catch (ControlCenterException e) {
            log.error(e.getMessage());
            throw new ServerException(e.getMessage());
        }

        Type userBeanType = new TypeToken<AccessDetailsBean>() {
        }.getType();

        AccessDetailsBean bean = CommonUtils.jsonToObject(accessDetails.getAccessDetailsJson(), userBeanType);
        if (bean == null) {
            log.error(UIMessages.INVALID_USER_ACCESS_DETAILS);
            throw new ServerException(UIMessages.INVALID_USER_ACCESS_DETAILS);
        }

        return bean;
    }

    @Override
    public List<Account> process(AccessDetailsBean bean) throws DataProcessingException {
        List<com.heal.configuration.pojos.Account> accessibleAccounts = new AccountRepo().getAccounts();
        if (Objects.nonNull(accessibleAccounts) && !bean.getAccounts().contains("*")) {
            accessibleAccounts = accessibleAccounts.parallelStream().filter(acc -> bean.getAccounts().contains(acc.getIdentifier())).collect(Collectors.toList());
        }

        List<Account> accountsList = new ArrayList<>();

        for(com.heal.configuration.pojos.Account accBean : accessibleAccounts) {
            MasterTimezoneBean timezoneBean;
            try {
                timezoneBean = AccountDataService.getAccountTimezoneDetails(accBean.getId(), null);
            } catch (ControlCenterException e) {
                throw new DataProcessingException(e.getMessage());
            }

            if(timezoneBean == null) {
                continue;
            }
            Account account = Account.builder()
                    .accountId(accBean.getId())
                    .accountName(accBean.getName())
                    .identifier(accBean.getIdentifier())
                    .privateKey(accBean.getPrivateKey())
                    .publicKey(accBean.getPublicKey())
                    .updatedTimestamp(Timestamp.valueOf(accBean.getUpdatedTime()))
                    .updatedTime(Timestamp.valueOf(accBean.getUpdatedTime()).getTime())
                    .timezoneMilli(timezoneBean.getTimeOffset())
                    .timeZoneString(timezoneBean.getTimeZoneId())
                    .status(accBean.getStatus())
                    .dateFormat("YYYY-MM-DD")
                    .timeFormat("HH:mm")
                    .build();

            account.getUserNameFromIdentifier(accBean.getLastModifiedBy());

            accountsList.add(account);
        }

        return accountsList;
    }
}
