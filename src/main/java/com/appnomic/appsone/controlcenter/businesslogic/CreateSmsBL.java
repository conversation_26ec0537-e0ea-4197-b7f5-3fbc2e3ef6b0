package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.NotificationDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.SMSDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.SMSParameterBean;
import com.appnomic.appsone.controlcenter.dao.redis.AccountRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.SmsDetails;
import com.appnomic.appsone.controlcenter.pojo.SmsParameter;
import com.appnomic.appsone.controlcenter.util.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.SMSDetails;
import com.heal.configuration.pojos.SMSParameters;
import org.bouncycastle.crypto.DataLengthException;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

public class CreateSmsBL implements BusinessLogic<SmsDetails, UtilityBean<SmsDetails>, String> {
    private static final ObjectMapper OBJECT_MAPPER = CommonUtils.getObjectMapperWithHtmlEncoder();
    private static final Logger LOGGER = LoggerFactory.getLogger(CreateSmsBL.class);
    private static final String ADD_LIST = "AddList";

    @Override
    public UtilityBean<SmsDetails> clientValidation(RequestObject requestObject) throws ClientException {
        SmsDetails smsDetails;

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authKey)) {
            LOGGER.error(UIMessages.AUTH_KEY_EMPTY);
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }

        try {
            smsDetails = OBJECT_MAPPER.readValue(requestObject.getBody(),
                    new TypeReference<SmsDetails>() {
                    });
        } catch (IOException e) {
            LOGGER.error(UIMessages.JSON_INVALID + " Details: {}", e.getMessage());
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        Map<String, String> error = smsDetails.validate();

        if (!error.isEmpty()) {
            String err = error.toString();
            LOGGER.error(err);
            throw new ClientException(err);
        }

        Set<SmsParameter> parameterSet = new HashSet<>(smsDetails.getParameters());
        if (parameterSet.size() < smsDetails.getParameters().size()) {
            LOGGER.error(UIMessages.DUPLICATE_SMS_PARAMETER);
            throw new ClientException(UIMessages.DUPLICATE_SMS_PARAMETER);
        }

        if (smsDetails.getParameters().stream().anyMatch(obj -> !obj.getAction().equalsIgnoreCase("add"))) {
            LOGGER.error("SMS parameters with action other than ‘add’ are not allowed");
            throw new ClientException("SMS parameters with action other than ‘add’ are not allowed");
        }

        return UtilityBean.<SmsDetails>builder().accountIdentifier(identifier).pojoObject(smsDetails).authToken(authKey).build();
    }

    @Override
    public UtilityBean<SmsDetails> serverValidation(UtilityBean<SmsDetails> utilityBean) throws ServerException {
        UserAccountBean userAccountBean;
        try {
            userAccountBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            String err = e.getSimpleMessage();
            LOGGER.error(err);
            throw new ServerException(err);
        }
        SMSDetailsBean smsDetailsBeanExists = new NotificationDataService().getSMSDetails(userAccountBean.getAccount().getId(), null);
        if (smsDetailsBeanExists != null) {
            String err = "Sms settings is already available for account ".concat(String.valueOf(userAccountBean.getAccount().getId()));
            LOGGER.error(err);
            throw new ServerException(err);
        }
        commonSMSServerValidation(utilityBean);
        utilityBean.setAccount(userAccountBean.getAccount());

        String plainTxt = "";
        if (utilityBean.getPojoObject().getPassword() == null || utilityBean.getPojoObject().getPassword().trim().isEmpty()) {
            utilityBean.getPojoObject().setPassword("");
        } else {
            try {
                plainTxt = new AECSBouncyCastleUtil().decrypt(utilityBean.getPojoObject().getPassword());
                if (StringUtils.isEmpty(plainTxt)) {
                    String err = "Password is not properly encrypted.";
                    LOGGER.error(err);
                    throw new ServerException(err);
                }
            } catch (InvalidCipherTextException | DataLengthException e) {
                LOGGER.error("Exception encountered while decrypting the password. Details: {}", e.getMessage(), e);
                throw new ServerException("Error while decrypting the password");
            }
        }
        utilityBean.getPojoObject().setPassword(CommonUtils.encryptInBCEC(plainTxt));

        return utilityBean;
    }

    private void commonSMSServerValidation(UtilityBean<SmsDetails> utilityBean) throws ServerException {

        SmsDetails smsDetails = utilityBean.getPojoObject();
        ViewTypes protocolType = MasterCache.getMstTypeForSubTypeName(Constants.SMS_PROTOCOLS, smsDetails.getProtocolName());
        if (protocolType == null) {
            String err = "SMS protocol unavailable for protocol name:".concat(smsDetails.getProtocolName());
            LOGGER.error(err);
            throw new ServerException(err);
        }

        if (protocolType.getSubTypeName().equalsIgnoreCase("HTTP")) {
            ViewTypes httpMethodType = MasterCache.getMstTypeForSubTypeName(Constants.SMS_HTTP_METHODS, smsDetails.getHttpMethod());
            if (httpMethodType == null) {
                String err = "HTTP method type unavailable for http method:".concat(smsDetails.getHttpMethod());
                LOGGER.error(err);
                throw new ServerException(err);
            }
            if (httpMethodType.getSubTypeName().equalsIgnoreCase("GET")) {
                smsDetails.setPostData("");
            }
        } else if (protocolType.getSubTypeName().equalsIgnoreCase("TCP")) {
            smsDetails.setPostData("");
            smsDetails.setHttpMethod("");
            smsDetails.setHttpRelativeUrl("");
            smsDetails.setIsMultiRequest(0);
        }

        addServerValidationsForSMSParameters(smsDetails.getParameters());
    }

    @Override
    public String process(UtilityBean<SmsDetails> bean) throws DataProcessingException {
        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        if (Constants.SUCCESS.equals(dbi.inTransaction((conn, status) -> this.addSMS(bean, conn)))) {
            return Constants.SUCCESS;
        } else {
            throw new DataProcessingException("Unable to add sms details.");
        }
    }

    private String addSMS(UtilityBean<SmsDetails> bean, Handle conn) throws DataProcessingException {
        Timestamp time = DateTimeUtil.getCurrentTimestampInGMT();

        String userId = ValidationUtils.getUserId(bean.getAuthToken());
        if (userId == null) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID);
            throw new DataProcessingException(UIMessages.AUTH_KEY_INVALID);
        }

        SMSDetailsBean smsDetailsBean = createSMSDetailsBean(bean.getPojoObject(), userId, bean.getAccount().getId(), time);

        int smsDetailsId = new NotificationDataService().addSMSDetails(smsDetailsBean, conn);

        LOGGER.info("SMS detail id: {} for account id:{}", smsDetailsId, bean.getAccount().getId());

        Map<String, List<SMSParameterBean>> smsParametersBeanMap = createSMSParameterList(smsDetailsId, bean.getPojoObject().getParameters(), userId, time);

        List<SMSParameterBean> smsParametersBeansAdd = smsParametersBeanMap.getOrDefault(ADD_LIST, new ArrayList<>());
        if (!smsParametersBeansAdd.isEmpty()) {
            new NotificationDataService().addSMSParameter(smsParametersBeansAdd, conn);
        }
        AccountRepo accountRepo = new AccountRepo();
        accountRepo.updateSmsConfiguration(bean.getAccount().getIdentifier() , SMSDetails
                .builder()
                        .id(bean.getPojoObject().getId())
                        .address(bean.getPojoObject().getAddress())
                        .port(bean.getPojoObject().getPort())
                        .countryCode(bean.getPojoObject().getCountryCode())
                        .protocolId(smsDetailsBean.getProtocolId())
                        .protocolName(bean.getPojoObject().getProtocolName())
                        .httpMethod(bean.getPojoObject().getHttpMethod())
                        .httpRelativeUrl(bean.getPojoObject().getHttpRelativeUrl())
                        .postData(bean.getPojoObject().getPostData())
                        .isMultiRequest(bean.getPojoObject().getIsMultiRequest())
                        .parameters(bean.getPojoObject().getParameters().stream()
                                .map(s->SMSParameters.builder()
                                        .parameterId(s.getParameterId())
                                        .parameterName(s.getParameterName())
                                        .parameterValue(s.getParameterValue())
                                        .action(s.getAction())
                                        .isPlaceholder(s.getIsPlaceholder())
                                        .build()).collect(Collectors.toList()))
                        .username(bean.getPojoObject().getUsername())
                        .password(bean.getPojoObject().getPassword())
                        .persistSMSNotifications(bean.getPojoObject().getPersistSmsNotifications())
                .build());
        return Constants.SUCCESS;
    }

    static SMSDetailsBean createSMSDetailsBean(SmsDetails smsDetails, String authToken, int accountId, Timestamp time) {
        ViewTypes protocolType = MasterCache.getMstTypeForSubTypeName(Constants.SMS_PROTOCOLS, smsDetails.getProtocolName());

        return SMSDetailsBean.builder()
                .accountId(accountId)
                .address(smsDetails.getAddress())
                .countryCode(smsDetails.getCountryCode())
                .httpMethod(smsDetails.getHttpMethod())
                .httpRelativeUrl(smsDetails.getHttpRelativeUrl())
                .port(smsDetails.getPort())
                .postData(smsDetails.getHttpMethod().equalsIgnoreCase("POST") ? smsDetails.getPostData() : null)
                .postDataFlag(smsDetails.getHttpMethod().equalsIgnoreCase("POST") ? 1 : 0)
                .protocolId(protocolType.getSubTypeId())
                .userDetailsId(authToken)
                .createdTime(time)
                .updatedTime(time)
                .status(1)
                .isMultiRequest(smsDetails.getIsMultiRequest())
                .username(smsDetails.getUsername())
                .password(smsDetails.getPassword())
                .persistSmsNotifications(smsDetails.getPersistSmsNotifications())
                .build();
    }

    private Map<String, List<SMSParameterBean>> createSMSParameterList(int smsDetailsId, List<SmsParameter> smsParameterList,
                                                                       String authToken, Timestamp time) throws DataProcessingException {
        Map<String, List<SMSParameterBean>> result = new HashMap<>();
        List<SMSParameterBean> addSmsParamList = new ArrayList<>();

        for (SmsParameter smsParameter : smsParameterList) {
            ViewTypes parameterType = MasterCache.getMstTypeForSubTypeName(Constants.SMS_PARAMETER_TYPE_NAME, smsParameter.getParameterType());
            if (parameterType == null) {
                String err = "Parameter type details unavailable for parameter type:".concat(String.valueOf(smsParameter.getParameterType()));
                LOGGER.error(err);
                throw new DataProcessingException(err);
            }

            SMSParameterBean smsBean = SMSParameterBean.builder()
                    .smsDetailsId(smsDetailsId)
                    .parameterName(smsParameter.getParameterName())
                    .parameterValue(smsParameter.getParameterValue())
                    .parameterTypeId(parameterType.getSubTypeId())
                    .createdTime(time)
                    .updatedTime(time)
                    .userDetailsId(authToken)
                    .isPlaceholder(smsParameter.getIsPlaceholder().equals(Boolean.TRUE) ? 1 : 0)
                    .build();

            if (smsParameter.getAction().equalsIgnoreCase(Constants.SMS_ACTION_ADD)) {
                addSmsParamList.add(smsBean);
            }
        }
        result.put(ADD_LIST, addSmsParamList);

        return result;
    }

    private void addServerValidationsForSMSParameters(List<SmsParameter> smsParameters) throws ServerException {
        for (SmsParameter smsParameter : smsParameters) {
            ViewTypes parameterType = MasterCache.getMstTypeForSubTypeName(Constants.SMS_PARAMETER_TYPE_NAME, smsParameter.getParameterType());
            if (parameterType == null) {
                String err = "Parameter type details unavailable for parameter type:".concat(String.valueOf(smsParameter.getParameterType()));
                LOGGER.error(err);
                throw new ServerException(err);
            }
            if (smsParameter.getIsPlaceholder().equals(Boolean.TRUE)) {
                Optional<ViewTypes> parameterValueType = MasterCache.getSubTypeDetails(Constants.SMS_PLACEHOLDERS)
                        .stream()
                        .filter(p -> smsParameter.getParameterValue().contains(p.getSubTypeName()))
                        .findAny();

                if (!parameterValueType.isPresent()) {
                    String err = "Sub type is not found for the given parameter value type: ".concat(smsParameter.getParameterName());
                    LOGGER.error(err);
                    throw new ServerException(err);
                }
            }
        }
    }
}
