package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.MasterComponentBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.MasterDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.ComponentDetails;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

public class GetComponentDetails implements BusinessLogic<Integer, Integer, List<ComponentDetails>> {
    private static final Logger LOGGER = LoggerFactory.getLogger(GetComponentDetails.class);

    @Override
    public UtilityBean<Integer> clientValidation(RequestObject request) throws ClientException {

        if (request == null) {
            LOGGER.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            LOGGER.error(UIMessages.AUTH_KEY_EMPTY);
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }

        String accountIdentifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(accountIdentifier)) {
            LOGGER.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        return UtilityBean.<Integer>builder()
                .accountIdentifier(accountIdentifier)
                .authToken(authToken)
                .build();
    }

    @Override
    public Integer serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            LOGGER.error(UIMessages.INVALID_ACCOUNT_MESSAGE);
            throw new ServerException(UIMessages.INVALID_ACCOUNT_MESSAGE);
        }

        return account.getId();
    }

    @Override
    public List<ComponentDetails> process(Integer account) throws DataProcessingException {

        try {
            List<ComponentDetails> componentDetailsList = new ArrayList<>();

            List<MasterComponentBean> componentsList = MasterDataService.getComponentMasterData(account);

            Map<ComponentDetails, List<MasterComponentBean>> componentDetailsMap = componentsList.parallelStream()
                    .collect(Collectors.groupingBy(c -> ComponentDetails.builder().id(c.getId()).name(c.getName()).build()));

            componentDetailsMap.forEach((componentDetails, masterComponentBeans) -> {
                Set<IdPojo> componentTypes = new HashSet<>();
                Set<ComponentDetails.Version> commonVersions = new HashSet<>();
                masterComponentBeans.forEach(component -> {
                    componentTypes.add(IdPojo.builder().id(component.getComponentTypeId())
                            .name(component.getComponentTypeName()).build());
                    commonVersions.add(ComponentDetails.Version.builder().id(component.getCommonVersionId())
                            .version(component.getCommonVersionName()).build());
                });
                componentDetails.setComponentTypes(componentTypes);
                componentDetails.setCommonVersions(commonVersions);
                componentDetailsList.add(componentDetails);
            });

            return componentDetailsList;

        } catch (Exception e) {
            throw new DataProcessingException(e.getMessage());
        }
    }
}
