package com.appnomic.appsone.controlcenter.businesslogic.connectors;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.BusinessLogic;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.connectors.TemplateUploadStatus;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

import static com.appnomic.appsone.controlcenter.dao.mysql.connectors.ConnectorDetailsDataService.getTemplateUploadStatus;

@Slf4j
public class GetTemplateUploadStatusBL implements BusinessLogic<Object, List<Integer>, TemplateUploadStatus>  {

    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty");
            throw new ClientException("Invalid account identifier");
        }
        int connectorId;
        try
        {
            connectorId = Integer.parseInt(requestObject.getParams().get(":connectorid"));
        } catch (NumberFormatException ex)
        {
            log.error("Invalid template id. It is either NULL or empty.");
            throw new ClientException("Invalid template id. It is either NULL or empty.");
        }

        return UtilityBean.builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .pojoObject(connectorId)
                .build();
    }

    @Override
    public List<Integer> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {

        String accountIdentifier = utilityBean.getAccountIdentifier();
        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);
        String connectorId = utilityBean.getPojoObject().toString();
        List<Integer> object = new ArrayList<>();
        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }
        if (connectorId == null || connectorId.trim().equals("")) {
            log.error("Connector Id is invalid");
            throw new ServerException("Connector Id is invalid");
        }
        object.add(account.getId());
        object.add(Integer.valueOf(connectorId));
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }
        return object;
    }

    @Override
    public TemplateUploadStatus process(List<Integer> object) throws DataProcessingException {
        try {
            TemplateUploadStatus templateUploadStatus = TemplateUploadStatus.builder().build();
            Integer statusCode = getTemplateUploadStatus(object.get(0), object.get(1));
            if(statusCode == null) {
                templateUploadStatus.setCode(-1);
                templateUploadStatus.setStatus("Not Started");
                templateUploadStatus.setErrorMessage("No configuration exists.");
                return templateUploadStatus;
            }
            templateUploadStatus.setCode(statusCode);
                switch (statusCode) {
                    case 0 : templateUploadStatus.setStatus("In Progress");
                            break;
                    case 1 : templateUploadStatus.setStatus("Success");
                            break;
                    case 2: templateUploadStatus.setStatus("Error...");
                            templateUploadStatus.setErrorMessage("Error while uploading template data. Please try again.");
                            break;
                    default: throw new DataProcessingException("Invalid status code.");
                }
            return templateUploadStatus;
        } catch (Exception e) {
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }

}
