package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.beans.MaintenanceWindowBean;
import com.appnomic.appsone.common.exception.AppsOneException;
import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.MaintenanceDetails;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.MaintenanceWindowDao;
import com.appnomic.appsone.controlcenter.dao.opensearch.MaintenanceWindowServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.MaintenanceWindowPojo;
import com.appnomic.appsone.controlcenter.pojo.RecurringBean;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
public class AddMaintenanceWindowBL implements BusinessLogic<MaintenanceWindowPojo, MaintenanceDetails, MaintenanceWindowBean> {

    private static final String MAINTENANCE_DETAILS_ERROR_MESSAGE = "Unable to add maintenance details (startTime = ";

    @Override
    public UtilityBean<MaintenanceWindowPojo> clientValidation(RequestObject requestObject) throws ClientException {
        String accountIdentifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        String serviceIdStr = requestObject.getParams().get(Constants.SERVICE_ID);
        String authToken = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

        String postFacto = "false";
        if(requestObject.getQueryParams() != null && !requestObject.getQueryParams().isEmpty()) {
            postFacto = requestObject.getQueryParams().get("postFacto")[0];
        }

        if(StringUtils.isEmpty(authToken)) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty.");
            throw new ClientException("Invalid authorization token");
        }

        if (StringUtils.isEmpty(accountIdentifier)) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty.");
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        if (StringUtils.isEmpty(serviceIdStr)) {
            log.error("Invalid service ID. Reason: It is either NULL or empty.");
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR);
        }

        if (StringUtils.isEmpty(postFacto)) {
            log.error("Invalid query parameter 'postFacto'. Reason: It is not defined.");
            throw new ClientException(UIMessages.INVALID_POST_FACTO);
        }

        int serviceId;
        try {
            serviceId = Integer.parseInt(serviceIdStr);
        } catch (Exception e) {
            throw new ClientException(MessageFormat.format(UIMessages.INVALID_VALUE, Constants.SERVICE_IDENTIFIER, serviceIdStr));
        }

        if (StringUtils.isEmpty(requestObject.getBody())) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        MaintenanceWindowPojo maintenanceWindowPojo;

        try {
            maintenanceWindowPojo = objectMapper.readValue(requestObject.getBody(), MaintenanceWindowPojo.class);
        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        maintenanceWindowPojo.setPostFacto(Boolean.parseBoolean(postFacto));
        maintenanceWindowPojo.setServiceId(serviceId);

        if (maintenanceWindowPojo.getMaintenanceType().equals(Constants.MST_SUB_TYPE_RECURRING)) {
            maintenanceWindowPojo.validateRecurring();
        } else {
            maintenanceWindowPojo.validate();
        }

        if (!maintenanceWindowPojo.getError().isEmpty()) {
            throw new ClientException(maintenanceWindowPojo.getError().toString());
        }

        return UtilityBean.<MaintenanceWindowPojo>builder()
                .accountIdentifier(accountIdentifier)
                .authToken(authToken)
                .pojoObject(maintenanceWindowPojo)
                .build();
    }

    @Override
    public MaintenanceDetails serverValidation(UtilityBean<MaintenanceWindowPojo> utilityBean) throws ServerException {
        String authToken = utilityBean.getAuthToken();

        UserAccountBean userAccBean;
        try {
            userAccBean = ValidationUtils.commonServerValidations(authToken, utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            log.error(e.getMessage());
            throw new ServerException(e.getMessage());
        }

        String userId = userAccBean.getUserId();
        int accountId = userAccBean.getAccount().getId();
        MaintenanceWindowPojo maintenanceWindowPojo = utilityBean.getPojoObject();
        int serviceId = maintenanceWindowPojo.getServiceId();

        ControllerBean controller = new ControllerDataService().getControllerById(serviceId, accountId, null);
        if (controller == null) {
            log.error("Service with id [{}] is unavailable for account id [{}]", serviceId, accountId);
            throw new ServerException(String.format("Service with id [%d] is unavailable for account id [%d]", serviceId, accountId));
        }

        Timestamp date = DateTimeUtil.getCurrentTimestampInGMT();

        ViewTypes maintenanceType = MasterCache.getMstTypeForSubTypeName(Constants.MST_TYPE_MAINTENANCE, maintenanceWindowPojo.getMaintenanceType());
        RecurringBean recurringDetailsBean = maintenanceWindowPojo.getRecurring();

        if (maintenanceType.getSubTypeName().equals(Constants.MST_SUB_TYPE_SCHEDULED)) {
            MaintenanceWindowBean maintenanceWindowBean;
            try {
                maintenanceWindowBean = MaintenanceWindowUtility.getMaintenanceConflictByServiceId(maintenanceWindowPojo.getId(), serviceId,
                        maintenanceWindowPojo.getStartTime(), maintenanceWindowPojo.getEndTime());
            } catch (AppsOneException e) {
                log.error("Error while checking maintenance window conflicts for service ID [{}]", serviceId);
                throw new ServerException(String.format("Error while checking maintenance window conflicts for service ID [%d]", serviceId));
            }

            if (maintenanceWindowBean != null && !(maintenanceWindowPojo.getStartTime().equals(new Timestamp(maintenanceWindowBean.getEndTime()))
                    || maintenanceWindowPojo.getEndTime().equals(new Timestamp(maintenanceWindowBean.getStartTime())))) {
                String error = "Maintenance window already exists from " + new Date(maintenanceWindowBean.getStartTime()) + " to " + new Date(maintenanceWindowBean.getEndTime());
                throw new ServerException(error);
            }
        } else {
            ViewTypes recurringType = MasterCache.getMstTypeForSubTypeName(Constants.MST_TYPE_RECURRING, maintenanceWindowPojo.getRecurring().getRecurringType());

            long duration;
            try {
                DateFormat formatter = new SimpleDateFormat("HH:mm");
                Date startHr = formatter.parse(maintenanceWindowPojo.getRecurring().getStartHour());
                Date endHr = formatter.parse(maintenanceWindowPojo.getRecurring().getEndHour());
                duration = endHr.getTime() - startHr.getTime();
            } catch (ParseException e) {
                log.error("Error while parsing start hour and end hour. Details: ", e);
                throw new ServerException("Error while parsing start hour and end hour.");
            }

            recurringDetailsBean.setCreatedTime(date);
            recurringDetailsBean.setUpdatedTime(date);
            recurringDetailsBean.setUserDetails(userId);
            recurringDetailsBean.setDuration(duration);
            recurringDetailsBean.setRecurringType(recurringType.getSubTypeName());
            recurringDetailsBean.setRecurringTypeId(recurringType.getSubTypeId());

            MaintenanceWindowUtility.verifyAndBuildRecurringMaintenanceWindow(maintenanceWindowPojo, serviceId, recurringDetailsBean, false);
        }

        return MaintenanceDetails.builder()
                .accountId(accountId)
                .typeId(maintenanceType.getSubTypeId())
                .status(true)
                .startTime(maintenanceWindowPojo.getStartTime())
                .endTime(maintenanceWindowPojo.getEndTime())
                .maintenanceType(maintenanceType.getSubTypeName())
                .accountIdentifier(userAccBean.getAccount().getIdentifier())
                .serviceIdentifier(controller.getIdentifier())
                .createdTime(date)
                .updatedTime(date)
                .name(controller.getName())
                .serviceId(serviceId)
                .userDetails(userId)
                .isCustom(1)
                .recurring(recurringDetailsBean)
                .build();
    }


    @Override
    public MaintenanceWindowBean process(MaintenanceDetails bean) throws DataProcessingException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();

            return dbi.inTransaction((conn, status) -> {
                int maintenanceId;
                if (bean.getMaintenanceType().equals(Constants.MST_SUB_TYPE_SCHEDULED)) {
                    maintenanceId = addScheduledMaintenanceWindowDetails(bean, conn);
                    MaintenanceWindowServiceRepo.insertServiceMaintenanceDetails(bean.getAccountIdentifier(), bean.getServiceIdentifier(), bean.getStartTime(), bean.getEndTime());
                } else {
                    maintenanceId = addRecurringMaintenanceWindowDetails(bean, conn);
                }
                return MaintenanceWindowBean.builder().id(maintenanceId).build();
            });
        } catch (Exception e) {
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }

    public int addRecurringMaintenanceWindowDetails(MaintenanceDetails maintenanceDetails, Handle handle) throws ControlCenterException {
        MaintenanceWindowDao maintenanceWindowDao = handle.attach(MaintenanceWindowDao.class);

        int maintenanceWindowDetailsId = maintenanceWindowDao.addMaintenanceWindowDetails(maintenanceDetails);
        if (maintenanceWindowDetailsId == -1) {
            String err = MAINTENANCE_DETAILS_ERROR_MESSAGE
                    + maintenanceDetails.getStartTime() + " endTime = " + maintenanceDetails.getEndTime()
                    + " maintenanceType = " + maintenanceDetails.getMaintenanceType() + " ) into Maintenance Window Details table";
            throw new ControlCenterException(err);
        }

        int recurringId = maintenanceWindowDao.addRecurringWindowDetails(maintenanceDetails.getRecurring(), maintenanceWindowDetailsId);
        if (recurringId == -1) {
            String err = MAINTENANCE_DETAILS_ERROR_MESSAGE
                    + maintenanceDetails.getStartTime() + " endTime = " + maintenanceDetails.getEndTime()
                    + " maintenanceType = " + maintenanceDetails.getMaintenanceType() + " ) into Recurring Details table";
            throw new ControlCenterException(err);
        }

        int maintenanceServiceWindowDetails = maintenanceWindowDao.addMaintenanceServiceWindowDetails(maintenanceDetails, maintenanceWindowDetailsId);
        if (maintenanceServiceWindowDetails == -1) {
            String err = MAINTENANCE_DETAILS_ERROR_MESSAGE
                    + maintenanceDetails.getStartTime() + " endTime = " + maintenanceDetails.getEndTime()
                    + " maintenanceType = " + maintenanceDetails.getMaintenanceType() + " ) into Maintenance Window Service Details table";
            throw new ControlCenterException(err);
        }

        return maintenanceWindowDetailsId;
    }

    public int addScheduledMaintenanceWindowDetails(MaintenanceDetails maintenanceDetails, Handle handle) throws ControlCenterException {
        MaintenanceWindowDao maintenanceWindowDao = handle.attach(MaintenanceWindowDao.class);

        int maintenanceWindowDetailsId = maintenanceWindowDao.addMaintenanceWindowDetails(maintenanceDetails);
        if (maintenanceWindowDetailsId == -1) {
            String err = MAINTENANCE_DETAILS_ERROR_MESSAGE
                    + maintenanceDetails.getStartTime() + " endTime = " + maintenanceDetails.getEndTime()
                    + " maintenanceType = " + maintenanceDetails.getMaintenanceType() + " ) into Maintenance Window Details table";
            throw new ControlCenterException(err);
        }

        int maintenanceServiceWindowDetails = maintenanceWindowDao.addMaintenanceServiceWindowDetails(maintenanceDetails, maintenanceWindowDetailsId);
        if (maintenanceServiceWindowDetails == -1) {
            String err = MAINTENANCE_DETAILS_ERROR_MESSAGE
                    + maintenanceDetails.getStartTime() + " endTime = " + maintenanceDetails.getEndTime()
                    + " maintenanceType = " + maintenanceDetails.getMaintenanceType() + " ) into Maintenance Window Service Details table";
            throw new ControlCenterException(err);
        }
        return maintenanceWindowDetailsId;
    }
}
