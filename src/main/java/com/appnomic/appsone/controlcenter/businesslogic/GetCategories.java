package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.beans.keys.AccountKPIKey;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.CategoryType;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CategoryDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CountBean;
import com.appnomic.appsone.controlcenter.dao.redis.CategoryRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.CategoryDetails;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.heal.configuration.pojos.Category;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
@Slf4j
public class GetCategories implements BusinessLogic<String, AccountKPIKey, List<CategoryDetails>> {

    CategoryDataService categoryDataService = new CategoryDataService();

    @Override
    public UtilityBean<String> clientValidation(RequestObject request) throws ClientException {
        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }

        return UtilityBean.<String>builder()
                .accountIdentifier(identifier)
                .authToken(authToken)
                .pojoObject((request.getQueryParams().get("kpiType") != null && request.getQueryParams()
                        .get("kpiType").length != 0) ? request.getQueryParams().get("kpiType")[0] : null)
                .build();
    }

    @Override
    public AccountKPIKey serverValidation(UtilityBean<String> utilityBean) throws ServerException {

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }
        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            log.error(UIMessages.INVALID_ACCOUNT_MESSAGE);
            throw new ServerException(UIMessages.INVALID_ACCOUNT_MESSAGE);
        }

        ViewTypes type = null;
        String kpiType = utilityBean.getPojoObject();
        if (kpiType != null && !kpiType.isEmpty()) {
            type = MasterCache.getMstTypeForSubTypeName(Constants.KPI_TYPE, kpiType);
            if (null == type) {
                log.error(UIMessages.INVALID_KPI_TYPE);
                throw new ServerException(UIMessages.INVALID_KPI_TYPE);
            }
        }

        return AccountKPIKey.builder()
                .accountId(account.getId())
                .accountIdentifier(account.getIdentifier())
                .kpiTypeId((type != null) ? type.getSubTypeId() : -1)
                .build();
    }

    @Override
    public List<CategoryDetails> process(AccountKPIKey accountKPIKey) throws DataProcessingException {
        try {
            List<Category> categoryDetailList;
            if(accountKPIKey.getKpiTypeId() == -1) {
                categoryDetailList = new CategoryRepo().getCategoryDetails(accountKPIKey.getAccountIdentifier());
            } else {
                categoryDetailList = categoryDataService.getCategoriesForKpiType(accountKPIKey.getAccountId(),
                        accountKPIKey.getKpiTypeId());
            }

            if (categoryDetailList.isEmpty()) {
                log.error("No category available for account id:[{}], kpi id:[{}]", accountKPIKey.getAccountId(), accountKPIKey.getKpiId());
                throw new DataProcessingException("No category available for account id: "+ accountKPIKey.getAccountId() +",kpi id: "+accountKPIKey.getKpiId());
            }

            Set<Integer> categoryIds = categoryDetailList.parallelStream().map(Category::getId).collect(Collectors.toSet());

            Map<Integer, Integer> categoriesKpiCount = new HashMap<>(new BindInDataService().getKpiCountForCategories(categoryIds)
                    .parallelStream().collect(Collectors.toMap(CountBean::getId, CountBean::getCount)));

            return categoryDetailList.parallelStream()
                    .map(c -> CategoryDetails.builder()
                            .identifier(c.getIdentifier())
                            .name(c.getName())
                            .id(c.getId())
                            .description(c.getDescription())
                            .status(c.getStatus())
                            .type((c.getCustom() == 1) ? Constants.CATEGORY_CUSTOM : Constants.CATEGORY_STANDARD)
                            .subType(getSubType(c).getType())
                            .kpiCount((categoriesKpiCount.getOrDefault(c.getId(), 0)))
                            .isWorkLoad(c.getWorkload() == 1)
                            .build())
                    .collect(Collectors.toList());

        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
    }

    CategoryType getSubType(Category category) {
        if (category.getWorkload() == 1) {
            return CategoryType.WORKLOAD;
        }

        if (category.getInformative() == 1) {
            return CategoryType.INFO;
        }

        return CategoryType.NON_INFO;
    }
}
