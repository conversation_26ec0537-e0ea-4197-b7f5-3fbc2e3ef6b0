package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.ClientValidations;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.AutoAcceptanceSettingsPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.heal.configuration.pojos.Service;
import com.heal.configuration.pojos.TransactionAutoAcceptance;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GetAutoAcceptanceSettingsBL implements BusinessLogic<String, UtilityBean<String>, AutoAcceptanceSettingsPojo> {

    @Override
    public UtilityBean<String> clientValidation(RequestObject request) throws ClientException {
        ClientValidations.requestNullCheck(request);

        String accountIdentifier = ClientValidations.accountNullCheck(request);
        String authToken = ClientValidations.authTokenNullCheck(request);
        String serviceId = ClientValidations.serviceNullCheck(request);

        return UtilityBean.<String>builder()
                .accountIdentifier(accountIdentifier)
                .serviceId(serviceId)
                .authToken(authToken)
                .pojoObject("")
                .build();
    }

    @Override
    public UtilityBean<String> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        UserAccountBean userAccountBean;
        try {
            userAccountBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
            utilityBean.setUserId(userAccountBean.getUserId());
        } catch (RequestException e) {
            log.error("Error while validating user and account details", e);
            throw new ServerException("Error while validating user and account details");
        }

        ControllerBean serviceDetails = new ControllerDataService().getControllerById(Integer.parseInt(utilityBean.getServiceId()), userAccountBean.getAccount().getId(), null);
        if (serviceDetails == null) {
            log.error("Service details unavailable for serviceID [{}]", utilityBean.getServiceId());
            throw new ServerException("Service details unavailable for serviceID [{}]" + utilityBean.getServiceId());
        }

        utilityBean.setServiceIdentifier(serviceDetails.getIdentifier());

        return utilityBean;
    }

    @Override
    public AutoAcceptanceSettingsPojo process(UtilityBean<String> bean) throws DataProcessingException {
        int DEFAULT_MIN_REQ_COUNT = ConfProperties.getInt(Constants.DEFAULT_MIN_REQ_COUNT, Constants.DEFAULT_MIN_REQ_COUNT_DEFAULT_VAL);
        int ALLOWED_MIN_REQ_COUNT = ConfProperties.getInt(Constants.ALLOWED_MIN_REQ_COUNT, Constants.ALLOWED_MIN_REQ_COUNT_DEFAULT_VAL);
        int DEFAULT_MAX_AUTO_ACCEPTED_REQS = ConfProperties.getInt(Constants.DEFAULT_MAX_AUTO_ACCEPTED_REQS, Constants.DEFAULT_MAX_AUTO_ACCEPTED_REQS_DEFAULT_VAL);
        int ALLOWED_MIN_AUTO_ACCEPTED_REQS = ConfProperties.getInt(Constants.ALLOWED_MIN_AUTO_ACCEPTED_REQS, Constants.ALLOWED_MIN_AUTO_ACCEPTED_REQS_DEFAULT_VAL);
        int ALLOWED_MAX_AUTO_ACCEPTED_REQS = ConfProperties.getInt(Constants.ALLOWED_MAX_AUTO_ACCEPTED_REQS, Constants.ALLOWED_MAX_AUTO_ACCEPTED_REQS_DEFAULT_VAL);
        int MIN_HOLD_DURATION = ConfProperties.getInt(Constants.MIN_HOLD_DURATION, Constants.MIN_HOLD_DURATION_DEFAULT_VAL);
        int MAX_HOLD_DURATION = ConfProperties.getInt(Constants.MAX_HOLD_DURATION, Constants.MAX_HOLD_DURATION_DEFAULT_VAL);
        int DEFAULT_HOLD_DURATION = ConfProperties.getInt(Constants.DEFAULT_HOLD_DURATION, Constants.DEFAULT_HOLD_DURATION_DEFAULT_VAL);

        Service service = new ServiceRepo().getServiceConfigurationByIdentifier(bean.getAccountIdentifier(), bean.getServiceIdentifier());
        if (service == null) {
            log.error("Service is null from redis for serviceId : {}", bean.getServiceId());
            throw new DataProcessingException("Service is null from redis");
        }

        TransactionAutoAcceptance txnAutoAcceptance = service.getTransactionAutoAcceptance();
        if (txnAutoAcceptance == null) {
            log.error("Service.TransactionAutoAcceptance is null from redis for serviceId : {}", bean.getServiceId());
            throw new DataProcessingException("Transaction auto acceptance is null for service");
        }

        return AutoAcceptanceSettingsPojo.builder()
                .minRequestCount(txnAutoAcceptance.getMinVolumeCount())
                .defaultMinRequestCount(DEFAULT_MIN_REQ_COUNT)  //consul key
                .allowedMinRequestCount(ALLOWED_MIN_REQ_COUNT)  //consul key
                .maxAutoAcceptedRequests(txnAutoAcceptance.getMaxTxnLimit())
                .defaultMaxAutoAcceptedRequests(DEFAULT_MAX_AUTO_ACCEPTED_REQS)  //consul key
                .allowedMinAutoAcceptedRequests(ALLOWED_MIN_AUTO_ACCEPTED_REQS)  //consul key
                .allowedMaxAutoAcceptedRequests(ALLOWED_MAX_AUTO_ACCEPTED_REQS)  //consul key
                .holdDuration(txnAutoAcceptance.getAutoCommitDuration())
                .minHoldDuration(MIN_HOLD_DURATION)  //consul key
                .maxHoldDuration(MAX_HOLD_DURATION)  //consul key
                .defaultHoldDuration(DEFAULT_HOLD_DURATION)  //consul key
                .build();
    }
}
