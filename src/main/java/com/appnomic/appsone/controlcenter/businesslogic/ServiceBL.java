package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.TagDetailsBean;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.keys.AccountControllerKey;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ServiceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ServiceInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.redis.AgentRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ApplicationRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceInstanceRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.ServicePojo;
import com.appnomic.appsone.controlcenter.service.ControlCenterService;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import com.heal.configuration.entities.BasicAgentBean;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.ServiceInstanceDetails;
import com.heal.configuration.pojos.ServiceInstanceKpiEntity;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.util.List;

public class ServiceBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceBL.class);

    public static ServicePojo remClientValidations(Request request) throws RequestException {
        ValidationUtils.commonClientValidations(request);

        String serviceIdentifier = request.params(Constants.SERVICE_IDENTIFIER);
        if (StringUtils.isEmpty(serviceIdentifier)) {
            LOGGER.error(UIMessages.EMPTY_SERVICE_IDENTIFIER);
            throw new RequestException(UIMessages.EMPTY_SERVICE_IDENTIFIER);
        }

        ServicePojo service = new ServicePojo();
        service.setIdentifier(serviceIdentifier);
        return service;
    }

    public static AccountControllerKey remServerValidations(ServicePojo service, String authToken, String accountIdentifier)
            throws RequestException, ControlCenterException {

        UserAccountBean bean = ValidationUtils.commonServerValidations(authToken, accountIdentifier);

        ControllerBean controller = new ControllerDataService().getServiceByIdentifierAndAccount(service.getIdentifier(), bean.getAccount().getId(), null);

        if (controller == null) {
            String err = "Service Identifier '" + service.getIdentifier() + "' does not exist for the account.";
            LOGGER.error(err);
            throw new RequestException(err);
        }

        int serviceId = controller.getId();

        TagDetailsBean controllerTag = MasterCache.getTagDetails(Constants.CONTROLLER_TAG);
        if (controllerTag == null) {
            throw new ControlCenterException("Tag details unavailable for 'Controller' tag");
        }

        if(ControlCenterService.getTotalTransaction(serviceId, Constants.TXN_TABLE, controllerTag.getId(), bean.getAccount().getId()) > 0) {
            String err = "Service '" + service.getIdentifier() + "' has transactions mapped to it hence it cannot be deleted.";
            LOGGER.error(err);
            throw new RequestException(err);
        }

        if(new CompInstanceDataService().getCompInstanceDetailsForService(serviceId,bean.getAccount().getId(),null).size() > 0) {
            String err = "Service '" + service.getIdentifier() + "' has component instance mapped to it hence it cannot be deleted.";
            LOGGER.error(err);
            throw new RequestException(err);
        }

        return AccountControllerKey.builder()
                .controllerId(serviceId)
                .account(bean.getAccount())
                .build();
    }

    public static int remove(AccountControllerKey key) throws ControlCenterException {
        try {
            String accountIdentifier = key.getAccount().getIdentifier();
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            Integer serviceId = dbi.inTransaction((conn, status) -> remService(key, conn));
            ServiceRepo serviceRepo = new ServiceRepo();
            AgentRepo agentRepo = new AgentRepo();
            ApplicationRepo applicationRepo = new ApplicationRepo();

            List<BasicEntity> allServicesDetails = serviceRepo.getAllServicesDetails(accountIdentifier);
            BasicEntity serviceToBeDeleted = allServicesDetails.parallelStream().filter(f -> f.getId()==serviceId).findAny().orElse(null);

            if(serviceToBeDeleted == null){
                LOGGER.error("Could not find service detail from services list for serviceId [{}]", serviceId);
                return -1;
            }
            deleteApplicationServiceMapping(accountIdentifier, serviceId, serviceRepo, applicationRepo, serviceToBeDeleted.getIdentifier());
            deleteAgentServiceMapping(accountIdentifier, serviceId, serviceRepo, agentRepo, serviceToBeDeleted.getIdentifier());
            serviceRepo.deleteServiceKeysBasedOnPattern(accountIdentifier, serviceToBeDeleted.getIdentifier());

            boolean serviceDeleted = serviceRepo.deleteServiceByServiceIdentifier(accountIdentifier, serviceToBeDeleted.getIdentifier());
            if(serviceDeleted){
                LOGGER.info("Service deleted from service level key successfully");
            }else{
                LOGGER.error("Error occurred while deleting service from service level key");
            }

            allServicesDetails.remove(serviceToBeDeleted);
            serviceRepo.updateServiceConfiguration(accountIdentifier, allServicesDetails);

            deleteServiceInstancesFromRedis(serviceId, accountIdentifier);

            return serviceId;
        } catch (Exception e) {
            throw new ControlCenterException(Throwables.getRootCause(e).getMessage());
        }
    }

    private static void deleteAgentServiceMapping(String accountIdentifier, Integer serviceId, ServiceRepo serviceRepo, AgentRepo agentRepo, String serviceIdentifier) {
        List<BasicAgentBean> agentsByServiceIdentifier = serviceRepo.getAgentsByServiceIdentifier(accountIdentifier, serviceIdentifier);
        for (BasicEntity agentDetail : agentsByServiceIdentifier) {
            List<BasicEntity> servicesMappedToAgents = agentRepo.getServices(accountIdentifier, agentDetail.getIdentifier());
            servicesMappedToAgents.removeIf(c->c.getId() == serviceId);
            agentRepo.updateServices(accountIdentifier, agentDetail.getIdentifier(), servicesMappedToAgents);
            agentRepo.deleteAgentServiceTxnMapperConfigurator(accountIdentifier, agentDetail.getIdentifier(), serviceIdentifier);
        }
    }

    private static void deleteApplicationServiceMapping(String accountIdentifier, Integer serviceId, ServiceRepo serviceRepo, ApplicationRepo applicationRepo, String serviceIdentifier) {
        List<BasicEntity> applicationsByServiceIdentifier = serviceRepo.getApplicationsByServiceIdentifier(accountIdentifier, serviceIdentifier);
        for (BasicEntity applicationDetail : applicationsByServiceIdentifier) {
            List<BasicEntity> servicesMappedToApplication = applicationRepo.getServicesMappedToApplication(accountIdentifier, applicationDetail.getIdentifier());
            servicesMappedToApplication.removeIf(c -> c.getId() == serviceId);
            applicationRepo.updateServiceApplication(accountIdentifier, applicationDetail.getIdentifier(), servicesMappedToApplication);
        }
    }

    private static int remService(AccountControllerKey key, Handle handle) throws ControlCenterException {

        int serviceId = key.getControllerId();
        int accountId = key.getAccount().getId();

        ServiceDataService serviceDataService = new ServiceDataService();
        serviceDataService.removeServiceConnections(accountId,serviceId,handle);
        serviceDataService.deleteServiceKpiThreshold(accountId,serviceId,handle);
        serviceDataService.deleteServiceConfigurations(accountId,serviceId,handle);
        serviceDataService.deleteServiceCommandArguments(serviceId,handle);
        serviceDataService.deleteUserForensicNotificationMapping(serviceId, handle);
        serviceDataService.deleteServiceAgentModeConfiguration(serviceId,handle);
        serviceDataService.deleteServiceMaintenanceMapping(serviceId,handle);
        serviceDataService.deletePluginKpiServiceMapping(serviceId,handle);
        serviceDataService.deleteServiceMappingDetails(serviceId,handle);
        serviceDataService.deleteTagMappingDetailsForController(serviceId,handle);
        deleteServiceInstances(serviceId, accountId, handle);
        serviceDataService.deleteController(serviceId,handle);

        LOGGER.info("Service is deleted successfully.");
        return serviceId;
    }

    private static void deleteServiceInstances(int serviceId, int accountId, Handle handle) throws ControlCenterException {
        ServiceInstanceDataService serviceInstanceDataService = new ServiceInstanceDataService();

        int serviceInstanceId = serviceInstanceDataService.getServiceInstanceIdByServiceId(serviceId, accountId, handle);
        if (serviceInstanceId == 0) {
            LOGGER.warn("Service instance not available for serviceId [{}] and accountId: [{}]", serviceId, accountId);
            return;
        }

        serviceInstanceDataService.deleteServiceInstanceAttributeByServiceInstanceId(serviceInstanceId, handle);
        serviceInstanceDataService.deleteServiceInstanceKpiDetailsByServiceInstanceId(serviceInstanceId, handle);
        serviceInstanceDataService.deleteServiceInstanceGroupKpiDetailsByServiceInstanceId(serviceInstanceId, handle);
        serviceInstanceDataService.deleteServiceInstanceByServiceId(serviceId, accountId, handle);

    }

    private static void deleteServiceInstancesFromRedis(int serviceId, String accountIdentifier) {
        ServiceInstanceRepo serviceInstanceRepo = new ServiceInstanceRepo();
        List<ServiceInstanceDetails> serviceInstances = serviceInstanceRepo.getServiceInstances(accountIdentifier);
        ServiceInstanceDetails serviceInstanceDetails = serviceInstances.parallelStream()
                .filter(serviceInstance -> serviceInstance.getServiceId() == serviceId)
                .findAny().orElse(null);

        serviceInstances.remove(serviceInstanceDetails);

        if (serviceInstanceDetails == null) {
            LOGGER.warn("serviceInstanceDetails not found for service Id: [{}], in redis", serviceId);
            return;
        }

        serviceInstanceRepo.deleteServiceInstance(accountIdentifier, serviceInstanceDetails.getIdentifier());
        serviceInstanceRepo.deleteServiceInstanceWiseAttributes(accountIdentifier, serviceInstanceDetails.getIdentifier());
        serviceInstanceRepo.updateServiceInstances(accountIdentifier, serviceInstances);

        List<ServiceInstanceKpiEntity> serviceInstancesWiseKpis = serviceInstanceRepo.getServiceInstancesWiseKpis(accountIdentifier, serviceInstanceDetails.getIdentifier());

        serviceInstancesWiseKpis.forEach(kpi -> {
            serviceInstanceRepo.deleteServiceInstanceKpiById(accountIdentifier, serviceInstanceDetails.getIdentifier(), kpi.getId());
            serviceInstanceRepo.deleteServiceInstanceKpiByIdentifier(accountIdentifier, serviceInstanceDetails.getIdentifier(), kpi.getIdentifier());
        });

        serviceInstanceRepo.deleteServiceInstanceWiseKpis(accountIdentifier, serviceInstanceDetails.getIdentifier());

    }
}
