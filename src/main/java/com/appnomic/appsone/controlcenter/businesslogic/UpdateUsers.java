package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.UserAccessDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.UserDataService;
import com.appnomic.appsone.controlcenter.dao.redis.AccountRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ApplicationRepo;
import com.appnomic.appsone.controlcenter.dao.redis.UsersRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.UpdateUsersPojo;
import com.appnomic.appsone.controlcenter.service.NotificationPreferencesDataService;
import com.appnomic.appsone.controlcenter.util.*;
import com.appnomic.appsone.keycloak.KeycloakConnectionManager;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import com.google.gson.reflect.TypeToken;
import com.heal.configuration.entities.UserAccessDetails;
import com.heal.configuration.pojos.*;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class UpdateUsers implements BusinessLogic<UpdateUsersPojo, UpdateUsersPojo, String> {

    private static final int SUPER_ADMIN = 1;

    private static final List<Integer> userAccessRole = Stream.of(ConfProperties.getString(Constants.USER_ACCESS_ROLE_PROPERTY_NAME, Constants.USER_ACCESS_ROLE_PROPERTY_VALUE)
                    .split(","))
            .map(Integer::parseInt)
            .collect(Collectors.toList());
    private static final String USER_NOT_EXIST = "User profile doesn't not exist.";
    private static final UserDataService userDataService = new UserDataService();


    @Override
    public UtilityBean<UpdateUsersPojo> clientValidation(RequestObject request) throws ClientException {

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        if (StringUtils.isEmpty(request.getBody())) {
            log.error("Request body is NULL or empty.");
            throw new ClientException("Request body is NULL or empty.");
        }

        log.info(request.getBody());
        String decryptedRequestBody;
        try {
            decryptedRequestBody = new AECSBouncyCastleUtil().decrypt(request.getBody());
        } catch (InvalidCipherTextException e) {
            throw new ClientException(e, e.getMessage());
        }
        UpdateUsersPojo updateUsersPojo;
        try {
            updateUsersPojo = new ObjectMapper().readValue(decryptedRequestBody,
                    new TypeReference<UpdateUsersPojo>() {
                    });
        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        if (!updateUsersPojo.validate()) {
            log.error("Request validation failure.");
            throw new ClientException("Request validation failure. Kindly check the logs.");
        }

        return UtilityBean.<UpdateUsersPojo>builder()
                .pojoObject(updateUsersPojo)
                .authToken(authToken)
                .build();

    }

    @Override
    public UpdateUsersPojo serverValidation(UtilityBean<UpdateUsersPojo> utilityBean) throws ServerException {

        String authorizedUser;
        try {
            authorizedUser = ValidationUtils.getUserId(utilityBean.getAuthToken());
            if (authorizedUser == null) {
                log.error(UIMessages.AUTH_KEY_INVALID);
                throw new ServerException(UIMessages.AUTH_KEY_INVALID);
            }
            UserAttributesBean authorizedUserAttributes = userDataService.getUserAttributes(authorizedUser);

            if (authorizedUserAttributes == null || (!userAccessRole.contains(authorizedUserAttributes.getRoleId()))) {
                log.error("This User either doesn't exist or unauthorized.");
                throw new ServerException("This User either doesn't exist or unauthorized.");
            }
        } catch (ControlCenterException e) {
            String err = "User Manager/SuperAdmin profile doesn't not exist.";
            log.error(err);
            throw new ServerException(err);
        }

        UpdateUsersPojo updateUsersPojo = utilityBean.getPojoObject();
        List<String> userIdentifiers = new ArrayList<>(updateUsersPojo.getDeleteUsers());
        userIdentifiers.addAll(updateUsersPojo.getModifyStatusForUsers());

        for (String identifier : userIdentifiers) {
            try {
                UserAttributesBean userAttributesForUser = userDataService.getUserAttributes(identifier);

                if (userAttributesForUser == null) {
                    log.error(USER_NOT_EXIST);
                    throw new ServerException(USER_NOT_EXIST);
                } else if (userAttributesForUser.getRoleId() == SUPER_ADMIN) {
                    log.error("Unauthorized to perform this operation. Reason: Super Admin can't be deleted or modified from the system.");
                    throw new ServerException("Unauthorized to perform this operation. Reason: Super Admin can't be deleted or modified from the system.");
                }
            } catch (ControlCenterException e) {
                log.error(USER_NOT_EXIST);
                throw new ServerException(USER_NOT_EXIST);
            }
        }

        updateUsersPojo.setUserId(authorizedUser);
        return updateUsersPojo;
    }

    @Override
    public String process(UpdateUsersPojo bean) throws DataProcessingException {
        try {
            String retVal = MySQLConnectionManager.getInstance().getHandle()
                    .inTransaction((conn, status) -> updateUsers(bean, conn));

            Set<String> modifyStatusForUsers = bean.getModifyStatusForUsers();
            Set<String> deleteUsers = bean.getDeleteUsers();

            if(!modifyStatusForUsers.isEmpty()) {
                for (String userIdentifier : modifyStatusForUsers) {
                    updateUsersInRedis(userIdentifier, bean.getStatus());
                }
            }

            if(!deleteUsers.isEmpty()) {
                for(String userIdentifier : deleteUsers) {
                    deleteUserDetailsFromRedis(userIdentifier);
                }
            }

            return retVal;

        } catch (Exception e) {
            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw (DataProcessingException) Throwables.getRootCause(e);
            } else {
                throw e;
            }
        }
    }

    private String updateUsers(UpdateUsersPojo bean, Handle handle) throws ControlCenterException {

        String response = "";

        Set<String> modifyStatusForUsers = bean.getModifyStatusForUsers();
        Set<String> deleteUsers = bean.getDeleteUsers();
        if (!modifyStatusForUsers.isEmpty()) {
            String userId = bean.getUserId();
            String timestamp = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
            for (String userIdentifier : modifyStatusForUsers) {
                userDataService.updateStatusForUser(userIdentifier, bean.getStatus(), timestamp, userId, handle);
            }


            response = "Status of the users updated successfully.";
        }

        if (!deleteUsers.isEmpty()) {
            String setup = userDataService.getSetup();
            if (setup == null) {
                log.error("Error while fetching integration mode.");
                throw new ControlCenterException("Unable to get Setup Mode.");
            }

            for (String userIdentifier : deleteUsers) {
                if (Constants.SETUP_KEYCLOAK.equalsIgnoreCase(setup.trim())) {
                    try {
                        KeycloakConnectionManager.deleteKeycloakUser(userIdentifier);
                    } catch (IOException e) {
                        log.error("Error while deleting the user in Keycloak.");
                        throw new ControlCenterException("Unable to delete user in Keycloak.");
                    }
                }

                NotificationPreferencesDataService.removeUserNotificationPreferencesForUser(userIdentifier, handle);
                //NotificationPreferencesDataService.removeForensicNotificationPreferencesForUser(userId, handle);
                NotificationPreferencesDataService.removeNotificationDetailsForUser(userIdentifier, handle);
                userDataService.deleteUserAttributesAndAccessDetails(userIdentifier, handle);
            }

            response = response.concat("Users deleted successfully.");
        }

        return response;
    }

    private void deleteUserDetailsFromRedis(String userIdentifier) {
        UsersRepo usersRepo = new UsersRepo();

        User userDetails = usersRepo.getUser(userIdentifier);

        if (userDetails == null) {
            log.info("All the user related redis keys have been already deleted for inactive users");
            return;
        }

        log.info("User related keys are present in Redis. Therefore it needs to be deleted");

        new UserUtility().removeUserDetailsFromAppLevelKeys(userIdentifier);

        usersRepo.deleteUserAccessDetails(userIdentifier);

        usersRepo.deleteUser(userIdentifier);
    }

    private void updateUsersInRedis(String userIdentifier, int status) throws DataProcessingException {
        try {
            UsersRepo usersRepo = new UsersRepo();
            UserUtility util = new UserUtility();

            if (status == 0) {
                log.info("Active user is deactivated. Therefore redis keys needs to be deleted");

                util.removeUserDetailsFromAppLevelKeys(userIdentifier);

                usersRepo.deleteUserAccessDetails(userIdentifier);

                usersRepo.deleteUser(userIdentifier);

                return;
            }

            updateInUserKey(userIdentifier, status, usersRepo);

            updateUserAccessKey(userIdentifier, status, usersRepo);

            util.populateUserDetailsInAppLevelKeys(userIdentifier);
        } catch (ControlCenterException e) {
            throw new DataProcessingException("Error while updating details for user " + userIdentifier);
        }
    }

    private void updateInUserKey(String userIdentifier, int status, UsersRepo usersRepo) throws ControlCenterException {
        User existingUsers = usersRepo.getUser(userIdentifier);
        if (existingUsers == null) {
            createUserDetailsForRedis(userIdentifier, status, usersRepo);
        } else {
            existingUsers.setStatus(status);
            usersRepo.addUser(existingUsers);
        }
    }

    private void updateUserAccessKey(String userIdentifier, int status, UsersRepo usersRepo) throws ControlCenterException {
        List<UserAccessDetails> userAccessDetails = usersRepo.getUserAccessDetails(userIdentifier);
        if (userAccessDetails == null) {
            log.info("User is being activated, and Redis has to be filled with user access information for this user.");
            createUserAccessDetails(userIdentifier);
        } else {
            log.debug("Update status field in user access details key");
            List<UserAccessDetails> updatedUserAccessDetails = usersRepo.getUserAccessDetails(userIdentifier)
                    .parallelStream()
                    .peek(p -> p.setStatus(status))
                    .collect(Collectors.toList());
            usersRepo.updateUserAccessDetails(userIdentifier, updatedUserAccessDetails);
        }
    }

    private void createUserDetailsForRedis(String userIdentifier, int status, UsersRepo usersRepo) throws ControlCenterException {
        log.info("User details added for user {} in redis", userIdentifier);

        UserDataService userDataService = new UserDataService();

        try {
            User userDetailsByUserIdentifier = userDataService.getUserDetailsByUserIdentifier(userIdentifier, null);
            NotificationChoiceBean userNotificationChoice = userDataService.getUserNotificationChoiceByIdentifier(userDetailsByUserIdentifier.getUserDetailsId());

            if (userNotificationChoice != null) {
                List<String> categoryIdentifiersByIds = null;
                // Populate categoryIdentifiers only if categoryIds are present in userNotificationChoice, otherwise set a null value in Redis.
                if (userNotificationChoice.getCategoryIds() != null && !userNotificationChoice.getCategoryIds().isEmpty()) {
                    categoryIdentifiersByIds = new BindInDataService().getCategoryIdentifiersByIds(userNotificationChoice.getCategoryIds(), null);
                }

                NotificationChoice notificationChoice = NotificationChoice.builder()
                        .categories(categoryIdentifiersByIds)
                        .component(userNotificationChoice.getComponentSelection())
                        .build();

                userDetailsByUserIdentifier.setNotificationChoice(notificationChoice);
            }

            userDetailsByUserIdentifier.setTimezoneDetail(userDataService.getUserTimeZone(userDetailsByUserIdentifier.getUserAttributesId(), null));
            userDetailsByUserIdentifier.setStatus(status);

            usersRepo.addUser(userDetailsByUserIdentifier);
        } catch (Exception e) {
            log.error("Failed to add user details in user level key in redis", e);
            throw new ControlCenterException(e.getMessage());
        }
    }

    private void createUserAccessDetails(String userIdentifier) throws ControlCenterException {
        AccountRepo accountRepo = new AccountRepo();
        ApplicationRepo applicationRepo = new ApplicationRepo();
        UsersRepo usersRepo = new UsersRepo();
        List<com.heal.configuration.entities.UserAccessDetails> userAccessDetailsList = new ArrayList<>();

        try {
            UserAccessBean userAccessDetailsFromDB = UserAccessDataService.getUserAccessDetails(userIdentifier);
            if (userAccessDetailsFromDB == null) {
                log.error("User access bean unavailable for user [{}]", userIdentifier);
                return;
            }

            Type userBeanType = new TypeToken<AccessDetailsBean>() {
            }.getType();

            AccessDetailsBean bean = CommonUtils.jsonToObject(userAccessDetailsFromDB.getAccessDetailsJson(), userBeanType);
            if (bean.getAccounts().get(0).contains("*")) {
                log.trace("Role of the user is User manager");
                log.trace("User profile is either super admin or user manager which means it has access to all accounts and applications hence skipping below steps");
                UserAccessDetails userAccessDetails = new UserAccessDetails();
                userAccessDetails.setAccountWiseAccessible(true);
                userAccessDetails.setStatus(1);
                userAccessDetailsList.add(userAccessDetails);
                usersRepo.updateUserAccessDetails(userIdentifier, userAccessDetailsList);

            } else if (bean.getAccountMapping().get(bean.getAccounts().get(0)).getApplications().contains("*")) {
                log.trace("Role of the user is heal admin");
                Map<String, Account> accountDetailsFromRedis = accountRepo.getAccounts().parallelStream().collect(Collectors.toMap(BasicEntity::getIdentifier, Function.identity()));
                bean.getAccounts().forEach(account -> {
                    Account accountWithAccountIdentifier = accountDetailsFromRedis.get(account);
                    if (accountWithAccountIdentifier == null) {
                        log.error("Account with Identifier: [{}] is missing from the DB but exists in the user_access_details table.", account);
                        return;
                    }
                    userAccessDetailsList.add(UserAccessDetails.builder()
                            .account(BasicEntity.builder()
                                    .id(accountWithAccountIdentifier.getId())
                                    .status(accountWithAccountIdentifier.getStatus())
                                    .createdTime(accountWithAccountIdentifier.getCreatedTime())
                                    .updatedTime(accountWithAccountIdentifier.getUpdatedTime())
                                    .name(accountWithAccountIdentifier.getName())
                                    .identifier(accountWithAccountIdentifier.getIdentifier())
                                    .lastModifiedBy(accountWithAccountIdentifier.getLastModifiedBy())
                                    .accountId(accountWithAccountIdentifier.getAccountId())
                                    .build())
                            .accountWiseAccessible(true)
                            .status(1)
                            .build());
                });
                usersRepo.updateUserAccessDetails(userIdentifier, userAccessDetailsList);
            } else {
                List<BasicEntity> applicationServiceMappingFinalList = new ArrayList<>();
                List<UserAccessDetails> userAccessDetails = new ArrayList<>();
                for (Map.Entry<String, AccessDetailsBean.Application> accountApplicationMapping : bean.getAccountMapping().entrySet()) {
                    List<String> accessibleApplications = accountApplicationMapping.getValue().getApplications();
                    Map<String, Account> accountDetailsFromRedis = accountRepo.getAccounts().parallelStream().collect(Collectors.toMap(BasicEntity::getIdentifier, Function.identity()));
                    List<Application> applicationsForAccountFromRedis = applicationRepo.getApplicationsForAccount(accountApplicationMapping.getKey());
                    List<BasicEntity> applicationBasicEntityList = applicationsForAccountFromRedis.parallelStream().filter(f -> accessibleApplications.contains(f.getIdentifier())).collect(Collectors.toList());
                    for (String accessibleApplication : accessibleApplications) {
                        List<BasicEntity> servicesMappedToApplication = applicationRepo.getServicesMappedToApplication(accountApplicationMapping.getKey(), accessibleApplication);
                        applicationServiceMappingFinalList.addAll(servicesMappedToApplication);
                    }
                    Account account = accountDetailsFromRedis.get(accountApplicationMapping.getKey());
                    userAccessDetails.add(UserAccessDetails.builder()
                            .account(BasicEntity.builder()
                                    .id(account.getId())
                                    .status(account.getStatus())
                                    .createdTime(account.getCreatedTime())
                                    .updatedTime(account.getUpdatedTime())
                                    .name(account.getName())
                                    .identifier(account.getIdentifier())
                                    .lastModifiedBy(account.getLastModifiedBy())
                                    .build())
                            .applications(applicationBasicEntityList)
                            .services(applicationServiceMappingFinalList)
                            .accountWiseAccessible(false)
                            .status(1)
                            .build());
                }
                usersRepo.updateUserAccessDetails(userIdentifier, userAccessDetails);
            }
        } catch (ControlCenterException e) {
            log.error("Error occurred while populating user access details for user [{}]", userIdentifier);
            log.error(e.getMessage());
            throw new ControlCenterException(e.getMessage());
        }
    }
}

