package com.appnomic.appsone.controlcenter.cache;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.keys.*;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.*;
import com.appnomic.appsone.controlcenter.pojo.AllKpiList;
import com.appnomic.appsone.controlcenter.pojo.Controller;
import com.appnomic.appsone.controlcenter.pojo.KeyCloakUserDetails;
import com.appnomic.appsone.controlcenter.pojo.TimezoneDetail;
import com.appnomic.appsone.controlcenter.service.KeyCloakAuthService;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Kumar : 15/1/19
 */
public enum MasterCache {

    INSTANCE;

    private static final Logger log = LoggerFactory.getLogger(MasterCache.class);

    private static final String LOGGER_TRACE = "Trace : ";

    private static final Integer MAX_SIZE = ConfProperties.getInt(Constants.CACHE_MAXIMUM_SIZE_PROPERTY_NAME,
            Constants.CACHE_MAXIMUM_SIZE_DEFAULT_VALUE);
    private static final Integer CACHE_TIMEOUT = ConfProperties.getInt(Constants.CACHE_TIMEOUT_IN_MINUTES_PROPERTY_NAME,
            Constants.CACHE_TIMEOUT_IN_MINUTES_DEFAULT_VALUE);
    public static final ConcurrentHashMap<String, String> addTxnRequests = new ConcurrentHashMap<>();



    /**
     * Key : 'viewKpiGroups'
     * value : records from view_kpi_groups
     */
    private static final LoadingCache<String, List<ViewKpiGroupsBean>> viewKpiGroups = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<String, List<ViewKpiGroupsBean>>() {
                @Override
                public List<ViewKpiGroupsBean> load(String s) throws Exception {
                    return MasterDataService.getViewKpiGroups();
                }
            });

    /**
     * Key : 'ALL_TYPES'
     * value : list of type and subtypes
     */
    private static final LoadingCache<String, List<ViewTypes>> viewAllTypes = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<String, List<ViewTypes>>() {
                @Override
                public List<ViewTypes> load(String s) throws Exception {
                    return MasterDataService.getAllTypes();
                }
            });

    /**
     * Key : 'masterComponents'
     * value : list of master component objects
     */
    private static final LoadingCache<String, List<MasterComponentBean>> masterComponents = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<String, List<MasterComponentBean>>() {
                @Override
                public List<MasterComponentBean> load(String s) throws Exception {
                    return MasterDataService.getComponentMasterData(Integer.parseInt(s));
                }
            });
    /**
     * Key : 'masterComponentsTypes'
     * value : list of master component types objects
     */
    private static final LoadingCache<String, List<MasterComponentTypeBean>> masterComponentTypes = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<String, List<MasterComponentTypeBean>>() {
                @Override
                public List<MasterComponentTypeBean> load(String s) throws Exception {
                    return MasterDataService.getMasterComponentTypeData(Constants.DEFAULT_ACCOUNT_ID);
                }
            });
    /**
     * Key : 'MstCompVersion'
     * value : list of master component version objects
     */
    private static final LoadingCache<MstCompVersion, MasterComponentVersionBean> masterComponentsVersion = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<MstCompVersion, MasterComponentVersionBean>() {
                @Override
                public MasterComponentVersionBean load(MstCompVersion key) throws Exception {
                    return MasterDataService.getMasterComponentVersionData(key.getMstCompId(), key.getCompVersionName(), key.getAccountId());
                }
            });

    /**
     * Key : 'mst_common_version_id'
     * value : list of attributes object
     */
    private static final LoadingCache<Integer, List<AttributesViewBean>> attributes = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<Integer, List<AttributesViewBean>>() {
                @Override
                public List<AttributesViewBean> load(Integer key) throws Exception {
                    return MasterDataService.getAttributesViewData(key);
                }
            });
    /**
     * Key : 'CommVersionKPIs'
     * value : list of common version KPIs object
     */
    private static final LoadingCache<CommVersionKPIs, List<ViewCommonVersionKPIsBean>> viewCommonVersionKPIsGroup = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<CommVersionKPIs, List<ViewCommonVersionKPIsBean>>() {
                @Override
                public List<ViewCommonVersionKPIsBean> load(CommVersionKPIs key) throws Exception {
                    return MasterDataService.getViewCommonVersionGroupKPIsData(key.getMstCommonVersionId(), key.getAccountId());
                }
            });
    /**
     * Key : 'CommVersionKPIs'
     * value : list of common version KPIs object
     */
    private static final LoadingCache<CommVersionKPIs, List<ViewCommonVersionKPIsBean>> viewCommonVersionKPIsNonGroup = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<CommVersionKPIs, List<ViewCommonVersionKPIsBean>>() {
                @Override
                public List<ViewCommonVersionKPIsBean> load(CommVersionKPIs key) throws Exception {
                    return MasterDataService.getViewCommonVersionNonGroupKPIsData(key.getMstCommonVersionId(), key.getAccountId());
                }
            });
    /**
     * Key : we are creating key using these fields-mst_kpi_details_id,mst_component_version_id,mst_component_id,mst_component_type_id
     * value : producer KPIs object
     */
    private static final LoadingCache<ProducerKpis, ViewProducerKPIsBean> viewProducerKPIsNonGroup = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<ProducerKpis, ViewProducerKPIsBean>() {
                @Override
                public ViewProducerKPIsBean load(ProducerKpis key) throws Exception {
                    return MasterDataService.getViewProducerNonGroupKPIsData(key.getMstKpiDetailsId(), key.getMstCompVersionId(),
                            key.getMstCompId(), key.getMstCompTypeId(), key.getAccountId());
                }
            });

    /**
     * Key : we are creating key using these fields-mst_kpi_details_id,mst_component_version_id,mst_component_id,mst_component_type_id
     * value : producer KPIs object
     */
    private static final LoadingCache<ProducerKpis, ViewProducerKPIsBean> viewProducerKPIsGroup = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<ProducerKpis, ViewProducerKPIsBean>() {
                @Override
                public ViewProducerKPIsBean load(ProducerKpis key) throws Exception {
                    return MasterDataService.getViewProducerGroupKPIsData(key.getMstKpiDetailsId(), key.getMstCompVersionId(),
                            key.getMstCompId(), key.getMstCompTypeId(), key.getAccountId());
                }
            });
    /**
     * Key : MstKpi
     * value : Master  KPI details object
     */
    private static final LoadingCache<MstKpi, MasterKPIDetailsBean> masterKPIDetailsBean = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<MstKpi, MasterKPIDetailsBean>() {
                @Override
                public MasterKPIDetailsBean load(MstKpi key) throws Exception {
                    return MasterDataService.getMasterKPIDetailsData(key.getKpiId(), key.getAccountId());
                }
            });

    /**
     * Key : agent uid
     * value : Master  KPI details object
     */
    private static final LoadingCache<String, AgentBean> agentBeans = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<String, AgentBean>() {
                @Override
                public AgentBean load(String key) throws Exception {
                    return AgentDataService.getAgentBeanData(key);
                }
            });

    /**
     * Key : agent name
     * value : Agent bean
     */
    private static final LoadingCache<String, AgentBean> agentListNameAsKey = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<String, AgentBean>() {
                @Override
                public AgentBean load(String key) throws Exception {
                    return AgentDataService.getAgentBeanDataForName(key);
                }
            });

    /**
     * Key : agent uid
     * value : Master  KPI details object
     */
    private static final LoadingCache<String, TagDetailsBean> tagDetails = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<String, TagDetailsBean>() {
                @Override
                public TagDetailsBean load(String key) throws Exception {
                    return MasterDataService.getTagDetails(key);
                }
            });

    /**
     * key: "DataCommunication"
     * value: DataCommunicationDetailsBean
     */
    private static final LoadingCache<DataCommunication, DataCommunicationDetailsBean> dataCommunicationDetails = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<DataCommunication, DataCommunicationDetailsBean>() {
                @Override
                public DataCommunicationDetailsBean load(DataCommunication key) throws Exception {
                    return DataCommunicationDataService.getDataCommunicationDetails(key.getName());
                }
            });

    /**
     * key: "data communication id"
     * value: DataCommunicationDetailsBean
     */
    private static final LoadingCache<Integer, DataCommunicationDetailsBean> dataCommunicationDetailsCache = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<Integer, DataCommunicationDetailsBean>() {
                @Override
                public DataCommunicationDetailsBean load(Integer key) throws Exception {
                    return DataCommunicationDataService.getDataCommunicationDetailsById(key);
                }
            });

    /**
     * key : agentDetails
     * value : all agent details
     */
    private static final LoadingCache<String, List<AgentBean>> agentDetails = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<String, List<AgentBean>>() {
                @Override
                public List<AgentBean> load(String agentDetails) throws Exception {
                    return AgentDataService.getAgentList();
                }
            });

    /**
     * key : accountId
     * value : list of all applications
     */
    private static final LoadingCache<Integer, List<Controller>> controllerDetails = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<Integer, List<Controller>>() {
                @Override
                public List<Controller> load(Integer accountId) throws Exception {
                    return MasterDataService.getControllerList(accountId);
                }
            });

    /**
     * Key : account id-component instance name
     * Value : Component instance object
     */
    private static final LoadingCache<String, ComponentInstanceBean> compInstancesForAccountCompInstName = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<String, ComponentInstanceBean>() {
                @Override
                public ComponentInstanceBean load(String key) throws Exception {
                    String[] strings = key.split(Constants.SEPARATOR);
                    return MasterDataService.getCompInstForAccountComInstName(strings[0], Integer.parseInt(strings[1]));
                }
            });

    /**
     * Key : account id-component instance id
     * Value : Component instance object
     */
    private static final LoadingCache<Integer, InstanceMappingDetails> instanceMappingDetailsCache = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<Integer, InstanceMappingDetails>() {
                @Override
                public InstanceMappingDetails load(Integer compInstanceId) throws Exception {
                    return MasterDataService.getInstanceMappingDetails(compInstanceId);
                }
            });

    private static final LoadingCache<Integer, List<MasterKpiGroupBean>> masterKpiGroupList = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<Integer, List<MasterKpiGroupBean>>() {
                @Override
                public List<MasterKpiGroupBean> load(Integer accountId) throws Exception {
                    return MasterDataService.getMasterKpiGroupDetails(accountId);
                }
            });

    private static final LoadingCache<String, List<ViewCoverageWinProfDetailsBean>> coverageWindowProfileDetails = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterAccess(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<String, List<ViewCoverageWinProfDetailsBean>>() {
                @Override
                public List<ViewCoverageWinProfDetailsBean> load(String key) throws Exception {
                    return ThresholdDataService.getCoverageWindowsProfiles();
                }
            });

    private static final LoadingCache<String, List<AllKpiList>> viewAllKpis = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterAccess(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<String, List<AllKpiList>>() {
                @Override
                public List<AllKpiList> load(String key) throws Exception {
                    return MasterDataService.getAllKpisList();
                }
            });

    /**
     * key: producerId, producerType
     * value: producer type detail
     */
    private static final LoadingCache<ProducerTypeDetailKey, ProducerTypeDetail> producerTypeDetailCache = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<ProducerTypeDetailKey, ProducerTypeDetail>() {
                @Override
                public ProducerTypeDetail load(ProducerTypeDetailKey key) {
                    if (key == null) {
                        return null;
                    }
                    return ProducerDataService.getProducerTypeDetail(key.getProducerId(), key.getProducerType());
                }
            });

    private static final LoadingCache<Integer, List<ProducerParameter>> producerParameterCache = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<Integer, List<ProducerParameter>>() {
                @Override
                public List<ProducerParameter> load(Integer producerId) {
                    return ProducerDataService.getProducerParameter(producerId);
                }
            });

    private static final LoadingCache<Integer, List<NotificationPlaceholderBean>> notificationPlaceholderCache =
            CacheBuilder.newBuilder().maximumSize(MAX_SIZE)
                    .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
                    .build(new CacheLoader<Integer, List<NotificationPlaceholderBean>>() {
                        @Override
                        public List<NotificationPlaceholderBean> load(Integer integer) throws Exception {
                            return new NotificationDataService().getNotificationPlaceholders();
                        }
                    });

    private static final LoadingCache<String, List<TimezoneDetail>> allTimezones = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<String, List<TimezoneDetail>>() {
                @Override
                public List<TimezoneDetail> load(String key) throws Exception {
                    return MasterDataService.getAllTimezones();
                }
            });

    private static final LoadingCache<String, List<ProducerTypeBean>> getProducerTypes = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<String, List<ProducerTypeBean>>() {
                @Override
                public List<ProducerTypeBean> load(String key) throws Exception {
                    return MasterDataService.getProducerTypes();
                }
            });

    private static final LoadingCache<String, List<MasterComponentVersionBean>> allMasterComponentVersion = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<String, List<MasterComponentVersionBean>>() {
                @Override
                public List<MasterComponentVersionBean> load(String s) throws Exception {
                    return MasterDataService.getAllMasterComponentVersionData();
                }
            });


    public static List<ProducerTypeBean> getProducerTypes() {
        try {
            return getProducerTypes.get("");
        } catch (Exception e) {
            log.error("Exception encountered while fetching timezones. Details: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public static List<TimezoneDetail> getTimezones() {
        try {
            return allTimezones.get("allTimezones");
        } catch (Exception e) {
            log.error("Exception encountered while fetching timezones. Details: {}", e.getMessage());
        }
        return Collections.emptyList();
    }

    /**
     * key: component name
     * value: list of component
     */
    private static final LoadingCache<String, List<ComponentDetailBean>> componentDetailCache =
            CacheBuilder.newBuilder()
                    .maximumSize(MAX_SIZE)
                    .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
                    .build(new CacheLoader<String, List<ComponentDetailBean>>() {
                        @Override
                        public List<ComponentDetailBean> load(String componentName) throws Exception {
                            return ComponentDataService.getComponentDetail(componentName);
                        }
                    });

    public static ComponentInstanceBean getCompInstUsingAccountIdAndInstName(String name, int accountId) {
        try {
            return compInstancesForAccountCompInstName.get(name + Constants.SEPARATOR + accountId);
        } catch (Exception e) {
            log.warn("Exception encountered while fetching component instance details from instance name. Details: {}", e.getMessage());
        }
        return null;
    }

    public static InstanceMappingDetails getInstanceMappingDetails(int compInstanceId) {
        try {
            return instanceMappingDetailsCache.get(compInstanceId);
        } catch (Exception e) {
            log.warn("Exception encountered while fetching instance mapping information. Details: {}", e.getMessage());
        }
        return null;
    }

    public static List<Controller> getControllerList(Integer accountId) {

        List<Controller> controllerList = new ArrayList<>();
        try {
            controllerList = controllerDetails.get(accountId);

        } catch (Exception e) {
            log.error("Error occurred while getting application list" + e);
        }
        return controllerList;
    }

    public static List<AgentBean> getAgentList(String agent) {

        List<AgentBean> agentBeanList = new ArrayList<>();
        try {
            agentBeanList = agentDetails.get(agent);
        } catch (Exception e) {
            log.error("Error occurred while getting agents" + e);
        }
        return agentBeanList;

    }

    public static List<AccountBean> getAccounts() {
        return AccountDataService.getAccountList(null);
    }

    public static List<AccountBean> getFilteredAccounts() {
        try {
            List<AccountBean> accountList = AccountDataService.getAccountList(null);
            return accountList.stream()
                    .filter(t -> t.getId() != 1)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Exception encountered while fetching account list. Details: {}", e.getMessage());
        }
        return Collections.emptyList();
    }

    /**
     * Get master component object using component name
     */
    public static MasterComponentBean getMasterComponentUsingName(String mstCompName, String accountId) {
        try {
            List<MasterComponentBean> masterComponentBeanList = masterComponents.get(accountId);
            return masterComponentBeanList.stream().filter(masterComponentBean -> masterComponentBean.getName().equals(mstCompName)).findAny().orElse(null);
        } catch (Exception e) {
            log.error("Exception encountered while fetching master component bean using component name. Details: {}", e.getMessage());
        }
        return null;
    }

    /**
     * Get master component object using component name
     */
    public static MasterComponentBean getMasterComponentUsingNameAndVersion(String mstCompName, String mstCompVersion, String accountId) {
        try {
            List<MasterComponentBean> masterComponentBeanList = masterComponents.get(accountId);
            return masterComponentBeanList.stream().filter(masterComponentBean -> masterComponentBean.getName().equals(mstCompName) && masterComponentBean.getComponentVersionName().equals(mstCompVersion)).findAny().orElse(null);
        } catch (Exception e) {
            log.error("Exception encountered while fetching master component bean. Details: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * Get master component object using component id
     */
    public static MasterComponentBean getMasterComponentUsingId(int mstCompId, String componentVersionId ,String accountId) {
        try {
            int compVersionId = Integer.parseInt(componentVersionId);
            List<MasterComponentBean> masterComponentBeanList = masterComponents.get(accountId);
            return masterComponentBeanList.stream().filter(masterComponentBean -> masterComponentBean.getId() == mstCompId && masterComponentBean.getComponentVersionId() == compVersionId).findAny().orElse(null);
        } catch (Exception e) {
            log.error("Exception encountered while fetching master component bean. Details: {}", e.getMessage(), e);
        }
        return null;
    }


    /**
     * Get all component by component id
     *
     * @return
     */
    public static List<MasterComponentBean> getMasterComponentsById(int mstCompId, String accountId) {
        try {
            List<MasterComponentBean> masterComponentBeanList = masterComponents.get(accountId);
            return masterComponentBeanList
                    .stream()
                    .filter(masterComponentBean -> masterComponentBean.getId() == mstCompId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Exception getting all component by component id: {}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    /**
     * Get master component type according to name
     */
    public static MasterComponentTypeBean getMasterComponentTypeUsingName(String mstCompTypeName, String accountId) {
        try {
            List<MasterComponentTypeBean> masterComponentTypeBeanList = masterComponentTypes.get(accountId);
            return masterComponentTypeBeanList.stream().filter(masterComponentTypeBean -> masterComponentTypeBean.getName()
                    .equalsIgnoreCase(mstCompTypeName)).findAny().orElse(null);
        } catch (Exception e) {
            log.debug("Error in getMasterComponentTypeUsingName.", e);
        }
        return null;
    }

    public static MasterComponentVersionBean getMasterComponentVersionUsingName(MstCompVersion mstCompVersion) {
        try {
            return masterComponentsVersion.get(mstCompVersion);
        } catch (Exception e) {
            log.debug("Error in getMasterComponentVersionUsingName.", e);
        }
        return null;
    }

    public static List<AttributesViewBean> getAttributesViewBeanList(int mstCommonVersionId) {
        try {
            return attributes.get(mstCommonVersionId);
        } catch (Exception e) {
            log.debug("Error in getAttributesViewBeanList.", e);
        }
        return null;
    }

    public static List<ViewCommonVersionKPIsBean> getGroupKPIUsingCommonVersionId(CommVersionKPIs commVersionKPIs) {
        try {
            return viewCommonVersionKPIsGroup.get(commVersionKPIs);
        } catch (Exception e) {
            log.debug("Error in getGroupKPIUsingCommonVersionId.", e);
        }
        return null;
    }

    public static List<ViewCommonVersionKPIsBean> getNonGroupKPIUsingCommonVersionId(CommVersionKPIs commVersionKPIs) {
        try {
            return viewCommonVersionKPIsNonGroup.get(commVersionKPIs);
        } catch (Exception e) {
            log.debug("Error in getNonGroupKPIUsingCommonVersionId.", e);
        }
        return null;
    }

    public static ViewProducerKPIsBean getViewProducerKPIsGroup(ProducerKpis key) {
        try {
            return viewProducerKPIsGroup.get(key);
        } catch (Exception e) {
            log.debug("Error in getViewProducerKPIsGroup.", e);
        }
        return null;
    }

    public static ViewProducerKPIsBean getViewProducerKPIsNonGroup(ProducerKpis key) {
        try {
            return viewProducerKPIsNonGroup.get(key);
        } catch (Exception e) {
            log.debug("Error in getViewProducerKPIsNonGroup.", e);
        }
        return null;
    }

    public static MasterKPIDetailsBean getMasterKPIDetailsBean(MstKpi kpiDetailsId) {
        try {
            return masterKPIDetailsBean.get(kpiDetailsId);
        } catch (Exception e) {
            log.debug("Error in getMasterKPIDetailsBean.", e);
        }
        return null;
    }

    public List<MasterComponentBean> getMstComponent(int accountId) {
        try {
            return masterComponents.get(String.valueOf(accountId));
        } catch (Exception e) {
            log.debug("Error in getMstComponent.", e);
        }
        return null;
    }

    public static List<MasterComponentBean> getMstComponentBypassCache(int accountId) {
        try {
            return MasterDataService.getComponentMasterData(accountId);
        } catch (Exception e) {
            log.error(LOGGER_TRACE, e);
        }
        return null;
    }

    public static AgentBean getAgentBean(String agentUid) {
        try {
            return agentBeans.get(agentUid);
        } catch (Exception e) {
            log.debug("Error in getAgentBean.", e);
        }
        return null;
    }

    public static AgentBean getAgentBeanForName(String agentName) {
        try {
            return agentListNameAsKey.get(agentName);
        } catch (Exception e) {
            log.debug("Error in getAgentBeanForName.", e);
        }
        return null;
    }

    public static TagDetailsBean getTagDetails(String tagName) {
        try {
            return tagDetails.get(tagName);
        } catch (Exception e) {
            log.debug("Error in getTagDetails.", e);
        }
        return null;
    }

    public static KeyCloakUserDetails getKeycloakUserDetails(String userIdentifier) {
        try {
            return keycloakUserData.get(userIdentifier);
        } catch (Exception e) {
            log.debug("Error in getKeycloakUserDetails.", e);
        }
        return null;
    }

    private static final LoadingCache<String, KeyCloakUserDetails> keycloakUserData = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<String, KeyCloakUserDetails>() {
                @Override
                public KeyCloakUserDetails load(String userIdentifier) throws Exception {
                    return KeyCloakAuthService.getKeycloakUserDataFromId(userIdentifier);
                }
            });

    public static DataCommunicationDetailsBean getDataCommunicationDetails(DataCommunication dataCommunication) {
        try {
            return dataCommunicationDetails.get(dataCommunication);
        } catch (Exception e) {
            log.debug("Error in getDataCommunicationDetails.", e);
        }
        return null;
    }

    public static DataCommunicationDetailsBean getDataCommunicationDetails(int dataCommunicationId) {
        try {
            return dataCommunicationDetailsCache.get(dataCommunicationId);
        } catch (Exception e) {
            log.debug("Error in getDataCommunicationDetails.", e);
        }
        return null;
    }

    public static List<ViewCoverageWinProfDetailsBean> getCoverageWinProfDetailsList(String profileName) {
        try {
            return coverageWindowProfileDetails.get(Constants.ALL_PROFILES).stream()
                    .filter(it -> it.getProfileName().equals(profileName))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.debug("Error in getCoverageWinProfDetailsList.", e);
        }
        return null;
    }

    public static List<MasterKpiGroupBean> getGroupKpiDetailList(int accId) {
        try {
            return masterKpiGroupList.get(accId);
        } catch (Exception e) {
            log.debug("Error in getGroupKpiDetailList.", e);
        }
        return Collections.emptyList();
    }

    public static ViewKpiGroupsBean getViewKpiGroupsByName(String identifier) {
        ViewKpiGroupsBean kpiGroupsBean = null;
        try {
            Optional<ViewKpiGroupsBean> kpiGroupsBeanOptional = viewKpiGroups.get("viewKpiGroups")
                    .stream()
                    .filter(it -> (it.getIdentifier().equals(identifier)))
                    .findFirst();

            if (kpiGroupsBeanOptional.isPresent()) {
                kpiGroupsBean = kpiGroupsBeanOptional.get();
            }

        } catch (Exception e) {
            log.debug("Error in getTypeDetails.", e);
        }
        return kpiGroupsBean;
    }

    public static ViewTypes getTypeDetails(String mstTypeName) {
        ViewTypes typeBean = null;
        try {
            /* get the master type bean to get id */
            Optional<ViewTypes> typeBeanOptional = viewAllTypes.get(Constants.ALL_TYPES)
                    .stream()
                    .filter(it -> (mstTypeName.trim().equals(it.getTypeName())))
                    .findFirst();

            if (typeBeanOptional.isPresent()) {
                typeBean = typeBeanOptional.get();
            }

        } catch (Exception e) {
            log.debug("Error in getTypeDetails.", e);
        }
        return typeBean;
    }

    public static List<ViewTypes> getTypeDetailsList(String mstTypeName) {
        try {
            /* get the master type bean to get id */
            return viewAllTypes.get(Constants.ALL_TYPES)
                    .stream()
                    .filter(it -> (mstTypeName.trim().equals(it.getTypeName())))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.debug("Error in getTypeDetailsList.", e);
        }
        return null;
    }

    public static List<ViewTypes> getTypeDetailsBypassCache(String mstTypeName) {
        try {
            return MasterDataService.getAllTypes()
                    .stream()
                    .filter(it -> (mstTypeName.trim().equals(it.getTypeName())))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error(LOGGER_TRACE, e);
        }
        return null;
    }

    public static List<ViewTypes> getSubTypeDetails(String type) {
        try {
            return viewAllTypes.get(Constants.ALL_TYPES)
                    .stream()
                    .filter(it -> it.getTypeName().equalsIgnoreCase(type))
                    .collect(Collectors.toList());
        } catch (ExecutionException e) {
            log.error("Trace log: {}", type, e);
        }
        return Collections.emptyList();
    }

    public static MasterKpiGroupBean getGroupKpiDetailList(int accountId, int kpiGroupId) {
        MasterKpiGroupBean groupBean = null;
        try {
            Optional<MasterKpiGroupBean> optionalGroupBean = getGroupKpiDetailList(accountId)
                    .stream()
                    .filter(it -> (it.getId() == kpiGroupId))
                    .findAny();

            if (optionalGroupBean.isPresent()) {
                groupBean = optionalGroupBean.get();
            }
        } catch (Exception e) {
            log.error("Error occurred while fetching group kpi details from database.", e);
        }
        return groupBean;
    }


    public static ViewTypes getMstTypeById(int typeId) {
        try {
            return viewAllTypes
                    .get(Constants.ALL_TYPES)
                    .stream()
                    .filter(it -> it.getTypeId() == typeId)
                    .findAny()
                    .orElse(null);

        } catch (Exception e) {
            log.error("Error occurred while fetching master type and sub type view from DB", e);
        }
        return null;
    }

    public static ViewTypes getMstTypeForSubTypeName(String typeName, String subTypeName) {
        ViewTypes subType = null;
        try {
            Optional<ViewTypes> subTypeOptional = viewAllTypes
                    .get(Constants.ALL_TYPES)
                    .stream()
                    .filter(it -> (typeName.trim().equalsIgnoreCase(it.getTypeName())))
                    .filter(it -> (subTypeName.trim().equalsIgnoreCase(it.getSubTypeName())))
                    .findAny();

            if (subTypeOptional.isPresent())
                subType = subTypeOptional.get();

        } catch (Exception e) {
            log.error("Error occurred while fetching master type and sub type view from DB. Reason: {}", e.getMessage());
            log.debug(LOGGER_TRACE, e);
        }
        return subType;
    }

    public static ViewTypes getMstSubTypeForSubTypeId(int subTypeId) {
        ViewTypes subType = null;
        try {
            Optional<ViewTypes> subTypeOptional = viewAllTypes
                    .get(Constants.ALL_TYPES)
                    .stream()
                    .filter(it -> (subTypeId == it.getSubTypeId()))
                    .findAny();

            if (subTypeOptional.isPresent())
                subType = subTypeOptional.get();
        } catch (Exception e) {
            log.error("Error occurred while fetching master type and sub type details from DB : {}", e.getMessage());
            log.debug(LOGGER_TRACE, e);
        }
        return subType;
    }

    public static ViewTypes getMstSubTypeForSubTypeName(String subTypeName) {
        ViewTypes subType = null;
        try {
            Optional<ViewTypes> subTypeOptional = viewAllTypes
                    .get(Constants.ALL_TYPES)
                    .stream()
                    .filter(it -> (subTypeName.equalsIgnoreCase(it.getSubTypeName())))
                    .findAny();

            if (subTypeOptional.isPresent())
                subType = subTypeOptional.get();
        } catch (Exception e) {
            log.error("Error occurred while fetching master type and sub type details from DB : {}", e.getMessage());
            log.debug(LOGGER_TRACE, e);
        }
        return subType;
    }

    public static List<ViewTypes> getSubtypesForTypeId(int typeId) {
        List<ViewTypes> subTypeList = null;
        try {
            subTypeList = viewAllTypes
                    .get(Constants.ALL_TYPES)
                    .stream()
                    .filter(subType -> (subType.getTypeId() == typeId))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error occurred while fetching sub types for typeId: {}", typeId, e);
        }
        return subTypeList;
    }

    public static List<ViewTypes> getSubtypesForTypeIdBypassCache(int typeId) {
        List<ViewTypes> subTypeList = null;
        try {
            subTypeList = MasterDataService.getAllTypes()
                    .stream()
                    .filter(subType -> (subType.getTypeId() == typeId))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error occurred while fetching sub types for typeId: {}", typeId, e);
        }
        return subTypeList;
    }

    public static List<AllKpiList> getAllKpi() {
        try {
            return viewAllKpis.get(Constants.VIEW_ALL_KPI);
        } catch (Exception e) {
            log.error("Error in getAllKpi.", e);
        }
        return Collections.emptyList();
    }

    public static ProducerTypeDetail getProducerTypeDetail(Integer producerId, String producerType) {
        try {
            ProducerTypeDetailKey key = new ProducerTypeDetailKey(producerId, producerType);
            return producerTypeDetailCache.get(key);
        } catch (Exception e) {
            log.warn("Error occurred while fetching producer type details for producerId: {}" +
                    " and producerType: {}", producerId, producerType);
        }
        return null;
    }

    public static List<ProducerParameter> getProducerParameter(Integer producerId) {
        try {
            return producerParameterCache.get(producerId);
        } catch (Exception e) {
            log.warn("Error occurred while fetching producer parameter for producerId: {}",
                    producerId);
        }
        return null;
    }

    public static List<NotificationPlaceholderBean> getNotificationPlaceholders() {
        try {
            return notificationPlaceholderCache.get(1);
        } catch (Exception e) {
            log.debug("Error occurred while fetching notification placeholders");
        }
        return Collections.emptyList();
    }

    public static List<ComponentDetailBean> getComponentDetail(String name) {
        try {
            return componentDetailCache.get(name);
        } catch (Exception e) {
            log.debug("Error occurred while fetching component details for name : {}, ", name, e);
        }
        return null;
    }


    /**
     * Key : 'masterPageActions'
     * value : list of master page actions
     */
    private static final LoadingCache<String, List<MasterPageActionBean>> masterPageActions = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<String, List<MasterPageActionBean>>() {
                @Override
                public List<MasterPageActionBean> load(String s) throws Exception {
                    return MasterDataService.getPageActionsMasterData();
                }
            });

    /**
     * Key : 'masterBigFeatures'
     * value : list of master big features
     */
    private static final LoadingCache<String, List<MasterBigFeatureBean>> masterBigFeatures = CacheBuilder.newBuilder()
            .maximumSize(MAX_SIZE)
            .expireAfterWrite(CACHE_TIMEOUT, TimeUnit.MINUTES)
            .build(new CacheLoader<String, List<MasterBigFeatureBean>>() {
                @Override
                public List<MasterBigFeatureBean> load(String s) throws Exception {
                    return MasterDataService.getBigFeaturesMasterData();
                }
            });

    public static List<MasterPageActionBean> getPageActionList() {
        try {
            return masterPageActions.get("masterPageActions");
        } catch (Exception e) {
            log.debug("Error occurred while fetching page action list");
        }
        return Collections.emptyList();
    }

    public static List<MasterBigFeatureBean> getBigFeatureList() {
        try {
            return masterBigFeatures.get("masterBigFeatures");
        } catch (Exception e) {
            log.debug("Error occurred while fetching big feature list");
        }
        return Collections.emptyList();
    }

    public static List<MasterComponentVersionBean> getAllMasterComponentVersionData() {
        try {
            return allMasterComponentVersion.get("allMasterComponentVersion");
        } catch (Exception e) {
            log.debug("Error occurred while fetching big feature list");
        }
        return Collections.emptyList();
    }
}
