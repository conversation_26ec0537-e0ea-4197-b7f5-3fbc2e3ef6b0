package com.appnomic.appsone.controlcenter.cache.keys;

import lombok.Data;

import java.util.Objects;

@Data
public class ProducerTypeDetailKey {

    private Integer producerId;
    private String producerType;

    public ProducerTypeDetailKey(Integer producerId, String producerType){
        this.producerId = producerId;
        this.producerType = producerType;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null) {
            return false;
        }
        if (this.getClass() != o.getClass()) {
            return false;
        }

        ProducerTypeDetailKey that = (ProducerTypeDetailKey) o;
        return Objects.equals(this.producerId, that.getProducerId())
                && Objects.equals(this.producerType, that.getProducerType());
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.producerId, this.producerType);
    }
}
