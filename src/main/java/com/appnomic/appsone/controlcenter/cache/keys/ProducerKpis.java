package com.appnomic.appsone.controlcenter.cache.keys;

import com.appnomic.appsone.controlcenter.common.Constants;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR> : 4/2/19
 */
@Data
public class ProducerKpis {
    private int defaultAccountId = Constants.DEFAULT_ACCOUNT_ID;
    private int accountId;
    private int mstKpiDetailsId;
    private int mstCompVersionId;
    private int mstCompId;
    private int mstCompTypeId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ProducerKpis that = (ProducerKpis) o;
        return accountId == that.accountId &&
                mstKpiDetailsId == that.mstKpiDetailsId &&
                mstCompVersionId == that.mstCompVersionId &&
                mstCompId == that.mstCompId &&
                mstCompTypeId == that.mstCompTypeId;
    }

    @Override
    public int hashCode() {
        return Objects.hash(accountId, mstKpiDetailsId, mstCompVersionId, mstCompId, mstCompTypeId);
    }
}
