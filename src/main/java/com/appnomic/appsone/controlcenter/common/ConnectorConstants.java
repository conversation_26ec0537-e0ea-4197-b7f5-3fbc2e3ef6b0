package com.appnomic.appsone.controlcenter.common;

import java.util.Arrays;
import java.util.List;

public class ConnectorConstants {

    private ConnectorConstants() {
    }

    public static final int CHAIN_NAME_INDEX = 3;
    public static final int CHAIN_SLEEP_INTERVAL_INDEX = 4;
    public static final int CHAIN_PULL_HISTORICAL_DATA_INDEX = 5;
    public static final int CHAIN_HISTORICAL_STARTDATE_INDEX = 6;
    public static final int CHAIN_HISTORICAL_ENDDATE_INDEX = 7;
    public static final int CHAIN_HISTORICAL_INTERVAL_INDEX = 8;

    public static final int CHAIN_DISABLE_CHAIN_RELOAD = 0;
    public static final int CHAIN_TEST_START_DATE = -1;
    public static final int CHAIN_TEST_END_DATE = -1;
    public static final int CHAIN_ADD_SYS_LOADER = 0;
    public static final int CHAIN_REALOAD_INTERVAL = 1;
    public static final int CHAIN_PROCESSOR_THREADPOOL_SIZE = 1;
    public static final int CHAIN_LOADER_THREADPOOL_SIZE = 1;
    public static final int CHAIN_DELTA_IN_MINUTES = 3;
    public static final int CHAIN_BACK_PRESSURE_MAX_SIZE = 1024;
    public static final String CHAIN_BACK_PRESSURE_STRATEGY = "BUFFER";
    public static final String CHAIN_PRE_PROCESSOR = "com.appnomic.heal.etladapter.etl.NullPreprocessor";
    public static final String KPI_TRANSFORMER_MAPPING_BY_IDENTIFIER = "KPI_MAPPING_BY_IDENTIFIER";

    public static final String CONNECTOR_CC_PORT = "connector.cc.port";
    public static final String CONNECTOR_CC_PORT_DEFAULT = "8996";

    public static final int COMMON_WORKER_PARAMETER_KEY_INDEX = 0;
    public static final int COMMON_WORKER_PARAMETER_VALUE_INDEX = 1;

    public static final int ADVANCE_WORKER_PARAMETER_KEY_INDEX = 0;
    public static final int ADVANCE_WORKER_PARAMETER_VALUE_INDEX = 1;

    public static final String SCHEMA_PREFIX = "dataadapter_";

    public static final int COMMON_SETTING_SHEET_INDEX = 0;

    public static final int ADVANCE_SETTING_SHEET_INDEX = 1;

    public static final String HEAL_SSL_ENABLE = "heal-ssl-enabled";
    public static final String HEAL_SSL_ENABLE_DEFAULT = "1";
    public static final String GRPC_ADDRESS = "grpc-address";
    public static final String GRPC_ADDRESS_DEFAULT = "haproxy.appnomic";
    public static final String GRPC_PORT = "grpc-port";
    public static final String CONNECTOR_PERCONA_URL = "connector-percona-url";
    public static final String GRPC_PORT_DEFAULT = "9991";
    public static final String HEAL_RETRY_INTERVAL = "Heal-Retry-Interval";
    public static final String HEAL_RETRY_INTERVAL_DEFAULT = "3";
    public static final String HEAL_MAX_RETRY_ATTEMPTS = "Heal-Max-Retry-Attempts";
    public static final String HEAL_MAX_RETRY_ATTEMPTS_DEFAULT = "60";
    public static final String HEAL_CERT = "heal-cert";
    public static final String HEAL_CERT_PATH = "/opt/heal/ext-system-adapter/conf/heal-grpc.cert";
    public static final String RELOAD_CONFIGURATION = "Reload-Configuration";
    public static final String RELOAD_CONFIGURATION_DEFAULT = "1";
    public static final String FILE_PATH = "file-path";
    public static final String CONNECTOR_DB_PARAMS = "?verifyServerCertificate=false&useSSL=true&requireSSL=true&enabledTLSProtocols=TLSv1.2,TLSv1.3&useServerPrepStmts=true&cacheServerConfiguration=true";

    //---------------Azure Connector Constants-------------------------

    public static final int AZURE_APPLICATION_ID_IDX = 3;
    public static final int AZURE_APPLICATION_APPID_IDX = 4;
    public static final int AZURE_APPLICATION_APPKEY_IDX = 5;
    public static final int AZURE_APPLICATION_APPINSTNAME_IDX = 6;
    public static final int AZURE_APPLICATION_AGENT_IDENTIFIER_IDX = 7;

    public static final int AZURE_KPI_ID_IDX = 9;
    public static final int AZURE_KPI_NAME_IDX = 10;
    public static final int AZURE_KPI_DOMAIN_IDX = 11;
    public static final int AZURE_KPI_TYPE_IDX = 12;
    public static final int AZURE_KPI_AGGREGATOR_IDX = 13;
    public static final int AZURE_HEAL_KPI_ID_IDX = 14;
    public static final int AZURE_HEAL_KPI_NAME_IDX = 15;
    public static final int AZURE_HEAL_KPI_IDENTIFIER_IDX = 16;
    public static final int AZURE_HEAL_IS_GROUP_KPI_IDX = 17;
    public static final int AZURE_HEAL_GROUP_NAME_IDX = 18;

    public static final int AZURE_TOKEN_ID_IDX = 20;
    public static final int AZURE_TOKEN_TENANT_ID_IDX = 21;
    public static final int AZURE_TOKEN_GRANT_TYPE_IDX = 22;
    public static final int AZURE_TOKEN_CLIENT_ID_IDX = 23;
    public static final int AZURE_TOKEN_CLIENT_SECRET_IDX = 24;
    public static final int AZURE_TOKEN_RESOURCE_NAME_IDX = 25;

    public static final int AZURE_RESOURCE_ID_IDX = 27;
    public static final int AZURE_RESOURCE_NAME_IDX = 28;
    public static final int AZURE_RESOURCE_GROUP_NAME_IDX = 29;
    public static final int AZURE_RESOURCE_SUBSCRIPTION_ID_IDX = 30;
    public static final int AZURE_RESOURCE_TOKEN_ID_IDX = 31;
    public static final int AZURE_RESOURCE_APPINSTNAME_IDX = 32;

    public static final int AZURE_AGENT_AZURE_INSTANCE_NAME_INDEX = 34;
    public static final int AZURE_AGENT_NAME_INDEX = 35;
    public static final int AZURE_AGENT_HEAL_INSTANCE_NAME_INDEX = 36;


    public static final String AZURE_APP_KPI = "Application Insights";
    public static final String AZURE_RES_KPI = "Resource";


    //---------------AppDynamics Connector Constants-------------------------

    public static final int APPD_APPLICATION_ID_IDX = 3;
    public static final int APPD_APPLICATION_APPID_IDX = 4;
    public static final int APPD_APPLICATION_NAME_IDX = 5;
    public static final int APPD_APPLICATION_ORG_IDX = 6;
    public static final int APPD_APPLICATION_PWD_IDX = 7;
    public static final int APPD_APPLICATION_USERNAME_IDX = 8;
    public static final int APPD_APPLICATION_APPINST_IDX = 9;
    public static final int APPD_APPLICATION_NODE_IDX = 10;
    public static final int APPD_APPLICATION_LOG_AGENT_IDENTFIER_IDX = 11;

    public static final int APPD_KPI_ID_IDX = 13;
    public static final int APPD_KPI_NAME_IDX = 14;
    public static final int APPD_KPI_AGGREGATOR_IDX = 15;
    public static final int APPD_KPI_AGGREGATION_LEVEL_IDX = 16;
    public static final int APPD_KPI_TYPE_IDX = 17;
    public static final int APPD_HEAL_KPI_ID_IDX = 18;
    public static final int APPD_HEAL_KPI_NAME_IDX = 19;
    public static final int APPD_HEAL_KPI_IDENTIFIER_IDX = 20;
    public static final int APPD_HEAL_IS_GROUP_KPI_IDX = 21;
    public static final int APPD_HEAL_GROUP_NAME_IDX = 22;

    public static final int APPD_INST_NAME_IDX = 24;
    public static final int APPD_HEAL_AGENT_ID_IDX = 25;
    public static final int APPD_HEAL_INST_NAME_IDX = 26;

    // Aws Connector Constants

    public static final int AWS_CREDENTIAL_ID_INDEX = 0;
    public static final int AWS_CREDENTIAL_REGION_INDEX = 1;
    public static final int AWS_CREDENTIAL_ACCESS_KEY_ID_INDEX = 2;
    public static final int AWS_CREDENTIAL_SECRET_KEY_ACCESS_INDEX = 3;

    public static final int AWS_INSTANCE_ID_INDEX = 5;
    public static final int AWS_INSTANCE_INSTANCE_ID_INDEX = 6;
    public static final int AWS_INSTANCE_DETAILED_MONITORING_INDEX = 7;
    public static final int AWS_INSTANCE_CREDENTIAL_ID_INDEX = 8;
    public static final int AWS_INSTANCE_LOG_KPIS_ID_INDEX = 9;
    public static final int AWS_INSTANCE_LOGS_ID_INDEX = 10;

    public static final int AWS_METRIC_ID_INDEX = 25;
    public static final int AWS_METRIC_NAME_INDEX = 26;
    public static final int AWS_METRIC_NAME_SPACE_INDEX = 27;
    public static final int AWS_METRIC_STAT_INDEX = 28;
    public static final int AWS_METRIC_UNIT_INDEX = 29;
    public static final int AWS_METRIC_DETAILED_MONITORED_INDEX = 30;
    public static final int AWS_METRIC_HEAL_ID_INDEX = 31;
    public static final int AWS_METRIC_HEAL_NAME_INDEX = 32;
    public static final int AWS_METRIC_HEAL_IDENTIFIER_INDEX = 33;
    public static final int AWS_METRIC_IS_GROUP_INDEX = 34;
    public static final int AWS_METRIC_GROUP_NAME_INDEX = 35;

    public static final int AWS_LOGS_ID_INDEX = 12;
    public static final int AWS_LOGS_GROUP_NAME_INDEX = 13;
    public static final int AWS_LOGS_STREAM_NAME_INDEX = 14;
    public static final int AWS_LOGS_FILTER_PATTERN_INDEX = 15;
    public static final int AWS_LOGS_DATE_PATTERN_INDEX = 16;
    public static final int AWS_LOGS_CREDENTIAL_ID_INDEX = 17;

    public static final int AWS_LOG_KPI_ID_INDEX = 19;
    public static final int AWS_LOG_KPI_GROUP_NAME_INDEX = 20;
    public static final int AWS_LOG_KPI_STREAM_NAME_INDEX = 21;
    public static final int AWS_LOG_KPI_FILTER_PATTERN_INDEX = 22;
    public static final int AWS_LOG_KPI_CREDENTIAL_ID_INDEX = 23;


    public static final int AWS_DIMENSION_ID_INDEX = 37;
    public static final int AWS_DIMENSION_KEY_INDEX = 38;
    public static final int AWS_DIMENSION_VALUE_INDEX = 39;
    public static final int AWS_DIMENSION_INSTANCE_ID_INDEX = 40;


    public static final int AWS_AGENT_AWS_INSTANCE_NAME_INDEX = 42;

    public static final int AWS_AGENT_NAME_INDEX = 43;

    public static final int AWS_AGENT_HEAL_INSTANCE_NAME_INDEX = 44;

// Sap Constants

    public static final List<Integer> SAP_LOADERS_IDS = Arrays.asList(4, 6);
    public static final int SAP_CONNECTION_ID_INDEX = 0;
    public static final int SAP_CONNECTION_FILE_NAME_INDEX = 1;
    public static final int SAP_CONNECTION_NAME_INDEX = 2;
    public static final int SAP_CONNECTION_VALUE_INDEX = 3;

    public static final int SAP_INSTANCE_ID_INDEX = 5;
    public static final int SAP_INSTANCE_NAME_INDEX = 6;
    public static final int SAP_INSTANCE_HEAL_NAME_INDEX = 7;
    public static final int SAP_INSTANCE_FILE_NAME_INDEX = 8;
    public static final int SAP_HEAL_KPI_ID_INDEX = 10;
    public static final int SAP_HEAL_KPI_NAME_INDEX = 11;
    public static final int SAP_HEAL_KPI_TYPE_INDEX = 12;
    public static final int SAP_HEAL_KPI_AGGREGATOR_INDEX = 13;
    public static final int SAP_HEAL_KPI_HEAL_ID_INDEX = 14;

    public static final int SAP_HEAL_KPI_HEAL_NAME_INDEX = 15;
    public static final int SAP_HEAL_KPI_HEAL_IDENTIFIER_INDEX = 16;
    public static final int SAP_HEAL_KPI_IS_GROUP_INDEX = 17;
    public static final int SAP_HEAL_KPI_GROUP_NAME_INDEX = 18;

    public static final int SAP_AGENT_SAP_INSTANCE_NAME_INDEX = 20;

    public static final int SAP_AGENT_NAME_INDEX = 21;

    public static final int SAP_AGENT_HEAL_INSTANCE_NAME_INDEX = 22;

    public static final List<Integer> SAP_FUNCTIONAL_MODULE_IDS = Arrays.asList(1, 2, 3);

    //Dynatrace Constants

    public static final List<Integer> DT_LOADERS_IDS = Arrays.asList(4, 6);
    public static final int DYNATRACE_ENTITY_ID_INDEX = 3;
    public static final int DYNATRACE_ENTITY_NAME_INDEX = 4;
    public static final int DYNATRACE_ENTITY_IDENTIFIER_INDEX = 5;
    public static final int DYNATRACE_ENTITY_TYPE = 6;

    public static final int DYNATRACE_METRIC_ID_INDEX = 8;
    public static final int DYNATRACE_METRIC_NAME_INDEX = 9;
    public static final int DYNATRACE_METRIC_IDENTIFIER_INDEX = 10;
    public static final int DYNATRACE_METRIC_RESOLUTION_INDEX = 11;
    public static final int DYNATRACE_METRIC_HEAL_ID_INDEX = 12;
    public static final int DYNATRACE_METRIC_HEAL_NAME_INDEX = 13;
    public static final int DYNATRACE_METRIC_HEAL_IDENTIFIER_INDEX = 14;
    public static final int DYNATRACE_METRIC_IS_GROUP_INDEX = 15;
    public static final int DYNATRACE_METRIC_GROUP_NAME_INDEX = 16;
    public static final int DYNATRACE_METRIC_ENTITY_TYPE_INDEX = 17;

    public static final int DYNATRACE_AGENT_SOURCE_INSTANCE_NAME_INDEX = 19;

    public static final int DYNATRACE_AGENT_NAME_INDEX = 20;

    public static final int DYNATRACE_AGENT_HEAL_INSTANCE_NAME_INDEX = 21;

    public static final String DYNATRACE_DOMAIN_NAME = "dynatrace";

    // --------------------------Kubernetes Constants--------------------------- //

    public static final int KUB_CONF_ID_IDX = 3;
    public static final int KUB_CONF_FILE_NAME_IDX = 4;
    public static final int KUB_CONF_CONFIG_IDX = 5;

    public static final int KUB_PROM_KPI_RESPONSE_PATH_ID_IDX = 7;
    public static final int KUB_PROM_KPI_RESPONSE_PATH_HOSTORCOMP_IDX = 8;
    public static final int KUB_PROM_KPI_RESPONSE_PATH_SERVICE_NAME_IDX = 9;
    public static final int KUB_PROM_KPI_RESPONSE_PATH_KPI_NAME_IDX = 10;
    public static final int KUB_PROM_KPI_RESPONSE_PATH_POD_NAME_IDX = 11;
    public static final int KUB_PROM_KPI_RESPONSE_PATH_POD_NAMESPACE_IDX = 12;

    public static final int KUB_PROM_APPLICATION_ID_IDX = 14;
    public static final int KUB_PROM_APPLICATION_HOST_PORT_IDX = 15;
    public static final int KUB_PROM_APPLICATION_USERNAME_IDX = 16;
    public static final int KUB_PROM_APPLICATION_PASSWORD_IDX = 17;

    public static final int KUB_PROM_KPI_ID_IDX = 19;
    public static final int KUB_PROM_KPI_METRIC_IDX = 20;
    public static final int KUB_PROM_KPI_HOSTORCOMP_IDX = 21;
    public static final int KUB_PROM_KPI_HEAL_KPI_ID_IDX = 22;
    public static final int KUB_PROM_KPI_HEAL_KPI_NAME_IDX = 23;
    public static final int KUB_PROM_KPI_HEAL_KPI_IDENTIFIER_IDX = 24;
    public static final int KUB_PROM_KPI_IS_GROUP_KPI_IDX = 25;
    public static final int KUB_PROM_KPI_GROUP_NAME_IDX = 26;
    public static final int KUB_PROM_KPI_APP_IDS_IDX = 27;

    public static final int KUB_ES_RESPONSE_PATH_ID_IDX = 29;
    public static final int KUB_ES_RESPONSE_PATH_TIMESTAMP_IDX = 30;
    public static final int KUB_ES_RESPONSE_PATH_URI_IDX = 31;
    public static final int KUB_ES_RESPONSE_PATH_STATUS_CODE_IDX = 32;
    public static final int KUB_ES_RESPONSE_PATH_METHOD_IDX = 33;
    public static final int KUB_ES_RESPONSE_PATH_RESPONSE_TIME_IDX = 34;
    public static final int KUB_ES_RESPONSE_PATH_POD_NAME_IDX = 35;
    public static final int KUB_ES_RESPONSE_PATH_NAMESPACE_NAME_IDX = 36;

    public static final int KUB_A1_LOGSCAN_ENDPT_ID_IDX = 38;
    public static final int KUB_A1_LOGSCAN_ENDPT_HOST_IP_IDX = 39;
    public static final int KUB_A1_LOGSCAN_ENDPT_PORT_IDX = 40;
    public static final int KUB_A1_LOGSCAN_ENDPT_TYPE_IDX = 41;

    // ----------------------- Kubernetes Advanced ---------------------------- //

    public static final int KUB_HICP_ID_IDX = 10;
    public static final int KUB_HICP_IMAGE_IDX = 11;
    public static final int KUB_HICP_NAMESPACE_IDX = 12;
    public static final int KUB_HICP_HI_COMP_NAME_IDX = 13;
    public static final int KUB_HICP_HI_COMP_VER_IDX = 14;
    public static final int KUB_HICP_HI_SERVICE_ID_IDX = 15;
    public static final int KUB_HICP_CI_COMP_NAME_IDX = 16;
    public static final int KUB_HICP_CI_COMP_VER_IDX = 17;
    public static final int KUB_HICP_CI_SERVICE_ID_IDX = 18;

    //--------------------------------- APPDYNAMICS CONSTANTS-----------------------------
    public static final String APPD_FILE_PATH = "/opt/heal/ext-system-adapter/data/";
    public static final String APPD_KPI_DATA_SOURCE = "json-files";
    public static final String APPD_BASE_URL = "https://${oraganisation}.saas.appdynamics.com/controller/rest/applications/";
    public static final String APPD_REGEX = "[^\\|]+\\|(?<servicename>[^\\|]+)\\|[^\\|]+\\|(?<instancename>[^\\|]+)\\|(?<metricname>.*+)";


    //--------------------------------- Connector Commands Constants -----------------------------
    public static final String CONNECTOR_COMMAND_JOB_NAME = "job_name";
    public static final String CONNECTOR_COMMAND_CONNECTOR_LOGS = "connector_logs";
    public static final String CONNECTOR_COMMAND_CONNECTOR_CONFIG = "connector_config";
    public static final String CONNECTOR_COMMAND_EXEC_TYPE = "Execute";
    public static final int CONNECTOR_COMMAND_RETRY_COUNT = 3;
    public static final String CONNECTOR_SUPERVISOR_IDENTIFIER = "SupervisorControllerIdentifier";
    public static final String CONNECTOR_AGENT_TYPE = "Supervisor";
    public static final String CONNECTOR_COMMAND_TRIGGER_SOURCE = "ControlCenter";
    public static final String CONNECTOR_SUPERVISOR_MODE_KEY = "SupervisorMode";
    public static final String CONNECTOR_SUPERVISOR_MODE_VAL = "REMOTE";
    public static final String CONNECTOR_PRODUCER_TYPE_KEY = "ProducerType";
    public static final String CONNECTOR_PRODUCER_TYPE_VAL = "JDBC";

}
