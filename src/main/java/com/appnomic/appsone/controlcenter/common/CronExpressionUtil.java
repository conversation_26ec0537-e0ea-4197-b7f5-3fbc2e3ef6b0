package com.appnomic.appsone.controlcenter.common;

import com.appnomic.appsone.controlcenter.dao.mysql.TransactionDataService;
import org.slf4j.LoggerFactory;

public class CronExpressionUtil {

    private static org.slf4j.Logger log = LoggerFactory.getLogger(TransactionDataService.class);

    public static String generateCronExpression(int hours, int fromHour) throws IllegalArgumentException {
        if (hours <= 0 || hours > 24) {
            log.error("Number of hours : {}, should be in between 1 and 24", hours);
            throw new IllegalArgumentException("Invalid input. Number of hours should be in between 1 and 24.");
        }
        return "0 0 " + fromHour + "/" + hours + " * * ?";
    }
}
