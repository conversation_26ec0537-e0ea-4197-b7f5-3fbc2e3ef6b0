package com.appnomic.appsone.controlcenter.common;

import com.appnomic.appsone.controlcenter.pojo.connectors.WorkerParameter;
import com.appnomic.appsone.controlcenter.util.ConfProperties;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class WorkerParameterStatement {
    public List<WorkerParameter> createStatementForWorkerParameterForSapFromMap(Map<String,WorkerParameter> workerParameters) {
        List<WorkerParameter> workerParametersForDb = new ArrayList<>();
        WorkerParameter kpiMapping = workerParameters.get(ConnectorConstants.KPI_TRANSFORMER_MAPPING_BY_IDENTIFIER);

        for (Integer workerId : ConnectorConstants.SAP_LOADERS_IDS) {
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_SSL_ENABLE, ConfProperties.getString(ConnectorConstants.HEAL_SSL_ENABLE,ConnectorConstants.HEAL_SSL_ENABLE_DEFAULT)));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.GRPC_ADDRESS, ConfProperties.getString(ConnectorConstants.GRPC_ADDRESS,ConnectorConstants.GRPC_ADDRESS_DEFAULT)));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.GRPC_PORT, ConfProperties.getString(ConnectorConstants.GRPC_PORT,ConnectorConstants.GRPC_PORT_DEFAULT)));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_CERT, "/opt/heal/ext-system-adapter/heal-grpc.cert"));
            if(workerParameters.get(ConnectorConstants.HEAL_RETRY_INTERVAL).getValue() == null)
                workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_RETRY_INTERVAL, ConnectorConstants.HEAL_RETRY_INTERVAL_DEFAULT));
            else
                workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_RETRY_INTERVAL, workerParameters.get(ConnectorConstants.HEAL_RETRY_INTERVAL).getValue()));
            if(workerParameters.get(ConnectorConstants.HEAL_MAX_RETRY_ATTEMPTS).getValue() == null)
                workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_MAX_RETRY_ATTEMPTS, ConnectorConstants.HEAL_MAX_RETRY_ATTEMPTS_DEFAULT));
            else
                workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_MAX_RETRY_ATTEMPTS, workerParameters.get(ConnectorConstants.HEAL_MAX_RETRY_ATTEMPTS).getValue()));
        }

        for (Integer workerId : Constants.SAP_RELOAD_CONFIG_IDS) {
            if(workerParameters.get(ConnectorConstants.RELOAD_CONFIGURATION).getValue() == null)
                workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.RELOAD_CONFIGURATION, ConnectorConstants.RELOAD_CONFIGURATION_DEFAULT));
            else
                workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.RELOAD_CONFIGURATION, workerParameters.get(ConnectorConstants.RELOAD_CONFIGURATION).getValue()));

            workerParametersForDb.add(new WorkerParameter(workerId, "repository", "LogAnalyzerKPIMaster"));
        }

        for (Integer ids : Constants.SAP_EXTRACTOR_IDS) {
            workerParametersForDb.add(new WorkerParameter(ids, "sapadaptor", "SapReposetory"));
            workerParametersForDb.add(new WorkerParameter(ids, "file-path", "/opt/heal/ext-system-adapter/conf"));
        }

        workerParametersForDb.add(new WorkerParameter(3, "domain", "sap"));
        if(kpiMapping.getValue() != null && kpiMapping.getValue().equalsIgnoreCase("True"))
            workerParametersForDb.add(new WorkerParameter(3, ConnectorConstants.KPI_TRANSFORMER_MAPPING_BY_IDENTIFIER, kpiMapping.getValue()));
        workerParametersForDb.add(new WorkerParameter(3, "AppsoneRepository", "AppsoneRepository"));
        return workerParametersForDb;
    }

    public List<WorkerParameter> createStatementWorkerParameterForDynatrace(List<WorkerParameter> workerParameterFromCommon,
                                                                 List<WorkerParameter> workerParameterFromAdvance) {
        List<WorkerParameter> workerParametersForDb = new ArrayList<>();
        WorkerParameter kpiMapping = workerParameterFromAdvance.get(3);

        for (Integer workerId : ConnectorConstants.SAP_LOADERS_IDS) {
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_SSL_ENABLE, ConfProperties.getString(ConnectorConstants.HEAL_SSL_ENABLE,ConnectorConstants.HEAL_SSL_ENABLE_DEFAULT)));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.GRPC_ADDRESS, ConfProperties.getString(ConnectorConstants.GRPC_ADDRESS,ConnectorConstants.GRPC_ADDRESS_DEFAULT)));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.GRPC_PORT, ConfProperties.getString(ConnectorConstants.GRPC_PORT,ConnectorConstants.GRPC_PORT_DEFAULT)));
            if(workerParameterFromAdvance.get(0).getValue() == null)
                workerParametersForDb.add(new WorkerParameter(workerId, workerParameterFromAdvance.get(0).getName(), ConnectorConstants.HEAL_RETRY_INTERVAL_DEFAULT));
            else
                workerParametersForDb.add(new WorkerParameter(workerId, workerParameterFromAdvance.get(0).getName(), workerParameterFromAdvance.get(0).getValue()));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_CERT, "/opt/heal/ext-system-adapter/heal-grpc.cert"));
            if(workerParameterFromAdvance.get(1).getValue() == null)
                workerParametersForDb.add(new WorkerParameter(workerId, workerParameterFromAdvance.get(1).getName(), ConnectorConstants.HEAL_MAX_RETRY_ATTEMPTS_DEFAULT));
            else
                workerParametersForDb.add(new WorkerParameter(workerId, workerParameterFromAdvance.get(1).getName(), workerParameterFromAdvance.get(1).getValue()));
        }

        for (Integer workerId : Constants.SAP_RELOAD_CONFIG_IDS) {
            if(workerParameterFromAdvance.get(2).getValue() == null)
                workerParametersForDb.add(new WorkerParameter(workerId, workerParameterFromAdvance.get(2).getName(), ConnectorConstants.RELOAD_CONFIGURATION_DEFAULT));
            else
                workerParametersForDb.add(new WorkerParameter(workerId, workerParameterFromAdvance.get(2).getName(), workerParameterFromAdvance.get(2).getValue()));
            workerParametersForDb.add(new WorkerParameter(workerId, "repository", "LogAnalyzerKPIMaster"));
        }

        for (WorkerParameter workerParameter : workerParameterFromCommon) {
            workerParametersForDb.add(new WorkerParameter(1, workerParameter.getName(), workerParameter.getValue()));
            workerParametersForDb.add(new WorkerParameter(2, workerParameter.getName(), workerParameter.getValue()));
        }
        workerParametersForDb.add(new WorkerParameter(1, "dynatraceadapter", "DynaTraceReposetory"));
        workerParametersForDb.add(new WorkerParameter(5, workerParameterFromAdvance.get(6).getName(), workerParameterFromAdvance.get(6).getValue()));
        workerParametersForDb.add(new WorkerParameter(5, workerParameterFromAdvance.get(7).getName(), workerParameterFromAdvance.get(7).getValue()));
        workerParametersForDb.add(new WorkerParameter(3, "domain", "dynatrace"));
        if(kpiMapping.getValue() != null && kpiMapping.getValue().equalsIgnoreCase("True"))
            workerParametersForDb.add(new WorkerParameter(3, kpiMapping.getName(), kpiMapping.getValue()));
        workerParametersForDb.add(new WorkerParameter(3, "AppsoneRepository", "AppsoneRepository"));

        return workerParametersForDb;
    }

    public List<WorkerParameter> createStatementWorkerParameterForDynatraceWithMap(Map<String,WorkerParameter> workerParameterFromCommon,
                                                                            Map<String,WorkerParameter> workerParameterFromAdvance) {
        List<WorkerParameter> workerParametersForDb = new ArrayList<>();
        WorkerParameter kpiMapping = workerParameterFromAdvance.get(ConnectorConstants.KPI_TRANSFORMER_MAPPING_BY_IDENTIFIER);
        //WorkerParameter account = workerParameterFromAdvance.get(ConnectorConstants.ACCOUNT_NAME);

        for (Integer workerId : ConnectorConstants.DT_LOADERS_IDS) {
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_SSL_ENABLE, ConfProperties.getString(ConnectorConstants.HEAL_SSL_ENABLE,ConnectorConstants.HEAL_SSL_ENABLE_DEFAULT)));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.GRPC_ADDRESS, ConfProperties.getString(ConnectorConstants.GRPC_ADDRESS,ConnectorConstants.GRPC_ADDRESS_DEFAULT)));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.GRPC_PORT, ConfProperties.getString(ConnectorConstants.GRPC_PORT,ConnectorConstants.GRPC_PORT_DEFAULT)));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_CERT, "/opt/heal/ext-system-adapter/heal-grpc.cert"));
            if(workerParameterFromAdvance.get(ConnectorConstants.HEAL_RETRY_INTERVAL).getValue() == null)
                workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_RETRY_INTERVAL, ConnectorConstants.HEAL_RETRY_INTERVAL_DEFAULT));
            else
                workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_RETRY_INTERVAL, workerParameterFromAdvance.get(ConnectorConstants.HEAL_RETRY_INTERVAL).getValue()));
            if(workerParameterFromAdvance.get(ConnectorConstants.HEAL_MAX_RETRY_ATTEMPTS).getValue() == null)
                workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_MAX_RETRY_ATTEMPTS, ConnectorConstants.HEAL_MAX_RETRY_ATTEMPTS_DEFAULT));
            else
                workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_MAX_RETRY_ATTEMPTS, workerParameterFromAdvance.get(ConnectorConstants.HEAL_MAX_RETRY_ATTEMPTS).getValue()));
        }

        for (Integer workerId : Constants.DT_RELOAD_CONFIG_IDS) {
            if(workerParameterFromAdvance.get(ConnectorConstants.RELOAD_CONFIGURATION).getValue() == null)
                workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.RELOAD_CONFIGURATION, ConnectorConstants.RELOAD_CONFIGURATION_DEFAULT));
            else
                workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.RELOAD_CONFIGURATION, workerParameterFromAdvance.get(ConnectorConstants.RELOAD_CONFIGURATION).getValue()));
            workerParametersForDb.add(new WorkerParameter(workerId, "repository", "LogAnalyzerKPIMaster"));
        }

        for (String key : workerParameterFromCommon.keySet()) {
            workerParametersForDb.add(new WorkerParameter(1, workerParameterFromCommon.get(key).getName(),
                    workerParameterFromCommon.get(key).getValue()));
            workerParametersForDb.add(new WorkerParameter(2, workerParameterFromCommon.get(key).getName(),
                    workerParameterFromCommon.get(key).getValue()));
        }
        workerParametersForDb.add(new WorkerParameter(1, "dynatraceadapter", "DynaTraceReposetory"));
        workerParametersForDb.add(new WorkerParameter(2, "dynatraceadapter", "DynaTraceReposetory"));
        workerParametersForDb.add(new WorkerParameter(5, "account", "SAP"));
        workerParametersForDb.add(new WorkerParameter(3, "domain", "dynatrace"));
        if(kpiMapping.getValue() != null && kpiMapping.getValue().equalsIgnoreCase("True"))
            workerParametersForDb.add(new WorkerParameter(3, kpiMapping.getName(), kpiMapping.getValue()));
        workerParametersForDb.add(new WorkerParameter(3, "AppsoneRepository", "AppsoneRepository"));
        workerParametersForDb.sort(WorkerParameter.comparator);
        return workerParametersForDb;
    }

    public List<WorkerParameter> createStatementWorkerParametersForAzure(List<WorkerParameter> workerParameterFromCommon,
                                                                 List<WorkerParameter> workerParametersAdvance) {
        List<WorkerParameter> workerParametersForDb = new ArrayList<>();
        for (Integer workerId : Constants.AZURE_LOADERS_IDS) {
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_SSL_ENABLE, ConfProperties.getString(ConnectorConstants.HEAL_SSL_ENABLE,ConnectorConstants.HEAL_SSL_ENABLE_DEFAULT)));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.GRPC_ADDRESS, ConfProperties.getString(ConnectorConstants.GRPC_ADDRESS,ConnectorConstants.GRPC_ADDRESS_DEFAULT)));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.GRPC_PORT, ConfProperties.getString(ConnectorConstants.GRPC_PORT,ConnectorConstants.GRPC_PORT_DEFAULT)));
            if(workerParametersAdvance.get(0).getValue() == null)
                workerParametersForDb.add(new WorkerParameter(workerId, workerParametersAdvance.get(0).getName(), ConnectorConstants.HEAL_RETRY_INTERVAL_DEFAULT));
            else
                workerParametersForDb.add(new WorkerParameter(workerId, workerParametersAdvance.get(0).getName(), workerParametersAdvance.get(0).getValue()));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_CERT, "/opt/heal/ext-system-adapter/heal-grpc.cert"));
            if(workerParametersAdvance.get(1).getValue() == null)
                workerParametersForDb.add(new WorkerParameter(workerId, workerParametersAdvance.get(1).getName(), ConnectorConstants.HEAL_MAX_RETRY_ATTEMPTS_DEFAULT));
            else
                workerParametersForDb.add(new WorkerParameter(workerId, workerParametersAdvance.get(1).getName(), workerParametersAdvance.get(1).getValue()));
        }

        for (Integer workerId : Constants.AZURE_RELOAD_CONFIG_IDS) {
            if(workerParametersAdvance.get(2).getValue() == null)
                workerParametersForDb.add(new WorkerParameter(workerId, workerParametersAdvance.get(2).getName(), ConnectorConstants.RELOAD_CONFIGURATION_DEFAULT));
            else
                workerParametersForDb.add(new WorkerParameter(workerId, workerParametersAdvance.get(2).getName(), workerParametersAdvance.get(2).getValue()));

            workerParametersForDb.add(new WorkerParameter(workerId, "repository", "LogAnalyzerKPIMaster"));
        }

        for (Integer workerId : Constants.AZURE_IDS) {
            workerParametersForDb.add(new WorkerParameter(workerId, "azure_repository", "AzureKPIMaster"));
        }

        for (WorkerParameter workerParameter : workerParameterFromCommon) {
            workerParametersForDb.add(new WorkerParameter(1, workerParameter.getName(), workerParameter.getValue()));
            workerParametersForDb.add(new WorkerParameter(2, workerParameter.getName(), workerParameter.getValue()));
        }
        workerParametersForDb.add(new WorkerParameter(3, "domain", "Azure"));
        if(workerParametersAdvance.get(3).getValue() != null && workerParametersAdvance.get(3).getValue().equalsIgnoreCase("True"))
            workerParametersForDb.add(new WorkerParameter(3, ConnectorConstants.KPI_TRANSFORMER_MAPPING_BY_IDENTIFIER, workerParametersAdvance.get(3).getValue()));
        workerParametersForDb.add(new WorkerParameter(3, "AppsoneRepository", "AppsoneRepository"));

        return workerParametersForDb;
    }

    public List<WorkerParameter> createStatementWorkerParametersAppDynamics(List<WorkerParameter> workerParametersCommon, List<WorkerParameter> workerParametersAdvance)
    {
        List<WorkerParameter> workerParametersForDb = new ArrayList<>();
        for(Integer workerId : Constants.APPD_ALL)
        {
            workerParametersForDb.add(new WorkerParameter(workerId, "appdynamics_repository", "AppdynamicsKPIMaster"));
            workerParametersForDb.add(new WorkerParameter(workerId, "repository", "LogAnalyzerKPIMaster"));
            if(workerParametersAdvance.get(2).getValue() == null)
                workerParametersForDb.add(new WorkerParameter(workerId, workerParametersAdvance.get(2).getName(), ConnectorConstants.RELOAD_CONFIGURATION_DEFAULT));
            else
                workerParametersForDb.add(new WorkerParameter(workerId, workerParametersAdvance.get(2).getName(), workerParametersAdvance.get(2).getValue()));

        }
        for(Integer workerId : Constants.APPD_APP)
        {
            if(workerParametersCommon.get(0).getValue() == null)
                workerParametersForDb.add(new WorkerParameter(workerId, "AppdynamicsBaseURL", ConnectorConstants.APPD_BASE_URL));
            else
                workerParametersForDb.add(new WorkerParameter(workerId, workerParametersCommon.get(0).getName(), workerParametersCommon.get(0).getValue()));
            if(workerParametersCommon.get(1).getValue() == null)
                workerParametersForDb.add(new WorkerParameter(workerId, "AppdynamicsRegex", ConnectorConstants.APPD_REGEX));
            else
                workerParametersForDb.add(new WorkerParameter(workerId, workerParametersCommon.get(1).getName(), workerParametersCommon.get(1).getValue()));
            workerParametersForDb.add(new WorkerParameter(workerId, "InstanceName", "instancename"));
            workerParametersForDb.add(new WorkerParameter(workerId, "MetricName", "metricname"));
            workerParametersForDb.add(new WorkerParameter(workerId, "ServiceName", "servicename"));

        }
        workerParametersForDb.add(new WorkerParameter(2, "domain", "Appdynamics"));
        workerParametersForDb.add(new WorkerParameter(7, "domain", "Appdynamics"));
        workerParametersForDb.add(new WorkerParameter(4, "kpi-data-source", ConnectorConstants.APPD_KPI_DATA_SOURCE));
        workerParametersForDb.add(new WorkerParameter(4, "file-path", ConnectorConstants.APPD_FILE_PATH));
        if(workerParametersCommon.get(2).getValue() == null)
            workerParametersForDb.add(new WorkerParameter(4, "batch-size", "5"));
        else
            workerParametersForDb.add(new WorkerParameter(4, workerParametersCommon.get(2).getName(), workerParametersCommon.get(2).getValue()));


        for(Integer workerId : Constants.APPD_LOADERS_ID) {
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_SSL_ENABLE, ConfProperties.getString(ConnectorConstants.HEAL_SSL_ENABLE,ConnectorConstants.HEAL_SSL_ENABLE_DEFAULT)));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.GRPC_ADDRESS, ConfProperties.getString(ConnectorConstants.GRPC_ADDRESS,ConnectorConstants.GRPC_ADDRESS_DEFAULT)));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.GRPC_PORT, ConfProperties.getString(ConnectorConstants.GRPC_PORT,ConnectorConstants.GRPC_PORT_DEFAULT)));
            if(workerParametersAdvance.get(0).getValue() == null)
                workerParametersForDb.add(new WorkerParameter(workerId, workerParametersAdvance.get(0).getName(), ConnectorConstants.HEAL_RETRY_INTERVAL_DEFAULT));
            else
                workerParametersForDb.add(new WorkerParameter(workerId, workerParametersAdvance.get(0).getName(), workerParametersAdvance.get(0).getValue()));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_CERT, "/opt/heal/ext-system-adapter/heal-grpc.cert"));
            if(workerParametersAdvance.get(1).getValue() == null)
                workerParametersForDb.add(new WorkerParameter(workerId, workerParametersAdvance.get(1).getName(), ConnectorConstants.HEAL_MAX_RETRY_ATTEMPTS_DEFAULT));
            else
                workerParametersForDb.add(new WorkerParameter(workerId, workerParametersAdvance.get(1).getName(), workerParametersAdvance.get(1).getValue()));
        }
        if(workerParametersAdvance.get(3).getValue() != null && workerParametersAdvance.get(3).getValue().equalsIgnoreCase("True"))
            workerParametersForDb.add(new WorkerParameter(2, ConnectorConstants.KPI_TRANSFORMER_MAPPING_BY_IDENTIFIER, workerParametersAdvance.get(3).getValue()));
        workerParametersForDb.add(new WorkerParameter(2, "AppsoneRepository", "AppsoneRepository"));

        return workerParametersForDb;
    }

    public List<WorkerParameter> createStatementWorkerParametersKubernetes(Map<String, WorkerParameter>  workerParametersCommon, List<WorkerParameter> workerParametersAdvance)
    {
        List<WorkerParameter> workerParametersForDb = new ArrayList<>();
        WorkerParameter kpiMapping = workerParametersAdvance.get(3);
        for(Integer workerId : Constants.KUB_LOADER_CHAIN) {
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_SSL_ENABLE, ConfProperties.getString(ConnectorConstants.HEAL_SSL_ENABLE,ConnectorConstants.HEAL_SSL_ENABLE_DEFAULT)));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.GRPC_ADDRESS, ConfProperties.getString(ConnectorConstants.GRPC_ADDRESS,ConnectorConstants.GRPC_ADDRESS_DEFAULT)));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.GRPC_PORT, ConfProperties.getString(ConnectorConstants.GRPC_PORT,ConnectorConstants.GRPC_PORT_DEFAULT)));
            if(workerParametersAdvance.get(0).getValue() == null)
                workerParametersForDb.add(new WorkerParameter(workerId, workerParametersAdvance.get(0).getName(), ConnectorConstants.HEAL_RETRY_INTERVAL_DEFAULT));
            else
                workerParametersForDb.add(new WorkerParameter(workerId, workerParametersAdvance.get(0).getName(), workerParametersAdvance.get(0).getValue()));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_CERT, "/opt/heal/ext-system-adapter/heal-grpc.cert"));
            if(workerParametersAdvance.get(1).getValue() == null)
                workerParametersForDb.add(new WorkerParameter(workerId, workerParametersAdvance.get(1).getName(), ConnectorConstants.HEAL_MAX_RETRY_ATTEMPTS_DEFAULT));
            else
                workerParametersForDb.add(new WorkerParameter(workerId, workerParametersAdvance.get(1).getName(), workerParametersAdvance.get(1).getValue()));
        }

        for (Integer workerId : Constants.KUB_ALL_CHAINS) {
            if(workerParametersAdvance.get(2).getValue() == null)
                workerParametersForDb.add(new WorkerParameter(workerId, workerParametersAdvance.get(2).getName(), ConnectorConstants.RELOAD_CONFIGURATION_DEFAULT));
            else
                workerParametersForDb.add(new WorkerParameter(workerId, workerParametersAdvance.get(2).getName(), workerParametersAdvance.get(2).getValue()));
            workerParametersForDb.add(new WorkerParameter(workerId, "repository", "LogAnalyzerKPIMaster"));
        }

        for (Integer workerId : Constants.KUB_TOPOLOGY_CHAIN) {
            workerParametersForDb.add(new WorkerParameter(workerId, "kubernetes_repository", "KubernetesKPIMaster"));
        }

        for (Integer workerId : Constants.KUB_PROMETHEUS_CHAIN) {
            workerParametersForDb.add(new WorkerParameter(workerId, "prometheus_repository", "PrometheusKPIMaster"));
        }

        for (Integer workerId : Constants.KUB_EXTRACTOR_CHAINS) {
            workerParametersForDb.add(new WorkerParameter(workerId, "component_suffix", "comp"));
            workerParametersForDb.add(new WorkerParameter(workerId, "host_suffix", "host"));
        }

        workerParametersForDb.add(new WorkerParameter( 1,"TokenExpiry", "300000"));
        workerParametersForDb.add(new WorkerParameter( 1,"cc_endpoints", ConfProperties.getString(Constants.KEYCLOAK_IP)+":"+ConfProperties.getString(ConnectorConstants.CONNECTOR_CC_PORT,ConnectorConstants.CONNECTOR_CC_PORT_DEFAULT)));
        workerParametersForDb.add(new WorkerParameter( 1,"token_url", workerParametersCommon.get("TokenURL").getValue()));
        workerParametersForDb.add(new WorkerParameter( 1,"delete_url",workerParametersCommon.get("InstanceDeleteURL").getValue()));
        workerParametersForDb.add(new WorkerParameter( 1,"create_url", workerParametersCommon.get("InstanceCreateURL").getValue()));
        workerParametersForDb.add(new WorkerParameter( 1,"log_forwarder_agent", workerParametersCommon.get("AgentIdentifier").getValue()));

        workerParametersForDb.add(new WorkerParameter( 2, "for_kubernetes", "true"));
        workerParametersForDb.add(new WorkerParameter( 2,"PrometheusBaseURL", "http://${host_port}/api/v1/query?query="));

        workerParametersForDb.add(new WorkerParameter(3, "pods_to_be_monitored", "pod_1|pod-2-ass|pod3/e|pod4"));
        workerParametersForDb.add(new WorkerParameter( 3, "logscanEndPointId", "1"));
        workerParametersForDb.add(new WorkerParameter( 3, "elasticsearch-connfail-retryinterval", "60"));
        workerParametersForDb.add(new WorkerParameter( 3, "mandatory-fields", ""));
        workerParametersForDb.add(new WorkerParameter( 3, "ls-scroll-size", "500"));
        workerParametersForDb.add(new WorkerParameter( 3, "mandatory-fields-separator", ","));
        workerParametersForDb.add(new WorkerParameter( 3, "elasticsearch-connfail-maxattempts", "-1"));
        workerParametersForDb.add(new WorkerParameter( 3, "is_kubernetes_transaction", "true"));
        workerParametersForDb.add(new WorkerParameter(3, "elasticSearch_timestamp_format", workerParametersCommon.get("elasticSearch_timestamp_format").getValue()));
        workerParametersForDb.add(new WorkerParameter(3, "index-pattern", workerParametersCommon.get("index-pattern").getValue()));
        workerParametersForDb.add(new WorkerParameter(3, "user_name", workerParametersCommon.get("user_name").getValue()));
        workerParametersForDb.add(new WorkerParameter(3, "password", workerParametersCommon.get("password").getValue()));
        workerParametersForDb.add(new WorkerParameter(3, "elastic_query", workerParametersCommon.get("elastic_query").getValue()));

        workerParametersForDb.add(new WorkerParameter(5, "domain", "Prometheus"));
        if(kpiMapping.getValue() != null && kpiMapping.getValue().equalsIgnoreCase("True"))
            workerParametersForDb.add(new WorkerParameter(5, kpiMapping.getName(), kpiMapping.getValue()));
        workerParametersForDb.add(new WorkerParameter(5, "AppsoneRepository", "AppsoneRepository"));
        workerParametersForDb.sort(WorkerParameter.comparator);
        return workerParametersForDb;
    }

    public List<WorkerParameter> createStatementWorkerParametersAwsWithMap(Map<String,WorkerParameter> workerParametersAdvance)
    {
        List<WorkerParameter> workerParametersForDb = new ArrayList<>();
        WorkerParameter healRetry = workerParametersAdvance.get(ConnectorConstants.HEAL_RETRY_INTERVAL);
        WorkerParameter maxRetry = workerParametersAdvance.get(ConnectorConstants.HEAL_MAX_RETRY_ATTEMPTS);
        WorkerParameter reloadConfig = workerParametersAdvance.get(ConnectorConstants.RELOAD_CONFIGURATION);

        for (Integer workerId : Constants.AWS_LOADER_IDS) {
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_SSL_ENABLE, ConfProperties.getString(ConnectorConstants.HEAL_SSL_ENABLE,ConnectorConstants.HEAL_SSL_ENABLE_DEFAULT)));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.GRPC_ADDRESS, ConfProperties.getString(ConnectorConstants.GRPC_ADDRESS,ConnectorConstants.GRPC_ADDRESS_DEFAULT)));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.GRPC_PORT, ConfProperties.getString(ConnectorConstants.GRPC_PORT,ConnectorConstants.GRPC_PORT_DEFAULT)));
            workerParametersForDb.add(new WorkerParameter(workerId, healRetry.getName().toLowerCase(), healRetry.getValue()));
            workerParametersForDb.add(new WorkerParameter(workerId, ConnectorConstants.HEAL_CERT, ConnectorConstants.HEAL_CERT_PATH));
            workerParametersForDb.add(new WorkerParameter(workerId, maxRetry.getName().toLowerCase(), maxRetry.getValue()));
        }

        for (Integer workerId : Constants.AWS_RELOAD_CONFIG_IDS) {
            workerParametersForDb.add(new WorkerParameter(workerId, reloadConfig.getName(), reloadConfig.getValue()));
            workerParametersForDb.add(new WorkerParameter(workerId, "repository", "LogAnalyzerKPIMaster"));
        }

        for(Integer workerId: Constants.AWS_REPO_INIT) {
            workerParametersForDb.add(new WorkerParameter(workerId, "aws_repository", "AWSKPIMaster"));
        }
        WorkerParameter p = workerParametersAdvance.get(ConnectorConstants.KPI_TRANSFORMER_MAPPING_BY_IDENTIFIER);
        workerParametersForDb.add(new WorkerParameter(4,"domain","aws"));
        workerParametersForDb.add(new WorkerParameter(4,p.getName(),p.getValue()));
        workerParametersForDb.add(new WorkerParameter(4,"AppsoneRepository","AppsoneRepository"));

        return workerParametersForDb;
    }

}
