package com.appnomic.appsone.controlcenter.exceptions;

public class CustomComponentException extends RuntimeException
{
    private final String errorMessage;

    public CustomComponentException(String message, Throwable cause, String errorMessage) {
        super(message, cause);
        this.errorMessage = errorMessage;
    }

    public CustomComponentException(Throwable cause, String errorMessage) {
        super(cause);
        this.errorMessage = errorMessage;
    }

    public CustomComponentException(String errorMessage)
    {
        super("CustomComponentException : "+ errorMessage);
        this.errorMessage  = errorMessage;
    }


    public String getSimpleMessage()    {
        return "CustomComponentException :: "+this.errorMessage;
    }

}
