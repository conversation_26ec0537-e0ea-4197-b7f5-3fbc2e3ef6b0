package com.appnomic.appsone.controlcenter.exceptions;

public class FileDeleteException extends Exception{
    private final String errorMessage;

    public FileDeleteException(String message, Throwable cause, String errorMessage) {
        super(message, cause);
        this.errorMessage = errorMessage;
    }

    public FileDeleteException(Throwable cause, String errorMessage) {
        super(cause);
        this.errorMessage = errorMessage;
    }

    public FileDeleteException(String errorMessage)
    {
        super("FileUploadException : "+ errorMessage);
        this.errorMessage  = errorMessage;
    }


    public String getSimpleMessage()    {
        return "FileDeleteException : "+this.errorMessage;
    }
}
