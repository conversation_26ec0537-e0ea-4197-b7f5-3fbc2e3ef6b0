package com.appnomic.appsone.controlcenter.rest;

import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.service.KeyCloakAuthService;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.controlcenter.util.JsonTransformer;
import com.appnomic.appsone.controlcenter.util.UserRouteAccessValidationUtil;
import com.appnomic.appsone.controlcenter.util.UserValidationUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Spark;
import spark.servlet.SparkApplication;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static spark.Spark.*;

public class Routes implements SparkApplication {
    private static final Logger log = LoggerFactory.getLogger(Routes.class);

    @Override
    public void init() {
        String staticFileLocation = ConfProperties.getString(Constants.STATIC_FILES_LOCATIONS, Constants.STATIC_FILES_LOCATIONS_DEFAULT);
        String keystoreFilePath = ConfProperties.getString(Constants.KEYSTORE_FILE_NAME, Constants.KEYSTORE_FILE_NAME_DEFAULT);
        String keystorePassword = ConfProperties.getString(Constants.KEYSTORE_PASSWORD, Constants.KEYSTORE_PASSWORD_DEFAULT);
        String truststoreFilePath = ConfProperties.getString(Constants.TRUSTSTORE_FILE_NAME, Constants.TRUSTSTORE_FILE_NAME_DEFAULT);
        String truststorePassword = ConfProperties.getString(Constants.TRUSTSTORE_PASSWORD, Constants.TRUSTSTORE_PASSWORD_DEFAULT);
        int httpPort = ConfProperties.getInt(Constants.HTTPS_PORT_PROPERTY_NAME, Constants.HTTPS_PORT_DEFAULT);
        int maxThreads = ConfProperties.getInt(Constants.HTTP_HANDLER_MAX_THREADS, Constants.HTTP_HANDLER_MAX_THREADS_DEFAULT);

        port(httpPort);

        log.info("Truststore:{}, keystore:{}", truststoreFilePath, keystoreFilePath);
        if (!keystoreFilePath.trim().isEmpty()) {
            secure(keystoreFilePath, keystorePassword,
                    !truststoreFilePath.trim().isEmpty() ? truststoreFilePath : null,
                    !truststorePassword.trim().isEmpty() ? truststorePassword : null);
        } else {
            log.warn("SSL is disabled for heal-controlcenter port.");
        }

        log.info("Control center to start with {} port, threads:{}.", httpPort, maxThreads);
        threadPool(maxThreads);

        staticFiles.location(staticFileLocation);
        staticFiles.header("Server", ConfProperties.getHeaderConfigurations().getOrDefault("Server", ""));
        Spark.init();

        //Getting allowed method calls from headers_details.json
        List<String> allowedMethodCalls = Arrays.stream(ConfProperties.getHeaderConfigurations()
                        .getOrDefault("Access-Control-Allow-Methods", "GET,DELETE,PUT,POST")
                        .split(","))
                .collect(Collectors.toList());

        before((request, response) -> {
            log.debug("Before method is called............{}", request.pathInfo());
            CCCache.INSTANCE.updateRequests(1);

            String requestMethod = request.requestMethod().toUpperCase();

            //Restricting the method calls that is not mentioned in headers_details.json
            if (!allowedMethodCalls.contains(requestMethod)) {
                CCCache.INSTANCE.updateUnauthorizedRequests(1);
                ConfProperties.getHeaderConfigurations().forEach(response::header);
                halt(405, "Method Not Allowed");
            }

            if (UserRouteAccessValidationUtil.shouldSkipValidation(request.pathInfo(),request.requestMethod().toUpperCase())) {
                CCCache.INSTANCE.updateSkipValidationRequests(1);
                log.warn("JWT, user status and user accessibility validation skipped. " +
                        "Request details: {}, {}, {}", request.pathInfo(), request.requestMethod(), request.headers());
            } else {
                String authKey = request.headers("Authorization");

                if (authKey == null || authKey.trim().isEmpty()) {
                    CCCache.INSTANCE.updateUnauthorizedRequests(1);
                    log.error("Request [{}] with requestMethod [{}] does not have authorization header", request.uri(), request.requestMethod());
                    CCCache.INSTANCE.updateStatusCodes(401, 1);
                    ConfProperties.getHeaderConfigurations().forEach(response::header);
                    halt(401, "Authorization token is invalid");
                }

                long st = System.currentTimeMillis();
                long start = System.currentTimeMillis();
                boolean userVerified = UserValidationUtil.verifyUserStatus(request);
                if (!userVerified) {
                    CCCache.INSTANCE.updateUnauthorizedRequests(1);
                    ConfProperties.getHeaderConfigurations().forEach(response::header);
                    CCCache.INSTANCE.updateStatusCodes(401, 1);
                    halt(401, "You are dormant.");
                }
                log.debug("Time taken for verify user status is {} ms", (System.currentTimeMillis() - start));

                start = System.currentTimeMillis();
                boolean authenticated = KeyCloakAuthService.isValidKey(authKey);
                if (!authenticated) {
                    CCCache.INSTANCE.updateUnauthorizedRequests(1);
                    ConfProperties.getHeaderConfigurations().forEach(response::header);
                    CCCache.INSTANCE.updateStatusCodes(401, 1);
                    halt(401, "You are not welcome here");
                }
                log.debug("Time taken for verify user key is {} ms", (System.currentTimeMillis() - start));

                start = System.currentTimeMillis();
                boolean isUserValid = UserValidationUtil.verifyUserAccessibility(request);
                if (!isUserValid) {
                    CCCache.INSTANCE.updateAccessDeniedRequests(1);
                    ConfProperties.getHeaderConfigurations().forEach(response::header);
                    CCCache.INSTANCE.updateStatusCodes(401, 1);
                    halt(401, "You do not have access to the resource");
                }
                log.debug("Time taken for verify user access is {} ms", (System.currentTimeMillis() - start));

                log.debug("Time taken for before method is {} ms", (System.currentTimeMillis() - st));
            }
        });

        after((request, response) -> ConfProperties.getHeaderConfigurations().forEach(response::header));

        options("*", (request, response) -> {
            response.status(200);
            return response;
        });

        path(Constants.API_BASE_URL, () -> {
            get("/auto-discovery-components", ControlCenterService.getAutoDiscoveryComponents, new JsonTransformer());
            get("/auto-discovery-configuration-entities", ControlCenterService.getAutoDiscoveryConfigurationEntities, new JsonTransformer());
            post("/aura-users-sync", ControlCenterService.auraUsersSync, new JsonTransformer());
            get("/keycloak-settings", ControlCenterService.keycloakSettings, new JsonTransformer());
            get("/agent-config-data", ControlCenterService.compConfigData, new JsonTransformer());
            get("/timezones", ControlCenterService.getTimezones, new JsonTransformer());
            get("/installation-attributes", ControlCenterService.getInstallationAttributes, new JsonTransformer());
            get("/page-details", ControlCenterService.getPageDetails, new JsonTransformer());
            get("/user-profiles", ControlCenterService.getUserProfiles, new JsonTransformer());
            put("/whatsapp-opt-in", ControlCenterService.updateWhatsappOptIn, new JsonTransformer());
            get("/whatsapp-opt-in", ControlCenterService.getWhatsappOptIn, new JsonTransformer());
            put("/webhook/whatsapp-opt-in", ControlCenterService.webhookWhatsappOptIn, new JsonTransformer());
            post("/push-discovery-data", ControlCenterService.pushDiscoveryData, new JsonTransformer());
            get("/license/hosts", ControlCenterService.getLicenseDetails, new JsonTransformer());
            get("/entities/tags", ControlCenterService.getEntityTags, new JsonTransformer());


            path("/users", () -> {
                get("", ControlCenterService.getUsers, new JsonTransformer());
                post("", ControlCenterService.addUser, new JsonTransformer());
                put("", ControlCenterService.putUsers, new JsonTransformer());
                get("/access-info", ControlCenterService.getUserAccessInformation, new JsonTransformer());
                get("/username-validation", ControlCenterService.validateUsername, new JsonTransformer());
                put("/notifications", ControlCenterService.putUserNotifications, new JsonTransformer());

                path("/" + Constants.USER_IDENTIFIER, () -> {
                    get("", ControlCenterService.getUserDetails, new JsonTransformer());
                    put("", ControlCenterService.editUser, new JsonTransformer());
                    delete("", ControlCenterService.deleteUser, new JsonTransformer());
                });
            });

            path("/roles", () -> {
                get("", ControlCenterService.getRoles, new JsonTransformer());
                get("/:roleId/profiles", ControlCenterService.getProfiles, new JsonTransformer());

            });

            path("/agents", () -> {
                post("", ControlCenterService.postAgents, new JsonTransformer());
                put("", ControlCenterService.updateAgents, new JsonTransformer());
                path("/" + Constants.AGENT_IDENTIFIER, () -> {
                    delete("", ControlCenterService.deleteAgents, new JsonTransformer());
                    get("/rules", ControlCenterService.agentRules, new JsonTransformer());
                    get("/jim-commands", ControlCenterService.getJimCommands, new JsonTransformer());
                });
            });

            path("/commands/:commandJobId", () -> post("/audit", ControlCenterService.commandAudit, new JsonTransformer()));

            path("/supervisors", () -> {
                get("", ControlCenterService.getSupervisors, new JsonTransformer());
                put("/:supervisorIdentifier", ControlCenterService.updateSupervisor, new JsonTransformer());
            });

            path("/accounts", () -> {
                post("", ControlCenterService.postAccounts, new JsonTransformer());
                get("", ControlCenterService.getAccounts, new JsonTransformer());

                path("/" + Constants.ACCOUNT_IDENTIFIER, () -> {

                    path("/schedulers", () -> {
                        get("", ControlCenterService.getConfiguredSchedulers, new JsonTransformer());
                        get("/:schedulerDetailsId/jobs", ControlCenterService.getConfiguredScheduledJobs, new JsonTransformer());
                        path("/jobs", () -> {
                            get("", ControlCenterService.getTriggeredScheduledJobs, new JsonTransformer());
                            post("", ControlCenterService.performRequestedSchedulerJobAction, new JsonTransformer());
                        });
                    });

                    path("/connectors", () -> {
                        get("", ControlCenterService.getConnectorsDetails, new JsonTransformer());
                        get("/:connectorId/template", ControlCenterService.getConnectorTemplate, new JsonTransformer());
                        post("/:connectorId/configuration", ControlCenterService.uploadConnectorTemplate, new JsonTransformer());
                        get("/:connectorId/configuration", ControlCenterService.getConnectorTemplateConfig, new JsonTransformer());
                        get("/:connectorId/configuration/status", ControlCenterService.getTemplateUploadStatus, new JsonTransformer());
                        post("/:connectorId/controller", ControlCenterService.connectorControllerActions, new JsonTransformer());
                        get("/command/status", ControlCenterService.connectorCommandStatus, new JsonTransformer());
                    });

                    post("/map-to-service", ControlCenterService.postMapToService, new JsonTransformer());
                    post("/ignore-undoignore-hosts", ControlCenterService.postEntityStatus, new JsonTransformer());
                    get("/ignored-entities", ControlCenterService.getIgnoredEntities, new JsonTransformer());
                    get("/entity-count", ControlCenterService.getEntityCount, new JsonTransformer());
                    get("/health/instances", ControlCenterService.getHealthOfInstances, new JsonTransformer());
                    post("/actions", ControlCenterService.postActions, new JsonTransformer());
                    post("/instances-old", ControlCenterService.postInstancesOld, new JsonTransformer());
                    post("/clusters", ControlCenterService.postClusters, new JsonTransformer());
                    post("/transaction-wrappers", ControlCenterService.postTransactionWrappers, new JsonTransformer());
                    post("/applications-old", ControlCenterService.postApplicationsOld, new JsonTransformer());
                    put("/transactions", ControlCenterService.updateTransactions, new JsonTransformer());
                    post("/process-configuration", ControlCenterService.processConfiguration, new JsonTransformer());
                    post("/generate-signature", ControlCenterService.generateSignature, new JsonTransformer());
                    post("/producers", ControlCenterService.postProducers, new JsonTransformer());
                    post("/components", ControlCenterService.postComponents, new JsonTransformer());
                    get("/audit-data", ControlCenterService.auditTrailService, new JsonTransformer());
                    get("/component-details", ControlCenterService.componentDetailsService, new JsonTransformer());


                    post("/kpi-groups", ControlCenterService.postKpiGroups, new JsonTransformer());
                    get("/availabilityCategories", ControlCenterService.getAvailabilityCategories, new JsonTransformer());

                    path("/forensic-actions", () -> {
                        get("", ControlCenterService.getForensicActions, new JsonTransformer());
                        post("/bulk-trigger", ControlCenterService.BulkTriggerAction, new JsonTransformer());
                    });

                    path("/kpis", () -> {
                        post("", ControlCenterService.postNonGroupKpis, new JsonTransformer());
                        get("", ControlCenterService.getKpiList, new JsonTransformer());
                    });
                    path("/custom-kpis", () -> {
                        post("", ControlCenterService.postCustomKpis, new JsonTransformer());
                        put("", ControlCenterService.putCustomKpis, new JsonTransformer());
                        delete("/" + Constants.KPI_IDENTIFIER_PATH_PARAMETER, ControlCenterService.deleteCustomKpi,
                                new JsonTransformer());
                    });

                    put("/whitelistsettings", ControlCenterService.updateWhitelistSettings, new JsonTransformer());

                    path("/producers", () -> {
                        post("", ControlCenterService.postProducers, new JsonTransformer());
                        get("", ControlCenterService.getProducers, new JsonTransformer());
                        get("/:producerId/mapped-kpis", ControlCenterService.getProducerKpiMapping, new JsonTransformer());
                    });

                    get("/service-clusters", ControlCenterService.getServicesClusters, new JsonTransformer());
                    get("/agent-types", ControlCenterService.getAgentTypesAtAccLvl, new JsonTransformer());
                    get("/agent-datasources", ControlCenterService.getAgentDataSourcesAtAccLevel, new JsonTransformer());
                    get("/agent-list", ControlCenterService.getAgentList, new JsonTransformer());
                    get("/component-attributes", ControlCenterService.getComponentAttributes, new JsonTransformer());

                    path("/agent-details", () -> {
                        get("", ControlCenterService.getAgentHeartbeatDetails, new JsonTransformer());
                        //TODO:Need to remove , currently unused.
                        put("/" + Constants.AGENT_IDENTIFIER, ControlCenterService.updateAgentHeartbeatDetails, new JsonTransformer());
                    });

                    path("/hosts", () -> {
                        post("", ControlCenterService.postHosts, new JsonTransformer());
                        get("", ControlCenterService.getHosts, new JsonTransformer());
                    });

                    get("/environment", ControlCenterService.getEnvironmentDetails, new JsonTransformer());
                    get("/installation-logs", ControlCenterService.getInstallationLogs, new JsonTransformer());


                    path("/whitelist", () -> {
                        get("", ControlCenterService.getWhitelist, new JsonTransformer());
                        post("", ControlCenterService.addApplicationWhitelist, new JsonTransformer());
                        put("", ControlCenterService.updateApplicationWhitelist, new JsonTransformer());
                        delete("", ControlCenterService.deleteApplicationWhitelist, new JsonTransformer());
                    });

                    path("/categories", () -> {
                        post("", ControlCenterService.postCategories, new JsonTransformer());
                        get("", ControlCenterService.getCategories, new JsonTransformer());
                        put("/:categoryId", ControlCenterService.putCategories, new JsonTransformer());
                        delete("/:categoryId", ControlCenterService.deleteCategories, new JsonTransformer());
                    });

                    path("/notification-settings", () -> {
                        get("", ControlCenterService.getNotificationSettings, new JsonTransformer());
                        put("", ControlCenterService.updateNotificationSettings, new JsonTransformer());
                    });

                    path("/commands/:commandJobId", () -> post("/command-status", ControlCenterService.postCommandStatus, new JsonTransformer()));
                    path("/supervisors", () -> post("", ControlCenterService.postSupervisors, new JsonTransformer()));

                    path("/webhook", () -> {
                        get("", ControlCenterService.getWebHook, new JsonTransformer());
                        post("", ControlCenterService.postWebHook, new JsonTransformer());
                        put("", ControlCenterService.updateWebHook, new JsonTransformer());
                        delete("", ControlCenterService.deleteWebHook, new JsonTransformer());
                    });

                    path("/sms-configurations", () -> {
                        post("", ControlCenterService.postSmsConfigurations, new JsonTransformer());
                        put("", ControlCenterService.updateSmsConfigurations, new JsonTransformer());
                        get("", ControlCenterService.getSmsConfigurations, new JsonTransformer());
                    });

                    path("/email-configurations", () -> {
                        post("", ControlCenterService.postEmailConfigurations, new JsonTransformer());
                        put("", ControlCenterService.updateEmailConfigurations, new JsonTransformer());
                        get("", ControlCenterService.getEmailConfigurations, new JsonTransformer());
                    });

                    path("/notifications", () -> {
                        get("", ControlCenterService.getAccountNotifPreferences, new JsonTransformer());
                        put("", ControlCenterService.postAccountNotifPreferences, new JsonTransformer());
                    });

                    path("/instances-forensics", () -> {
                        get("", ControlCenterService.getCategoryForensics, new JsonTransformer());
                        put("", ControlCenterService.putCategoryForensics, new JsonTransformer());
                    });

                    path("/instances", () -> {
                        get("", ControlCenterService.getInstancesAtAccLevel, new JsonTransformer());
                        post("", ControlCenterService.postInstances, new JsonTransformer());
                        put("/environment-details", ControlCenterService.updateInstancesEnvDetails, new JsonTransformer());
                        path("/agent-mapping", () -> {
                            get("", ControlCenterService.getAgentMappingForCompInstance, new JsonTransformer());
                            put("", ControlCenterService.putAgentMappingForCompInstance, new JsonTransformer());
                        });
                        put("/" + Constants.COMP_INSTANCE_IDENTIFIER, ControlCenterService.updateInstances, new JsonTransformer());
                        put("", ControlCenterService.updateInstanceDetails, new JsonTransformer());
                        delete("", ControlCenterService.deleteInstances, new JsonTransformer());

                        path("/kpis", () -> {
                            post("/thresholds", ControlCenterService.postInstanceKpiThresholds, new JsonTransformer());
                            put("/thresholds", ControlCenterService.putInstanceKpiThresholds, new JsonTransformer());
                            get("/thresholds", ControlCenterService.getInstanceKpiThresholds, new JsonTransformer());
                            delete("/thresholds", ControlCenterService.deleteInstanceKpiThresholds, new JsonTransformer());
                            post("/persistence-suppression-configs", ControlCenterService.postInstanceKpiPersistenceSuppressionConfig, new JsonTransformer());
                            put("/persistence-suppression-configs", ControlCenterService.putInstanceKpiPersistenceSuppressionConfig, new JsonTransformer());
                            get("/persistence-suppression-configs", ControlCenterService.getInstanceKpiPersistenceSuppressionConfig, new JsonTransformer());
                            delete("/persistence-suppression-configs", ControlCenterService.deleteInstanceKpiPersistenceSuppressionConfig, new JsonTransformer());
                            put("/metric-maintenance-status", ControlCenterService.putInstanceKpiMaintenanceStatus, new JsonTransformer());
                            post("/threshold-config", ControlCenterService.postInstanceKpiSeverityAnomaly, new JsonTransformer());
                        });

                        path("/metric-groups", () -> {
                            get("", ControlCenterService.getMetricGroups, new JsonTransformer());
                            path("/" + Constants.GROUP_ID + "/attributes", () -> {
                                get("", ControlCenterService.getMetricGroupAttributes, new JsonTransformer());
                                put("", ControlCenterService.putMetricGroupAttributes, new JsonTransformer());
                            });
                        });

                        path("/" + Constants.INSTANCE_ID, () -> {
                            get("/attributes", ControlCenterService.getInstanceAttributes, new JsonTransformer());
                            post("/attributes", ControlCenterService.editInstanceAttributes, new JsonTransformer());
                            put("/name", ControlCenterService.updateInstanceName, new JsonTransformer());
                        });

                        path("/metadata", () -> {
                            post("", ControlCenterService.addInstanceMetadata, new JsonTransformer());
                        });
                    });

                    path("/applications", () -> {
                        get("", ControlCenterService.getApplications, new JsonTransformer());
                        post("", ControlCenterService.postApplications, new JsonTransformer());
                        put("/application-notification-settings", ControlCenterService.updateApplicationNotificationSettings, new JsonTransformer());
                        put("/:applicationIdentifier", ControlCenterService.updateApplication, new JsonTransformer());
                        delete("", ControlCenterService.deleteApplications, new JsonTransformer());
                        get("/" + Constants.APPLICATION_ID + "/application-notification-settings", ControlCenterService.getApplicationNotificationSettings, new JsonTransformer());
                        path("/" + Constants.APPLICATION_ID + "/whitelist", () -> {
                            post("", ControlCenterService.addServiceWhitelist, new JsonTransformer());
                            put("", ControlCenterService.updateServiceWhitelist, new JsonTransformer());
                            delete("", ControlCenterService.deleteServiceWhitelist, new JsonTransformer());
                        });

                        path("/" + Constants.APPLICATION_ID + "/service/" + Constants.SERVICE_ID + "/whitelist", () -> {
                            post("", ControlCenterService.addKPIWhitelist, new JsonTransformer());
                            put("", ControlCenterService.updateKPIWhitelist, new JsonTransformer());
                            delete("", ControlCenterService.deleteKPIWhitelist, new JsonTransformer());
                        });

                        path("/" + Constants.APPLICATION_ID + "/percentiles", () -> {
                            get("", ControlCenterService.getAppPercentiles, new JsonTransformer());
                            put("", ControlCenterService.updateAppPercentiles, new JsonTransformer());
                        });
                    });

                    path("/connections", () -> {
                        get("", ControlCenterService.getConnections, new JsonTransformer());
                        post("", ControlCenterService.postConnections, new JsonTransformer());
                        delete("", ControlCenterService.deleteConnection, new JsonTransformer());
                    });

                    path("/agents", () -> {
                        post("/trigger-command", ControlCenterService.postCommandTrigger, new JsonTransformer());
                        post("/command-trigger-status", ControlCenterService.getCommandTriggerStatus, new JsonTransformer());
                    });

                    path("/users/:userId/notifications", () -> {
                        get("", ControlCenterService.getUserNotifPreferences, new JsonTransformer());
                        post("", ControlCenterService.postUserNotifPreferences, new JsonTransformer());
                    });

                    path("/transactions", () -> {
                        post("", ControlCenterService.postTransactions, new JsonTransformer());
                        post("/" + Constants.TRANSACTION_IDENTIFIER + "/group-tags", ControlCenterService.postServiceTransactionGroupTags, new JsonTransformer());
                        get("/group-tags", ControlCenterService.getServiceTransactionGroupTags, new JsonTransformer());
                    });

                    path("/parent-applications", () -> {
                        post("", ControlCenterService.postParentApplications, new JsonTransformer());
                        get("", ControlCenterService.getParentApplications, new JsonTransformer());

                        path("/" + Constants.PARENT_APPLICATION_IDENTIFIER, () ->
                                delete("", ControlCenterService.deleteParentApplication, new JsonTransformer()));
                    });

                    path("/services", () -> {
                        get("", ControlCenterService.getServices, new JsonTransformer());
                        post("", ControlCenterService.postServices, new JsonTransformer());
                        get("/agents-health", ControlCenterService.getAgentHealth, new JsonTransformer());

                        path("/entry-point", () -> {
                            post("", ControlCenterService.postServiceEntryPoint, new JsonTransformer());
                            delete("", ControlCenterService.deleteServiceEntryPoint, new JsonTransformer());
                        });

                        path("/" + Constants.SERVICE_IDENTIFIER, () -> {
                            put("", ControlCenterService.updateServiceDetails, new JsonTransformer());
                            delete("", ControlCenterService.deleteService, new JsonTransformer());

                            path("/forensic-actions", () -> {
                                get("", ControlCenterService.getJIMForensicActions, new JsonTransformer());
                                get("/triggered-forensics", ControlCenterService.getTriggeredForensicsList, new JsonTransformer());
                                post("/trigger", ControlCenterService.triggerAction, new JsonTransformer());

                            });

                            get("/requests", ControlCenterService.getRequestsByTransactionStatus, new JsonTransformer());

                            get("/commit-details", ControlCenterService.getCommitDetails, new JsonTransformer());

                            path("/auto-acceptance-setting", () -> {
                                get("", ControlCenterService.getAutoAcceptanceSettings, new JsonTransformer());
                                post("", ControlCenterService.addAutoAcceptanceSettings, new JsonTransformer());
                            });
                            post("/request-action", ControlCenterService.addRequestDiscoveryStatus, new JsonTransformer());
                        });

                        path("/" + Constants.SERVICE_ID, () -> {
                            get("/attributes", ControlCenterService.getServicePageAttributes, new JsonTransformer());
                            get("/agent-types", ControlCenterService.getAgentTypes, new JsonTransformer());
                            get("/agent-instances", ControlCenterService.getAgentInstances, new JsonTransformer());
                            get("/instances", ControlCenterService.getInstances, new JsonTransformer());
                            get("/jim-agents", ControlCenterService.getServiceJIMAgents, new JsonTransformer());
                            put("", ControlCenterService.updateServiceDetails, new JsonTransformer());

                            path("/metricDetails", () -> {
                                post("", ControlCenterService.postMetricDetailsForService, new JsonTransformer());
                                put("", ControlCenterService.putMetricDetailsForService, new JsonTransformer());
                            });

                            path("/agent-commands", () -> {
                                get("", ControlCenterService.getServiceAgentCommands, new JsonTransformer());
                                post("", ControlCenterService.postServiceAgentCommands, new JsonTransformer());
                            });

                            path("/configurations", () -> {
                                get("", ControlCenterService.getServiceConfigurations, new JsonTransformer());
                                post("", ControlCenterService.postServiceConfigurations, new JsonTransformer());
                            });

                            path("/rules", () -> {
                                get("", ControlCenterService.getServiceRules, new JsonTransformer());
                                post("", ControlCenterService.postServiceRules, new JsonTransformer());
                                put("", ControlCenterService.updateServiceRules, new JsonTransformer());
                                get("/:ruleId", ControlCenterService.getServiceRule, new JsonTransformer());
                                get("/download", ControlCenterService.downloadRules, new JsonTransformer());
                                post("/order", ControlCenterService.postServiceRulesOrder, new JsonTransformer());
                            });

                            path("/maintenance-details", () -> {
                                get("", ControlCenterService.getMaintenanceWindow, new JsonTransformer());
                                post("", ControlCenterService.postMaintenanceWindow, new JsonTransformer());
                                path("/:maintenanceId", () -> {
                                    put("", ControlCenterService.updateMaintenanceWindow, new JsonTransformer());
                                    delete("", ControlCenterService.deleteMaintenanceWindow, new JsonTransformer());
                                });
                            });

                            path("/instances/maintenance-details", () -> {
                                get("", ControlCenterService.getAdhocMaintenanceWindow, new JsonTransformer());
                                post("", ControlCenterService.postAdhocMaintenanceWindow, new JsonTransformer());
                                delete("/:maintenanceId", ControlCenterService.deleteAdhocMaintenanceWindow, new JsonTransformer());
                            });

                            path("/threshold-types/:threshold-type/thresholds", () -> {
                                get("", ControlCenterService.getServiceThresholds, new JsonTransformer());
                                post("", ControlCenterService.postServiceThresholds, new JsonTransformer());
                            });

                            path("/transactions", () -> {
                                get("", ControlCenterService.getServiceTransactions, new JsonTransformer());
                                post("/group-tags", ControlCenterService.getTransactionGroupTags, new JsonTransformer());
                                path("/" + Constants.TRANSACTION_IDENTIFIER, () -> {
                                    get("/thresholds", ControlCenterService.getTransactionThresholds, new JsonTransformer());
                                    post("/thresholds", ControlCenterService.postTransactionThresholds, new JsonTransformer());
                                });
                            });
                            path("/summary", () -> get("", ControlCenterService.viewSummary, new JsonTransformer()));
                        });
                    });

                    path("/batch-details/process-details", () -> {
                        post("", ControlCenterService.postProcessDetails, new JsonTransformer());
                        path("/" + Constants.PROCESS_DETAILS_ID, () -> {
                            put("", ControlCenterService.putProcessDetails, new JsonTransformer());
                            delete("", ControlCenterService.deleteProcessDetails, new JsonTransformer());
                        });
                    });

                    post("/add-to-system", ControlCenterService.addToSystem, new JsonTransformer());

                    path("/agents-config", () -> {
                        post("", ControlCenterService.postAgentsConfig, new JsonTransformer());
                        put("", ControlCenterService.updateAgentsConfig, new JsonTransformer());
                        delete("", ControlCenterService.deleteAgentsConfig, new JsonTransformer());
                    });
                    put("/agents-data-source", ControlCenterService.updateAgentsDataSources, new JsonTransformer());
                });
            });
        });

        after((req, res) -> res.type("application/json"));
    }
}
