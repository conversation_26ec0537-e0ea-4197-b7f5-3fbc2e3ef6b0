package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Actions {
    private int id;
    @NonNull
    private String name;
    private String identifier;
    @NonNull
    private int standardType;
    @NonNull
    private int agentType;
    @NonNull
    private int accountId;
    private String createdTime;
    private String updatedTime;
    private String userDetailsId;
    private int actionTypeId;
    private int status;
}
