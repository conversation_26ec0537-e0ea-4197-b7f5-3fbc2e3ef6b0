package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.ComponentAgentBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.ComponentAgentDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR> <PERSON> : 7/2/19
 */
public class ComponentAgentDataService {
    private static final Logger logger = LoggerFactory.getLogger(ComponentAgentDataService.class);

    public static int addComponentAgent(ComponentAgentBean componentAgentBean, Handle handle) {
        ComponentAgentDao componentAgentDao = getComponentDaoObject(handle);
        try {
            return componentAgentDao.addComponentAgent(componentAgentBean);
        } catch (Exception e) {
            logger.error("Error occurred while adding component agent details" + e.getMessage(), e);
            logger.debug("trace: ", e);
        } finally {
            closeDaoConnection(handle, componentAgentDao);
        }
        return 0;
    }

    public static ComponentAgentBean getComponentAgent(int agentId) {
        ComponentAgentDao componentAgentDao = MySQLConnectionManager.getInstance().open(ComponentAgentDao.class);
        try {
            return componentAgentDao.getComponentAgentBean(agentId);
        } catch (Exception e) {
            logger.error("Error occurred while getting component agent details" + e.getMessage(), e);
            logger.debug("trace: ", e);
        } finally {
            MySQLConnectionManager.getInstance().close(componentAgentDao);
        }
        return null;
    }

    public static List<ComponentAgentBean> getComponentAgentBeanList(int dataCommunicationId) {
        ComponentAgentDao componentAgentDao = MySQLConnectionManager.getInstance().open(ComponentAgentDao.class);
        try {
            return componentAgentDao.getComponentAgentBeanList(dataCommunicationId);
        } catch (Exception e) {
            logger.error("Error occurred while getting component agent details" + e.getMessage(), e);
            logger.debug("trace: ", e);
        } finally {
            MySQLConnectionManager.getInstance().close(componentAgentDao);
        }
        return null;
    }

    public static int updateComponentAgent(ComponentAgentBean componentAgentBean, Handle handle) {
        ComponentAgentDao componentAgentDao = getComponentDaoObject(handle);
        try {
            return componentAgentDao.updteComponentAgent(componentAgentBean);
        } catch (Exception e) {
            logger.error("Error occurred while adding component agent details" + e.getMessage(), e);
            logger.debug("trace: ", e);
        } finally {
            closeDaoConnection(handle, componentAgentDao);
        }
        return 0;
    }

    private static ComponentAgentDao getComponentDaoObject(Handle handle){
        if(handle == null){
            return MySQLConnectionManager.getInstance().open(ComponentAgentDao.class);
        }
        else{
            return handle.attach(ComponentAgentDao.class);
        }
    }
    private static void closeDaoConnection(Handle handle, ComponentAgentDao dao){
        if(handle == null){
            MySQLConnectionManager.getInstance().close(dao);
        }
    }

}
