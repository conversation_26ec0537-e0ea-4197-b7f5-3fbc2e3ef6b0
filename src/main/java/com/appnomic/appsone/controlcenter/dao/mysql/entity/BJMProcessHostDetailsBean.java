package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.Data;

import java.util.List;

@Data
public class BJMProcessHostDetailsBean {

    private int processHostDetailsId;
    private int processDetailsId;
    private String processDetailsName;
    private String processDetailsIdentifier;
    private int processTypeId;
    private int batchJobId;
    private String batchJobName;
    private String batchJobIdentifier;
    private String batchJobGroupName;
    private int serviceId;
    private String directoryPath;
    private String hostAddress;
    private int status;

    private List<ProcessArgumentBean> processArgumentDetails;
}
