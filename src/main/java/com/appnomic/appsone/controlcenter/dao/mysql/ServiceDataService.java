package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.ServiceTransactionSettingBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.ServiceDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ServiceDataService extends AbstractDaoService<ServiceDao> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceDataService.class);

    public void deleteController(int id, Handle handle) throws ControlCenterException {
        ServiceDao dao = getDaoConnection(handle, ServiceDao.class);
        try {
            dao.deleteController(id);
        } catch (Exception e) {
            LOGGER.error("Exception while deleting controller from 'controller' table.", e);
            throw new ControlCenterException("Exception while deleting controller from 'controller' table.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void deleteTagMappingDetailsForController(int id, Handle handle) throws ControlCenterException {
        ServiceDao dao = getDaoConnection(handle, ServiceDao.class);
        try {
            dao.deleteTagMappingDetailsForController(id);
        } catch (Exception e) {
            LOGGER.error("Exception while deleting tag mapping details for controller from 'tag_mapping' table.", e);
            throw new ControlCenterException("Exception while deleting tag mapping details for controller from 'tag_mapping' table.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void deleteServiceMappingDetails(int id, Handle handle) throws ControlCenterException {
        ServiceDao dao = getDaoConnection(handle, ServiceDao.class);
        try {
            dao.deleteServiceMappingDetails(id);
        } catch (Exception e) {
            LOGGER.error("Exception while deleting details for a service from 'tag_mapping' table.", e);
            throw new ControlCenterException("Exception while deleting details for a service from 'tag_mapping' table.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void removeServiceConnections(int accountId, int serviceId, Handle handle) throws ControlCenterException {
        ServiceDao dao = getDaoConnection(handle, ServiceDao.class);
        try {
            dao.removeServiceConnections(serviceId, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while deleting service connections from 'connection_details' table.", e);
            throw new ControlCenterException("Exception while deleting service connections from 'connection_details' table.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void deleteServiceConfigurations(int accountId, int serviceId, Handle handle) throws ControlCenterException {
        ServiceDao dao = getDaoConnection(handle, ServiceDao.class);
        try {
            dao.deleteServiceConfigurations(accountId, serviceId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting values for persistence and suppression values for service from 'service_configurations' table." + e.getMessage(), e);
            throw new ControlCenterException("Failed to delete persistence and suppression values for service from 'service_configurations' table.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void deleteServiceKpiThreshold(int accountId, int serviceId, Handle handle) throws ControlCenterException {
        ServiceDao dao = getDaoConnection(handle, ServiceDao.class);
        try {
            dao.deleteServiceKpiThreshold(accountId, serviceId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting service KPI threshold values from 'service_kpi_thresholds' table." + e.getMessage(), e);
            throw new ControlCenterException("Error occurred while deleting service KPI threshold values from 'service_kpi_thresholds' table.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void deleteServiceCommandArguments(int serviceId, Handle handle) throws ControlCenterException {
        ServiceDao dao = getDaoConnection(handle, ServiceDao.class);
        try {
            dao.deleteServiceCommandArguments(serviceId);
        } catch (Exception e) {
            LOGGER.error("Exception while deleting service command arguments from 'service_command_arguments' table.", e);
            throw new ControlCenterException("Exception while deleting service command arguments from 'service_command_arguments' table.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void deleteUserForensicNotificationMapping(int serviceId, Handle handle) throws ControlCenterException {
        ServiceDao dao = getDaoConnection(handle, ServiceDao.class);
        try {
            int rows = dao.deleteServiceUserForensicNotificationMapping(serviceId);
            if(rows > 0) {
                LOGGER.info("Mapping from user_forensic_notification_mapping has been deleted for serviceId [{}]",serviceId);
            }
        } catch (Exception e) {
            LOGGER.error("Exception while deleting service mapping from 'user_forensic_notification_mapping' table.", e);
            throw new ControlCenterException("Exception while deleting service mapping from 'user_forensic_notification_mapping' table.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void deleteServiceAgentModeConfiguration(int serviceId, Handle handle) throws ControlCenterException {
        ServiceDao dao = getDaoConnection(handle, ServiceDao.class);
        try {
            dao.deleteServiceAgentModeConfiguration(serviceId);
        } catch (Exception e) {
            LOGGER.error("Exception while deleting service agent mode configuration from 'agent_mode_configuration' table.", e);
            throw new ControlCenterException("Exception while deleting service agent mode configuration from 'agent_mode_configuration' table.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void deleteServiceMaintenanceMapping(int serviceId, Handle handle) throws ControlCenterException {
        ServiceDao dao = getDaoConnection(handle, ServiceDao.class);
        try {
            dao.deleteServiceMaintenanceMapping(serviceId);
        } catch (Exception e) {
            LOGGER.error("Exception while deleting service maintenance details from 'service_maintenance_mapping' table.", e);
            throw new ControlCenterException("Exception while deleting service maintenance details from 'service_maintenance_mapping' table.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void deletePluginKpiServiceMapping(int serviceId, Handle handle) throws ControlCenterException {
        ServiceDao dao = getDaoConnection(handle, ServiceDao.class);
        try {
            dao.deletePluginKpiServiceMapping(serviceId);
        } catch (Exception e) {
            LOGGER.error("Exception while deleting plugin KPI service details from 'plugin_kpi_service_mapping' table.", e);
            throw new ControlCenterException("Exception while deleting plugin KPI service details from 'plugin_kpi_service_mapping' table.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void insertServiceTransactionDetails(ServiceTransactionSettingBean serviceTransactionSettingBean, Handle handle) throws ControlCenterException {
        ServiceDao dao = getDaoConnection(handle, ServiceDao.class);
        try {
            dao.insertServiceTransactionDetails(serviceTransactionSettingBean);
        } catch (Exception e) {
            LOGGER.error("Exception while inserting service transaction details into 'transaction_settings' table.", e);
            throw new ControlCenterException("Exception while inserting service transaction details into 'transaction_settings' table.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

}
