package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.ViewApplicationServiceMappingBean;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.PluginKPIServiceMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.WhitelistDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.pojo.AllKpiList;
import com.appnomic.appsone.controlcenter.pojo.ApplicationWhitelist;
import com.appnomic.appsone.controlcenter.pojo.Controller;
import com.appnomic.appsone.controlcenter.pojo.ServiceWhitelist;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 10/03/2021
 */

public class WhitelistDataService extends AbstractDaoService<WhitelistDao> {

    private static final Logger LOGGER = LoggerFactory.getLogger(WhitelistDao.class);

    private static List<AllKpiList> allKPIs = MasterCache.getAllKpi();

    public List<ApplicationWhitelist> getWhitelist(int accountId) {
        List<Controller> whitelistedApplications = getWhitelistedEntries(accountId, false);
        List<ApplicationWhitelist> whiteList = new ArrayList<>();
        for (Controller application : whitelistedApplications) {
            List<ServiceWhitelist> linkedWhitelistedServices = getLinkedWhitelistedServices(accountId, application);
            ApplicationWhitelist linkedAppEntry = new ApplicationWhitelist();
            linkedAppEntry.setServiceWhitelist(linkedWhitelistedServices);
            linkedAppEntry.setApplicationName(application.getName());
            linkedAppEntry.setApplicationSuppressionInterval(application.getPluginSuppressionInterval());
            whiteList.add(linkedAppEntry);
        }
        return whiteList;
    }

    public int[] updateApplicationWhitelist(List<ApplicationWhitelist> whitelistBeans) throws ControlCenterException {
        WhitelistDao whitelistDao = getDaoConnection(null, WhitelistDao.class);
        try {
            return whitelistDao.updateApplicationWhitelist(whitelistBeans);
        } catch (Exception e) {
            LOGGER.error("Error occurred while updating whitelist", e);
            throw new ControlCenterException("Error occurred while updating whitelist");
        } finally {
            closeDaoConnection(null, whitelistDao);
        }
    }

    public int[] deleteApplicationWhitelist(List<ApplicationWhitelist> whitelistedEntries) throws ControlCenterException {
        WhitelistDao whitelistDao = getDaoConnection(null, WhitelistDao.class);
        try {
            return whitelistDao.deleteApplicationWhitelist(whitelistedEntries);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting application whitelist", e);
            throw new ControlCenterException("Error occurred while deleting application whitelist");
        } finally {
            closeDaoConnection(null, whitelistDao);
        }
    }

    public int[] updateServiceWhitelist(List<ServiceWhitelist> whitelistBeans) throws ControlCenterException {
        WhitelistDao whitelistDao = getDaoConnection(null, WhitelistDao.class);
        try {
            return whitelistDao.updateServiceWhitelist(whitelistBeans);
        } catch (Exception e) {
            LOGGER.error("Error occurred while updating whitelist", e);
            throw new ControlCenterException("Error occurred while updating whitelist");
        } finally {
            closeDaoConnection(null, whitelistDao);
        }
    }

    public int[] deleteServiceWhitelist(List<ServiceWhitelist> whitelistedEntries) throws ControlCenterException {
        WhitelistDao whitelistDao = getDaoConnection(null, WhitelistDao.class);
        try {
            return whitelistDao.deleteServiceWhitelist(whitelistedEntries);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting service whitelist", e);
            throw new ControlCenterException("Error occurred while deleting service whitelist");
        } finally {
            closeDaoConnection(null, whitelistDao);
        }
    }

    public int[] addKPIWhitelist(List<PluginKPIServiceMapping> kpiServiceList) throws ControlCenterException {
        WhitelistDao whitelistDao = getDaoConnection(null, WhitelistDao.class);
        try {
            return whitelistDao.addKPIWhitelist(kpiServiceList);
        } catch (Exception e) {
            LOGGER.error("Error occurred while adding kpi whitelist", e);
            throw new ControlCenterException("Error occurred while adding kpi whitelist");
        } finally {
            closeDaoConnection(null, whitelistDao);
        }
    }

    public PluginKPIServiceMapping getPluginKPIServiceMapping(Integer kpiId, Integer serviceId) throws ControlCenterException {
        WhitelistDao whitelistDao = getDaoConnection(null, WhitelistDao.class);
        try {
            return whitelistDao.getPluginKPIServiceMapping(kpiId,serviceId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while retrieving kpi service mapping", e);
            throw new ControlCenterException("Error occurred while retrieving kpi service mapping");
        } finally {
            closeDaoConnection(null, whitelistDao);
        }
    }

    public int[] updateKPIWhitelist(List<PluginKPIServiceMapping> kpiServiceList) throws ControlCenterException {
        WhitelistDao whitelistDao = getDaoConnection(null, WhitelistDao.class);
        try {
            return whitelistDao.updateKPIWhitelist(kpiServiceList);
        } catch (Exception e) {
            LOGGER.error("Error occurred while updating kpi whitelist", e);
            throw new ControlCenterException("Error occurred while updating kpi whitelist");
        } finally {
            closeDaoConnection(null, whitelistDao);
        }
    }

    public int[] deleteKPIWhitelist(List<PluginKPIServiceMapping> kpiServiceList) throws ControlCenterException {
        WhitelistDao whitelistDao = getDaoConnection(null, WhitelistDao.class);
        try {
            return whitelistDao.deleteKPIWhitelist(kpiServiceList);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting kpi whitelist", e);
            throw new ControlCenterException("Error occurred while deleting kpi whitelist");
        } finally {
            closeDaoConnection(null, whitelistDao);
        }
    }

    public boolean isWhitelistActive() throws ControlCenterException {
        WhitelistDao whitelistDao = getDaoConnection(null, WhitelistDao.class);
        try {
            return whitelistDao.isWhitelistActive();
        } catch (Exception e) {
            LOGGER.error("Error occurred while retrieving Whitelist Active settings", e);
            throw new ControlCenterException("Error occurred while retrieving Whitelist Active settings");
        } finally {
            closeDaoConnection(null, whitelistDao);
        }
    }

    public int updateWhitelistActive(boolean whitelistActive) throws ControlCenterException {
        WhitelistDao whitelistDao = getDaoConnection(null, WhitelistDao.class);
        try {
            return whitelistDao.updateWhitelistActive(whitelistActive);
        } catch (Exception e) {
            LOGGER.error("Error occurred while retrieving Whitelist Active settings", e);
            throw new ControlCenterException("Error occurred while retrieving Whitelist Active settings");
        } finally {
            closeDaoConnection(null, whitelistDao);
        }
    }

    public List<Controller> getWhitelistedEntries(int accountId, boolean isServices) {
        List<Controller> whitelistedControllers = MasterCache.getControllerList(accountId)
                .stream()
                .filter(ctrl -> (ctrl.isPluginWhitelisted()))
                .collect(Collectors.toList());
        if (isServices) {
            return whitelistedControllers.stream()
                    .filter(ctrl -> (ctrl.getControllerTypeId() == 192))
                    .collect(Collectors.toList());
        }
        return whitelistedControllers.stream()
                .filter(ctrl -> (ctrl.getControllerTypeId() == 191))
                .collect(Collectors.toList());
    }

    public List<ServiceWhitelist> getLinkedWhitelistedServices(int accountId, Controller application) {
        WhitelistDataService whitelistDataService = new WhitelistDataService();
        List<Controller> whitelistedServices = whitelistDataService.getWhitelistedEntries(accountId, true);
        List<ServiceWhitelist> linkedWhitelistedServices = new ArrayList<>();
        if (whitelistedServices != null && whitelistedServices.size() > 0) {
            List<ViewApplicationServiceMappingBean> linkedServices = new ControllerDataService().getServicesForApplication(accountId, application.getIdentifier());
            for (ViewApplicationServiceMappingBean linkedService : linkedServices) {
                Optional<Controller> linkedServiceWhitelisted = whitelistedServices.stream()
                        .filter(wh -> (wh.getName().equals(linkedService.getServiceName())))
                        .findFirst();
                if (linkedServiceWhitelisted.isPresent()) {
                    ServiceWhitelist serviceWhitelist = new ServiceWhitelist();
                    serviceWhitelist.setServiceName(linkedService.getServiceName());
                    serviceWhitelist.setServiceSuppressionInterval(linkedServiceWhitelisted.get().getPluginSuppressionInterval());
                    serviceWhitelist.setKpiWhitelist(getWhitelistedKPINames(linkedService.getServiceId()));
                    linkedWhitelistedServices.add(serviceWhitelist);
                }
            }
        }
        return linkedWhitelistedServices;
    }

    private List<String> getWhitelistedKPINames(Integer serviceId) {
        KPIDataService kpiDataService = new KPIDataService();
        List<String> whiteListedKPINames = new ArrayList<>();
        List<PluginKPIServiceMapping> kpiList = kpiDataService.getPluginKPIServiceMapping(serviceId, null);
        for (PluginKPIServiceMapping kpiServiceMapping : kpiList) {
            Optional<AllKpiList> kpiFound = allKPIs.stream()
                    .filter(k -> kpiServiceMapping.getKpiId() == k.getKpiId())
                    .findFirst();
            if (kpiFound.isPresent()) {
                whiteListedKPINames.add(kpiFound.get().getKpiName());
            }
        }
        return whiteListedKPINames;
    }

    public Integer getKpiId(String kpiName) {
        Optional<AllKpiList> kpiFound = allKPIs.stream()
                .filter(k -> kpiName.equalsIgnoreCase(k.getKpiName()))
                .findFirst();
        if (kpiFound.isPresent()) {
            return kpiFound.get().getKpiId();
        }

        return 0;
    }
}