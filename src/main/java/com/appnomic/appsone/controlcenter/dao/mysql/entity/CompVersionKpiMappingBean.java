package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.*;

import java.sql.Timestamp;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CompVersionKpiMappingBean {

    private int id;
    @NonNull
    private int kpiDetailsId;
    private int isCustom;
    private int doAnalytics;
    @NonNull
    private int commonVersionId;
    private Timestamp createdTime;
    @NonNull
    private Timestamp updatedTime;
    @NonNull
    private String userDetailsId;
    @NonNull
    private int defaultCollectionInterval;
    @NonNull
    private int status;
    @NonNull
    private int componentId;
    @NonNull
    private int componentTypeId;
    private int defaultOperationId;
    private double defaultThreshold;
    private int compInstanceId;
    private int importantId;
}
