package com.appnomic.appsone.controlcenter.dao.mysql.connectors;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.AppDynamicsKpi;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.ApplicationToKpiMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.connectors.AppDynamicsDataDao;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.connectors.AzureConnectorDataDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.connectors.AppDynamicFileHashList;
import com.appnomic.appsone.controlcenter.pojo.connectors.AppDynamicsApplication;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class AppDynamicsConnectorDataService {

    private static final Logger logger = LoggerFactory.getLogger(AzureConnectorDataService.class);

    public AppDynamicsConnectorDataService(){}

    public List<String> getAppDKpis()
    {
        AppDynamicsDataDao appDynamicsDataDao = getMsqlDataDao();
        try{
            return appDynamicsDataDao.getAppDynamicsKpis();
        }
        catch (Exception e)
        {
            logger.error("Error occurred while fetching kpi list", e);
        }
        finally {
            MySQLConnectionManager.getInstance().close(appDynamicsDataDao);
        }
        return new ArrayList<>();
    }

    public List<String> getHealKpis()
    {
        AppDynamicsDataDao appDynamicsDataDao = getMsqlDataDao();
        try {
            return appDynamicsDataDao.getHealKpis();
        } catch (Exception e) {
            logger.error("Error occurred while getting heal KPIs{}", (Object) e.getStackTrace());
        } finally {
            MySQLConnectionManager.getInstance().close(appDynamicsDataDao);
        }
        return new ArrayList<>();
    }

    public int[] addAppDynamicsKpi(List<AppDynamicsKpi> appDynamicsKpis)
    {
        AppDynamicsDataDao appDynamicsDataDao = getMsqlDataDao();
        try {
            return appDynamicsDataDao.addAppDynamicsKpis(appDynamicsKpis);
        } catch (Exception e) {
            logger.error("Error occurred while fetching component details compId{}", appDynamicsKpis, e);
        } finally {
            MySQLConnectionManager.getInstance().close(appDynamicsDataDao);
        }
        return new int[]{};
    }

    public void updateAppDynamicsKpi(List<AppDynamicsKpi> appDynamicsKpis)
    {
        AppDynamicsDataDao appDynamicsDataDao = getMsqlDataDao();
        try {
            appDynamicsDataDao.updateAppDynamicsKpis(appDynamicsKpis);
        } catch (Exception e) {
            logger.error("Error occurred while updating AppDynamics KPIs {}", appDynamicsKpis, e);
        } finally {
            MySQLConnectionManager.getInstance().close(appDynamicsDataDao);
        }
    }

    public List<Integer> getAppDynamicsKpiList()
    {
        AppDynamicsDataDao appDynamicsDataDao = getMsqlDataDao();
        try {
            return appDynamicsDataDao.getAppDynamicsKpisList();
        } catch (Exception e) {
            logger.error("Error occurred while fetching kpi ids list", e.getMessage());
        } finally {
            MySQLConnectionManager.getInstance().close(appDynamicsDataDao);
        }
        return new ArrayList<>();
    }

    public void addAppDynamicsApplications(List<AppDynamicsApplication> appDynamicsApplications)
    {
        AppDynamicsDataDao appDynamicsDataDao = getMsqlDataDao();
        try{
            appDynamicsDataDao.deleteAppDynamicsApplication();
            appDynamicsDataDao.addAppDynamicsApplications(appDynamicsApplications);
        } catch (Exception e)
        {
            logger.error("Error occured while adding AppDynamics applications. {}", appDynamicsApplications, e);
        } finally {
            MySQLConnectionManager.getInstance().close(appDynamicsDataDao);
        }
    }

    public void addAppDynamicsFileHashList(List<AppDynamicFileHashList> appDynamicFileHashLists)
    {
        AppDynamicsDataDao appDynamicsDataDao = getMsqlDataDao();
        try{
            appDynamicsDataDao.deleteAppDynamicsFileHashList();
            appDynamicsDataDao.addAppDynamicsFileHashList(appDynamicFileHashLists);
        } catch (Exception e)
        {
            logger.error("Error occured while adding AppDynamics File Hash List. {}", appDynamicFileHashLists, e);
        } finally {
            MySQLConnectionManager.getInstance().close(appDynamicsDataDao);
        }
    }

    public void addAppDynamicsApplicationKpiMapping(List<ApplicationToKpiMapping> applicationToKpiMappings)
    {
        AppDynamicsDataDao appDynamicsDataDao = getMsqlDataDao();
        try {
            appDynamicsDataDao.deleteAppDynamicsApplicationKpiMapping();
            appDynamicsDataDao.addAppDynamicsApplicationKpiMapping(applicationToKpiMappings);
        } catch (Exception e) {
            logger.error("Error occurred while adding AppDynamics applications to kpi mapping.{}", applicationToKpiMappings, e);
        } finally {
            MySQLConnectionManager.getInstance().close(appDynamicsDataDao);
        }
    }

    private AppDynamicsDataDao getMsqlDataDao(){
        return MySQLConnectionManager.getInstance().open(AppDynamicsDataDao.class);
    }
}
