package com.appnomic.appsone.controlcenter.dao.mysql.interfaces.connectors;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.AppDynamicsKpi;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.ApplicationToKpiMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.AzureKpi;
import com.appnomic.appsone.controlcenter.pojo.connectors.AppDynamicFileHashList;
import com.appnomic.appsone.controlcenter.pojo.connectors.AppDynamicsApplication;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface AppDynamicsDataDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into dataadapter_appdynamics.appdynamics_kpis(kpi_aggregator,kpi_string,kpi_type,aggregation_level)" +
            " values(:kpiAggregator,:kpiName,:kpiType,:aggregationLevel);")
    @GetGeneratedKeys
    int[] addAppDynamicsKpis(@BindBean List<AppDynamicsKpi> appDynamicsKpis);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update dataadapter_appdynamics.appdynamics_kpis " +
            "Set kpi_string = :kpiName , kpi_type = :kpiType, aggregation_level = :aggregationLevel " +
            " where kpi_aggregator = :kpiAggregator;")
    @GetGeneratedKeys
    void updateAppDynamicsKpis(@BindBean List<AppDynamicsKpi> appDynamicsKpis);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("Select id from dataadapter_appdynamics.appdynamics_kpis")
    List<Integer> getAppDynamicsKpisList();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("Select kpi_aggregator from dataadapter_appdynamics.appdynamics_kpis")
    List<String> getAppDynamicsKpis();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select kpi_string from dataadapter_appdynamics.heal_kpis")
    List<String> getHealKpis();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into dataadapter_appdynamics.appdynamics_application(id,application_id,application_name,organization,password, username, application_instance, node, log_agent)" +
            " values(:id,:applicationId,:applicationName,:organisation,:password,:username,:appInstance,:node,:logAgent);")
    @GetGeneratedKeys
    void addAppDynamicsApplications(@BindBean List<AppDynamicsApplication> appDynamicsApplications);

    @SqlUpdate("delete from dataadapter_appdynamics.appdynamics_application")
    void deleteAppDynamicsApplication();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into dataadapter_appdynamics.appdynamics_application_kpi_map (application_id, kpi_id) " +
            "values (:application_id, :kpi_id);")
    @GetGeneratedKeys
    void addAppDynamicsApplicationKpiMapping(@BindBean List<ApplicationToKpiMapping> applicationToKpiMappings);

    @SqlUpdate("delete from dataadapter_appdynamics.appdynamics_application_kpi_map")
    void deleteAppDynamicsApplicationKpiMapping();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into dataadapter_appdynamics.file_hash_list (file_hash, file_path, timestamp) " +
            "values (:fileHash, :filePath, :timestamp);")
    @GetGeneratedKeys
    void addAppDynamicsFileHashList(@BindBean List<AppDynamicFileHashList> appDynamicFileHashLists);

    @SqlUpdate("delete from dataadapter_appdynamics.file_hash_list")
    void deleteAppDynamicsFileHashList();

}
