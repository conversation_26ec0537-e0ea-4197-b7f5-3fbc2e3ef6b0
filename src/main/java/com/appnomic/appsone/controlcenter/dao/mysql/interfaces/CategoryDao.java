package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.CategoryDetailBean;
import com.heal.configuration.pojos.Category;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

@UseStringTemplate3StatementLocator
public interface CategoryDao {
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, account_id accountId, created_time createdTime, updated_time updatedTime, " +
            "user_details_id userDetailsId, identifier, status, is_workload isWorkLoad " +
            "from mst_category_details where status = 1")
    List<CategoryDetailBean> getCategories();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, description, account_id accountId, created_time createdTime, updated_time updatedTime, " +
            "user_details_id userDetailsId, identifier, status, is_workload isWorkLoad, is_informative isInformative, " +
            "is_custom isCustom from mst_category_details where account_id in (1, :account)")
    List<CategoryDetailBean> getCategoriesForAccount(@Bind("account") Integer account);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(id) from mst_category_details where account_id in (1, :account)")
    int getCategoryCountForAccount(@Bind("account") Integer account);

    @SqlUpdate("INSERT INTO mst_category_details (name, account_id, created_time, updated_time," +
            "user_details_id, identifier, status, is_workload, is_informative, is_custom, description) " +
            "VALUES ( :name, :accountId, :createdTime, :updatedTime, :userDetailsId, :identifier, :status, :isWorkLoad, " +
            ":isInformative, :isCustom, :description)")
    @GetGeneratedKeys
    int addCategory(@BindBean CategoryDetailBean categoryBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, description, account_id accountId, created_time createdTime, updated_time updatedTime, " +
            "user_details_id userDetailsId, identifier, status, is_workload isWorkLoad, is_informative isInformative," +
            "is_custom isCustom  from mst_category_details where identifier = :identifier")
    CategoryDetailBean getCategoryUsingIdentifier(@Bind("identifier") String identifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct mcd.name, vkcd.category_id id, vkcd.is_workload isWorkLoad from view_kpi_category_details vkcd, mst_category_details mcd " +
            "where kpi_type_id=29 and mcd.id=vkcd.category_id and mcd.account_id in (1, :account)")
    List<CategoryDetailBean> getAvailabilityKpiCategories(@Bind("account") Integer account);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct mcd.name, mcd.id, mcd.identifier identifier, mcd.is_workload isWorkload " +
            "from view_kpi_category_details vkcd right outer join mst_category_details mcd on mcd.id = vkcd.category_id " +
            "where (vkcd.kpi_type_id =:kpiTypeId) and mcd.account_id in (1, :account)")
    List<Category> getCategoriesForKpiType(@Bind("account") Integer account, @Bind("kpiTypeId") Integer kpiTypeId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct mcd.name, mcd.id, mcd.identifier identifier, mcd.is_workload isWorkLoad from view_kpi_category_details vkcd right " +
            "outer join mst_category_details mcd on mcd.id = vkcd.category_id where (vkcd.kpi_type_id =:kpiTypeId or vkcd.kpi_type_id is NULL) " +
            "and mcd.account_id in (1, :account) and mcd.id = :categoryId")
    CategoryDetailBean getCategoryForKpiTypeAndCategoryId(@Bind("account") Integer account, @Bind("kpiTypeId") Integer kpiTypeId, @Bind("categoryId") Integer categoryId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, identifier from mst_category_details where name = :categoryName and account_id in (1, :accountId)")
    CategoryDetailBean checkForCategoryUsingName(@Bind("categoryName") String categoryName, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(kpi_id) count from view_kpi_category_details where category_id = :categoryId group by category_id")
    int getKpiCountForCategory(@Bind("categoryId") Integer categoryId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select kpi_id from view_kpi_category_details where category_id = :categoryId")
    List<Integer> getKpiIdsForCategory(@Bind("categoryId") Integer categoryId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("update mst_category_details set name = :name, updated_time = :updatedTime, status = :status, " +
            "is_workload = :isWorkLoad, is_informative = :isInformative, description = :description where id = :id")
    void updateCategory(@BindBean CategoryDetailBean categoryBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from mst_category_details where id = :categoryId")
    int deleteCategoryById(@Bind("categoryId") Integer categoryId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(*) from mst_category_details where id = :categoryId and account_id = :accountId")
    int getCategoryById(@Bind("categoryId") int categoryId, @Bind("accountId") int accountId);
}
