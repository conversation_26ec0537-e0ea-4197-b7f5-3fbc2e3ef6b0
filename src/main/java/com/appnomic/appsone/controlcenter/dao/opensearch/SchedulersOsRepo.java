package com.appnomic.appsone.controlcenter.dao.opensearch;

import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.manager.OpenSearchConnectionManager;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.opeasearchquery.helper.OpenSearchQueryHelper;
import com.appnomic.appsone.opeasearchquery.queryopts.Documents;
import com.appnomic.appsone.opeasearchquery.queryopts.QueryOptions;
import com.appnomic.appsone.opeasearchquery.results.NameValuePair;
import com.appnomic.appsone.opeasearchquery.results.RawDocumentResults;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.enums.JobStatus;
import com.heal.configuration.pojos.DateWeekBean;
import com.heal.configuration.pojos.opensearch.TriggeredJobData;
import com.heal.configuration.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch._types.Result;
import org.opensearch.client.opensearch.core.IndexResponse;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class SchedulersOsRepo {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final String dataTimezone = ConfProperties.getString(Constants.DATA_TIMEZONE, Constants.DATA_TIMEZONE_DEFAULT_TIMEZONE);

    public void insertRollupJobDetails(TriggeredJobData data) {
        List<DateWeekBean> osAuditDateIndex = DateHelper.getDatesWeeksAsString(System.currentTimeMillis(), System.currentTimeMillis() + 1000, dataTimezone);
        String indexName = String.format(Constants.INDEX_PREFIX_HEAL_SCHEDULED_JOB + "_%s_%s", data.getAccountIdentifier().toLowerCase(), osAuditDateIndex.get(0).getIndexYearWeek());

        try {
            IndexResponse resp = new OpenSearchRepo<TriggeredJobData>().insertIndex(indexName, data, data.getJobId(), data.getAccountIdentifier(), Constants.INDEX_PREFIX_HEAL_SCHEDULED_JOB);
            if (resp == null) {
                throw new ControlCenterException("Error while inserting agent health details");
            }
            if (resp.result() == Result.Created) {
                log.debug("Successfully added document to [{}]", indexName);
            }
        } catch (IOException | ControlCenterException e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error while writing triggered rollup job details to OpenSearch. Details: ", e);
        }
    }

    public Set<TriggeredJobData> getTriggeredJobs(String accountIdentifier, String status, long fromTime, long toTime) {
        String indexName = Constants.INDEX_PREFIX_HEAL_SCHEDULED_JOB + "_" + accountIdentifier.toLowerCase();

        try {
            OpenSearchClient openSearchClient  = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, Constants.INDEX_PREFIX_HEAL_SCHEDULED_JOB);
            if (openSearchClient == null) {
                return Collections.emptySet();
            }

            List<String> dateList = DateHelper.getWeeksAsString(fromTime, toTime);
            List<String> indexNames = new ArrayList<>();
            dateList.forEach(date -> indexNames.add(indexName + "_" + date));

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            if (JobStatus.ALL.name().equalsIgnoreCase(JobStatus.getName(status))) {
                matchAnyFields.add(new NameValuePair("status", JobStatus.FAILED.name()));
                matchAnyFields.add(new NameValuePair("status", JobStatus.STOPPED.name()));
            } else {
                matchAnyFields.add(new NameValuePair("status", status));
            }

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("triggerTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .matchAnyOfFields(Optional.of(new ArrayList<>() {{
                        add(matchAnyFields);
                    }}))
                    .isTimeSeriesData(false)
                    .fetchAllRecords(true)
                    .build();

            log.debug("OS query for fetching triggered job data: {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, openSearchClient);

            if (rawDocuments != null && rawDocuments.getDocuments() != null && !rawDocuments.getDocuments().isEmpty()) {
                return OBJECT_MAPPER.readValue(rawDocuments.getDocuments().stream().map(Documents::getSource).collect(Collectors.toList()).toString(), new TypeReference<>() {
                });
            }
        } catch (Exception e) {
            log.error("Error in getting doc from index {} : ", indexName, e);
        }

        return Collections.emptySet();
    }

    public Map<Long, Integer> getReTriggeredActionCount(String accountIdentifier, String status, List<Long> schedulerTriggerTimes) {
        String indexName = Constants.INDEX_PREFIX_HEAL_SCHEDULED_JOB + "_" + accountIdentifier.toLowerCase() + "_*";

        try {
            OpenSearchClient openSearchClient  = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, Constants.INDEX_PREFIX_HEAL_SCHEDULED_JOB);
            if (openSearchClient == null) {
                return Collections.emptyMap();
            }

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            schedulerTriggerTimes.forEach(t -> matchAnyFields.add(new NameValuePair("schedulerTriggerTime", String.valueOf(t))));

            List<String> columns = new ArrayList<>();
            columns.add("schedulerTriggerTime");
            columns.add("status");

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(Collections.singletonList(indexName))
                    .matchAnyOfFields(Optional.of(new ArrayList<>() {{
                        add(matchAnyFields);
                    }}))
                    .groupByColumns(Optional.of(columns))
                    .isTimeSeriesData(false)
                    .fetchAllRecords(true)
                    .build();

            log.debug("OS query for fetching triggered job data: {}", queryOptions);

            Map<Long, Integer> actionCount = new HashMap<>();

            TabularResults tabularResults = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, openSearchClient);

            if (tabularResults != null && tabularResults.getRowResults() != null && !tabularResults.getRowResults().isEmpty()) {
                for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
                    long schedulerTriggerTime = 0L;
                    int count = 0;
                    for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                        if (resultRowColumn.getColumnName().equalsIgnoreCase("schedulerTriggerTime")) {
                            schedulerTriggerTime = Long.parseLong(resultRowColumn.getColumnValue());
                        }
                        if (resultRowColumn.getColumnName().equalsIgnoreCase("status") && resultRowColumn.getColumnValue().equalsIgnoreCase(status)) {
                            count++;
                        }
                    }

                    actionCount.put(schedulerTriggerTime, count);
                }
            }

            return actionCount;
        } catch (Exception e) {
            log.error("Error in getting doc from index {} : ", indexName, e);
        }

        return Collections.emptyMap();
    }
}
