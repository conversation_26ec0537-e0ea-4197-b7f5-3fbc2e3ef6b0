package com.appnomic.appsone.controlcenter.dao.opensearch;

import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.manager.OpenSearchConnectionManager;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch._types.FieldValue;
import org.opensearch.client.opensearch._types.SortOrder;
import org.opensearch.client.opensearch._types.aggregations.Aggregation;
import org.opensearch.client.opensearch._types.query_dsl.Query;
import org.opensearch.client.opensearch.core.*;
import org.opensearch.client.opensearch.indices.ExistsRequest;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR> - 06-01-2022
 */
@Slf4j
public class OpenSearchRepo<T> {
    public static SearchResponse<Object> searchQueryByQueryBuilder(Query qb, String indexName, String accountIdentifier, String index) throws IOException {
        OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, index);
        if (openSearchClient == null) {
            return null;
        }

        SearchRequest searchRequest = new SearchRequest.Builder()
                .index(indexName)
                .query(qb)
                .build();

        return openSearchClient.search(searchRequest, Object.class);
    }

    public IndexResponse insertIndex(String indexName, T mapObject, String docId, String accountIdentifier, String index) throws IOException {
        OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, index);
        if (openSearchClient == null) {
            return null;
        }
        IndexRequest.Builder<T> indexRequestBuilder = new IndexRequest.Builder<T>()
                .index(indexName)
                .document(mapObject);
        if (docId != null && !docId.trim().isEmpty()) {
            indexRequestBuilder.id(docId);
        }
        return openSearchClient.index(indexRequestBuilder.build());
    }

    public static CountResponse getCountByQueryBuilder(String indexName, Query qb, String accountIdentifier, String index) throws IOException {
        OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, index);
        if (openSearchClient == null) {
            return null;
        }

        CountRequest countRequest = new CountRequest.Builder()
                .index(indexName)
                .query(qb)
                .build();

        return openSearchClient.count(countRequest);
    }

    public static SearchResponse<Object> getLimitedDocByQueryBuilder(Query qb, String indexName, int limit, String accountIdentifier, String index) throws IOException {
        OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, index);
        if (openSearchClient == null) {
            return null;
        }
        SearchRequest searchRequest = new SearchRequest.Builder()
                .index(indexName)
                .query(qb)
                .size(limit)
                .build();

        return openSearchClient.search(searchRequest, Object.class);
    }

    public static DeleteResponse deleteDocById(String id, String indexName, String accountIdentifier, String index) throws IOException {
        OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, index);
        if (openSearchClient == null) {
            return null;
        }

        DeleteRequest deleteRequest = new DeleteRequest.Builder()
                .index(indexName)
                .id(id)
                .build();

        return openSearchClient.delete(deleteRequest);
    }

    public static DeleteByQueryResponse deleteDocByInstanceIdentifier(String indexName, String instanceIdentifier, String osIdentifier, String accountIdentifier, String index) throws IOException, ControlCenterException {
        OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, index);
        if (openSearchClient == null) {
            log.error("Could not find OpenSearch client details for indexName: {}, instanceIdentifier: {}, osIdentifier: {}", indexName, instanceIdentifier, osIdentifier);
            return null;
        }

        boolean exists = openSearchClient.indices().exists(e -> e.index(indexName)).value();
        if (!exists) {
            log.warn("Could not delete the data from OpenSearch as index is not present for indexName:{}, instanceIdentifier:{}, osIdentifier:{}", indexName, instanceIdentifier, osIdentifier);
            return null;
        }

        DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest.Builder()
                .index(indexName)
                .query(Query.of(c -> c.term(d -> d.field(osIdentifier).value(FieldValue.of(instanceIdentifier)))))
                .build();

        return openSearchClient.deleteByQuery(deleteByQueryRequest);
    }

    public UpdateResponse<Object> updateDocById(String indexName, String docIdHex, T updateField, String accountIdentifier, String index) throws IOException {
        OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, index);
        if (openSearchClient == null) {
            return null;
        }
        UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<>()
                .index(indexName)
                .id(docIdHex)
                .doc(updateField)
                .build();

        return openSearchClient.update(updateRequest, Object.class);
    }

    public static SearchResponse<Object> getLimitedSortedDocument(Query qb, String indexName, String fieldName, boolean isDesc, int limit, String accountIdentifier, String index) throws IOException {
        OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, index);
        if (openSearchClient == null) {
            return null;
        }

        SearchRequest searchRequest = new SearchRequest.Builder()
                .index(indexName)
                .query(qb)
                .size(limit)
                .sort(c -> c.field(f -> f.field(fieldName).order(isDesc ? SortOrder.Desc : SortOrder.Asc)))
                .build();

        return openSearchClient.search(searchRequest, Object.class);
    }

    public static SearchResponse<Object> getDistinctFieldsAndCount(Query qb, Map<String, Aggregation> termAggs, String indexName, String accountIdentifier, String index) throws IOException {
        OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, index);
        if (openSearchClient == null) {
            return null;
        }

        SearchRequest searchRequest = new SearchRequest.Builder()
                .index(indexName)
                .query(qb)
                .aggregations(termAggs)
                .build();

        return openSearchClient.search(searchRequest, Object.class);
    }

    public static boolean indexExist(String indexName, String accountIdentifier, String index) {
        OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, index);
        if (openSearchClient == null) {
            return false;
        }

        ExistsRequest request = ExistsRequest.of(builder -> builder.index(indexName));

        try {
            return openSearchClient.indices().exists(request).value();
        } catch (IOException e) {
            log.error("Error in checking if index exists: {}", indexName, e);
            return false;
        }
    }
}
