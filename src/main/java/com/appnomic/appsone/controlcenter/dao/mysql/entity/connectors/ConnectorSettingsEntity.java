package com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ConnectorSettingsEntity {
    private int id;
    private String chainName;
    private int sleepInterval;
    private int reloadInterval;
    private int processorThreadPoolSize;
    private int loaderThreadPoolSize;
    private int pullHistoricData;
    private long historicStartDate;
    private long historicEndDate;
    private int historicInterval;
    private long testStartDate;
    private long testEndDate;
    private int addSysLoader;
    private int disableChain;
    private int disableChainReload;
    private String backPressureStrategy;
    private int backPressureMaxSize;
    private int deltaInMinutes;
    private int extractorId;
    private String preProcessor;
}