package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.CompInstanceKpiGroupDetailsBean;
import com.appnomic.appsone.controlcenter.beans.KpiMaintenanceStatusBean;
import com.appnomic.appsone.controlcenter.beans.MasterKpiGroupBean;
import com.appnomic.appsone.controlcenter.dao.mysql.ComponentBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.*;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.TagMappingDetails;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;
import org.skife.jdbi.v2.unstable.BindIn;

import java.util.List;
@UseStringTemplate3StatementLocator
public interface KPIDataDao {
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT distinct kvm.mst_kpi_details_id id, mk.name name,kvm.mst_component_id componentId,mk.kpi_type_id typeId," +
            "mk.cluster_operation clusterOperation, mk.measure_units measureUnits,mk.data_type dataType " +
            "FROM mst_component_version_kpi_mapping kvm join mst_kpi_details mk " +
            "on kvm.mst_kpi_details_id = mk.id " +
            "where kvm.status = 1   and kvm.mst_common_version_id = :commonVersionId " +
            "and kvm.mst_component_id = :componentId ")
    List<KpiDetailsBean> getKpiList(@Bind("commonVersionId") Integer commonVersionId, @Bind("componentId") Integer componentId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id id, kpi_id kpiId, service_id serviceId, plugin_whitelist_status pluginWhitelistStatus, plugin_supr_interval pluginSuprInterval FROM plugin_kpi_service_mapping where plugin_whitelist_status = true and service_id = :svcId")
    List<PluginKPIServiceMapping> getPluginKPIServiceMapping(@Bind("svcId") Integer svcId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("insert into mst_kpi_group (name, description, created_time, updated_time, account_id," +
            "user_details_id, kpi_type_id, discovery, regex, status, is_custom, identifier) " +
            "values (:name, :description, :createdTime, :updatedTime, :accountId," +
            ":userDetailsId, :kpiTypeId, :discovery, :regex, :status, :isCustom, :identifier)")
    @GetGeneratedKeys
    int createGroupKpi(@BindBean MasterKpiGroupBean groupKpiBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("insert into mst_kpi_details (name, description, data_type, value_type, is_custom, is_computed, status," +
            "kpi_type_id, measure_units, cluster_operation, created_time, updated_time, user_details_id, " +
            "account_id,  kpi_group_id, identifier, rollup_operation, cluster_aggregation_type, instance_aggregation_type, " +
            "cron_expression, reset_delta_value, delta_per_sec)" +
            "values (:name, :description, :dataType, :valueType, :isCustom, :isComputed, :status, :kpiTypeId, :measureUnits, " +
            ":clusterOperation, :createdTime, :updatedTime, :userId, :accountId, :groupKpiId," +
            " :identifier, :rollupOperation, :clusterAggregation, :instanceAggregation, :cronExpression, :resetDeltaValue, :deltaPerSec)")
    @GetGeneratedKeys
    int createKpi(@BindBean KpiBean kpiBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, description, created_time createdTime, updated_time updatedTime, account_id accountId, " +
            "user_details_id userDetailsId, kpi_type_id kpiTypeId, discovery, regex, status, is_custom isCustom, identifier identifier " +
            "from mst_kpi_group where identifier = :identifier")
    List<MasterKpiGroupBean> checkForGroupKpiUsingIdentifier(@Bind("identifier") String identifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(*) from mst_kpi_details where name = :kpiName and account_id in (1, :accountId)")
    int checkForKpiUsingName(@Bind("kpiName") String kpiName, @Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(*) from mst_kpi_details where identifier = :kpiIdentifier")
    int checkForKpiUsingIdentifier(@Bind("kpiIdentifier") String kpiIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from mst_kpi_details where identifier = :kpiIdentifier")
    int checkForKpiIdUsingIdentifier(@Bind("kpiIdentifier") String kpiIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select comp_instance_id compInstanceId, mst_kpi_group_id kpiGroupId, mst_kpi_details_id kpiId, attribute_value attributeValue " +
            "from comp_instance_kpi_group_details where comp_instance_id = :instanceId")
    List<InstanceKpiAttributeThresholdBean> checkGroupKpiCompInstanceMapping(@Bind("instanceId") int instanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select comp_instance_id compInstanceId, mst_kpi_group_id kpiGroupId, mst_kpi_details_id kpiId, attribute_value attributeValue " +
            "from comp_instance_kpi_group_details where comp_instance_id = :instanceId")
    List<InstKpiAttrPersistenceSuppressionBean> fetchGroupKpiCompInstanceMapping(@Bind("instanceId") int instanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select comp_instance_id compInstanceId, mst_kpi_details_id kpiId from comp_instance_kpi_details where comp_instance_id=:instanceId")
    List<InstanceKpiAttributeThresholdBean> checkKpiCompInstanceMapping(@Bind("instanceId") int instanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select comp_instance_id compInstanceId, mst_kpi_details_id kpiId " +
            "from comp_instance_kpi_details where comp_instance_id = :instanceId ")
    List<InstKpiAttrPersistenceSuppressionBean> fetchKpiCompInstanceMapping(@Bind("instanceId") int instanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into comp_instance_kpi_threshold_details (comp_instance_id, kpi_id, operation_id, min_threshold, max_threshold, kpi_group_id, account_id, status," +
            "attribute_value, severity, user_details_id, created_time, updated_time, start_time) values(:compInstanceId, :kpiId, :operationId, :minThreshold, :maxThreshold, " +
            ":kpiGroupId, :accountId, :status, :attributeValue, :severity, :userDetailsId, :createdTime, :updatedTime, :startTime)")
    int[] addInstanceKpiAttributeLevelThresholds(@BindBean List<InstanceKpiAttributeThresholdBean> thresholdBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update comp_instance_kpi_threshold_details set operation_id=ifnull(:operationId, operation_id), min_threshold=ifnull(:minThreshold,min_threshold), " +
            "max_threshold=ifnull(:maxThreshold, max_threshold), status=:status, severity=:severity, updated_time=:updatedTime, start_time = :startTime where comp_instance_id=:compInstanceId " +
            "and kpi_group_id=:kpiGroupId and kpi_id=:kpiId and attribute_value=:attributeValue")
    int[] updateInstanceKpiAttributeLevelThresholds(@BindBean List<InstanceKpiAttributeThresholdBean> thresholdBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update comp_instance_kpi_threshold_details set status=:status, severity=:severity, updated_time=:updatedTime " +
            "where comp_instance_id=:compInstanceId and kpi_group_id=:kpiGroupId " +
            "and kpi_id=:kpiId and attribute_value=:attributeValue")
    int[] updateInstanceKpiAttributeLevelThresholdSeverityAndGenAnomaly(@BindBean List<InstanceKpiAttributeThresholdBean> thresholdBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("delete from comp_instance_kpi_threshold_details where comp_instance_id=:compInstanceId and kpi_id=:kpiId and kpi_group_id=:kpiGroupId " +
            "and account_id=:accountId and attribute_value=:attributeValue")
    int[] deleteInstanceKpiAttributeLevelThresholds(@BindBean List<InstanceKpiAttributeThresholdBean> thresholdBeans);
    
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select comp_instance_id compInstanceId, kpi_id kpiId, operation_id operationId, min_threshold minThreshold, max_threshold maxThreshold, " +
            "attribute_value attributeValue, account_id accountId, kpi_group_id kpiGroupId, status, severity " +
            "from comp_instance_kpi_threshold_details where comp_instance_id=:instanceId ")
    List<InstanceKpiAttributeThresholdBean> fetchCompInstanceKpiAttrThresholds(@Bind("instanceId") int instanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into comp_instance_kpi_configuration (comp_instance_id, kpi_id, kpi_group_id, account_id, " +
            "attribute_value, user_details_id, created_time, updated_time, sor_persistence, sor_suppression) values(:compInstanceId, :kpiId, " +
            ":kpiGroupId, :accountId, :attributeValue, :userDetailsId, :createdTime, :updatedTime, :persistence, :suppression)")
    int[] addInstanceKpiAttributeLevelPersistSuppress(@BindBean List<InstKpiAttrPersistenceSuppressionBean> thresholdBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("delete from comp_instance_kpi_configuration where comp_instance_id=:compInstanceId and kpi_id=:kpiId and kpi_group_id=:kpiGroupId " +
            "and account_id=:accountId and attribute_value=:attributeValue")
    int[] deleteInstanceKpiAttributeLevelPersistSuppress(@BindBean List<InstKpiAttrPersistenceSuppressionBean> thresholdBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from comp_instance_kpi_configuration where comp_instance_id=:compInstanceId and kpi_group_id=:kpiGroupId " +
            "and account_id=:accountId and attribute_value=:attributeValue")
    void deleteInstanceKpiAttributeLevelPersistSuppressByGroupId(@BindBean InstKpiAttrPersistenceSuppressionBean thresholdBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update comp_instance_kpi_configuration set sor_persistence=ifnull(:persistence, sor_persistence), sor_suppression=ifnull(:suppression, sor_suppression), " +
            "updated_time=:updatedTime, user_details_id=:userDetailsId where comp_instance_id=:compInstanceId and kpi_group_id=:kpiGroupId " +
            "and kpi_id=:kpiId and attribute_value=:attributeValue")
    int[] updateInstanceKpiAttributePersistenceSuppression(@BindBean List<InstKpiAttrPersistenceSuppressionBean> thresholdBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select sor_persistence persistence, sor_suppression suppression, exclude_maintenance isMaintenanceExcluded, kpi_id kpiId, comp_instance_id compInstanceId, account_id accountId, attribute_value attributeValue, " +
            "kpi_group_id kpiGroupId from comp_instance_kpi_configuration where comp_instance_id=:compInstanceId and account_id=:accountId")
    List<InstKpiAttrPersistenceSuppressionBean> fetchCompInstanceKpiPerSupValuesForCompInstance(@Bind("compInstanceId") int compInstanceId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select sor_persistence persistence, sor_suppression suppression, exclude_maintenance isMaintenanceExcluded, kpi_id kpiId, comp_instance_id compInstanceId, account_id accountId, attribute_value attributeValue, " +
            "kpi_group_id kpiGroupId from comp_instance_kpi_configuration where comp_instance_id=:compInstanceId and account_id=:accountId and kpi_id=:kpiId")
    List<InstKpiAttrPersistenceSuppressionBean> fetchCompInstanceKpiPerSupValuesForCompInstanceKpi(@Bind("compInstanceId") int compInstanceId,
                                                                                                   @Bind("kpiId") int kpiId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select comp_instance_id compInstanceId, mst_kpi_details_id mstKpiDetailsId from comp_instance_kpi_details " +
            "where comp_instance_id=:compInstanceId and mst_kpi_details_id=:mstKpiDetailsId")
    List<CompInstanceKpiGroupDetailsBean> getNonGroupKpiListForCompInstance(@Bind("compInstanceId") int instanceId, @Bind("mstKpiDetailsId") int kpiId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select attribute_value attributeValue, comp_instance_id compInstanceId, mst_kpi_details_id mstKpiDetailsId, " +
            "mst_kpi_group_id mstKpiGroupId, mst_producer_kpi_mapping_id mstProducerKpiMappingId, collection_interval collectionInterval, " +
            "kpi_group_name kpiGroupName, mst_producer_id mstProducerId, notification " +
            "from comp_instance_kpi_group_details where comp_instance_id = :instanceId and mst_kpi_group_id=:groupKpiId")
    List<CompInstanceKpiGroupDetailsBean> getGroupKpiListForCompInstance(@Bind("instanceId") int instanceId, @Bind("groupKpiId") int groupKpiId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from comp_instance_kpi_threshold_details where comp_instance_id = :instanceId")
    void deleteCompInstanceThresholdDetails(@Bind("instanceId") int instanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT distinct mk.id id, mk.name name, mk.kpi_type_id kpiTypeId, mk.cluster_operation clusterOperation, mk.measure_units measureUnits, mk.data_type dataType, " +
            "mk.status status, mk.kpi_group_id groupKpiId, mk.value_type valueType, mk.rollup_operation rollupOperation, mk.cluster_aggregation_type clusterAggregation, " +
            "mk.instance_aggregation_type instanceAggregation, mk.is_computed isComputed, mk.is_custom isCustom, mk.description description, " +
            "kvm.mst_component_id componentId, kvm.mst_component_type_id componentTypeId, " +
            "kvm.mst_common_version_id commonVersionId, kvm.default_collection_interval collectionIntervalSeconds, kvm.do_analytics availableForAnalytics " +
            "FROM mst_component_version_kpi_mapping kvm join mst_kpi_details mk " +
            "on kvm.mst_kpi_details_id = mk.id where mk.id=:id and mk.account_id in (1, :accountId)")
    KpiBean fetchKpiUsingKpiId(@Bind("id") int id, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select kpi_id kpiId, category_id categoryId, category_identifier categoryIdentifier, name categoryName, is_workload workLoad " +
            "from view_kpi_category_details where kpi_id = :kpiId")
    KpiCategoryMapping getCategoryDetailsForKpi(@Bind("kpiId") Integer kpiId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(*) from mst_kpi_group where name = :kpiName and account_id in (1, :accountId)")
    int checkForGroupKpiUsingName(@Bind("kpiName") String kpiName, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("insert into mst_computed_kpi_details (mst_kpi_details_id, formula, display_formula, created_time, updated_time, user_details_id, account_id)" +
            "values (:kpiDetailsId, :formula, :displayFormula, :createdTime, :updatedTime, :userDetailsId, :accountId)")
    @GetGeneratedKeys
    int addComputedKpi(@BindBean ComputedKpiBean computedKpiDetails);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into mst_computed_kpi_mappings (mst_computed_kpi_details_id, computed_kpi_id, base_kpi_id, created_time, updated_time, user_details_id, account_id)" +
            "values (:computedKpiDetailsId, :computedKpiId, :baseKpiId, :createdTime, :updatedTime, :userDetailsId, :accountId)")
    @GetGeneratedKeys
    int[] mapComputedKpiToMstKpi(@BindBean List<ComputedKpiToKpiMapping> computedKpiDetails);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into mst_computed_kpi_mappings (mst_computed_kpi_details_id, computed_kpi_id, base_kpi_id, created_time, updated_time, user_details_id, account_id)" +
            "values (:computedKpiDetailsId, :computedKpiId, :baseKpiId, :createdTime, :updatedTime, :userDetailsId, :accountId)")
    @GetGeneratedKeys
    int[] mapExistingComputedKpiToMstKpi(@BindBean List<ComputedKpiToKpiMapping> computedKpiDetails);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from mst_computed_kpi_details where mst_kpi_details_id=:computedKpiId")
    int getComputedDetailsId(@Bind("computedKpiId") int computedKpiId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(1) from mst_kpi_details where account_id in (1, :accountId)")
    int getKpiCount(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update mst_kpi_details set description = ifnull(:description, description), name = ifnull(:name, name), updated_time = :updatedTime, " +
            "status = :status, value_type = ifnull(:valueType, value_type), data_type = ifnull(:dataType, data_type), " +
            "measure_units = ifnull(:measureUnits, measure_units), cluster_operation = ifnull(:clusterOperation, cluster_operation), " +
            "rollup_operation = ifnull(:rollupOperation, rollup_operation), cluster_aggregation_type = ifnull(:clusterAggregation, cluster_aggregation_type), " +
            "instance_aggregation_type = ifnull(:instanceAggregation, instance_aggregation_type), cron_expression =ifnull(:cronExpression, cron_expression)" +
            " where id = :id and account_id = :accountId and is_custom = 1")
    int[] updateKpiDetails(@BindBean List<KpiBean> kpiBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update tag_mapping set tag_key = :tagKey, tag_value = :tagValue, updated_time = :updatedTime, " +
            "user_details_id = :userDetailsId where tag_id = :tagId and object_id = :objectId and " +
            "object_ref_table = 'mst_kpi_details' and account_id = :accountId")
    int[] updateKpiCategoryMapping(@BindBean List<TagMappingDetails> kpiBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update mst_component_version_kpi_mapping set default_collection_interval = :defaultCollectionInterval, " +
            "updated_time = :updatedTime, user_details_id = :userDetailsId where mst_kpi_details_id = :kpiDetailsId")
    int[] updateDefaultCollectionIntervalForKPI(@BindBean List<CompVersionKpiMappingBean> kpiBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select base_kpi_id kpiId from mst_computed_kpi_mappings where computed_kpi_id=:computedKpiId and account_id in (1, :accountId)")
    List<Integer> getComputedKpiMapping(@Bind("computedKpiId") int computedKpiId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from mst_computed_kpi_mappings where computed_kpi_id=:computedKpiId and account_id=:accountId")
    int deleteComputedKpiMapping(@Bind("computedKpiId") int computedKpiId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update comp_instance_kpi_group_details set status=:status where mst_kpi_details_id=:id")
    int[] statusChangeInstanceLevelGroupKpi(@BindBean List<KpiBean> kpiBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update comp_instance_kpi_details set status=:status where mst_kpi_details_id=:id")
    int[] statusChangeInstanceLevelNonGroupKpi(@BindBean List<KpiBean> kpiBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select formula, display_formula displayFormula from mst_computed_kpi_details where mst_kpi_details_id=:computedKpiId and account_id in (1, :accountId)")
    ComputedKpiBean fetchComputedKpiFormula(@Bind("computedKpiId") int computedKpiId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update mst_computed_kpi_details set formula=:formula, display_formula=:displayFormula, updated_time=:updatedTime, user_details_id=:userDetailsId where mst_kpi_details_id=:computedKpiId")
    int[] updateComputedKpiFormula(@BindBean List<ComputedKpiBean> computedKpiBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select k.id id, k.name name, k.identifier identifier, k.description description, k.data_type dataType, value_type valueType, cluster_operation clusterOperation, " +
            "rollup_operation rollupOperation, cluster_aggregation_type clusterAggregation, instance_aggregation_type instanceAggregation, measure_units kpiUnit, k.kpi_group_id groupKpiId, " +
            "k.is_custom standardType, k.cron_expression cronExpression, k.delta_per_sec deltaPerSec, k.reset_delta_value resetDeltaValue, pkm.status status, g.is_custom groupKpiStandardType, " +
            "g.discovery groupKpiDiscovery, g.identifier groupKpiIdentifier, g.description groupKpiDescription, g.name groupKpiName, st.name kpiType, " +
            "pkm.mst_component_id componentId, pkm.default_collection_interval collectionInterval, pkm.mst_common_version_id commonVersionId, pkm.do_analytics availableForAnalytics, " +
            "pkm.mst_component_type_id componentTypeId from mst_kpi_details k left outer join mst_component_version_kpi_mapping pkm on k.id=pkm.mst_kpi_details_id " +
            "left join mst_kpi_group g on k.kpi_group_id = g.id, mst_sub_type st where st.id = k.kpi_type_id and k.account_id in (1, :accountId)")
    List<KpiListBean> getKpiDetails(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mcv.mst_component_id componentId,mc.id commonVersionId,mc.name commonVersionName,mcv.id componentVersionId," +
            "mcv.name componentVersionName from mst_common_version mc,mst_component_version mcv where mcv.mst_common_version_id=mc.id " +
            "and mc.account_id=mcv.account_id and mcv.account_id in (1, :accountId)")
    List<ComponentBean> getComponentDetailsForAccount(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mst_kpi_details_id computedKpiId, formula, display_formula displayFormula, base_kpi_id kpiDetailsId " +
            "from mst_computed_kpi_details ckd, mst_computed_kpi_mappings ckm, mst_kpi_details mkd where " +
            "ckm.mst_computed_kpi_details_id=ckd.id and ckm.base_kpi_id=mkd.id and ckd.account_id in (1, :accountId)")
    List<ComputedKpiBean> getComputedKpiDetails(@Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mst_kpi_details_id computedKpiId, formula, display_formula displayFormula " +
            "from mst_computed_kpi_details ckd where ckd.account_id in (1, :accountId)")
    List<ComputedKpiBean> getComputedExpression(@Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mst_kpi_details_id computedKpiId, formula, display_formula displayFormula, base_kpi_id kpiDetailsId, identifier kpiDetailsIdentifier " +
            "from mst_computed_kpi_details ckd, mst_computed_kpi_mappings ckm, mst_kpi_details mkd where " +
            "ckm.mst_computed_kpi_details_id=ckd.id and ckm.base_kpi_id=mkd.id")
    List<ComputedKpiBean> getComputationExpressions();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from mst_kpi_details where identifier = :kpiIdentifier and account_id in (:accountId, 1)")
    int getKpiId(@Bind("kpiIdentifier") String kpiIdentifier, @Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT distinct mst_component_id FROM mst_component_version_kpi_mapping where mst_kpi_details_id = :kpiId")
    List<Integer> getComponentIdsForKPI(@Bind("kpiId") Integer kpiId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update mst_component_version_kpi_mapping set status = :status, updated_time = :updatedTime, " +
            "user_details_id = :userDetailsId where mst_kpi_details_id = :kpiDetailsId and mst_component_id = :componentId")
    void updateCompVersionKpiMappingStatus(@BindBean List<CompVersionKpiMappingBean> kpiBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update comp_instance_kpi_group_details set status = :status, updated_time = :updatedTime, " +
            "user_details_id = :userDetailsId where mst_kpi_details_id = :kpiDetailsId and comp_instance_id = :compInstanceId")
    void updateCompInstanceGroupKpiMappingStatus(@BindBean List<CompVersionKpiMappingBean> kpiBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update comp_instance_kpi_details set status = :status, updated_time = :updatedTime, " +
            "user_details_id = :userDetailsId where mst_kpi_details_id = :kpiDetailsId and comp_instance_id = :compInstanceId")
    void updateCompInstanceNonGroupKpiMappingStatus(@BindBean List<CompVersionKpiMappingBean> kpiBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT count(*) FROM mst_computed_kpi_mappings where base_kpi_id = :kpiId and account_id in (1, :accountId)")
    int kpiMappedToComputedKpi(@Bind("kpiId") int kpiId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT count(*) count, base_kpi_id id FROM mst_computed_kpi_mappings where account_id in (1, :accountId) group by base_kpi_id")
    List<CountBean> kpisMappedToComputedKpi(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT discovery FROM mst_kpi_group where id=:groupKpiId")
    int getGroupKpiDiscovery(@Bind("groupKpiId") int groupKpiId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select comp_instance_id compInstanceId, mst_kpi_group_id kpiGroupId, mst_kpi_details_id kpiId, attribute_value attributeValue " +
            "from comp_instance_kpi_group_details where comp_instance_id = :instanceId")
    List<KpiMaintenanceStatusBean> fetchGroupKpiCompInstMapping(@Bind("instanceId") int instanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select comp_instance_id compInstanceId, mst_kpi_details_id kpiId " +
            "from comp_instance_kpi_details where comp_instance_id = :instanceId ")
    List<KpiMaintenanceStatusBean> fetchKpiCompInstMapping(@Bind("instanceId") int instanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select comp_instance_id compInstanceId, account_id accountId, kpi_id kpiId, kpi_group_id kpiGroupId, exclude_maintenance isMaintenanceExcluded " +
            "from comp_instance_kpi_configuration where comp_instance_id=:compInstanceId and account_id=:accountId")
    List<KpiMaintenanceStatusBean> fetchCompInstanceKpiMaintenanceStatus(@Bind("compInstanceId") int compInstanceId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into comp_instance_kpi_configuration (comp_instance_id, kpi_id, kpi_group_id, account_id, " +
            "attribute_value, user_details_id, created_time, updated_time, sor_persistence, sor_suppression, exclude_maintenance) values(:compInstanceId, :kpiId, " +
            ":kpiGroupId, :accountId, :attributeValue, :userDetailsId, :createdTime, :updatedTime, :persistence, :suppression, :isMaintenanceExcluded)")
    int[] addInstanceKpiMaintenanceStatus(@BindBean List<KpiMaintenanceStatusBean> kpiMaintenanceStatusBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update comp_instance_kpi_configuration set exclude_maintenance=:isMaintenanceExcluded, updated_time=:updatedTime " +
            "where account_id=:accountId and comp_instance_id=:compInstanceId and kpi_group_id=:kpiGroupId and kpi_id=:kpiId")
    int[] updateInstanceKpiMaintenanceStatus(@BindBean List<KpiMaintenanceStatusBean> kpiMaintenanceStatusBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(*) from comp_instance_kpi_configuration where comp_instance_id=:compInstanceId " +
            "and kpi_group_id=:kpiGroupId and kpi_id=:kpiId and account_id=:accountId")
    int fetchInstKpiMaintenanceStatusCount(@Bind("compInstanceId") int compInstanceId, @Bind("kpiGroupId") int kpiGroupId,  @Bind("kpiId") int kpiId, @Bind("accountId") int accountId);
    
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("update comp_instance_kpi_threshold_details set attribute_value = :attributeValue where comp_instance_id=:compInstanceId " +
            "and kpi_group_id=:kpiGroupId and attribute_value=:attributeOldValue")
    void updateInstanceKpiAttributeNames(@BindBean InstanceKpiAttributeThresholdBean thresholdBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from comp_instance_kpi_threshold_details where comp_instance_id=:compInstanceId and kpi_group_id=:kpiGroupId " +
            "and attribute_value=:attributeOldValue")
    void deleteInstanceKpiAttributeName(@BindBean InstanceKpiAttributeThresholdBean thresholdBean);
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT identifier FROM mst_kpi_group where id=:groupKpiId")
    String getGroupKpiIdentifier(@Bind("groupKpiId") int groupKpiId);
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct base_kpi_id from mst_computed_kpi_mappings where account_id= :accountId")
    List<Integer> getBaseKpiIds(@Bind("accountId") int accountId);
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update comp_instance_kpi_threshold_details set severity= :severity where comp_instance_id= :compInstanceId and kpi_id=:kpiId")
    void updateInstanceKpiSeverity(@Bind("severity") int severity, @BindBean List<KpiMaintenanceStatusBean> beans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update comp_instance_kpi_details set notification= :notification where comp_instance_id= :compInstanceId and mst_kpi_details_id = :kpiId")
    void updateNonGroupInstanceKpiAnomaly(@Bind("notification") int notification, @BindBean List<KpiMaintenanceStatusBean> beans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update comp_instance_kpi_group_details set notification= :notification where comp_instance_id= :compInstanceId and mst_kpi_details_id = :kpiId")
    void updateGroupInstanceKpiAnomaly(@Bind("notification") int notification, @BindBean List<KpiMaintenanceStatusBean> beans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update comp_instance_kpi_threshold_details set status=:status where comp_instance_id=:compInstanceId and kpi_group_id=:kpiGroupId and kpi_id=:kpiId")
    int[] updateStatusInstanceKpiThresholds(@BindBean List<InstanceKpiAttributeThresholdBean> thresholdBeans);


    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mk.id id, mk.identifier identifier from mst_component_version_kpi_mapping mcvk right join mst_kpi_details mk on mcvk.mst_kpi_details_id = mk.id where mcvk.mst_component_id = :componentId")
    List<IdPojo> getKpisForComponent(@Bind("componentId") int componentId);


    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id as producerMappingId, producer_id as producerId, mst_kpi_details_id as kpiId FROM mst_producer_kpi_mapping WHERE mst_kpi_details_id IN (<kpiDetailsIds>)")
    List<ProducerMapping> getMappingsByKpiDetailsIds(@BindIn("kpiDetailsIds") List<Integer> kpiDetailsIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("update comp_instance_kpi_configuration set attribute_value=:attributeValue where comp_instance_id=:compInstanceId and kpi_group_id=:kpiGroupId and kpi_id=:kpiId and attribute_value=:attributeOldValue")
    void updateInstanceKpiAttributeValueInPersistSuppress(@BindBean InstKpiAttrPersistenceSuppressionBean instKpiAttrPersistenceSuppressionBean);

}