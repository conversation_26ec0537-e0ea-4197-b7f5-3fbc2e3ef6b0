package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.DBTestCache;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.ComponentDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.ApplicationInstanceAttributeBean;
import com.appnomic.appsone.controlcenter.pojo.ViewComponentAttributesPojo;
import com.appnomic.appsone.controlcenter.pojo.agentconfig.CategoryKpiMapping;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ComponentDataService {

    private ComponentDataService() {
    }

    private static final Logger logger = LoggerFactory.getLogger(ComponentDataService.class);

    public static MasterComponentBean getComponent(String name, Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            return componentDao.getComponentMasterData(name.toLowerCase());
        } catch (Exception e) {
            logger.error("Error in getting master component with name: {}", name, e);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return null;
    }

    public static MasterComponentBean getComponentWithId(int mstComponentId, Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            return componentDao.getComponentWithId(mstComponentId);
        } catch (Exception e) {
            logger.error("Error in getting master component with id: {}", mstComponentId, e);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return null;
    }

    public static List<MasterComponentBean> getAllComponents(Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            return componentDao.getAllComponents();
        } catch (Exception e) {
            logger.error("Error in getting master component list.", e);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return null;
    }

    public static ViewComponentBean getComponentDetail(int componentId, int typeId,
                                                       String version, Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            return componentDao.getComponentDetail(componentId, typeId, version.toLowerCase());
        } catch (Exception e) {
            logger.error("Error in getting component detail with componentId:{}, typeId:{}," +
                    " version:{}", componentId, typeId, version, e);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return null;
    }

    public static List<ViewComponentAttributesPojo> getComponentAttributeDetails(Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            return componentDao.getComponentAttributeDetails();
        } catch (Exception e) {
            logger.error("Error in getting component-attributes details.", e);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return null;
    }

    public static List<MasterComponentTypeBean> getComponentType(Integer componentId,
                                                                 Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            return componentDao.getComponentType(componentId);
        } catch (Exception e) {
            logger.error("Error in getting component type for component id: {}", componentId, e);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return new ArrayList<>();
    }

    public static CommonVersionBean getCommonVersion(Integer componentId,
                                                     String commonVersion, int accountId, Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            return componentDao.getCommonVersion(componentId, commonVersion.toLowerCase(), accountId);
        } catch (Exception e) {
            logger.error("Error in getting common version", e);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return null;
    }

    public static int addComponent(AddComponentBean addComponentBean, Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            int id = componentDao.addComponent(addComponentBean);
            DBTestCache.addToCache("mst_component", id);
            return id;
        } catch (Exception e) {
            logger.error("Error in adding component to database:{}", addComponentBean, e);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return -1;
    }

    public static int addComponentMapping(AddComponentMappingBean addComponentMappingBean, Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            int id = componentDao.addComponentMapping(addComponentMappingBean);
            DBTestCache.addToCache("mst_component_mapping", id);
            return id;
        } catch (Exception e) {
            logger.error("Error in adding component mapping: {}", addComponentMappingBean, e);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return -1;
    }

    public static int addCommonVersion(CommonVersionBean addCommonVersionBean, Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            int id = componentDao.addCommonVersion(addCommonVersionBean);
            DBTestCache.addToCache("mst_common_version", id);
            return id;
        } catch (Exception e) {
            logger.error("Error in adding common version for componentId: {}, commonVersion: {}, " +
                            "accountId: {}", addCommonVersionBean.getComponentId(),
                    addCommonVersionBean.getName(), addCommonVersionBean.getAccountId(), e);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return -1;
    }

    public static int addComponentVersion(AddComponentVersionBean addComponentVersion, Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            int id = componentDao.addComponentVersion(addComponentVersion);
            DBTestCache.addToCache("mst_component_version", id);
            return id;
        } catch (Exception e) {
            logger.error("Error in adding component version: {}", addComponentVersion, e);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return -1;

    }

    public static MasterCommonAttributeBean getCommonAttribute(String name, int accountId, Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            name = name.replace(" ", "");
            return componentDao.getCommonAttribute(name.toLowerCase(), accountId);
        } catch (Exception e) {
            logger.error("Error in getting common attribute with name: {}, accountId: {}", name,
                    accountId, e);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return null;
    }

    public static int addCommonAttribute(MasterCommonAttributeBean addCommonAttributeBean, Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            int id = componentDao.addCommonAttribute(addCommonAttributeBean);
            DBTestCache.addToCache("mst_common_attributes", id);
            return id;
        } catch (Exception e) {
            logger.error("Error in adding common attribute: {}", addCommonAttributeBean, e);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return -1;
    }

    public static boolean isComponentAttributeExists(int componentId, int componentTypeId,
                                                     int commonVersionId, int commonAttributeId, Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            return componentDao.isComponentAttributeExists(componentId, componentTypeId,
                    commonVersionId, commonAttributeId);
        } catch (Exception e) {
            logger.error("Error in checking common attribute", e);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return false;
    }

    public static int addComponentAttribute(AddComponentAttributeMappingBean addCompAttributeBean, Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            int id = componentDao.addComponentAttribute(addCompAttributeBean);
            DBTestCache.addToCache("mst_component_attribute_mapping", id);
            return id;
        } catch (Exception e) {
            logger.error("Error in adding component attribute: {}", addCompAttributeBean, e);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return -1;
    }

    private static ComponentDao getComponentDaoObject(Handle handle) {
        if (handle == null) {
            return MySQLConnectionManager.getInstance().open(ComponentDao.class);
        } else {
            return handle.attach(ComponentDao.class);
        }
    }

    private static void closeDaoConnection(Handle handle, ComponentDao dao) {
        if (handle == null) {
            MySQLConnectionManager.getInstance().close(dao);
        }
    }

    public static List<CompClusterMappingBean> getClusterMapping(int clusterId, int accountId) {

        ComponentDao componentDao =
                MySQLConnectionManager.getInstance().getHandle().open(ComponentDao.class);
        try {
            return componentDao.getClusterMapping(clusterId, accountId);
        } catch (Exception e) {
            logger.error("Error occurred while getting component details" + e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public static ComponentInstanceBean getClusterDetails(String tagKey, String objRefName, int accountId) {

        ComponentDao componentDao =
                MySQLConnectionManager.getInstance().getHandle().open(ComponentDao.class);
        try {
            return componentDao.getClusterDetails(tagKey, objRefName, accountId);
        } catch (Exception e) {
            logger.error("Error occurred while getting cluster details" + e.getMessage(), e);
        }
        return null;
    }

    public static List<ComponentDetailBean> getComponentDetail(String name) {

        ComponentDao componentDao =
                MySQLConnectionManager.getInstance().getHandle().open(ComponentDao.class);
        try {
            return componentDao.getComponentDetailBean(name);
        } catch (Exception e) {
            logger.error("Error occurred while getting component details" + e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public static List<CompClusterMappingBean> getInstanceClusterMapping(int accountId, Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            return componentDao.getInstanceClusterMapping(accountId);
        } catch (Exception e) {
            logger.error("Error in fetching component instance cluster details for account [{}]", accountId);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return new ArrayList<>();
    }

    public static List<CompInstAttributesBean> getInstanceAttributes(int instanceId, Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            return componentDao.getInstanceAttributes(instanceId);
        } catch (Exception e) {
            logger.error("Error in fetching component instance cluster details for account [{}]", instanceId);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return new ArrayList<>();
    }

    public static List<CompInstAttributesBean> getInstanceMappingAttributes(int instanceId, Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            return componentDao.getInstanceMappingAttributes(instanceId);
        } catch (Exception e) {
            logger.error("Error in fetching component instance cluster details for account [{}]", instanceId);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return new ArrayList<>();
    }

    public static ComponentDetailBean checkIfComponentExists(int componentId, int componentTypeId, int commonVersionId, Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            return componentDao.checkIfComponentExists(componentId, componentTypeId, commonVersionId);
        } catch (Exception e) {
            logger.error("Error in fetching component details", e);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return null;
    }

    public static List<CompInstanceKpiGroupDetailsBean> getConfigWatchKpis(int componentId, int componentVersionId) {
        ComponentDao componentDao = getComponentDaoObject(null);
        try {
            return componentDao.getConfigWatchFilesByComponentId(componentId, componentVersionId);
        } catch (Exception e) {
            logger.error("Error in fetching component configuration files, componentId:{}, componentVersionId:{}",
                    componentId, componentVersionId, e);
        } finally {
            closeDaoConnection(null, componentDao);
        }
        return null;
    }

    public static MasterComponentBean getComponentDetails(int accountId, String componentName, String componentVersionName, String componentTypeName, Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            return componentDao.getComponentDetails(accountId, componentName, componentVersionName, componentTypeName);
        } catch (Exception e) {
            logger.error("Error in fetching component details", e);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return null;
    }

    public static ViewCommonVersionKPIsBean getKpisMappedToComponentCommonVersion(int accountId, int commonVersionId, int isGroupKpi, String kpiIdentifier, Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            return componentDao.getKpisMappedToComponentCommonVersion(accountId, commonVersionId, isGroupKpi, kpiIdentifier);
        } catch (Exception e) {
            logger.error("Error in fetching component details", e);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return null;
    }

    public static List<MasterComponentBean> getComponentDetailsWithNameandVersion(String componentName, String componentVersionName, int accId, Handle handle) {
        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            return componentDao.getComponentDetailsWithNameandVersion(accId, componentName, componentVersionName);
        } catch (Exception e) {
            logger.error("Error in getting component details.", e);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return null;
    }

    public static List<CategoryKpiMapping> getCategoryByKpi(int accountId, Handle handle) {

        ComponentDao componentDao = getComponentDaoObject(handle);
        try {
            return componentDao.getCategoryByKpi(accountId);
        } catch (Exception e) {
            logger.error("Error occurred while getting cluster details" + e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, componentDao);
        }
        return null;
    }

    public static MasterComponentBean getComponentDetailsByName(String componentName) throws ControlCenterException {
        ComponentDao componentDao = MySQLConnectionManager.getInstance().open(ComponentDao.class);
        try {
            return componentDao.getComponentDetailsByName(componentName);
        } catch (Exception e) {
            logger.error("Exception while getting component details by componentName:[{}]. Details ", componentName, e);
            throw  new ControlCenterException("Exception while getting component details by componentName");
        } finally {
            MySQLConnectionManager.getInstance().close(componentDao);
        }
    }
    public static ApplicationInstanceAttributeBean getAttributeMappingByComponentId(int componentId) throws ControlCenterException{
        ComponentDao componentDao = MySQLConnectionManager.getInstance().open(ComponentDao.class);
        try {
            return componentDao.getAttributeMappingByComponentId(componentId);
        } catch (Exception e) {
            logger.error("Exception while getting attribute mapping details by componentId:[{}]. Details ", componentId, e);
            throw  new ControlCenterException("Exception while getting attribute mapping details componentId");
        } finally {
            MySQLConnectionManager.getInstance().close(componentDao);
        }
    }

}
