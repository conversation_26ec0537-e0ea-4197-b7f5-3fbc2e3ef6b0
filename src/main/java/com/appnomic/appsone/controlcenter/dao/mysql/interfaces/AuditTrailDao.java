package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.AuditBean;
import org.skife.jdbi.v2.sqlobject.Bind;
import org.skife.jdbi.v2.sqlobject.SqlQuery;
import org.skife.jdbi.v2.sqlobject.customizers.Define;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

@UseStringTemplate3StatementLocator
public interface AuditTrailDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mst_big_feature_id bigFeatureId, mst_page_action_id pageActionId,application_id appId, service_id svcId, unix_timestamp(audit_time), " +
            "audit_time auditTime, audit_user updatedBy, operation operationType, audit_data auditData " +
            "from audit_data where <whereSection> " +
            "audit_time BETWEEN CONVERT_TZ(from_unixtime(:fromTime), :timeZone, :defaultTimeZone) " +
            "and CONVERT_TZ(from_unixtime(:toTime), :timeZone, :defaultTimeZone)")
    List<AuditBean> getAuditTrail(@Bind("fromTime") Long fromTime, @Bind("toTime") Long toTime, @Bind("timeZone") String timeZone,
                                  @Bind("defaultTimeZone") String defaultTimeZone, @Define("whereSection") String whereSection);
}