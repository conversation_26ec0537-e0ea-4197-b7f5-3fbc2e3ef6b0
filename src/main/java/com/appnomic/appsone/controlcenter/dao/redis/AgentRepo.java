package com.appnomic.appsone.controlcenter.dao.redis;

import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.util.RedisUtilities;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.fasterxml.jackson.core.type.TypeReference;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.heal.configuration.pojos.Agent;
import com.heal.configuration.pojos.BasicAgentEntity;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.Supervisor;
import lombok.extern.slf4j.Slf4j;

@Slf4j

public class AgentRepo {
    String ACCOUNTS_KEY = "/accounts";
    String AGENTS_KEY = "/agents";
    String INSTANCES_KEY = "/instances";
    String SERVICES_KEY = "/services";
    String ACCOUNTS_HASH = "ACCOUNTS";
    String AGENTS_HASH = "_AGENTS_";
    String INSTANCES_HASH = "_INSTANCES";
    String SERVICES_HASH = "_SERVICES";
    String TXN_MAPPER_CONFIGURATOR_KEY = "/txn_mapper_configurator";
    String TXN_MAPPER_CONFIGURATOR_HASH = "_TXN_MAPPER_CONFIGURATOR";

    public Agent getAgentDetails(String agentIdentifier) {
        try {
            String agentJson = RedisUtilities.getKey(AGENTS_KEY + "/" + agentIdentifier, "AGENTS_" +  agentIdentifier);
            if (agentJson == null) {
                log.debug("Agent details not found for agentId:{}", agentIdentifier);
                return null;
            }

            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(agentJson, new TypeReference<Agent>() {
            });
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while getting agent details for agent:{}", agentIdentifier, e);
            return null;
        }
    }

    public List<BasicEntity> getAgentInstanceMappingDetails(String accountIdentifier, String agentIdentifier) {
        try {
            String compInstancesObj = RedisUtilities.getKey(ACCOUNTS_KEY + "/" + accountIdentifier + AGENTS_KEY + "/" + agentIdentifier + INSTANCES_KEY ,
                    ACCOUNTS_HASH + "_" + accountIdentifier + AGENTS_HASH +  agentIdentifier + INSTANCES_HASH);
            if (compInstancesObj == null) {
                log.debug("Component agent instance details not found for account: [{}]", accountIdentifier);
                return Collections.emptyList();
            }

            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(compInstancesObj, new TypeReference<List<BasicEntity>>() {
            });
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while getting agent instance mapping details for account [{}]. Details: ", accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<BasicAgentEntity> getAgents(String accountIdentifier) {
        String AGENTS_HASH = "_AGENTS";
        try {
            String agentsJsonObj = RedisUtilities.getKey(ACCOUNTS_KEY + "/" + accountIdentifier + AGENTS_KEY, ACCOUNTS_HASH + "_" + accountIdentifier + AGENTS_HASH);
            if (agentsJsonObj == null) {
                log.debug("Agents not found for accountId:{}", accountIdentifier);
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(agentsJsonObj, new TypeReference<List<BasicAgentEntity>>() {
            });
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while getting agents for accountId:{}.", accountIdentifier, e);
            return new ArrayList<>();
        }
    }


    public List<Supervisor> getSupervisors(String accountIdentifier) {
        String SUPERVISOR_KEY = "/supervisors";
        String SUPERVISOR_HASH = "_SUPERVISORS";

        try {
            String agentsJsonObj = RedisUtilities.getKey(ACCOUNTS_KEY + "/" + accountIdentifier + SUPERVISOR_KEY, ACCOUNTS_HASH + "_" + accountIdentifier + SUPERVISOR_HASH);
            if (agentsJsonObj == null) {
                log.debug("Supervisors not found for accountId:{}", accountIdentifier);
                return Collections.emptyList();
            }

            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(agentsJsonObj, new TypeReference<List<Supervisor>>() {
            });
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while getting supervisors for accountId:{}.", accountIdentifier, e);
            return new ArrayList<>();
        }
    }

    public List<BasicEntity> getServices(String accountIdentifier, String agentIdentifier) {
        try {
            String agentServiceObj = RedisUtilities.getKey("/accounts/" + accountIdentifier + "/agents/" + agentIdentifier + SERVICES_KEY,
                    "ACCOUNTS_" + accountIdentifier + "_AGENTS_" + agentIdentifier + SERVICES_HASH);
            if (agentServiceObj == null) {
                log.debug("Service details not found for accountId:{}, agentId:{}", accountIdentifier, agentIdentifier);
                return new ArrayList<>();
            }

            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(agentServiceObj, new TypeReference<List<BasicEntity>>() {
            });
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while getting agent service mapping details for accountId:{}, agentId:{} ", accountIdentifier, agentIdentifier, e);
            return new ArrayList<>();
        }
    }
    public void updateAgentInstanceMappingDetails(String accountIdentifier, String agentIdentifier, List<BasicEntity> agentInstanceMappingDetails){
        try{
            RedisUtilities.updateKey(ACCOUNTS_KEY + "/" + accountIdentifier + AGENTS_KEY + "/" + agentIdentifier + INSTANCES_KEY,
                    ACCOUNTS_HASH + "_" + accountIdentifier + AGENTS_HASH + agentIdentifier +  INSTANCES_HASH, agentInstanceMappingDetails);
        }catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while updating the agent instance mapping  details for the agentIdentifier: {} and accountIdentifier: {} ", agentIdentifier, accountIdentifier, e);
        }
    }
    public void updateAgents(String accountIdentifier, List<BasicAgentEntity> agentEntityList){
        try{
            String AGENTS_HASH = "_AGENTS";
            RedisUtilities.updateKey(ACCOUNTS_KEY + "/" + accountIdentifier + AGENTS_KEY, ACCOUNTS_HASH + "_" + accountIdentifier + AGENTS_HASH, agentEntityList);
        }catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while updating  the agent details for accountIdentifier: {}", accountIdentifier, e);
        }
    }
    public void updateAgentDetailsForAgentIdentifier(Agent newAgentDetail ){
        String AGENT_HASH = "AGENTS_";
        try{
            RedisUtilities.updateKey(AGENTS_KEY + "/" + newAgentDetail.getIdentifier(), AGENT_HASH + newAgentDetail.getIdentifier(), newAgentDetail );
        }catch (Exception e){
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while updating agent details for the agentIdentifier: {}", newAgentDetail.getIdentifier());
        }
    }
    public void updateServices(String accountIdentifier, String agentIdentifier, List<BasicEntity> serviceDetails){
        try{

            RedisUtilities.updateKey(ACCOUNTS_KEY + "/" + accountIdentifier + AGENTS_KEY + "/" + agentIdentifier + SERVICES_KEY,
                    ACCOUNTS_HASH  + "_" + accountIdentifier + AGENTS_HASH + agentIdentifier + SERVICES_HASH, serviceDetails);
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while updating the agent instance mapping  details for the accountIdentifier: {} and agentIdentifier: {}", accountIdentifier, agentIdentifier, e);
        }
    }

    public void deleteAgents(String agentIdentifier){
        String AGENTS_HASH = "AGENTS_";
        try{
            RedisUtilities.deleteKey(AGENTS_KEY + "/" + agentIdentifier,
                     AGENTS_HASH + agentIdentifier);

        }   catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Exception encountered while deleting agent detail for agentIdentifier: {}", agentIdentifier, e);
        }

        }

    public void deleteAgentServiceMapping(String accountIdentifier, String agentIdentifier){
        try {
            RedisUtilities.deleteKey(ACCOUNTS_KEY + "/" + accountIdentifier + AGENTS_KEY + "/" + agentIdentifier + SERVICES_KEY,
                    ACCOUNTS_HASH + "_" + accountIdentifier + AGENTS_HASH + agentIdentifier + SERVICES_HASH);
        } catch (Exception e) {
                CCCache.INSTANCE.updateCCErrors(1);
                log.error("Exception encountered while deleteAgentServiceMapping details for the accountIdentifier: {} and agentIdentifier: {}.", accountIdentifier, agentIdentifier, e);
        }
    }

    public Agent getAgent(String agentIdentifier){
        String AGENTS_HASH = "AGENTS_";
        try{
            String agentsJsonObj = RedisUtilities.getKey(AGENTS_KEY + "/" + agentIdentifier,AGENTS_HASH + agentIdentifier);
            if (agentsJsonObj == null){
                log.debug("Agents not found for agentId:{}", agentIdentifier);
                return null;
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(agentsJsonObj, new TypeReference<Agent>() {
            });
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while getting agents for agentId:{}.", agentIdentifier, e);
            return null;

        }
    }

    public boolean deleteAgentServiceTxnMapperConfigurator(String accountIdentifier, String agentIdentifier, String serviceIdentifier){
        try{
            return RedisUtilities.deleteKey(ACCOUNTS_KEY + "/" + accountIdentifier + AGENTS_KEY + "/" +agentIdentifier + SERVICES_KEY + "/" + serviceIdentifier + TXN_MAPPER_CONFIGURATOR_KEY,
                    ACCOUNTS_HASH + "_" + accountIdentifier + AGENTS_HASH + agentIdentifier + SERVICES_HASH + "_" + serviceIdentifier + TXN_MAPPER_CONFIGURATOR_HASH);

        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while deleting key for agentId: [{}], serviceId: [{}]", agentIdentifier, serviceIdentifier, e);
        }
        return false;
    }

    public Supervisor getSupervisorDetails(String accountIdentifier, String supervisorIdentifier) {
        String SUPERVISOR_KEY = "/supervisors";
        String SUPERVISOR_HASH = "_SUPERVISORS";

        try {
            String agentsJsonObj = RedisUtilities.getKey(ACCOUNTS_KEY + "/" + accountIdentifier + SUPERVISOR_KEY + "/" + supervisorIdentifier , ACCOUNTS_HASH + "_" + accountIdentifier + SUPERVISOR_HASH + "_" + supervisorIdentifier);
            if (agentsJsonObj == null) {
                log.debug("Supervisor details not found for supervisorId {} of accountId:{}", supervisorIdentifier, accountIdentifier);
                return null;
            }

            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(agentsJsonObj, new TypeReference<Supervisor>() {
            });
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while getting supervisor details for supervisorId {} of accountId:{}.", supervisorIdentifier, accountIdentifier, e);
            return null;
        }
    }
}


