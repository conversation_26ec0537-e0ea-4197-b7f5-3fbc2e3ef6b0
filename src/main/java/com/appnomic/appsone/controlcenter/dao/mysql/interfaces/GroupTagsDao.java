package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.DiscoveryTagsBean;
import com.appnomic.appsone.controlcenter.beans.TransactionGroupMapBean;
import com.appnomic.appsone.controlcenter.beans.TransactionGroupsBean;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;
import java.util.Set;

public interface GroupTagsDao {
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name from transaction_groups where account_id = :accountId")
    List<DiscoveryTagsBean> getTransactionTags(@Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name from transaction_groups where account_id = :accountId and name = :name and status = 1")
    DiscoveryTagsBean getTransactionTags(@Bind("accountId") Integer accountId, @Bind("name") String name);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("delete from transaction_group_mapping where account_id = :accountId and id = :ids")
    int[] deleteRequestTagMapping(@Bind("accountId") Integer accountId, @Bind("ids") Set<Integer> id);

    @SqlUpdate("INSERT INTO transaction_groups (`name`, `account_id`, `user_details_id`, `created_time`, `updated_time`, `status`) VALUES(:name,:accountId,:userDetailsId,:createdTime,:updatedTime,:status)")
    @GetGeneratedKeys
    int addTransactionGroups(@BindBean TransactionGroupsBean transactionGroupsBean);

    @SqlUpdate("INSERT INTO transaction_group_mapping (`txn_group_id`, `account_id`, `object_id`, `object_ref_table`, `user_details_id`, `created_time`, `updated_time`) VALUES(:txnGroupId, :accountId, :objectId, :objectRefTable, :userDetailsId, :createdTime, :updatedTime)")
    @GetGeneratedKeys
    int addTransactionGroupMap(@BindBean TransactionGroupMapBean transactionMapBean);
}
