package com.appnomic.appsone.controlcenter.dao.opensearch;

import com.appnomic.appsone.common.beans.discovery.DiscoveryError;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.Host;
import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.manager.OpenSearchConnectionManager;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.opeasearchquery.helper.OpenSearchQueryHelper;
import com.appnomic.appsone.opeasearchquery.queryopts.QueryOptions;
import com.appnomic.appsone.opeasearchquery.results.NameValuePair;
import com.appnomic.appsone.opeasearchquery.results.RawDocumentResults;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.heal.configuration.pojos.opensearch.AutoDiscoveryData;
import com.heal.configuration.pojos.opensearch.RawExternalData;
import com.heal.configuration.util.DateHelper;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch._types.aggregations.Aggregate;
import org.opensearch.client.opensearch._types.aggregations.Aggregation;
import org.opensearch.client.opensearch._types.aggregations.StringTermsBucket;
import org.opensearch.client.opensearch._types.query_dsl.Query;
import org.opensearch.client.opensearch.core.BulkRequest;
import org.opensearch.client.opensearch.core.BulkResponse;
import org.opensearch.client.opensearch.core.IndexRequest;
import org.opensearch.client.opensearch.core.SearchResponse;
import org.opensearch.client.opensearch.core.search.Hit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * <AUTHOR> Suman - 20-01-2022
 */
public class AutoDiscoveryRepo {

    private static final Logger log = LoggerFactory.getLogger(AutoDiscoveryRepo.class);

    private final ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    public Map<String, String> getAutoDiscoveryHostErrorLogs() {
        String indexName = Constants.INDEX_PREFIX_HEAL_AUTO_DISCOVERY_ERRORS + "_*";
        Map<String, String> result = new HashMap<>();
        try {
            Query qb = Query.of(c -> c.bool(b -> b.must(m -> m.matchAll(t -> t))));

            Map<String, Aggregation> aggregationMap = new HashMap<>();
            aggregationMap.put("DISTINCT_VALUES", Aggregation.of(c -> c.terms(d -> d.field("hostNodeId"))));
            SearchResponse<Object> searchResponse = OpenSearchRepo.getDistinctFieldsAndCount(qb, aggregationMap, indexName, Constants.GLOBAl_ACCOUNT_IDENTIFIER, Constants.INDEX_PREFIX_HEAL_AUTO_DISCOVERY_ERRORS);
            if (searchResponse == null || searchResponse.hits() == null) {
                return Collections.emptyMap();
            }

            Gson gson = new Gson();

            List<Hit<Object>> searchHits = searchResponse.hits().hits();
            if (searchHits != null && !searchHits.isEmpty()) {
                Aggregate fieldCountsAggs = searchResponse.aggregations().get("DISTINCT_VALUES");
                List<String> listOfUniqueHostIds = new ArrayList<>();
                if (fieldCountsAggs.isSterms()) {
                    for (StringTermsBucket bucket : fieldCountsAggs.sterms().buckets().array()) {
                        listOfUniqueHostIds.add(bucket.key());
                    }
                }

                for (String hostId : listOfUniqueHostIds) {
                    Query qb1 = Query.of(c -> c.bool(b -> b.must(m -> m.matchPhrase(v -> v.field("hostNodeId").query(hostId)))));
                    SearchResponse<Object> resp = OpenSearchRepo.getLimitedSortedDocument(qb1, indexName, "time", true, 1, Constants.GLOBAl_ACCOUNT_IDENTIFIER, Constants.INDEX_PREFIX_HEAL_AUTO_DISCOVERY_ERRORS);
                    if (resp == null || resp.hits() == null) {
                        throw new ControlCenterException("OpenSearch connection unavailable");
                    }

                    List<Hit<Object>> hits = resp.hits().hits();
                    if (hits != null) {
                        for (Hit<Object> hit : hits) {
                            AutoDiscoveryData autoDiscoveryData = objectMapper.readValue(gson.toJson(hit.source()), new TypeReference<>() {
                            });
                            String hostNodeId = autoDiscoveryData.getHostNodeId();
                            long errorCount = autoDiscoveryData.getErrorCount();
                            String errorMessage = autoDiscoveryData.getErrorMessage();
                            String stackTrace = autoDiscoveryData.getStackTrace();
                            String value = "\"Error Count\" : \"" + errorCount + "\", \n\"Error Message\" : \"" + errorMessage +
                                    "\", \n\"Stack Trace\" : \n\"" + stackTrace + "\"";
                            result.put(hostNodeId, value);
                        }
                    }
                }
            }
            return result;
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.warn("Unable to get AutodiscoveryError logs. Details: ", e);
        }
        return Collections.emptyMap();
    }

    public boolean insertAutoDiscoveryErrors(Host host) {
        String indexName = Constants.INDEX_PREFIX_HEAL_AUTO_DISCOVERY_ERRORS + "_" + DateHelper.getWeeksAsString(host.getLastUpdatedTime(), host.getLastUpdatedTime()).get(0);
        try {
            OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(Constants.GLOBAl_ACCOUNT_IDENTIFIER, Constants.INDEX_PREFIX_HEAL_AUTO_DISCOVERY_ERRORS);
            if (openSearchClient == null) {
                return false;
            }

            List<IndexRequest<AutoDiscoveryData>> indexRequestList = new ArrayList<>();
            String hostNodeId = host.getHostIdentifier();
            for (DiscoveryError e : host.getDiscoveryErrors()) {
                AutoDiscoveryData autoDiscoveryData = AutoDiscoveryData.builder()
                        .hostNodeId(hostNodeId)
                        .time(host.getLastUpdatedTime())
                        .errorCount(e.getErrorCount())
                        .stackTrace(e.getStackTrace())
                        .errorMessage(e.getErrorMessage())
                        .variables(e.getVariables())
                        .timestamp(DateHelper.getDate(host.getLastUpdatedTime()))
                        .build();
                IndexRequest<AutoDiscoveryData> indexRequest = new IndexRequest.Builder<AutoDiscoveryData>().index(indexName)
                        .document(autoDiscoveryData)
                        .build();

                indexRequestList.add(indexRequest);
            }

            BulkRequest.Builder bulkRequestBuilder = new BulkRequest.Builder();

            // Add each index request to the bulk request
            indexRequestList.forEach(indexRequest ->
                    bulkRequestBuilder.operations(op ->
                            op.index(idx -> idx
                                    .index(indexRequest.index())
                                    .id(indexRequest.id())
                                    .document(indexRequest.document())
                            )));
            // Build the request
            BulkRequest bulkRequest = bulkRequestBuilder.build();

            BulkResponse bulkResponse = openSearchClient.bulk(bulkRequest);

            return !bulkResponse.errors();
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.warn("Unable to add AutodiscoveryError. Details: ", e);
            return false;
        }
    }

    public RawExternalData getRawExternalData(String hostAddress, String accountIdentifier) {
        String indexName = Constants.HEAL_RAW_EXTERNAL_DATA + "*";
        try {
            OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, Constants.HEAL_RAW_EXTERNAL_DATA);
            if (openSearchClient == null) {
                return null;
            }
            List<String> indexNames = new ArrayList<>();
            indexNames.add(indexName);
            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair(Constants.METADATA_HOST_ADDRESS_NAME, hostAddress));
            matchFields.add(new NameValuePair(Constants.TAGS_NAME, Constants.INSTALLATION_LOG_NAME));
            matchFields.add(new NameValuePair(Constants.DATA_SOURCE_NAME, Constants.DATA_SOURCE_VALUE));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .isTimeSeriesData(false)
                    .numberOfRawRecords(1)
                    .matchAllFields(Optional.of(matchFields))
                    .build();

            RawDocumentResults result = OpenSearchQueryHelper.getRawDocuments(queryOptions, openSearchClient);
            if (result != null && result.getDocuments() != null) {
                return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(result.getDocuments().get(0).getSource(), new TypeReference<>() {
                });
            }
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while fetching raw external data", e);
        }
        return null;
    }
}
