package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class HttpPatternDataBean {
    private int id;
    private int ruleId;
    private int accountId;
    private String userDetails;
    private int httpMethodTypeId;
    private int firstUriSegments;
    private int lastUriSegments;
    private String customSegments;
    private boolean completeURI;
    private int payloadTypeId;
    private String completePattern;
    private Timestamp createdTime;
    private Timestamp updatedTime;
}