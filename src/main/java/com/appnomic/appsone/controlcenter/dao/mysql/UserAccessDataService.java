package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.UserAccessBean;
import com.appnomic.appsone.controlcenter.beans.UserBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.RoutesInformation;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserAttributesBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserProfileBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.UserAccessDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;

public class UserAccessDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserAccessDataService.class);

    public UserAttributesBean getUserAttributeDetails(String userIdentifier) throws ControlCenterException {
        UserAccessDao userAccessDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);

        try {
            return userAccessDao.getRoleProfileInfoForUserId(userIdentifier);
        } catch (Exception e) {
            LOGGER.error("Exception encountered while fetching user role and profile. Reason: {}", e.getMessage(), e);
            throw new ControlCenterException("Exception encountered while fetching user role and profile");
        } finally {
            MySQLConnectionManager.getInstance().close(userAccessDao);
        }
    }

    public List<String> getUserAccessibleActions(int profileId) throws ControlCenterException {
        UserAccessDao userAccessDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);

        try {
            return userAccessDao.getUserAccessibleActions(profileId);
        } catch (Exception e) {
            LOGGER.error("Exception encountered while fetching user attributes information. Reason: {}", e.getMessage(), e);
            throw new ControlCenterException("Exception encountered while fetching user attributes information");
        } finally {
            MySQLConnectionManager.getInstance().close(userAccessDao);
        }
    }

    public List<RoutesInformation> getAccessibleRoutesForUser(int profileId) throws ControlCenterException {
        UserAccessDao userAccessDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);

        try {
            return userAccessDao.getAccessibleRoutesForUser(profileId);
        } catch (Exception e) {
            LOGGER.error("Exception encountered while fetching action names. Reason: {}", e.getMessage(), e);
            throw new ControlCenterException("Exception encountered while fetching routes information");
        } finally {
            MySQLConnectionManager.getInstance().close(userAccessDao);
        }
    }

    public static UserAccessBean getUserAccessDetails(String userIdentifier) throws ControlCenterException {
        UserAccessDao userAccessDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);

        try {
            return userAccessDao.getUserAccessDetails(userIdentifier);
        } catch (Exception e) {
            LOGGER.error("Exception encountered while fetching action names. Reason: {}", e.getMessage(), e);
            throw new ControlCenterException("Exception encountered while fetching user access details");
        } finally {
            MySQLConnectionManager.getInstance().close(userAccessDao);
        }
    }

    public static List<UserProfileBean> getUserProfiles() throws ControlCenterException {
        UserAccessDao userAccessDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);

        try {
            return userAccessDao.getUserProfiles();
        } catch (Exception e) {
            LOGGER.error("Error while getting user profile details from DB", e);
            throw new ControlCenterException("Error in fetching user profile details");
        } finally {
            MySQLConnectionManager.getInstance().close(userAccessDao);
        }
    }

    public static List<IdPojo> getRoles() throws ControlCenterException {
        UserAccessDao userAccessDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);

        try {
            return userAccessDao.getRoles();
        } catch (Exception e) {
            LOGGER.error("Error while getting user roles details from DB", e);
            throw new ControlCenterException("Error in fetching user roles details");
        } finally {
            MySQLConnectionManager.getInstance().close(userAccessDao);
        }
    }

    public int getProfileId(String profileName) throws ControlCenterException {
        UserAccessDao userAccessDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);

        try {
            return userAccessDao.getProfileIdForHealAdmin(profileName);
        } catch (Exception e) {
            LOGGER.error("Error while getting user roles details from DB", e);
            throw new ControlCenterException("Error in fetching user roles details");
        } finally {
            MySQLConnectionManager.getInstance().close(userAccessDao);
        }
    }

    public static List<String> getAccessProfileMapping(int profileId) throws ControlCenterException {
        UserAccessDao userAccessDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);

        try {
            return userAccessDao.getAccessProfileMapping(profileId);
        } catch (Exception e) {
            LOGGER.error("Error while getting user profile mapping details from DB", e);
            throw new ControlCenterException("Error in fetching user profile mapping");
        } finally {
            MySQLConnectionManager.getInstance().close(userAccessDao);
        }
    }

    public static String getUserIdentifierFromName(String username) throws ControlCenterException {
        UserAccessDao userAccessDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);

        try {
            return userAccessDao.getUserIdentifierFromName(username);
        } catch (Exception e) {
            LOGGER.error("Error while getting user profile mapping details from DB", e);
            throw new ControlCenterException("Error in fetching user profile mapping");
        } finally {
            MySQLConnectionManager.getInstance().close(userAccessDao);
        }
    }

    public static List<String> getUserIdentifiers() throws ControlCenterException {
        UserAccessDao userAccessDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);

        try {
            return userAccessDao.getUserIdentifiers();
        } catch (Exception e) {
            LOGGER.error("Error while getting user attribute details from DB", e);
            throw new ControlCenterException("Error in fetching user attribute details");
        } finally {
            MySQLConnectionManager.getInstance().close(userAccessDao);
        }
    }

    public static List<String> getKeycloakUserIdentifiers() throws ControlCenterException {
        UserAccessDao userAccessDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);
        try {
            return userAccessDao.getKeycloakUserIdentifiers();
        } catch (Exception e) {
            LOGGER.error("Error while getting keycloak user identifiers from DB", e);
            throw new ControlCenterException("Error in fetching keycloak user identifiers.");
        } finally {
            MySQLConnectionManager.getInstance().close(userAccessDao);
        }
    }

    public static UserBean getUserDetailsFromUsername(String username) throws ControlCenterException {
        UserAccessDao userAccessDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);

        try {
            return userAccessDao.getUserDetailsFromUsername(username);
        } catch (Exception e) {
            LOGGER.error("Error while getting user identifier from DB, username:{}", username, e);
            throw new ControlCenterException("Error in fetching user identifier, username:"+username);
        } finally {
            MySQLConnectionManager.getInstance().close(userAccessDao);
        }
    }

    public static List<UserBean> getUserDetailsFromKeycloak() throws ControlCenterException {
        UserAccessDao userAccessDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);

        try {
            return userAccessDao.getUserDetailsFromKeycloak();
        } catch (Exception e) {
            LOGGER.error("Error while getting user identifier", e);
            throw new ControlCenterException("Error while getting user identifier");
        } finally {
            MySQLConnectionManager.getInstance().close(userAccessDao);
        }
    }

    public static List<IdPojo> getActiveUsers() {
        UserAccessDao userAccessDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);

        try {
            return userAccessDao.getActiveUsers();
        } catch (Exception e) {
            LOGGER.error("Error while getting active users ", e);
            return Collections.emptyList();
        } finally {
            MySQLConnectionManager.getInstance().close(userAccessDao);
        }
    }
    public static int getUserProfileId(String userIdentifier) {
        UserAccessDao userAccessDao = MySQLConnectionManager.getInstance().open(UserAccessDao.class);
        try {
            return userAccessDao.getUserProfileId(userIdentifier);
        } catch (Exception e) {
            LOGGER.error("Error while getting user role Id ", e);
            return 0;
        } finally {
            MySQLConnectionManager.getInstance().close(userAccessDao);
        }
    }
}
