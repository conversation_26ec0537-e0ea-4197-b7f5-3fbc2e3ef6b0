package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.pojo.ApplicationInstanceAttributeBean;
import com.appnomic.appsone.controlcenter.pojo.ViewComponentAttributesPojo;
import com.appnomic.appsone.controlcenter.pojo.agentconfig.CategoryKpiMapping;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface ComponentDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,is_custom isCustom,status,created_time createdTime,updated_time " +
              "updatedTime,user_details_id userDetailsId,account_id accountId,description " +
              "description from mst_component where lower(name) = :name")
    MasterComponentBean getComponentMasterData(@Bind("name") String name);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,is_custom isCustom,status,created_time createdTime,updated_time " +
            "updatedTime,user_details_id userDetailsId,account_id accountId,description " +
            "description from mst_component")
    List<MasterComponentBean> getAllComponents();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,is_custom isCustom,status,created_time createdTime,updated_time " +
            "updatedTime,user_details_id userDetailsId,account_id accountId,description " +
            "description from mst_component where id = :mstComponentId")
    MasterComponentBean getComponentWithId(@Bind("mstComponentId") int mstComponentId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select component_id componentId, component_name componentName, component_type_id " +
              "componentTypeId, component_type_name componentTypeName, component_type_status " +
              "componentTypeStatus, is_custom isCustom, component_status componentStatus, " +
              "common_version_id commonVersionId, common_version_name commonVersionName, " +
              "component_version_id componentVersionId, component_version_name " +
              "componentVersionName, is_custom_version isCustomVersion, is_version_status " +
              "isVersionStatus from view_components where component_id = :componentId and " +
              "component_type_id = :typeId and lower(component_version_name) = :version")
    ViewComponentBean getComponentDetail(@Bind("componentId") int componentId,
            @Bind("typeId") int typeId, @Bind("version") String version);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mcam.mst_common_attributes_id attributeId, mca.name name,mca.attribute_name attributeName, mcam.default_value defaultValue," +
            " mcam.is_mandatory isMandatory, vc.component_id componentId, vc.component_name componentName," +
            " vc.component_type_id componentTypeId, vc.component_type_name componentTypeName, vc.common_version_id commonVersionId," +
            " vc.common_version_name commonVersionName, vc.component_version_id componentVersionId," +
            " vc.component_version_name componentVersionName from view_components vc, mst_component_attribute_mapping mcam," +
            " mst_common_attributes mca where vc.component_id = mcam.mst_component_id and vc.common_version_id = mcam.mst_common_version_id" +
            " and vc.component_type_id = mcam.mst_component_type_id and mcam.mst_common_attributes_id = mca.id and" +
            " vc.component_type_status = 1 and vc.component_status = 1 and vc.is_version_status = 1")
    List<ViewComponentAttributesPojo> getComponentAttributeDetails();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mct.id id, mct.name name from mst_component_type mct, " +
              "mst_component_mapping mcm where mcm.mst_component_id = :componentId " +
              "and mcm.mst_component_type_id = mct.id")
    List<MasterComponentTypeBean> getComponentType(@Bind("componentId") Integer componentId);

    @SqlUpdate("insert into mst_component (name, is_custom, status, created_time, updated_time, " +
               "user_details_id, account_id, description) values (:name, :isCustom, :status, " +
               ":createdAt, :updatedAt, :userId, :accountId, :description)")
    @GetGeneratedKeys
    int addComponent(@BindBean AddComponentBean addComponentBean);

    @SqlUpdate("insert into mst_component_mapping (mst_component_type_id, mst_component_id, " +
               "created_time, user_details_id, account_id) values (:componentTypeId, " +
               ":componentId, :createdAt, :userId, :accountId)")
    @GetGeneratedKeys
    int addComponentMapping(@BindBean AddComponentMappingBean addComponentMappingBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, mst_component_id componentId from mst_common_version where " +
              "mst_component_id = :componentId and lower(name)" +
              " =:commonVersion and account_id in (1, :accountId)")
    CommonVersionBean getCommonVersion(@Bind("componentId") Integer componentId,
            @Bind("commonVersion") String commonVersion, @Bind("accountId") int accountId);

    @SqlUpdate("insert into mst_common_version (name, mst_component_id, is_custom, status, " +
               "created_time, updated_time, user_details_id, account_id) values (:name, " +
               ":componentId, :isCustom, :status, :createdAt, :updatedAt, :userId, :accountId)")
    @GetGeneratedKeys
    int addCommonVersion(@BindBean CommonVersionBean addCommonVersionBean);

    @SqlUpdate("insert into mst_component_version (name, is_custom, status, version_from, " +
               "version_to, mst_common_version_id, mst_component_id, created_time, updated_time, " +
               "user_details_id, account_id) values (:name, :isCustom, :status, :versionFrom, " +
               ":versionTo, :commonVersionId, :componentId, :createdAt, :updatedAt, :userId, " +
               ":accountId)")
    @GetGeneratedKeys
    int addComponentVersion(@BindBean AddComponentVersionBean addComponentVersion);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, attribute_name attributeName, attribute_type attributeType, is_custom " +
              "isCustom, status, created_time createdAt, updated_time updatedAt, user_details_id " +
              "userId, account_id accountId, name from mst_common_attributes where " +
              "lower(attribute_name) = :name and account_id in (1, :accountId)")
    MasterCommonAttributeBean getCommonAttribute(@Bind("name") String name, @Bind("accountId") int accountId);


    @SqlUpdate("insert into mst_common_attributes (attribute_name, attribute_type, is_custom, " +
               "status, created_time, updated_time, user_details_id, account_id, name) values " +
               "(:attributeName, :attributeType, :isCustom, :status, :createdAt, :updatedAt, " +
               ":userId, :accountId, :name)")
    @GetGeneratedKeys
    int addCommonAttribute(@BindBean MasterCommonAttributeBean addCommonAttributeBean);

    @SqlUpdate("insert into mst_component_attribute_mapping (mst_common_attributes_id, is_custom," +
               " is_mandatory, default_value, mst_common_version_id, created_time, updated_time, " +
               "user_details_id, min_length, max_length, regex, mst_component_id, " +
               "mst_component_type_id) values (:commonAttributesId, :isCustom, :isMandatory, " +
               ":defaultValue, :commonVersionId, :createdAt, :updatedAt, :userId, :minLength, " +
               ":maxLength, :regex, :componentId, :componentTypeId)")
    @GetGeneratedKeys
    int addComponentAttribute(@BindBean AddComponentAttributeMappingBean addCompAttributeBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select exists(select id from mst_component_attribute_mapping where " +
              "mst_component_id = :componentId and mst_component_type_id = :componentTypeId and " +
              "mst_common_version_id = :commonVersionId and mst_common_attributes_id = " +
              ":commonAttributeId)")
    boolean isComponentAttributeExists(@Bind("componentId") int componentId, @Bind(
            "componentTypeId") int componentTypeId, @Bind("commonVersionId") int commonVersionId,
            @Bind("commonAttributeId") int commonAttributeId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,comp_instance_id compInstanceId,cluster_id clusterId from component_cluster_mapping where cluster_id=:clusterId and account_id=:accountId")
    List<CompClusterMappingBean> getClusterMapping(@Bind("clusterId") int clusterId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,status,host_id hostId,is_dr isDR,is_cluster isCluster,mst_component_version_id mstComponentVersionId,created_time createdTime,updated_time updatedTime,user_details_id userDetailsId,account_id accountId,mst_component_id mstComponentId,mst_component_type_id mstComponentTypeId,discovery,host_address hostAddress,identifier,mst_common_version_id mstCommonVersionId from comp_instance where is_cluster=1 and  id in (select object_id from tag_mapping where tag_id=1 and object_ref_table=:objectRefName and tag_key=:tagKey and account_id=:accountId) and account_id=:accountId")
    ComponentInstanceBean getClusterDetails(@Bind("tagKey") String tagKey, @Bind("objectRefName") String objectRefName, @Bind("accountId") int accountId);


    @SqlBatch("insert into comp_instance (name,status,host_id,is_DR,is_cluster,mst_component_version_id,created_time,updated_time,user_details_id,account_id,mst_component_id,mst_component_type_id,discovery,host_address,identifier, mst_common_version_id) values (:name,:status,:hostId,:isDR,:isCluster,:mstComponentVersionId,:createdTime,:updatedTime,:userDetailsId,:accountId,:mstComponentId,:mstComponentTypeId,:discovery,:hostAddress,:identifier, :mstCommonVersionId)")
    @GetGeneratedKeys
    int[] addComponentInstanceList(@BindBean List<ComponentInstanceBean> componentInstanceBean);

    @SqlUpdate("update comp_instance set host_id = :hostId where id = :id and account_id=:accountId")
    int updateInstanceHostIds(@Bind("hostId") int hostId, @Bind("id") int id,@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,status,host_id  hostId,is_dr  isDR,is_cluster  isCluster,mst_component_version_id  mstComponentVersionId,created_time  createdTime,updated_time  updatedTime,user_details_id  userDetailsId,account_id  accountId,mst_component_id  mstComponentId,mst_component_type_id  mstComponentTypeId,discovery,host_address  hostAddress,identifier,mst_common_version_id  mstCommonVersionId from comp_instance where (name = :name or  identifier = :name) and host_address=:hostAddress and account_id = :accountId")
    ComponentInstanceBean getCompInstFortComInstName(@Bind("name") String name,@Bind("hostAddress") String hostAddress, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select cluster_id clusterId from component_cluster_mapping where  comp_instance_id=:hostId and account_id = :accountId")
    CompClusterMappingBean getCompClusterDetails(@Bind("hostId") int hostId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from component_cluster_mapping where comp_instance_id=:compInstanceId and account_id=:accountId")
    int getClusterInstance(@Bind("compInstanceId") int compInstanceId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select component_type_id componentTypeId, component_type_name componentTypeName, " +
            "component_type_status componentTypeStatus, component_id componentId, component_name " +
            "componentName, is_custom custom, component_status componentStatus, " +
            "common_version_id commonVersionId, common_version_name commonVersionName, " +
            "is_custom_version customVersion,component_version_name componentVersionName, is_version_status versionStatus from " +
            "view_components where component_name = :name")
    List<ComponentDetailBean> getComponentDetailBean(@Bind("name") String name);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select comp_instance_id compInstanceId, cluster_id clusterId from component_cluster_mapping where account_id=:accountId")
    List<CompClusterMappingBean> getInstanceClusterMapping(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT ciav.id attributeId, ciav.attribute_value attributeValue, ciav.user_details_id userDetailsId, mca.name attributeName," +
            " mca.attribute_name attributeKey, mca.attribute_type type, mca.is_custom isCustom, mca.status, mcam.is_mandatory required, mcam.max_length max, mcam.min_length min," +
            " mcam.regex pattern, mcam.error_message errorMessage , group_concat(mst.name) options, ciav.created_time createdTime," +
            " ciav.updated_time updatedTime, ciav.comp_instance_id compInstanceId, mcam.id mstComponentAttributeMappingId," +
            " mca.id mstCommonAttributesId FROM comp_instance_attribute_values ciav JOIN mst_common_attributes mca" +
            " ON(mca.id=ciav.mst_common_attributes_id) JOIN mst_component_attribute_mapping mcam ON(mcam.id=ciav.mst_component_attribute_mapping_id)" +
            " LEFT JOIN mst_common_attribute_type_values mcat ON(mcat.mst_common_attributes_id=mca.id) LEFT JOIN mst_sub_type mst" +
            " ON(mst.mst_type_id=mcat.mst_type_id) WHERE ciav.comp_instance_id=:instanceId group by ciav.id")
    List<CompInstAttributesBean> getInstanceAttributes(@Bind("instanceId") int instanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mca.attribute_name attributeKey, " +
            "mca.name attributeName,mca.attribute_type type,mcam.is_mandatory required,mcam.max_length max,mcam.min_length min, " +
            "mcam.regex pattern,mcam.error_message errorMessage ,group_concat(mst.name) options , " +
            "mcam.created_time createdTime,mcam.updated_time updatedTime ,ci.id compInstanceId, " +
            "mcam.id mstComponentAttributeMappingId ,mca.id mstCommonAttributesId " +
            "from mst_common_attributes mca " +
            "join mst_component_attribute_mapping mcam on(mcam.mst_common_attributes_id=mca.id) " +
            "join comp_instance ci on(ci.mst_common_version_id = mcam.mst_common_version_id and " +
            "ci.mst_component_id = mcam.mst_component_id and ci.mst_component_type_id = mcam.mst_component_type_id) " +
            "  left join mst_common_attribute_type_values mcat on(mcat.mst_common_attributes_id=mca.id) " +
            "  left join mst_sub_type mst on(mst.mst_type_id=mcat.mst_type_id) " +
            "  where ci.id=:instanceId group by ci.id,mca.attribute_name;")
    List<CompInstAttributesBean> getInstanceMappingAttributes(@Bind("instanceId") int instanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select component_type_name componentTypeName, component_name componentName, component_version_name componentVersionName, component_version_id componentVersionId " +
            "from view_components where component_id =:componentId and component_type_id=:componentTypeId and common_version_id =:commonVersionId ")
    ComponentDetailBean checkIfComponentExists(@Bind("componentId") int componentId, @Bind("componentTypeId") int componentTypeId, @Bind("commonVersionId") int commonVersionId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select concat(cf.relative_path, cf.file_name) attributeValue,pk.id mstProducerKpiMappingId," +
            "pk.producer_id mstProducerId, pk.mst_kpi_details_id mstKpiDetailsId, k.kpi_group_id mstKpiGroupId, g.name kpiGroupName," +
            "cm.default_collection_interval collectionInterval " +
            "from mst_producer_kpi_mapping pk, mst_component_files cf, mst_kpi_details k, mst_kpi_group g," +
            "mst_component_version_kpi_mapping cm, mst_component_version cv " +
            "where pk.mst_component_id = :componentId and cf.mst_component_version_id =:componentVersionId and " +
            "cf.mst_component_id=pk.mst_component_id and pk.mst_component_version_id=cf.mst_component_version_id and " +
            "cf.mst_kpi_details_id =pk.mst_kpi_details_id and pk.is_default=1 and cf.mst_kpi_details_id=k.id " +
            "and k.kpi_group_id = g.id and cv.mst_component_id = cm.mst_common_version_id and " +
            "cv.mst_common_version_id=cm.mst_common_version_id and cm.mst_component_id=pk.mst_component_id and " +
            "cv.id=cf.mst_component_version_id  and cm.mst_kpi_details_id=cf.mst_kpi_details_id and " +
            "pk.mst_kpi_details_id = cm.mst_kpi_details_id and cf.is_discovery = 0")
    List<CompInstanceKpiGroupDetailsBean> getConfigWatchFilesByComponentId(@Bind("componentId") int componentId,
                                                     @Bind("componentVersionId") int componentVersionId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select vc.component_id id, vc.component_name name, vc.is_custom isCustom, vc.component_status status," +
            " mc.created_time createdTime, mc.updated_time updatedTime, mc.user_details_id userDetailsId, mc.account_id accountId," +
            " mc.description description, vc.component_type_name componentTypeName, vc.component_version_name componentVersionName," +
            " vc.component_version_id componentVersionId, vc.component_type_id componentTypeId, vc.common_version_name commonVersionName," +
            " vc.common_version_id commonVersionId from view_components vc, mst_component mc, mst_component_attribute_mapping mcam" +
            " where vc.component_id = mc.id and mc.account_id in (1,:accId) and vc.component_id = mcam.mst_component_id and" +
            " vc.common_version_id = mcam.mst_common_version_id and vc.component_type_id = mcam.mst_component_type_id and" +
            " vc.component_type_status = 1 and vc.component_status = 1 and vc.is_version_status = 1 and vc.component_name = :componentName and" +
            " vc.component_version_name = :componentVersionName")
    List<MasterComponentBean> getComponentDetailsWithNameandVersion(@Bind("accId") int accId, @Bind("componentName") String componentName,
                                                                           @Bind("componentVersionName") String componentVersionName);


    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select vc.component_id id,vc.component_name name,vc.component_type_name componentTypeName, " +
            "vc.component_version_name componentVersionName, vc.component_version_id componentVersionId, " +
            "vc.component_type_id componentTypeId, vc.common_version_name commonVersionName, " +
            "vc.common_version_id commonVersionId from view_components vc, mst_component mc " +
            "where vc.component_id = mc.id and mc.account_id in (1,:accountId) and mc.name=:componentName and " +
            "vc.component_version_name=:componentVersionName and vc.component_type_name=:componentTypeName")
    MasterComponentBean getComponentDetails(@Bind("accountId") int accountId, @Bind("componentName") String componentName, @Bind("componentVersionName") String componentVersionName,
                                                     @Bind("componentTypeName") String componentTypeName);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mst_component_id  mstComponentId, kpi_id  kpiId,kpi_name  kpiName,kpi_identifier  kpiIdentifier, mst_common_version_id  mstCommonVersionId," +
            "kpi_group_id  kpiGroupId, kpi_type_id  kpiTypeId, default_collection_interval  defaultCollectionInterval, status, default_operation_id  defaultOperationId," +
            "default_threshold  defaultThreshold, account_id accountId from view_common_version_kpis where mst_common_version_id = :mstCommonVersionId " +
            "and kpi_group_id =:kpiGroup and kpi_identifier=:kpiIdentifier and account_id in (1, :accountId)")
    ViewCommonVersionKPIsBean getKpisMappedToComponentCommonVersion(@Bind("accountId") int accountId, @Bind("mstCommonVersionId") int mstCommonVersionId, @Bind("kpiGroup") int kpiGroupId, @Bind("kpiIdentifier") String kpiIdentifier);

    //Fetch common version kpi from view
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select DISTINCT mst_component_id as mstComponentId,kpi_id as kpiId,kpi_name as kpiName,kpi_identifier  kpiIdentifier,mst_common_version_id  mstCommonVersionId," +
            "kpi_group_id  kpiGroupId,kpi_type_id  kpiTypeId,default_collection_interval  defaultCollectionInterval,status,default_operation_id  defaultOperationId," +
            "default_threshold  defaultThreshold,account_id  accountId from view_common_version_kpis where mst_common_version_id = :mstCommonVersionId and not kpi_group_id = 0 and account_id in (1, :accountId)")
    List<ViewCommonVersionKPIsBean> getViewCommonVersionGroupKPIsData(@Bind("mstCommonVersionId") int mstCommonVersionId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select tag_key categoryId, object_id kpiId from tag_mapping " +
            "where tag_id=5 and object_ref_table='mst_kpi_details' and account_id in (1, :accountId);")
    List<CategoryKpiMapping> getCategoryByKpi(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT component_id as id, component_type_name as componentTypeName, " +
            "component_version_name as componentVersionName, component_version_id as componentVersionId, " +
            "component_type_id as componentTypeId, common_version_name as commonVersionName," +
            "common_version_id as commonVersionId FROM appsone.view_components " +
            "WHERE component_name = :componentName")
    MasterComponentBean getComponentDetailsByName(@Bind("componentName") String componentName);


    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id as componentAttributeMappingId, mst_common_attributes_id as mstCommonAttributesId " +
            "FROM mst_component_attribute_mapping " +
            "WHERE mst_component_id = :componentId")
    ApplicationInstanceAttributeBean getAttributeMappingByComponentId(@Bind("componentId") int componentId);


}