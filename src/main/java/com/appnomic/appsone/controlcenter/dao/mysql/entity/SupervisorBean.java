package com.appnomic.appsone.controlcenter.dao.mysql.entity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties({"createdTime", "updatedTime", "userId", "status", "accountId"})
public class SupervisorBean {
    private int id;
    private String identifier;
    private String name;
    private int supervisorType;
    private String hostAddress;
    private String hostBoxName;
    private String version;
    private String mode;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private String userId;
    private boolean status;
    private int accountId;
    private int communicationInterval;
    private String previousCommandJobId;
}
