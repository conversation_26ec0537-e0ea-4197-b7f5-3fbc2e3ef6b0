package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.xpt.UpdateBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.NotificationDao;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.UpdateDao;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class UpdateObjectReferenceDataService extends AbstractDaoService<UpdateDao>{
    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateObjectReferenceDataService.class);
    public void updateTransactions(List<UpdateBean> updateBeans, Handle handle) throws DataProcessingException {
        UpdateDao updateDao = null;
        try {
            updateDao = getDaoConnection(handle, UpdateDao.class);
            updateDao.updateTransaction(updateBeans);
        } catch (Exception e) {
            LOGGER.error("Error while updating Transaction . Reason: {}", e.getMessage(), e);
            throw new DataProcessingException("Error while updating Transaction");
        } finally {
            closeDaoConnection(handle, updateDao);
        }
    }
    public void updateRules(List<UpdateBean> updateBeans, Handle handle) throws DataProcessingException {
        UpdateDao updateDao = null;
        try {
            updateDao = getDaoConnection(handle, UpdateDao.class);
            updateDao.updateRules(updateBeans);
        } catch (Exception e) {
            LOGGER.error("Error while updating Transaction . Reason: {}", e.getMessage(), e);
            throw new DataProcessingException("Error while updating Transaction");
        } finally {
            closeDaoConnection(handle, updateDao);
        }
    }
}
