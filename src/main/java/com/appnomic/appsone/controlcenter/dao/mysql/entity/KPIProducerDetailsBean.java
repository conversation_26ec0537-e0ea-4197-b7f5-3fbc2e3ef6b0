package com.appnomic.appsone.controlcenter.dao.mysql.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KPIProducerDetailsBean {

    private int kpiId;
    private int kpiGroupId;
    private int kpiTypeId;
    private String kpiName;
    private String kpiIdentifier;
    private String kpiGroup;
    private int producerId;
    private String producerName;
    private int collectionInterval;
    private int discovery;
    private int mstKpiProducerMappingId;
    private String unit;
    private String dataType;
}
