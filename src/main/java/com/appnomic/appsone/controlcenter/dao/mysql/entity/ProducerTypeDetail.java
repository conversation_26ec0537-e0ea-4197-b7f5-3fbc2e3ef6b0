package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
public abstract class ProducerTypeDetail {

    protected int producerId;
    protected String createdTime;
    protected String updatedTime;
    protected String userDetailsId;

    public ProducerTypeDetail(int producerId, String createdTime, String updatedTime, String userDetailsId) {
        this.producerId = producerId;
        this.createdTime = createdTime;
        this.updatedTime = updatedTime;
        this.userDetailsId = userDetailsId;
    }

    public abstract Map<String, String> getDataMap();
}
