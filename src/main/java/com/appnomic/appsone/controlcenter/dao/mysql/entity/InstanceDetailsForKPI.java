package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InstanceDetailsForKPI {

    private int instanceKPIMappingId;
    private int instanceId;
    private String instanceName;
    private int producerId;
    private String producerName;
    private int collectionInterval;
    private int status;
    private int mstProducerKPIMappingId;
    private int isGroup;
    private int groupKpiId;
    private int severity;
    private int thresholdStatus;

}
