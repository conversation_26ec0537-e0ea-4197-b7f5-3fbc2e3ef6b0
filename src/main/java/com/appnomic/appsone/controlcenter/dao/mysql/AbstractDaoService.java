package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import org.skife.jdbi.v2.Handle;

/**
 * <AUTHOR> on 13/8/20
 */
public class AbstractDaoService<DaoConnection> {

    public DaoConnection getDaoConnection(Handle handle, Class<DaoConnection> dao){
        if(handle == null) {
            return MySQLConnectionManager.getInstance().open(dao);
        }
        else{
            return handle.attach(dao);
        }
    }

    public void closeDaoConnection(Handle handle, DaoConnection dao){
        if(handle == null) {
            MySQLConnectionManager.getInstance().close(dao);
        }
    }
}
