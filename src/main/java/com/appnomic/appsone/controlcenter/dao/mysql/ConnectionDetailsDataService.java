package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.ConnectionDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.ConnectionDetailsDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.heal.configuration.entities.ConnectionBean;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class ConnectionDetailsDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConnectionDetailsDataService.class);

    private ConnectionDetailsDataService(){}

    public static int[] addConnection(List<ConnectionDetailsBean> beanList, Handle handle) {
        ConnectionDetailsDao dao = getConnectionDetailsDao(handle);
        try {
            return dao.addConnection(beanList);
        } catch (Exception e) {
            LOGGER.error("Error occurred while adding the connections err : ", e);

        } finally {
            closeDaoConnection(handle, dao);
        }
        return new int[0];
    }

    public static ConnectionDetailsBean getConnection(int srcId, int destId, int accountId, Handle handle) {
        ConnectionDetailsDao dao = getConnectionDetailsDao(handle);
        try {
            return dao.getConnection(srcId, destId, accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting the connection for srcId:{}, destId:{} err :{}", srcId, destId, e.getMessage());

        } finally {
            closeDaoConnection(handle, dao);
        }
        return null;
    }

    public static List<ConnectionDetailsBean> getConnectionWithAccountId(int accountId, Handle handle) {
        ConnectionDetailsDao dao = getConnectionDetailsDao(handle);
        try {
            return dao.getConnectionWithAccountId(accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting the connection details for accountId [{}]. err :{}", accountId, e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public static List<ConnectionBean> getServiceConnectionBeansForAccount(int accountId, Handle handle) {
        ConnectionDetailsDao dao = getConnectionDetailsDao(handle);
        try {
            return dao.getServiceConnectionBeansForAccount(accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting the connection details for accountId [{}]. err :{}", accountId, e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public static int remConnection(int srcId, int destId, int accountId, Handle handle) {
        ConnectionDetailsDao dao = getConnectionDetailsDao(handle);
        try {
            return dao.remConnection(srcId, destId, accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while removing the connection for srcId:{}, destId:{} err :{}", srcId, destId, e.getMessage());

        } finally {
            closeDaoConnection(handle, dao);
        }
        return -1;
    }

    public static int remConnectionBySourceId(int srcId, int accountId, Handle handle) {
        ConnectionDetailsDao dao = getConnectionDetailsDao(handle);
        try {
            dao.remConnectionBySourceId(srcId, accountId);
            return 0;
        } catch (Exception e) {
            LOGGER.error("Error occurred while removing the connection for srcId:{} err :{}", srcId, e.getMessage());

        } finally {
            closeDaoConnection(handle, dao);
        }
        return -1;
    }

    public static int remConnectionByDestinationId(int destId, int accountId, Handle handle) {
        ConnectionDetailsDao dao = getConnectionDetailsDao(handle);
        try {
            dao.remConnectionByDestinationId(destId, accountId);
            return 0;
        } catch (Exception e) {
            LOGGER.error("Error occurred while removing the connection for destId:{} err :{}", destId, e.getMessage());

        } finally {
            closeDaoConnection(handle, dao);
        }
        return -1;
    }

    private static ConnectionDetailsDao getConnectionDetailsDao(Handle handle){
        if(handle == null){
            return MySQLConnectionManager.getInstance().open(ConnectionDetailsDao.class);
        }
        else{
            return handle.attach(ConnectionDetailsDao.class);
        }
    }
    private static void closeDaoConnection(Handle handle, ConnectionDetailsDao dao){
        if(handle == null){
            MySQLConnectionManager.getInstance().close(dao);
        }
    }
}
