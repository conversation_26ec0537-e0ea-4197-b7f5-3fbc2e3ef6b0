package com.appnomic.appsone.controlcenter.dao.redis;

import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.ParentApplication;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class ParentApplicationRepo {


    private static final String ACCOUNTS_KEY = "/accounts";
    private static final String PARENTAPPLICATIONS_KEY = "/parent_applications";
    private static final String ACCOUNTS_HASH = "ACCOUNTS";
    private static final String PARENTAPPLICATIONS_HASH = "_PARENTAPPLICATIONS";

    public List<ParentApplication> getAllParentApplications(String accountIdentifier) {

        try {
            String parentApplicationDetails = RedisUtilities.getKey(ACCOUNTS_KEY + "/" + accountIdentifier + PARENTAPPLICATIONS_KEY,
                    ACCOUNTS_HASH + "_" + accountIdentifier + PARENTAPPLICATIONS_HASH);
            if (parentApplicationDetails == null) {
                log.debug(" Parent Application details not found for account: [{}]", accountIdentifier);
                return new ArrayList<>();
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(parentApplicationDetails, new TypeReference<List<ParentApplication>>() {
            });
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while getting Parent Applications for accountId:{}.", accountIdentifier, e);
            return new ArrayList<>();
        }
    }

    public ParentApplication getParentApplication(String accountIdentifier, String parentApplicationIdentifier) {

        try {
            String parentApplicationDetails = RedisUtilities.getKey(ACCOUNTS_KEY + "/" + accountIdentifier + PARENTAPPLICATIONS_KEY + "/"
                    + parentApplicationIdentifier, ACCOUNTS_HASH + "_" + accountIdentifier + PARENTAPPLICATIONS_HASH + "_" + parentApplicationIdentifier);
            if (parentApplicationDetails == null) {
                log.debug(" Application details not found for Parent Application Identifier: [{}]", parentApplicationIdentifier);
                return null;
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(parentApplicationDetails, new TypeReference<ParentApplication>() {
            });
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while getting Applications for Parent Application Identifier:{}.", parentApplicationIdentifier, e);
            return null;
        }
    }

    public void updateParentApplication(List<ParentApplication> parentApplications, String accountIdentifier) {
        try {
            RedisUtilities.updateKey(ACCOUNTS_KEY + "/" + accountIdentifier + PARENTAPPLICATIONS_KEY,
                    ACCOUNTS_HASH + "_" + accountIdentifier + PARENTAPPLICATIONS_HASH, parentApplications);
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while updating parent application details for the accountIdentifier: {} ", accountIdentifier, e);
        }
    }

    public void updateParentApplicationByIdentifier(ParentApplication parentApplication, String accountIdentifier) {
        try {
            RedisUtilities.updateKey(ACCOUNTS_KEY + "/" + accountIdentifier + PARENTAPPLICATIONS_KEY + "/" + parentApplication.getIdentifier(),
                    ACCOUNTS_HASH + "_" + accountIdentifier + PARENTAPPLICATIONS_HASH + "_" + parentApplication.getIdentifier(), parentApplication);
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while updating parent application identifier: {} ", parentApplication.getIdentifier(), e);
        }
    }

    public void deleteParentApplicationByIdentifier(String parentApplicationIdentifier, String accountIdentifier) {
        try {
            RedisUtilities.deleteKey(ACCOUNTS_KEY + "/" + accountIdentifier + PARENTAPPLICATIONS_KEY + "/"
                    + parentApplicationIdentifier, ACCOUNTS_HASH + "_" + accountIdentifier + PARENTAPPLICATIONS_HASH + "_" + parentApplicationIdentifier);
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while deleting parent application identifier: {} ", parentApplicationIdentifier, e);
        }
    }
}
