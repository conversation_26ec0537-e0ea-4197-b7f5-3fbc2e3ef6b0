package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.ActionCategoryCommandDetails;
import com.appnomic.appsone.controlcenter.beans.ActionDetails;
import com.appnomic.appsone.controlcenter.beans.CommandArg;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ActionCategoryMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.Actions;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandArgumentBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ComponentAttributesBean;
import com.appnomic.appsone.controlcenter.pojo.ForensicActionsCategoryList;
import com.appnomic.appsone.controlcenter.pojo.ForensicActionsParameters;
import com.appnomic.appsone.controlcenter.pojo.ForensicActionsPojo;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface ActionScriptDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("INSERT INTO actions (name, standard_type_id, account_id, created_time, updated_time, " +
            "user_details_id, agent_type_id, identifier,action_type_id,status) VALUES (:name, :standardType, :accountId, :createdTime, " +
            ":updatedTime, :userDetailsId, :agentType, :identifier, :actionTypeId, :status)")
    @GetGeneratedKeys
    int addActionScript(@BindBean Actions bean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,identifier,standard_type_id standardType,agent_type_id agentType," +
            " created_time createdTime, updated_time updatedTime, user_details_id userDetailsId, account_id accountId" +
            " from actions where account_id in(:accountId,1) and action_type_id=:actionTypeId")
    List<Actions> getActionsByType(@Bind("accountId") Integer accountId,@Bind("actionTypeId") int actionTypeId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(distinct Actions.id)  from actions as Actions, mst_sub_type as MasterSubType"+
            " where MasterSubType.name = 'Forensic'"+
            " and Actions.account_id in (1, :accountId)")
    int getForensicCountForAccount(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO action_category_mapping (category_id, action_id, created_time, updated_time, " +
            "user_details_id, time_window_in_secs, action_exec_type_id, download_type_id, retries, ttl_in_secs, " +
            "object_id, object_ref_table, command_exec_type_id, status) VALUES (:categoryId, :actionId, :createdTime, " +
            ":updatedTime, :userDetailsId, :timeWindowInSecs, :actionExecTypeId, :downloadTypeId, :retries, " +
            ":ttlInSecs, :objectId, :objectRefTable, :commandExecTypeId, :status)")
    @GetGeneratedKeys
    int[] addActionScriptCategoryMapping(@BindBean List<ActionCategoryMapping> list);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,identifier,standard_type_id standardType,agent_type_id agentType, action_type_id actionTypeId," +
            " created_time createdTime, updated_time updatedTime, user_details_id userDetailsId, account_id accountId" +
            " from actions")
    List<Actions> getActionScriptDetails();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from mst_sub_type where name=:actionType")
    int getActionTypeIdByName(@Bind("actionType") String actionType);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,category_id categoryId,action_exec_type_id actionExecTypeId,object_id objectId,object_ref_table objectRefTable," +
            "action_id actionId,time_window_in_secs timeWindowInSecs,command_exec_type_id commandExecTypeId,download_type_id downloadTypeId,retries,ttl_in_secs ttlInSecs, " +
            " created_time createdTime, updated_time updatedTime, user_details_id userDetailsId " +
            "from  action_category_mapping where action_id=:actionId ")
    List<ActionCategoryMapping> getActionCategoryDetails(@Bind("actionId") Integer actionId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, command_id commandId, argument_key argumentKey,argument_value value,default_value defaultValue,argument_value_type_id valueType,argument_type_Id argumentType,is_placeholder isPlaceHolder from command_arguments where command_id=:commandId")
    List<CommandArgumentBean> getActionArgumentsDetails(@Bind("commandId") Integer commandId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, category_id categoryId, action_id actionId, created_time createdTime, updated_time updated" +
            "Time, user_details_id userDetailsId, time_window_in_secs timeWindowInSecs" +
            ", action_exec_type_id actionExecTypeId, download_type_id downloadTypeId, retries, ttl_in_secs ttlInSecs" +
            ", object_id objectId, object_ref_table objectRefTable, command_exec" +
            "_type_id commandExecTypeId FROM action_category_mapping WHERE action_id =:actionId")
    List<ActionCategoryMapping> getActionCategoryMapping(@Bind("actionId") Integer actionId);

    @SqlUpdate("DELETE FROM action_category_mapping WHERE action_id = :actionId")
    void deleteActionCategoryMapping(@Bind("actionId") Integer actionId);

    @SqlUpdate("DELETE FROM actions WHERE id = :actionId")
    void deleteAction(@Bind("actionId") Integer actionId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select CIA.id attributeId,CIA.attribute_name attributeName,CIA.attribute_value attributeValue,CIA.mst_common_attributes_id,MCA.attribute_type attributeType,MCA.status status,MCA.is_custom isCustom from comp_instance_attribute_values CIA,mst_common_attributes MCA where CIA.mst_common_attributes_id=MCA.id and CIA.comp_instance_id=:compInstId")
    List<ComponentAttributesBean> getAttributeListForConfig(@Bind("compInstId") Integer compInstId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select Actions.id id, Actions.name name, \n" +
            "MasterSubType.name type, CmdDetails.name commandName, \n" +
            "CmdDetails.identifier commandIdentifier, CmdDetails.timeout_in_secs commandTimeoutInSeconds,\n" +
            "ACM.ttl_in_secs supCtrlTimeoutInSeconds, ACM.retries supCtrlRetryCount,\n"+
            "Actions.status status, UA.username lastModifiedBy,\n" +
            "Actions.updated_time lastModifiedOn\n" +
            "from actions Actions, mst_sub_type MasterSubType, command_details CmdDetails,\n" +
            "mst_category_details MasterCategoryDetails, action_category_mapping ACM, user_attributes UA\n" +
            "where Actions.action_type_id = 289\n" +
            "and Actions.account_id in (1,:accountId)\n" +
            "and Actions.standard_type_id = MasterSubType.id\n" +
            "and Actions.id = ACM.action_id\n" +
            "and ACM.object_id = CmdDetails.id\n" +
            "and ACM.category_id = MasterCategoryDetails.id\n" +
            "and UA.user_identifier = Actions.user_details_id\n" +
            "group by ACM.action_id \n" +
            "order by Actions.id asc;")
    List<ForensicActionsPojo> getForensicActions(@Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct MCD.id id, MCD.name name, MCD.identifier identifier, MCD.is_custom custom\n" +
            "from mst_category_details MCD, actions Actions, action_category_mapping ACM\n" +
            "where MCD.id = ACM.category_id\n" +
            "and ACM.action_id = :actionId\n" +
            "order by MCD.id asc;")
    List<ForensicActionsCategoryList> getForensicsCategoryList(@Bind("actionId") Integer actionId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct CA.id id, CA.argument_key, CA.argument_value value, \n" +
            "CA.default_value defaultValue, MST.name valueType, \n" +
            "(select name from mst_sub_type where id = CA.argument_type_id) type \n" +
            "from command_arguments CA, mst_sub_type MST \n" +
            "where CA.command_id =1 \n" +
            "and CA.argument_value_type_id = MST.id;")
    List<ForensicActionsParameters> getForensicsParameters(@Bind("actionId") Integer actionId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mcd.id categoryId, mcd.name categoryName, mcd.identifier categoryIdentifier, acm.object_id commandId, acm.action_id actionId, 'Category' as type from " +
            "mst_category_details mcd, action_category_mapping acm where acm.category_id=mcd.id and mcd.account_id in (1,:accountId) and mcd.status=1;")
    List<ActionCategoryCommandDetails> getCategoryKeyMapping(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select CA.command_id id, CA.argument_key `key`, CA.argument_value value, CA.default_value defaultValue, " +
            "CA.is_placeholder isPlaceHolder, ifnull ((select name from mst_sub_type where id = CA.argument_type_id), 'COMMANDLINE') argumentType, " +
            "(select name from mst_sub_type where id = CA.argument_value_type_id) valueType from command_arguments CA;")
    List<CommandArg> getAllCommandArguments(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select a.id id,a.name name,a.identifier identifier, vt.name standardType " +
            "from actions a, view_types vt where vt.subtypeid = a.standard_type_id;")
    List<ActionDetails> getActionScriptDetailsWithStandardType();
}
