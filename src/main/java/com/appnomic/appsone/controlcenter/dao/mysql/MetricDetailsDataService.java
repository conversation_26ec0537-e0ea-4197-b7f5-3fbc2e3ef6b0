package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.CompInstanceKPIDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.InstanceDetailsForKPI;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.KPIProducerDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.MetricDetailsDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.pojo.CompInstClusterDetails;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.MetricGroupAttribute;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class MetricDetailsDataService extends AbstractDaoService<MetricDetailsDao> {

    private static final Logger LOGGER = LoggerFactory.getLogger(MetricDetailsDataService.class);

    public List<CompInstClusterDetails> getCompInstanceDetailsForService(int serviceId, int accountId, Handle handle) throws ControlCenterException {
        MetricDetailsDao dao = getDaoConnection(handle, MetricDetailsDao.class);
        try {
            return dao.getCompInstanceDetailsForService(serviceId, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching component instances for service : {}", serviceId, e);
            throw new ControlCenterException("Exception while fetching component instances for the service.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public List<KPIProducerDetailsBean> getKPIProducerDetailsForComponent(int componentId, int accountId, Handle handle) throws ControlCenterException {
        MetricDetailsDao dao = getDaoConnection(handle, MetricDetailsDao.class);
        try {
            return dao.getKPIProducerDetailsForComponent(componentId, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching KPI Producer details for component : {}", componentId, e);
            throw new ControlCenterException("Exception while fetching KPI Producer details for component.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public List<IdPojo> getProducersForKPI(int componentId, int kpiId, int accountId, Handle handle) throws ControlCenterException {
        MetricDetailsDao dao = getDaoConnection(handle, MetricDetailsDao.class);
        try {
            return dao.getProducersForKPI(componentId, kpiId, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching Producer details for KPI : {}", kpiId, e);
            throw new ControlCenterException("Exception while fetching Producer details for KPI.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public List<InstanceDetailsForKPI> getInstancesForComponentKPI(int componentId, int kpiId, int accountId, Handle handle) throws ControlCenterException {
        MetricDetailsDao dao = getDaoConnection(handle, MetricDetailsDao.class);
        try {
            return dao.getInstancesForComponentKPI(componentId, kpiId, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching mapped instance details for component and non-group KPI : {}", kpiId, e);
            throw new ControlCenterException("Exception while fetching mapped instance details for component and non-group KPI.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public List<InstanceDetailsForKPI> getInstancesForComponentGroupKPI(int componentId, int kpiId, int accountId, Handle handle) throws ControlCenterException {
        MetricDetailsDao dao = getDaoConnection(handle, MetricDetailsDao.class);
        try {
            return dao.getInstancesForComponentGroupKPI(componentId, kpiId, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching mapped instance details for component and group KPI : {}", kpiId, e);
            throw new ControlCenterException("Exception while fetching mapped instance details for component and group KPI.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public int getMstProducerKPIMappingIdForCompInstance(int producerId, int componentId, int instanceId, int kpiId, int accountId, Handle handle) throws ControlCenterException {
        MetricDetailsDao dao = getDaoConnection(handle, MetricDetailsDao.class);
        try {
            return dao.getMstProducerKPIMappingIdForCompInstance(producerId, componentId, instanceId, kpiId, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching mstProducerKPIMappingId For Component Instance - KPI mapping.", e);
            throw new ControlCenterException("Exception while fetching mstProducerKPIMappingId For Component Instance - KPI mapping.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void updateInstanceProducerMappingDetailsForNonGroupKPI(List<CompInstanceKPIDetailsBean> list, Handle handle) throws ControlCenterException {
        MetricDetailsDao dao = getDaoConnection(handle, MetricDetailsDao.class);
        try {
            dao.updateInstanceProducerMappingDetailsForNonGroupKPI(list);
        } catch (Exception e) {
            LOGGER.error("Exception while updating instance producer mapping details for non group KPI : ", e);
            throw new ControlCenterException("Exception while updating instance producer mapping details for non group KPI.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void updateInstanceProducerMappingDetailsForGroupKPI(List<CompInstanceKPIDetailsBean> list, Handle handle) throws ControlCenterException {
        MetricDetailsDao dao = getDaoConnection(handle, MetricDetailsDao.class);
        try {
            dao.updateInstanceProducerMappingDetailsForGroupKPI(list);
        } catch (Exception e) {
            LOGGER.error("Exception while updating instance producer mapping details for group KPI : ", e);
            throw new ControlCenterException("Exception while updating instance producer mapping details for group KPI.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public CompInstanceKPIDetailsBean getDefaultCompInstanceKPIMappingDetails(int componentId, int instanceId, int kpiId, int accountId, Handle handle)
            throws ControlCenterException {
        MetricDetailsDao dao = getDaoConnection(handle, MetricDetailsDao.class);
        try {
            return dao.getDefaultCompInstanceKPIMappingDetails(componentId, instanceId, kpiId, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching default details for Component Instance - KPI mapping.", e);
            throw new ControlCenterException("Exception while fetching default details for Component Instance - KPI mapping.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void addInstanceProducerMappingDetailsForNonGroupKPI(List<CompInstanceKPIDetailsBean> list, Handle handle) throws ControlCenterException {
        MetricDetailsDao dao = getDaoConnection(handle, MetricDetailsDao.class);
        try {
            dao.addInstanceProducerMappingDetailsForNonGroupKPI(list);
        } catch (Exception e) {
            LOGGER.error("Exception while adding instance producer mapping details for non group KPI : ", e);
            throw new ControlCenterException("Exception while adding instance producer mapping details for non group KPI.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void addInstanceProducerMappingDetailsForGroupKPI(List<CompInstanceKPIDetailsBean> list, Handle handle) throws ControlCenterException {
        MetricDetailsDao dao = getDaoConnection(handle, MetricDetailsDao.class);
        try {
            dao.addInstanceProducerMappingDetailsForGroupKPI(list);
        } catch (Exception e) {
            LOGGER.error("Exception while adding instance producer mapping details for group KPI : ", e);
            throw new ControlCenterException("Exception while adding instance producer mapping details for group KPI.");
        }finally {
            closeDaoConnection(handle, dao);
        }
    }

    public List<IdPojo> getNonDiscoveredKPIGroupsForComponentId(int componentId, int accountId, Handle handle) throws ControlCenterException {
        MetricDetailsDao dao = getDaoConnection(handle, MetricDetailsDao.class);
        try {
            return dao.getNonDiscoveredKPIGroupsForComponentId(componentId, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching KPI groups for Component : {}", componentId, e);
            throw new ControlCenterException("Exception while fetching KPI groups for Component.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public Set<MetricGroupAttribute> getGroupAttributesForInstance(int instanceId, int groupId, Handle handle) throws ControlCenterException {
        MetricDetailsDao dao = getDaoConnection(handle, MetricDetailsDao.class);
        try {
            return new HashSet<>(dao.getGroupAttributesForInstance(instanceId, groupId));
        } catch (Exception e) {
            LOGGER.error("Exception while fetching KPI group attributes for Instance : {}", instanceId, e);
            throw new ControlCenterException("Exception while fetching KPI group attributes Instance.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void updateGroupAttributeForInstances(int instanceId, int groupId, String attribute, String newAttribute,
                                                 String aliasName, int status, String timestamp, String userId, Handle handle)
            throws ControlCenterException {
        MetricDetailsDao dao = getDaoConnection(handle, MetricDetailsDao.class);
        try {
            dao.updateGroupAttributeForInstances(instanceId, groupId, attribute,
                    newAttribute, status, timestamp, userId, aliasName);
        } catch (Exception e) {
            LOGGER.error("Exception while updating group attributes for instance.", e);
            throw new ControlCenterException("Exception while updating group attributes for instance.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void deleteGroupAttributeForInstances(int instanceId, int groupId, String attribute, Handle handle)
            throws ControlCenterException {
        MetricDetailsDao dao = getDaoConnection(handle, MetricDetailsDao.class);
        try {
            dao.deleteGroupAttributeForInstances(instanceId, groupId, attribute);
        } catch (Exception e) {
            LOGGER.error("Exception while deleting group attributes for instance.", e);
            throw new ControlCenterException("Exception while deleting group attributes for instance.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public List<KPIProducerDetailsBean> getKPIsForGroupAndForComponent(int componentId, int instanceId, int groupId, int accountId, Handle handle) throws ControlCenterException {
        MetricDetailsDao dao = getDaoConnection(handle, MetricDetailsDao.class);
        try {
            return dao.getKPIsForGroupAndForComponent(componentId, instanceId, groupId, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while getKPIsForGroupAndForComponent : {}", componentId, e);
            throw new ControlCenterException("Exception while getKPIsForGroupAndForComponent.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public Set<Integer> getKpiIdsForGroup(int groupId, int accountId, Handle handle) throws ControlCenterException {
        MetricDetailsDao dao = getDaoConnection(handle, MetricDetailsDao.class);
        try {
            return new HashSet<>(dao.getKpiIdsForGroup(groupId, accountId));
        } catch (Exception e) {
            LOGGER.error("Exception while getKPIsForGroup : {}", groupId, e);
            throw new ControlCenterException("Exception while getKPIsForGroup.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }
    public List<Integer> getCollectionIntervalForGroup(int instanceId, int groupId, Handle handle) throws ControlCenterException {
        MetricDetailsDao dao = getDaoConnection(handle, MetricDetailsDao.class);
        try {
            return dao.getCollectionIntervalForGroup(instanceId, groupId);
        } catch (Exception e) {
            LOGGER.error("Exception while CollectionInterval for group : {}", groupId, e);
            throw new ControlCenterException("Exception while getCollectionIntervalForGroup.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }
}
