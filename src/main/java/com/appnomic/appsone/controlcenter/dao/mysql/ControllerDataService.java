package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.ViewApplicationServiceMappingBean;
import com.appnomic.appsone.controlcenter.beans.ViewClusterServicesBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.TagMappingBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.ControllerDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.pojo.Controller;
import com.appnomic.appsone.controlcenter.pojo.GetApplication.ClusterComponentDetails;
import com.appnomic.appsone.controlcenter.pojo.TagMappingDetails;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ControllerDataService extends AbstractDaoService<ControllerDao> {

    private final Logger LOGGER = LoggerFactory.getLogger(ControllerDataService.class);

    public void editControllerName(int id, String name, String updatedTime, Handle handle) throws ControlCenterException {
        ControllerDao dao = getDaoConnection(handle, ControllerDao.class);
        try {
            dao.editControllerName(name, updatedTime, id);
        } catch (Exception e) {
            LOGGER.error("Error occurred while modifying the name of the controller." + e.getMessage(), e);
            throw new ControlCenterException("Error occurred while modifying the name of controller");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void updateControllerDetails(int id, String user, String updatedTime, Handle handle) {
        ControllerDao dao = getDaoConnection(handle, ControllerDao.class);
        try {
            dao.updateControllerDetails(id, user, updatedTime);
        } catch (Exception e) {
            LOGGER.error("Error occurred while modifying the details of the controller." + e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public int addController(ControllerBean controllerBean, Handle handle) {
        ControllerDao dao = getDaoConnection(handle, ControllerDao.class);
        try {
            return dao.addController(controllerBean);
        } catch (Exception e) {
            LOGGER.error("Error occurred while creating controller", e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return -1;
    }

    public int updateController(ControllerBean controllerBean, Handle handle) {
        ControllerDao dao = getDaoConnection(handle, ControllerDao.class);
        try {
            return dao.updateController(controllerBean);
        } catch (Exception e) {
            LOGGER.error("Error occurred while updating controller", e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return -1;
    }

    public int remController(int accountId, String identifier, String userId, Handle handle) {
        ControllerDao dao = getDaoConnection(handle, ControllerDao.class);
        try {
            dao.remController(identifier, userId, accountId);
            return 0;
        } catch (Exception e) {
            LOGGER.error("Error occurred while creating new tag" + e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return -1;
    }

    public ControllerBean getControllerByIdentifierOrName(String identifier, String name, Handle handle) {
        ControllerDao dao = getDaoConnection(handle, ControllerDao.class);
        try {
            return dao.getControllerByIdentifierOrName(identifier, name);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting controller for identifier:{}, err:", identifier, e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return null;
    }

    public ControllerBean getControllerById(int id, int accountId, Handle handle) {
        ControllerDao dao = getDaoConnection(handle, ControllerDao.class);
        try {
            return dao.getControllerById(id, accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting controller for id:{}, err:", id, e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return null;
    }

    public List<ControllerBean> getControllerByAccountId(int accountId, Handle handle) throws ControlCenterException {
        ControllerDao dao = getDaoConnection(handle, ControllerDao.class);
        try {
            return dao.getControllerByAccountId(accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting controller list for account id:{}, err:", accountId, e);
            throw new ControlCenterException("Error while fetching controller list.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public List<ControllerBean> getAllServicesByAccountId(int accountId, Handle handle) {
        ControllerDao dao = getDaoConnection(handle, ControllerDao.class);
        try {
            return dao.getAllServicesByAccountId(accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting controller for account id:{}, err:", accountId, e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return null;
    }

    public ControllerBean getApplicationsById(int id, int accountId, Handle handle) {
        ControllerDao dao = getDaoConnection(handle, ControllerDao.class);
        try {
            return dao.getApplicationsById(id, accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting application for id:{}, err:", id, e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return null;
    }

    public ControllerBean getServiceByIdentifierAndAccount(String identifier, int accountId, Handle handle) {
        ControllerDao dao = getDaoConnection(handle, ControllerDao.class);
        try {
            return dao.getServiceByIdentifierAndAccount(identifier, accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting controller for identifier:{}, err:", identifier, e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return null;
    }

    public ControllerBean getControllerByIdAndTypeId(int id, int accountId, int typeId, Handle handle) {
        ControllerDao dao = getDaoConnection(handle, ControllerDao.class);
        try {
            return dao.getControllerByIdAndTypeId(id, accountId, typeId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting controller for id:{}, err:", id, e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return null;
    }

    public int getControllerApplicationId(int controllerId, int tagId, int accountId, Handle handle) {
        ControllerDao dao = getDaoConnection(handle, ControllerDao.class);
        try {
            return dao.getControllerApplicationId(controllerId, tagId, accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting Application identifier for controllerId:{}, tagId:{}, " +
                    "accountId:{} err:", controllerId, tagId, accountId, e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return -1;
    }

    public List<ControllerBean> getServicesForAccount(int accountId, Handle handle) throws ControlCenterException {
        ControllerDao controllerDao = getDaoConnection(handle, ControllerDao.class);
        try {
            return controllerDao.getServicesForAccount(accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching services for account [{}]", accountId, e);
            throw new ControlCenterException("Error while fetching services");
        } finally {
            closeDaoConnection(handle, controllerDao);
        }
    }

    public List<ControllerBean> getServices(Handle handle) {
        ControllerDao controllerDao = getDaoConnection(handle, ControllerDao.class);
        try {
            return controllerDao.getServices();
        } catch (Exception e) {
            LOGGER.error("Exception while fetching services from 'controller' table", e);
        } finally {
            closeDaoConnection(handle, controllerDao);
        }
        return new ArrayList<>();
    }

    public List<TagMappingBean> getTagsForService(int serviceId, int accountId, Handle handle) {
        ControllerDao controllerDao = getDaoConnection(handle, ControllerDao.class);
        try {
            return controllerDao.getTagsForService(serviceId, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching services for account from DB", e);
        } finally {
            closeDaoConnection(handle, controllerDao);
        }
        return Collections.emptyList();
    }

    public List<ViewApplicationServiceMappingBean> getApplicationServiceMapping(Integer accountId) {
        ControllerDao controllerDao = getDaoConnection(null, ControllerDao.class);
        try {
            return controllerDao.getApplicationServiceMapping(accountId);
        } catch (Exception e) {
            LOGGER.error("Exception occurred while fetching getApplicationServiceMapping ");
        } finally {
            closeDaoConnection(null, controllerDao);
        }
        return new ArrayList<>();
    }

    public List<ViewApplicationServiceMappingBean> getServicesForApplication(int accountId, String applicationIdentifier) {
        ControllerDao controllerDao = getDaoConnection(null, ControllerDao.class);
        try {
            return controllerDao.getServicesForApplication(accountId, applicationIdentifier);
        } catch (Exception e) {
            LOGGER.error("Exception occurred while fetching getApplicationServiceMapping. Details: {}", e.getMessage(), e);
        } finally {
            closeDaoConnection(null, controllerDao);
        }
        return new ArrayList<>();
    }

    public List<ViewApplicationServiceMappingBean> getServicesForApplicationWithAccID(int accountId) {
        ControllerDao controllerDao = getDaoConnection(null, ControllerDao.class);
        try {
            return controllerDao.getServicesForApplicationWithAccID(accountId);
        } catch (Exception e) {
            LOGGER.error("Exception occurred while fetching getApplicationServiceMapping with account id. Details: {}", e.getMessage(), e);
        } finally {
            closeDaoConnection(null, controllerDao);
        }
        return new ArrayList<>();
    }

    public List<ViewApplicationServiceMappingBean> getApplicationServiceMappingWithAppId(int accountId, int applicationId, Handle handle) {
        ControllerDao controllerDao = getDaoConnection(handle, ControllerDao.class);
        try {
            return controllerDao.getApplicationServiceMappingWithAppId(accountId, applicationId);
        } catch (Exception e) {
            LOGGER.error("Exception occurred while fetching Application Service Mapping with app id and account id. Details: {}", e.getMessage(), e);
        } finally {
            closeDaoConnection(null, controllerDao);
        }
        return new ArrayList<>();
    }

    public List<ViewClusterServicesBean> getClusterServiceMapping(Handle handle) {
        ControllerDao controllerDao = getDaoConnection(handle, ControllerDao.class);
        try {
            return controllerDao.getClusterServiceMapping();
        } catch (Exception e) {
            LOGGER.error("Exception occurred while fetching Cluster Service Mapping. Details: {}", e.getMessage(), e);
        } finally {
            closeDaoConnection(null, controllerDao);
        }
        return new ArrayList<>();
    }

    public List<ClusterComponentDetails> getInstClusterComponentDetailsForService(Handle handle) {
        ControllerDao controllerDao = getDaoConnection(handle, ControllerDao.class);
        try {
            return controllerDao.getInstClusterComponentDetailsForService();
        } catch (Exception e) {
            LOGGER.error("Exception occurred while fetching Instance Cluster Component Details Mapping. Details: {}", e.getMessage(), e);
        } finally {
            closeDaoConnection(null, controllerDao);
        }
        return null;
    }

    List<TagMappingDetails> getRulesForAccount(int accountId) {
        ControllerDao controllerDao = getDaoConnection(null, ControllerDao.class);
        try {
            return controllerDao.getRulesForAccount(accountId);
        } catch (Exception e) {
            LOGGER.error("Exception occurred while fetching getApplicationServiceMapping ");
        } finally {
            closeDaoConnection(null, controllerDao);
        }
        return new ArrayList<>();
    }

    public List<Controller> getApplicationsList(Integer accountId) {
        ControllerDao controllerDao = getDaoConnection(null, ControllerDao.class);
        try {
            return controllerDao.getApplicationsList(accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting controller list" + e.getMessage(), e);
        } finally {
            closeDaoConnection(null, controllerDao);
        }
        return new ArrayList<>();
    }

    public List<Controller> getApplicationIdWithTimeOffset(Integer accountId) {
        ControllerDao controllerDao = getDaoConnection(null, ControllerDao.class);
        try {
            return controllerDao.getApplicationIdWithTimeOffset(accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting controller list" + e.getMessage(), e);
        } finally {
            closeDaoConnection(null, controllerDao);
        }
        return new ArrayList<>();
    }

    public List<Controller> getServicesNotMappedToApplication(int accountId) {
        ControllerDao controllerDao = getDaoConnection(null, ControllerDao.class);
        try {
            return controllerDao.getServicesNotMappedToApplication(accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting controller list" + e.getMessage(), e);
        } finally {
            closeDaoConnection(null, controllerDao);
        }
        return new ArrayList<>();
    }

    public List<Integer> getApplicationIdsForService(int serviceId) {
        ControllerDao controllerDao = getDaoConnection(null, ControllerDao.class);
        try {
            return controllerDao.getApplicationIdsForService(serviceId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting applications for the service. " + e.getMessage(), e);
        } finally {
            closeDaoConnection(null, controllerDao);
        }
        return new ArrayList<>();
    }

    public List<String> getLayers(String type) {
        ControllerDao controllerDao = getDaoConnection(null, ControllerDao.class);
        try {
            return controllerDao.getLayers(type);
        } catch (Exception e) {
            LOGGER.error("Exception while getting layers for the service. " + e.getMessage(), e);
        } finally {
            closeDaoConnection(null, controllerDao);
        }
        return new ArrayList<>();
    }

    public int deleteControllerWithId(int id, Handle handle) {
        ControllerDao controllerDao = getDaoConnection(handle, ControllerDao.class);
        try {
            return controllerDao.deleteControllerWithId(id);
        } catch (Exception e) {
            LOGGER.error("Exception while deleting controller from 'controller' table.", e);
        } finally {
            closeDaoConnection(handle, controllerDao);
        }
        return -1;
    }

    public int deleteApplicationNotificationMappingWithAppId(int id, Handle handle) {
        ControllerDao controllerDao = getDaoConnection(handle, ControllerDao.class);
        try {
            return controllerDao.deleteApplicationNotificationMappingWithAppId(id);
        } catch (Exception e) {
            LOGGER.error("Exception while deleting application-notification-mapping from 'application_notification_mapping' table.", e);
        } finally {
            closeDaoConnection(handle, controllerDao);
        }
        return -1;
    }

    public int deleteUserNotificationMappingWithAppId(int id, Handle handle) {
        ControllerDao controllerDao = getDaoConnection(handle, ControllerDao.class);
        try {
            return controllerDao.deleteUserNotificationMappingWithAppId(id);
        } catch (Exception e) {
            LOGGER.error("Exception while deleting application-notification-mapping from 'application_notification_mapping' table.", e);
        } finally {
            closeDaoConnection(handle, controllerDao);
        }
        return -1;
    }

    public int deleteApplicationPercentilesWithAppId(int id, Handle handle) {
        ControllerDao controllerDao = getDaoConnection(handle, ControllerDao.class);
        try {
            return controllerDao.deleteApplicationPercentilesWithAppId(id);
        } catch (Exception e) {
            LOGGER.error("Exception while deleting application percentiles from 'application_percentiles' table.", e);
        } finally {
            closeDaoConnection(handle, controllerDao);
        }
        return -1;
    }
}
