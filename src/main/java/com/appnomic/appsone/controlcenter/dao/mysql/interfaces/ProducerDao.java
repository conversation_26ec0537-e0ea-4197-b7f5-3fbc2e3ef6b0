package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.*;
import com.appnomic.appsone.controlcenter.pojo.ProducerDetailsPojo;
import com.appnomic.appsone.controlcenter.pojo.ProducerKPIMappingDetailsPojo;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface ProducerDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select script_name scriptName, signature from ssh_producer where mst_producer_id =:producerId")
    SSHProducerTypeDetail getSSHProducerTypeDetail(@Bind("producerId") Integer producerId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select script_name scriptName, signature from shell_producer where mst_producer_id =:producerId")
    WMIProducerTypeDetail getWMIProducerTypeDetail(@Bind("producerId") Integer producerId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select driver jdbcDriver, url jdbcUrl, query_result query_type, query from jdbc_producer " +
            "where mst_producer_id =:producerId")
    JDBCProducerTypeDetail getJDBCProducerTypeDetail(@Bind("producerId") Integer producerId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select json_url httpStatusUrl from http_json_producer where mst_producer_id =:producerId")
    HTTPJsonProducerTypeDetail getHTTPJsonProducerTypeDetail(@Bind("producerId") Integer producerId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select target_object_name targetObjectName, url jmxUrl, attribute_data_type_id " +
            "attributeDataTypeId from jmx_producer " +
            "where mst_producer_id =:producerId")
    JMXProducerTypeDetail getJMXProducerTypeDetail(@Bind("producerId") Integer producerId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select status_url httpStatusUrl from httpd_producer where mst_producer_id =:producerId")
    HTTPProducerTypeDetail getHTTPProducerTypeDetail(@Bind("producerId") Integer producerId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select module webSphereModule, target_object_name targetObjectName " +
            "from was_producer where mst_producer_id =:producerId")
    WASProducerTypeDetail getWASProducerTypeDetail(@Bind("producerId") Integer producerId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, parameter_name name, parameter_value value, parameter_type parameterType, " +
            "parameter_order `order` from producer_parameters where mst_producer_id =:producerId")
    List<ProducerParameter> getProducerParameter(@Bind("producerId") Integer producerId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select producer_id  producerId,producer_name  producerName,is_custom  isCustom," +
            "status,account_id  accountId,is_kpi_group  isKpiGroup,kpi_type  kpiType," +
            "producer_type  producerType,classname,is_default  isDefault," +
            "mst_kpi_details_id  mstKpiDetailsId,mst_component_version_id  mstComponentVersionId," +
            "kpi_name  kpiName,mst_component_id  mstComponentId,mst_component_type_id  " +
            "mstComponentTypeId,mst_producer_kpi_mapping_id  mstProducerKpiMappingId " +
            "from view_producer_kpis where mst_kpi_details_id = :mstKpiDetailsId and " +
            "(account_id=:accountId or account_id=1) and mst_component_version_id =:compVersionId")
    List<ViewProducerKPIsBean> getProducerKPIs(@Bind("accountId") int accountId,
                                               @Bind("mstKpiDetailsId") int mstKpiDetailsId,
                                               @Bind("compVersionId") int compVersionId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, description, is_custom isCustom, status, version, is_deprecated isDeprecated, created_time createdTime, " +
            "updated_time updatedTime, user_details_id userDetailsId, account_id accountId, mst_producer_type_id producerTypeId, " +
            "mst_sub_type_id kpiTypeId, is_kpi_group isKpiGroup from mst_producer where account_id in (1, :accountId) and name=:producerName")
    MasterProducerBean getProducerData(@Bind("accountId") int accountId, @Bind("producerName") String producerName);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mp.id id, mp.name name, mp.description description, mp.is_custom isCustom, mp.status status, mp.created_time createdTime," +
            " mp.updated_time updatedTime, mp.user_details_id userDetailsId, ua.username userName, mp.mst_producer_type_id producerTypeId," +
            " mpt.type producerTypeName, mp.is_kpi_group isKpiGroup from mst_producer mp, user_attributes ua, mst_producer_type mpt where" +
            " mp.user_details_id = ua.user_identifier and  mp.mst_producer_type_id = mpt.id and mp.account_id in (1, :accountId)")
    List<ProducerDetailsPojo> getProducerDetailsWithAccId(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(*) from mst_producer where account_id in (1, :accountId)")
    int getProducerCount(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(*) from mst_producer where account_id in (1, :accountId) and id=:producerId")
    int getProducer(@Bind("accountId") int accountId, @Bind("producerId") int producerId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, mst_producer_type_id producerTypeId, attribute_name attributeName, is_custom isCustom, status, is_mandatory isMandatory, created_time createdTime, " +
            "updated_time updatedTime, user_details_id userDetailsId, default_value defaultValue from mst_producer_attributes where mst_producer_type_id=:producerTypeId")
    List<MasterProducerAttributeBean> getProducerAttributesBasedOnProducerType(@Bind("producerTypeId") int producerTypeId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, type, description, classname, status, created_time createdTime," +
            "updated_time updatedTime, user_details_id userDetailsId, parameter_type_id parameterTypeId ," +
            "producer_table_name producerTableName from mst_producer_type where type=:producerType and account_id in (1, :accountId)")
    MasterProducerTypeBean getProducerTypeData(@Bind("accountId") int accountId, @Bind("producerType") String producerType);

    @SqlUpdate("insert into mst_producer (name, description, is_custom, " +
            "status, version, is_deprecated, created_time, updated_time, " +
            "user_details_id, account_id, mst_producer_type_id, " +
            "mst_sub_type_id, is_kpi_group) values (:name,:description,:isCustom," +
            ":status,:version,:isDeprecated,:createdTime,:updatedTime,:userDetailsId," +
            ":accountId,:producerTypeId,:kpiTypeId,:isKpiGroup)")
    @GetGeneratedKeys
    int addProducerData(@BindBean MasterProducerBean masterProducerBean);

    @SqlUpdate("insert into mst_producer_kpi_mapping (producer_id, " +
            "is_default, created_time, updated_time, user_details_id, " +
            "account_id, mst_kpi_details_id, mst_component_version_id, " +
            "mst_component_id, mst_component_type_id) values (:producerId," +
            ":isDefault,:createdTime,:updatedTime,:userDetailsId,:accountId," +
            ":kpiDetailsId,:componentVersionId,:componentId,:componentTypeId)")
    @GetGeneratedKeys
    int addProducerKpiMapping(@BindBean MasterProducerKpiMappingBean masterProducerKpiMappingBean);

    @SqlUpdate("update mst_producer_kpi_mapping set is_default = 0 where " +
            "mst_kpi_details_id= :kpiId and is_default=1 and " +
            "mst_component_version_id = :componentVersionId and account_id = :accountId and " +
            "mst_component_id = :componentId and mst_component_type_id = :componentTypeId")
    @GetGeneratedKeys
    int resetIsDefaultFlagForProducerKpi(@Bind("kpiId") int kpiId,
                                         @Bind("componentVersionId") int componentVersionId,
                                         @Bind("accountId") int accountId, @Bind("componentId") int componentId,
                                         @Bind("componentTypeId") int componentTypeId);

    @SqlUpdate("update comp_instance_kpi_details set mst_producer_id=:defaultProducerId where " +
            "mst_kpi_details_id = :kpiId and mst_producer_id = :newProducerId")
    @GetGeneratedKeys
    int updateDefaultProducerMapping(@Bind("defaultProducerId") int defaultProducerId, @Bind("kpiId") int kpiId,
                                     @Bind("newProducerId") int newProducerId);

    @SqlUpdate("insert into producer_parameters (mst_producer_id, parameter_type, " +
            "parameter_name, parameter_value, created_time, updated_time, " +
            "user_details_id, parameter_order) values (:producerId,:parameterType,:name,:value," +
            ":createdTime,:updatedTime,:userDetailsId,:parameterOrder)")
    @GetGeneratedKeys
    int addProducerParameter(@BindBean ProducerParameterBean producerParameterBean);

    @SqlUpdate("insert into ssh_producer (mst_producer_id, script_name, created_time, updated_time, user_details_id, " +
            "signature) values (:producerId, :scriptName, :createdTime, :updatedTime, :userDetailsId, :signature)")
    @GetGeneratedKeys
    int addSSHProducerAttributes(@BindBean SSHProducerTypeDetail sshProducerBean);

    @SqlUpdate("insert into shell_producer (mst_producer_id, script_name, created_time, updated_time, user_details_id, " +
            "signature) values (:producerId, :scriptName, :createdTime, :updatedTime, :userDetailsId, :signature)")
    @GetGeneratedKeys
    int addWMIProducerAttributes(@BindBean WMIProducerTypeDetail wmiProducerTypeDetail);

    @SqlUpdate("insert into httpd_producer (status_url, created_time, updated_time, user_details_id, mst_producer_id) " +
            "values (:httpStatusUrl, :createdTime, :updatedTime, :userDetailsId, :producerId)")
    @GetGeneratedKeys
    int addHTTPProducerAttributes(@BindBean HTTPProducerTypeDetail httpProducerTypeDetail);

    @SqlUpdate("insert into jmx_producer (created_time, updated_time, user_details_id, mst_producer_id, url, " +
            "attribute_data_type_id, target_object_name) values (:createdTime, :updatedTime, :userDetailsId, " +
            ":producerId, :jmxUrl, :attributeDataTypeId, :targetObjectName )")
    @GetGeneratedKeys
    int addJMXProducerAttributes(@BindBean JMXProducerTypeDetail jmxProducerTypeDetail);

    @SqlUpdate("insert into was_producer ( created_time, updated_time, user_details_id, mst_producer_id, module, " +
            "target_object_name) values (:createdTime, :updatedTime, :userDetailsId, :producerId, :webSphereModule, " +
            ":targetObjectName )")
    @GetGeneratedKeys
    int addWASProducerAttributes(@BindBean WASProducerTypeDetail wasProducerTypeDetail);

    @SqlUpdate("insert into jdbc_producer ( created_time, updated_time, user_details_id, mst_producer_id, driver, url, " +
            "query_result, is_query_encrypted, query) values (:createdTime, :updatedTime, :userDetailsId, :producerId," +
            " :jdbcDriver, :jdbcUrl, :query_type, :isQueryEncrypted, :query)")
    @GetGeneratedKeys
    int addJDBCProducerAttributes(@BindBean JDBCProducerTypeDetail jdbcProducerTypeDetail);

    @SqlUpdate("insert into http_json_producer ( created_time, updated_time, user_details_id, mst_producer_id, " +
            "json_url) values (:createdTime, :updatedTime, :userDetailsId, :producerId, :httpStatusUrl)")
    @GetGeneratedKeys
    int addHTTPJSONProducerAttributes(@BindBean HTTPJsonProducerTypeDetail httpJsonProducerTypeDetail);

    @SqlUpdate("insert into jppf_producer (created_time, updated_time, user_details_id, mst_producer_id, " +
            "jppf_type_id) values (:createdTime, :updatedTime, :userDetailsId, :producerId, :jppfTypeId)")
    @GetGeneratedKeys
    int addJPPFProducerAttributes(@BindBean JPPFProducerTypeDetail jppfProducerTypeDetail);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mpkm.producer_id producerId, mkd.name kpiName, mct.name componentTypeName, mc.name componentName," +
            " mcv.name componentVersionName from (((mst_producer_kpi_mapping mpkm join mst_component mc on mpkm.mst_component_id = mc.id)" +
            " join mst_component_version mcv on mpkm.mst_component_version_id = mcv.id) join mst_component_type mct on" +
            " mpkm.mst_component_type_id = mct.id) join mst_kpi_details mkd on mpkm.mst_kpi_details_id = mkd.id where" +
            " mpkm.account_id in (1, :accountId)")
    List<ProducerKPIMappingDetailsPojo> getProducerKPIMappingDetails(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mst_producer_id producerId, script_name scriptName, created_time createdTime, " +
            "updated_time updatedTime, user_details_id userDetailsId, signature from ssh_producer " +
            "where mst_producer_id = :producerId")
    SSHProducerTypeDetail getSSHAttribute(@Bind("producerId") Integer producerId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select type from mst_producer_type where id = :producerId")
    String getProducerTypeById(@Bind("producerId") Integer producerId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mp.id from mst_producer mp, mst_producer_type mpt where mpt.id=mst_producer_type_id and mpt.type=:producerTypeId and mp.mst_sub_type_id=:kpiTypeId and mp.is_kpi_group=:isGroup")
    int getProducerByTypeId(@Bind("producerTypeId") String producerTypeId, @Bind("kpiTypeId") int kpiTypeId, @Bind("isGroup") int isGroup);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct producer_id producerId, is_default isDefault from mst_producer_kpi_mapping where " +
            "mst_kpi_details_id in (select id from mst_kpi_details where kpi_group_id = :groupKpiId) and account_id in " +
            "(1, :accountId)")
    List<MasterProducerKpiMappingBean> getProducerIdsForKpiGroupId(@Bind("groupKpiId") Integer groupKpiId,
                                                                   @Bind("accountId") Integer accountId);

    @SqlBatch("insert into mst_producer_kpi_mapping (producer_id, is_default, created_time, updated_time, user_details_id, " +
            "account_id, mst_kpi_details_id, mst_component_version_id, mst_component_id, mst_component_type_id) values " +
            "(:producerId,:isDefault,:createdTime,:updatedTime,:userDetailsId,:accountId,:kpiDetailsId,:componentVersionId," +
            ":componentId,:componentTypeId)")
    void addProducerKpiMappings(@BindBean List<MasterProducerKpiMappingBean> masterProducerKpiMappingBean);

    //below queries are for integration test cases
    @SqlUpdate("DELETE FROM mst_producer_kpi_mapping WHERE producer_id = :id")
    void removeProducerMapping(@Bind("id") int producerId);

    @SqlUpdate("DELETE FROM producer_parameters WHERE mst_producer_id = :id")
    void removeProducerParameter(@Bind("id") int mstProducerId);

    @SqlUpdate("DELETE FROM ssh_producer WHERE mst_producer_id = :id")
    void removeSSHProducerType(@Bind("id") int mstProducerId);

    @SqlUpdate("DELETE FROM shell_producer WHERE mst_producer_id = :id")
    void removeWMIProducerType(@Bind("id") int mstProducerId);

    @SqlUpdate("DELETE FROM httpd_producer WHERE mst_producer_id = :id")
    void removeHTTPDProducerType(@Bind("id") int mstProducerId);

    @SqlUpdate("DELETE FROM jmx_producer WHERE mst_producer_id = :id")
    void removeJMXProducerType(@Bind("id") int mstProducerId);

    @SqlUpdate("DELETE FROM was_producer WHERE mst_producer_id = :id")
    void removeWASProducerType(@Bind("id") int mstProducerId);

    @SqlUpdate("DELETE FROM jdbc_producer WHERE mst_producer_id = :id")
    void removeJDBCProducerType(@Bind("id") int mstProducerId);

    @SqlUpdate("DELETE FROM http_json_producer WHERE mst_producer_id = :id")
    void removeHTTPjsonProducerType(@Bind("id") int mstProducerId);

    @SqlUpdate("DELETE FROM jppf_producer WHERE mst_producer_id = :id")
    void removeJPPFProducerType(@Bind("id") int mstProducerId);

    @SqlUpdate("DELETE FROM mst_producer WHERE id = :id")
    void removeProducer(@Bind("id") int pId);

    @SqlUpdate("INSERT INTO mst_producer_type (type, description, classname, status, created_time," +
            " updated_time, user_details_id, producer_table_name, account_id) VALUES (:type," +
            " :description, :classname, :status, :createdTime, :updatedTime, :userDetailsId, :producerTableName, :accountId)")
    @GetGeneratedKeys
    int insertDummyProducerType(@BindBean MasterProducerTypeBean masterProducerTypeBean, @Bind("accountId") int accountId);

    @SqlUpdate("DELETE FROM mst_producer_type WHERE id = :id")
    void removeProducerType(@Bind("id") int producerTypeId);

    @SqlUpdate("INSERT INTO mst_producer_attributes (mst_producer_type_id, attribute_name, is_custom, status, is_mandatory, " +
            "created_time, updated_time, user_details_id, default_value) VALUES (:producerTypeId,:attributeName,:isCustom,:status," +
            ":isMandatory,:createdTime,:updatedTime,:userDetailsId,:defaultValue)")
    void insertDummyProducerAttributes(@BindBean MasterProducerAttributeBean masterProducerAttributeBean);

    @SqlUpdate("DELETE FROM mst_producer_attributes WHERE mst_producer_type_id = :id")
    void removeProducerAttribute(@Bind("id") int producerTypeId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mpkm.producer_id producerId, mkd.name kpiName, mct.name componentTypeName, mc.name componentName, mpkm.is_default isDefault," +
            " mcv.name componentVersionName from (((mst_producer_kpi_mapping mpkm join mst_component mc on mpkm.mst_component_id = mc.id)" +
            " join mst_component_version mcv on mpkm.mst_component_version_id = mcv.id) join mst_component_type mct on" +
            " mpkm.mst_component_type_id = mct.id) join mst_kpi_details mkd on mpkm.mst_kpi_details_id = mkd.id where" +
            " mpkm.account_id in (1, :accountId) and producer_id =:producerId")
    List<ProducerKPIMappingDetailsPojo> getProducerKPIMappingDetails(@Bind("accountId") int accountId, @Bind("producerId") int producerId);

}