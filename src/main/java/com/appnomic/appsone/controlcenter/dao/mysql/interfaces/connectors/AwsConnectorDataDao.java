package com.appnomic.appsone.controlcenter.dao.mysql.interfaces.connectors;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.*;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface AwsConnectorDataDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO dataadapter_aws.aws_credentials (id, region, access_key_id, secret_access_key) " +
            "VALUES (:id, :region, :accessKeyId, :secretKeyAccess);")
    void addAwsCredential(@BindBean List<AwsCredentialDetailBean> awsCredentialDetailBeans);

    @SqlUpdate("delete from dataadapter_aws.aws_credentials")
    void deleteAwsCredential();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO dataadapter_aws.ec2_instances (id, instance_id, detailed_monitoring_enabled)" +
            " VALUES (:id, :instanceId, :enableDetailMonitoring);")
    void addAwsInstances(@BindBean List<AwsInstanceBean> awsInstanceBeans);

    @SqlUpdate("delete from dataadapter_aws.ec2_instances")
    void deleteAwsInstances();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO `dataadapter_aws`.`metric_details` (`id`, `metric_name`, `metric_namespace`," +
            " `metric_stat`, `metric_unit`, `is_detailed_metric`) " +
            "VALUES (:id, :awsMetricName, :awsMetricNamespace, :awsMetricStat, :awsMetricUnit, :isDetailedMatric);")
    void addAwsMatrics(@BindBean List<AwsMetricBean> awsMetricBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from dataadapter_aws.metric_details")
    List<Integer> getAwsMetrics();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO dataadapter_aws.logs_details (id, log_group_name, log_stream_name, " +
            "log_pattern, date_pattern)" +
            " VALUES (:id, :logGroupName, :logStreamName, :logPattern, :datePattern);")
    void addLogsDetails(@BindBean List<AwsLogsBean> awsLogsBeans);

    @SqlUpdate("delete from dataadapter_aws.logs_details")
    void deleteLogsDetails();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO `dataadapter_aws`.`log_kpi_details`" +
            " (`id`, `log_group_name`, `log_stream_name`, `filter_pattern`) " +
            "VALUES (:id, :logGroupName, :logStreamName, :filterPattern);")
    void addLogKpis(@BindBean List<AwsLogKpiBean> awsLogKpiBeans);

    @SqlUpdate("delete from dataadapter_aws.log_kpi_details")
    void deleteLogKpis();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO dataadapter_aws.dimensions (id, dimension_key, dimension_value) " +
            "VALUES (:id, :dimensionKey, :dimensionValue);")
    void addDimension(@BindBean List<AwsDimensionBean> awsDimensionBeans);

    @SqlUpdate("delete from dataadapter_aws.dimensions")
    void deleteDimension();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO dataadapter_aws.credentials_instance_mapping" +
            " (credentials_id, instance_details_id) " +
            "VALUES (:credentialId, :instanceId);")
    int[] addCredentialInstanceMappings(@BindBean List<AwsCredentialInstanceMapping> awsCredentialInstanceMappings);

    @SqlUpdate("delete from dataadapter_aws.credentials_instance_mapping")
    void deleteCredentialInstanceMappings();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO dataadapter_aws.ci_metrics_mapping" +
            " (ci_id, metrics_id) " +
            "VALUES (:credentialId, :metricId);")
    void addCredentialMatricMappings(@BindBean List<AwsCredentialMetricMapping> awsCredentialMatricMappings);

    @SqlUpdate("delete from dataadapter_aws.ci_metrics_mapping")
    void deleteCredentialMetricMappings();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO dataadapter_aws.credentials_logs_details_mapping" +
            " (credentials_id, logs_details_id) " +
            "VALUES (:credentialId, :logsId);")
    void addCredentialLogsMappings(@BindBean List<AwsCredentialLogsMapping> awsCredentialLogsMappings);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO dataadapter_aws.credentials_log_kpi_mapping" +
            " (credentials_id, log_kpi_id) " +
            "VALUES (:credentialId, :logKpiId);")
    void addCredentialLogKpiMappings(@BindBean List<AwsCredentialLogKpiMapping> awsCredentialLogKpiMappings);

    @SqlUpdate("delete from dataadapter_aws.credentials_log_kpi_mapping")
    void deleteCredentialLogKpiMappings();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO dataadapter_aws.logs_details_instance_mapping" +
            " (logs_details_id, instance_id) " +
            "VALUES (:logsId, :instanceId);")
    void addLogsInstanceMappings(@BindBean List<AwsLogsInstanceMapping> awsLogsInstanceMappings);

    @SqlUpdate("delete from dataadapter_aws.logs_details_instance_mapping")
    void deleteLogsInstanceMappigs();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO dataadapter_aws.log_kpi_instance_mapping" +
            " (log_kpi_id, instance_id) " +
            "VALUES (:logKpiId, :instanceId);")
    void addLogKpiInstanceMappings(@BindBean List<AwsLogKpiInstanceMapping> awsLogKpiInstanceMappings);

    @SqlUpdate("delete from dataadapter_aws.log_kpi_instance_mapping")
    void deleteLogKpiInstanceMappings();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO dataadapter_aws.metric_dimensions_mapping " +
            "(instance_id, metric_id, dimension_id) " +
            "VALUES (:instanceId, :metricId, :dimensionId);")
    void addMetricDimensionMappings(@BindBean List<AwsMetricDimensionMapping> awsMatricDimensionMappings);


    @SqlUpdate("delete from dataadapter_aws.metric_dimensions_mapping")
    void deleteMetricDimensionMappings();

}
