package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.SchedulersDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.heal.configuration.entities.ScheduledJobArguments;
import com.heal.configuration.entities.ScheduledJobDetails;
import com.heal.configuration.entities.SchedulerArguments;
import com.heal.configuration.entities.SchedulerDetails;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.Handle;

@Slf4j
public class SchedulersDataService extends AbstractDaoService<SchedulersDao> {

    public void updateScheduledJobStatus(int scheduledJobId, String jobStatus) throws ControlCenterException {
        SchedulersDao dao = getDaoConnection(null, SchedulersDao.class);
        try {
            dao.updateScheduledJobStatus(scheduledJobId, jobStatus);
        } catch (Exception e) {
            log.error("Exception while updating status [{}] of scheduledJob ID [{}]", jobStatus, scheduledJobId, e);
            throw new ControlCenterException("Error while updating scheduled job status");
        } finally {
            closeDaoConnection(null, dao);
        }
    }

    public int insertSchedulerDetails(SchedulerDetails schedulerDetails, Handle handle) throws ControlCenterException {
        SchedulersDao dao = getDaoConnection(handle, SchedulersDao.class);
        try {
            return dao.insertSchedulerDetails(schedulerDetails);
        } catch (Exception e) {
            log.error("Exception while inserting scheduler details into 'scheduler_details' table.", e);
            throw new ControlCenterException("Exception while inserting scheduler details into 'scheduler_details' table.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void insertSchedulerArguments(SchedulerArguments schedulerArguments, Handle handle) throws ControlCenterException {
        SchedulersDao dao = getDaoConnection(handle, SchedulersDao.class);
        try {
            dao.insertSchedulerArguments(schedulerArguments);
        } catch (Exception e) {
            log.error("Exception while inserting scheduler arguments into 'scheduler_arguments' table.", e);
            throw new ControlCenterException("Exception while inserting scheduler arguments into 'scheduler_arguments' table.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public int insertSchedulerJobDetails(ScheduledJobDetails schedulerJobDetails, Handle handle) throws ControlCenterException {
        SchedulersDao dao = getDaoConnection(handle, SchedulersDao.class);
        try {
            return dao.insertSchedulerJobDetails(schedulerJobDetails);
        } catch (Exception e) {
            log.error("Exception while inserting scheduler job details into 'scheduler_job_details' table.", e);
            throw new ControlCenterException("Exception while inserting scheduler job details into 'scheduler_job_details' table.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void insertSchedulerJobArguments(ScheduledJobArguments scheduledJobArguments, Handle handle) throws ControlCenterException {
        SchedulersDao dao = getDaoConnection(handle, SchedulersDao.class);
        try {
            dao.insertSchedulerJobArguments(scheduledJobArguments);
        } catch (Exception e) {
            log.error("Exception while inserting scheduler job arguments into 'scheduler_job_argument' table.", e);
            throw new ControlCenterException("Exception while inserting scheduler job arguments into 'scheduler_job_arguments' table.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }
}
