package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.LicenseInfoDao;
import com.appnomic.appsone.controlcenter.pojo.*;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class LicenseDetailService extends AbstractDaoService<LicenseInfoDao> {
    private final Logger LOGGER = LoggerFactory.getLogger(LicenseDetailService.class);

    public List<LicenseInfo> getLicenseInfoDetails(Handle handle) {
        LicenseInfoDao licenseInfoDao = getDaoConnection(handle, LicenseInfoDao.class);
        try {
            return licenseInfoDao.getLicenseInfoDetails();
        } catch (Exception e){
            LOGGER.error("Error occurred while getting license info details, err {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, licenseInfoDao);
        }
        return null;
    }
}
