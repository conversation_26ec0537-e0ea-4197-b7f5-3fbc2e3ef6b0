package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.ViewApplicationServiceMappingBean;
import com.appnomic.appsone.controlcenter.beans.ViewClusterServicesBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.TagMappingBean;
import com.appnomic.appsone.controlcenter.pojo.Controller;
import com.appnomic.appsone.controlcenter.pojo.GetApplication.ClusterComponentDetails;
import com.appnomic.appsone.controlcenter.pojo.TagMappingDetails;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface ControllerDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("Update controller set name = :name, updated_time = :updatedTime where id =:id")
    int editControllerName(@Bind("name") String name, @Bind("updatedTime") String updatedTime, @Bind("id") int id);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("Update controller set user_details_id = :userId, updated_time = :updatedTime" +
            " where id =:id")
    void updateControllerDetails(@Bind("id") int id, @Bind("userId") String userId, @Bind("updatedTime") String updatedTime);

    @SqlUpdate("INSERT INTO controller ( name, identifier, account_id, user_details_id, created_time, updated_time, controller_type_id) VALUES ( :name, :identifier, :accountId, :userDetailsId, :createdTime, :updatedTime, :controllerTypeId)")
    @GetGeneratedKeys
    int addController(@BindBean ControllerBean controllerBean);

    @SqlUpdate("Update controller set name = :name, user_details_id = :userDetailsId, updated_time = :updatedTime, status = :status where identifier = :identifier and account_id = :accountId")
    @GetGeneratedKeys
    int updateController(@BindBean ControllerBean controllerBean);

    @SqlUpdate("update controller set status = 0, user_details_id = :userId where identifier = :identifier  and account_id = :accountId")
    void remController(@Bind("identifier") String identifier, @Bind("userId") String userId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, identifier, account_id as accountId, user_details_id as userDetailsId, created_time as createdTime, updated_time as updatedTime, controller_type_id as controllerTypeId, status from controller where identifier = :identifier or name = :name")
    ControllerBean getControllerByIdentifierOrName(@Bind("identifier") String identifier, @Bind("name") String name);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select tm.object_id from tag_mapping tm join controller c on tm.tag_key = c.id and tm.account_id = c.account_id where tm.account_id = :accountId and c.id = :controllerId and tm.tag_id = :tagId and tm.object_ref_table = 'controller';")
    int getControllerApplicationId(@Bind("controllerId") int controllerId, @Bind("tagId") int tagId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, identifier, account_id as accountId, user_details_id as userDetailsId, created_time as createdTime, updated_time as updatedTime, controller_type_id as controllerTypeId, status from controller where id = :id and account_id = :accountId")
    ControllerBean getControllerById(@Bind("id") int id, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, identifier, account_id as accountId, user_details_id as userDetailsId, created_time as createdTime, " +
            "updated_time as updatedTime, controller_type_id as controllerTypeId, status from controller where account_id = :accountId")
    List<ControllerBean> getControllerByAccountId(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, identifier, account_id as accountId, user_details_id as userDetailsId," +
            " created_time as createdTime, updated_time as updatedTime, controller_type_id as" +
            " controllerTypeId, status from controller where controller_type_id = 192 and" +
            " account_id = :accountId")
    List<ControllerBean> getAllServicesByAccountId(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, identifier, account_id accountId, user_details_id userDetailsId, created_time createdTime, updated_time updatedTime, " +
            "controller_type_id controllerTypeId, status from controller where identifier = :identifier and account_id = :accountId and status=1")
    ControllerBean getServiceByIdentifierAndAccount(@Bind("identifier") String identifier, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, identifier, account_id as accountId, user_details_id as userDetailsId, created_time as createdTime, updated_time as updatedTime, controller_type_id as controllerTypeId, status from controller where id = :id and account_id = :accountId and controller_type_id = :typeId")
    ControllerBean getControllerByIdAndTypeId(@Bind("id") int id, @Bind("accountId") int accountId, @Bind("typeId") int typeId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id id,name name,controller_type_id controllerTypeId,identifier identifier from controller " +
            "where account_id in(:accountId,1) and controller_type_id=192")
    List<ControllerBean> getServicesForAccount(@Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,identifier,account_id as accountId, user_details_id as userDetailsId, created_time as createdTime," +
            " updated_time as updatedTime, controller_type_id as controllerTypeId, status from controller " +
            "where controller_type_id=192")
    List<ControllerBean> getServices();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select tag_id tagId, tag_key tagKey, tag_value tagValue from tag_mapping " +
            "where object_id=:serviceId and account_id=:accountId and object_ref_table='controller'")
    List<TagMappingBean> getTagsForService(@Bind("serviceId") int serviceId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id appId,name name,controller_type_id controllerTypeId,identifier identifier, " +
            "status status,user_details_id createdBy,created_time createdOn, updated_time updatedTime ,account_id accountId " +
            "from controller where account_id = :account_id and status = 1 and controller_type_id = 191")
    List<Controller> getApplicationsList(@Bind("account_id") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, identifier, account_id as accountId, user_details_id as userDetailsId, created_time as createdTime," +
            " updated_time as updatedTime, controller_type_id as controllerTypeId, status from controller" +
            " where id = :id and account_id = :accountId and controller_type_id = 191")
    ControllerBean getApplicationsById(@Bind("id") int id, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select object_id appId, tag_value timeOffset from tag_mapping where account_id = :account_id and tag_id=2 " +
            "and object_ref_table='controller'")
    List<Controller> getApplicationIdWithTimeOffset(@Bind("account_id") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select application_id applicationId, application_name applicationName, application_identifier applicationIdentifier, " +
            "service_id serviceId, service_name serviceName, service_identifier serviceIdentifier, account_id accountId " +
            "from view_application_service_mapping where account_id = :account_id")
    List<ViewApplicationServiceMappingBean> getApplicationServiceMapping(@Bind("account_id") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select service_id serviceId, service_name serviceName, service_identifier serviceIdentifier, " +
            "application_id applicationId, application_name applicationName, application_identifier applicationIdentifier " +
            "from view_application_service_mapping " +
            "where account_id = :account_id and application_identifier =:applicationIdentifier")
    List<ViewApplicationServiceMappingBean> getServicesForApplication(@Bind("account_id") Integer accountId, @Bind("applicationIdentifier") String applicationIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select service_id serviceId, service_name serviceName, service_identifier serviceIdentifier, " +
            "application_id applicationId, application_name applicationName, application_identifier applicationIdentifier " +
            "from view_application_service_mapping " +
            "where account_id = :account_id")
    List<ViewApplicationServiceMappingBean> getServicesForApplicationWithAccID(@Bind("account_id") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select application_id applicationId, application_name applicationName, application_identifier applicationIdentifier, " +
            "service_id serviceId, service_name serviceName, service_identifier serviceIdentifier, account_id accountId " +
            "from view_application_service_mapping where account_id = :accountId and application_id =:applicationId")
    List<ViewApplicationServiceMappingBean> getApplicationServiceMappingWithAppId(@Bind("accountId") int accountId, @Bind("applicationId") int applicationId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id clusterId,name clusterName,identifier clusterIdentifier,host_cluster_id hostClusterId,mst_component_id mstComponentId," +
            " mst_component_type_id mstComponentTypeId,mst_component_version_id mstComponentVersionId,service_id serviceId," +
            " service_name serviceName,service_identifier serviceIdentifier from view_cluster_services")
    List<ViewClusterServicesBean> getClusterServiceMapping();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select vc.id, vc.name, vc.identifier, vci.mst_component_id componentId," +
            " vci.component_name componentName, vci.mst_component_type_id componentTypeId," +
            " vci.component_type_name componentTypeName, vci.mst_component_version_id" +
            " componentVersionId, vci.component_version_name componentVersionName," +
            " vci.common_version_id commonVersionId, vci.common_version_name commonVersionName," +
            " vc.service_id serviceId, vc.service_name serviceName, vc.service_identifier" +
            " serviceIdentifier from view_cluster_services vc, view_component_instance vci" +
            " where vc.id = vci.id")
    List<ClusterComponentDetails> getInstClusterComponentDetailsForService();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select object_id objectId, object_ref_table objectRefTable, tag_value tagValue, tag_key tagKey, tag_id tagId from tag_mapping where account_id = :account_id and tag_id=1 " +
            "and object_ref_table='rules'")
    List<TagMappingDetails> getRulesForAccount(@Bind("account_id") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct c.id appId, c.name name, c.controller_type_id controllerTypeId, c.identifier identifier, " +
            "c.status status, c.user_details_id createdBy, c.created_time createdOn, c.updated_time updatedTime, " +
            "c.account_id accountId from controller c where c.account_id = :accountId  and controller_type_id = 192 and " +
            "c.status=1 and id not in (Select tag_key from tag_mapping where tag_id = 1 and object_ref_table = 'controller')")
    List<Controller> getServicesNotMappedToApplication(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select application_id from view_application_service_mapping where service_id = :serviceId")
    List<Integer> getApplicationIdsForService(@Bind("serviceId") Integer serviceId);

    @SqlQuery("select distinct mst.name from mst_type mt join mst_sub_type mst on(mt.id = mst_type_id) where mt.type = :type")
    List<String> getLayers(@Bind("type") String type);

    @SqlUpdate("DELETE FROM application_notification_mapping where application_id = :id")
    int deleteApplicationNotificationMappingWithAppId(@Bind("id") int id);

    @SqlUpdate("DELETE FROM user_notification_mapping where application_id = :id")
    int deleteUserNotificationMappingWithAppId(@Bind("id") int id);

    @SqlUpdate("DELETE FROM application_percentiles where application_id = :id")
    int deleteApplicationPercentilesWithAppId(@Bind("id") int id);

    @SqlUpdate("DELETE FROM controller where id = :id")
    int deleteControllerWithId(@Bind("id") int id);

}
