package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.pojo.EmailTemplate;
import lombok.Data;

@Data
public class EmailTemplateBean {

    private int id;
    private String userDetailsId;
    private String subject;
    private String body;
    private boolean status;
    private String name;
    private int templateTypeId;
    private String toAddress;
    private String ccAddress;
    private String bccAddress;
    private String templateStatus;


    public EmailTemplate getEmailTemplate(){
        String templateType = MasterCache.getMstSubTypeForSubTypeId(
                this.templateTypeId).getSubTypeName();
        return EmailTemplate.builder()
                .id(this.id)
                .userId(this.userDetailsId)
                .subject(this.subject)
                .body(this.body)
                .status(this.status)
                .name(this.name)
                .templateTypeId(this.templateTypeId)
                .templateType(templateType)
                .toAddress(this.toAddress)
                .ccAddress(this.ccAddress)
                .bccAddress(this.bccAddress)
                .templateStatus(this.templateStatus)
                .build();

    }
}
