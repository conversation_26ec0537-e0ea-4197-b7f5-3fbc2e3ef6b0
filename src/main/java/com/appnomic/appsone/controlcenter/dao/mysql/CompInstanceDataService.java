package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CompInstanceControllerTag;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.TagMappingBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.ComponentInstanceDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.heal.configuration.entities.InstanceMetadataBean;
import com.heal.configuration.pojos.InstanceAttributes;
import com.heal.configuration.pojos.InstanceMetadata;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class CompInstanceDataService extends AbstractDaoService<ComponentInstanceDao> {
    private final Logger LOGGER = LoggerFactory.getLogger(CompInstanceDataService.class);

    public int addAgentCompInstMapping(AgentCompInstMappingBean agentCompInstMappingBean, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.addAgentCompInstMapping(agentCompInstMappingBean);
        } catch (Exception e) {
            LOGGER.error("Exception while creating component instance agent mapping-" + e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int[] addAgentCompInstMapping(List<AgentCompInstMappingBean> agentCompInstMappingBean, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.addAgentCompInstMapping(agentCompInstMappingBean);
        } catch (Exception e) {
            LOGGER.error("Exception while creating component instance agent mapping-" + e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return null;
    }

    public int addAgentAccountMapping(AgentAccountMappingBean agentAccountMappingBean, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.addAgentAccountMapping(agentAccountMappingBean);
        } catch (Exception e) {
            LOGGER.error("Exception while creating account- agent mapping-" + e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int addComponentInstanceAttributes(CompInstanceAttributesBean compInstanceAttributesBean, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return dao.addAttributesForComponentInstance(compInstanceAttributesBean);
        } catch (Exception e) {
            LOGGER.error("Exception while adding attributes for component instance-" + compInstanceAttributesBean.getCompInstanceId() + ":", e);
        }
        closeDaoConnection(handle, dao);
        return -1;
    }

    public int updateComponentInstanceAttributes(CompInstanceAttributesBean compInstanceAttributesBean, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return dao.updateAttributesForComponentInstance(compInstanceAttributesBean);
        } catch (Exception e) {
            LOGGER.error("Exception while updating attributes for component instance-" + compInstanceAttributesBean.getCompInstanceId() + ":", e);
        }
        closeDaoConnection(handle, dao);
        return -1;
    }

    public int addNonGroupComponentInstanceKPI(CompInstanceKpiDetailsBean compInstanceKpiDetailsBean, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return dao.addNonGroupCompInstanceKpiDetails(compInstanceKpiDetailsBean);
        } catch (Exception e) {
            LOGGER.error("Exception while adding non group kpi for component instance-" + compInstanceKpiDetailsBean.getCompInstanceId() + ":" + e.getMessage(), e);
        }
        closeDaoConnection(handle, dao);
        return -1;
    }
    public List<InstanceAttributes> getInstanceAttributeDetailsForCompInstanceId(int accountId, int compInstId, Handle handle){
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return dao.getInstanceAttributesForCompInstanceId(accountId, compInstId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting Instance attributes for account-{}, instance-{}", accountId, compInstId, e);
        }finally {
            closeDaoConnection(handle, dao);
        }
        return Collections.emptyList();
    }

    public int[] addMultipleNonGroupComponentInstanceKPI(List<CompInstanceKpiDetailsBean> compInstanceKpiDetailsBeans, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return dao.addMultipleNonGroupCompInstanceKpiDetails(compInstanceKpiDetailsBeans);
        } catch (Exception e) {
            LOGGER.error("Error while adding component instances to non-group KPI mapping", e);
        }
        closeDaoConnection(handle, dao);
        return null;
    }

    public int addGroupComponentInstanceKPI(CompInstanceKpiGroupDetailsBean compInstanceKpiGroupDetailsBean, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            if (compInstanceKpiGroupDetailsBean.getAliasName() == null || compInstanceKpiGroupDetailsBean.getAliasName().trim().isEmpty()) {
                compInstanceKpiGroupDetailsBean.setAliasName(compInstanceKpiGroupDetailsBean.getAttributeValue());
            }
            return dao.addGroupCompInstanceKpiDetails(compInstanceKpiGroupDetailsBean);
        } catch (Exception e) {
            LOGGER.error("Exception while adding group kpi for component instance-" + compInstanceKpiGroupDetailsBean.getCompInstanceId() + ":" + e.getMessage(), e);
        }
        closeDaoConnection(handle, dao);
        return -1;
    }

    public int[] addMultipleGroupCompInstanceKpiDetails(List<CompInstanceKpiGroupDetailsBean> compInstanceKpiGroupDetailsBeans, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            compInstanceKpiGroupDetailsBeans.parallelStream()
                    .forEach(bean -> {
                        if (bean.getAliasName() == null || bean.getAliasName().trim().isEmpty()) {
                            bean.setAliasName(bean.getAttributeValue());
                        }
                    });
            return dao.addMultipleGroupCompInstanceKpiDetails(compInstanceKpiGroupDetailsBeans);
        } catch (Exception e) {
            LOGGER.error("Error while adding component instances to group KPI mapping", e);
        }
        closeDaoConnection(handle, dao);
        return null;
    }

    public void updateInstanceHostIds(List<ComponentInstanceBean> componentInstanceBeanList, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            dao.updateInstanceHostIds(componentInstanceBeanList);
        } catch (Exception e) {
            LOGGER.error("Exception while updating instance host Ids. Details: {}", componentInstanceBeanList, e);
        } finally {
            closeDaoConnection(handle, dao);
        }
    }


    public ComponentInstanceBean getComponentInstanceBean(String identifier, String compInstanceName, int accountId, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return dao.getComponentInstance(identifier, compInstanceName, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting component instanceIdentifier:{}, instanceName:{}, accountId:{}", identifier, compInstanceName, accountId, e);
        } finally {
            closeDaoConnection(handle, dao);
        }

        return null;
    }

    public ComponentInstanceBean getComponentInstanceByIdAndAccount(int instanceId, int accountId) {
        ComponentInstanceDao dao = getDaoConnection(null, ComponentInstanceDao.class);
        try {
            return dao.getComponentInstanceById(instanceId, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting component instance ids -" + e.getMessage(), e);
        } finally {
            closeDaoConnection(null, dao);
        }

        return null;
    }

    public ComponentInstanceBean getComponentInstanceByIdAndIdentifierAndAccount(int instanceId, String identifier, int accountId) {
        ComponentInstanceDao dao = getDaoConnection(null, ComponentInstanceDao.class);
        try {
            return dao.getComponentInstanceByIdAndIdentifierAndAccount(instanceId, identifier, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting component instance ids -" + e.getMessage(), e);
        } finally {
            closeDaoConnection(null, dao);
        }

        return null;
    }

    public ComponentKpiDetail getComponentDetails(int compId) {
        ComponentInstanceDao componentInstanceDao = MySQLConnectionManager.getInstance().open(ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getComponentDetails(compId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while fetching component details compId{}", compId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(componentInstanceDao);
        }
        return new ComponentKpiDetail();
    }

    public ComponentInstanceBean getActiveComponentInstanceByIdentifierAndName(String identifier, String name, int accountId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getActiveComponentInstanceByIdentifierAndName(identifier, name, accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting component instance by identifier and name. identifier: {} acc id:{} err: {}", identifier, accountId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return null;
    }

    public ComponentInstanceBean getActiveComponentInstance(String identifier, String name, int accountId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getActiveComponentInstance(identifier, name, accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting component instance by identifier. identifier: {} acc id:{} err: {}", identifier, accountId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return null;
    }

    public ComponentInstanceBean getComponentInstanceByIdentifier(String identifier, int accountId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getComponentInstanceByIdentifier(identifier, accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting component instance by identifier. identifier: {} acc id:{} err: {}", identifier, accountId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return null;
    }

    public ComponentInstanceBean getComponentInstanceByName(String name, int accountId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getComponentInstanceByName(name, accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting component instance by name. name: {} acc id:{} err: {}", name, accountId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return null;
    }

    public int getComponentInstanceIdByComponentServiceCluster(int componentId, int componentVersionId, int componentTypeId, int serviceId, int tagId, int accountId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getComponentInstanceIdByComponentServiceCluster(componentId, componentVersionId, componentTypeId, serviceId, tagId, accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting component instance service cluster for componentId:{}, componentVersionId:{}, componentTypeId:{}, acc id:{} err: {}", componentId, componentVersionId, componentTypeId, accountId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int getClusterId(int componentId, int commonVersionId, int componentTypeId, int serviceId, int tagId, int accountId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getClusterId(componentId, commonVersionId, componentTypeId, serviceId, tagId, accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting cluster id componentId:{}, commonVersionId:{}, componentTypeId:{}, acc id:{} err: {}", componentId, commonVersionId, componentTypeId, accountId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int getComponentInstanceIdByAttributeServiceCluster(int componentId, String attributeName, String attributeValue, int serviceId, int tagId, int accountId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getComponentInstanceIdByAttributeServiceCluster(componentId, attributeName, attributeValue, serviceId, tagId, accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting component instance attribute service cluster for attribute name:{}, acc id:{} err: {}", attributeName, accountId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public TagMappingBean getComponentInstanceTagMappingDetails(int instanceId, int tagId, int accountId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getComponentInstanceTagMappingDetails(instanceId, tagId, accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting component instance service identifier for instanceId:{}, " +
                    "tagId:{}, acc id:{}", instanceId, tagId, accountId, e);
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return null;
    }

    public int addComponentInstance(ComponentInstanceBean bean, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            int id = componentInstanceDao.addComponentInstance(bean);

            InstanceMetadataBean metadata = InstanceMetadataBean.builder()
                    .id(id)
                    .userDetailsId(bean.getUserDetailsId())
                    .createdTime(bean.getCreatedTime())
                    .updatedTime(bean.getUpdatedTime())
                    .environmentId(383)
                    .status(1)
                    .build();

            int instanceMetadataId = componentInstanceDao.addInstanceMetadata(metadata);
            LOGGER.debug("Inserted instance metadata for instanceId:{}, metadataId:{}", id, instanceMetadataId);
            return id;

        } catch (Exception e) {
            LOGGER.error("Error occurred while adding component instance for component name:{}, acc id:{} err: {}", bean.getName(), bean.getAccountId(), e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int updateComponentInstance(ComponentInstanceBean bean, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.updateComponentInstance(bean);
        } catch (Exception e) {
            LOGGER.error("Error occurred while updating component instance for component name:{}, acc id:{} err: {}", bean.getName(), bean.getAccountId(), e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int editComponentInstance(String instanceName, int id, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.editComponentInstance(instanceName, id);
        } catch (Exception e) {
            LOGGER.error("Error occurred while updating instance name [{}]", instanceName, e);
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int addCompClusterMapping(CompClusterMappingBean bean, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.addCompClusterMapping(bean);
        } catch (Exception e) {
            LOGGER.error("Error occurred while adding component instance cluster for component id:{}, acc id:{} err: {}", bean.getCompInstanceId(), bean.getAccountId(), e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public CompClusterMappingBean getCompClusterDetails(String instanceIdentifier, String instanceName, int accountId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getCompClusterDetails(instanceIdentifier, instanceName, accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting component instance cluster details for instance identifier:{}, " +
                    "instanceName:{}, accountId:{}", instanceIdentifier, instanceName, accountId, e);
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return null;
    }

    public int updateComponentInstanceStatusById(int id, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            componentInstanceDao.updateComponentInstanceStatusById(id);
            return 0;
        } catch (Exception e) {
            LOGGER.error("Error occurred while inactivating instance with id: {} err: {}", id, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int deleteCompInstanceKpiGroupDetailsWithInstId(int instanceId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteCompInstanceKpiGroupDetailsWithInstId(instanceId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting group KPIs details related to instance with id: {} err: {}", instanceId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int deleteCompInstanceKpiDetailsWithInstId(int instanceId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteCompInstanceKpiDetailsWithInstId(instanceId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting KPIs details related to instance with id: {} err: {}", instanceId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int deleteJimAgentMappingWithInstId(int instanceId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteJimAgentMappingWithInstId(instanceId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting JIM agent mapping details related to instance with id: {} err: {}", instanceId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int deleteJimTransactionMappingWithInstId(int instanceId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteJimTransactionMappingWithInstId(instanceId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting JIM transaction mapping details related to instance with id: {} err: {}", instanceId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int deleteAgentCompInstanceMappingWithInstId(int instanceId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteAgentCompInstanceMappingWithInstId(instanceId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting agent-instance mapping details related to instance with id: {} err: {}", instanceId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int deleteCompInstanceMaintenanceMappingWithInstId(int instanceId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteCompInstanceMaintenanceMappingWithInstId(instanceId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting instance-maintenance mapping details related to instance with id: {} err: {}", instanceId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int deleteCompInstanceAttributeValuesWithInstId(int instanceId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteCompInstanceAttributeValuesWithInstId(instanceId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting attribute details related to instance with id: {} err: {}", instanceId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int deleteCompInstanceKpiThresholdDetailsWithInstId(int instanceId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteCompInstanceKpiThresholdDetailsWithInstId(instanceId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting kpi threshold details related to instance with id: {} err: {}", instanceId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int deleteCompInstanceForensicDetailsWithInstId(int instanceId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteCompInstanceForensicDetailsWithId(instanceId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting instance forensic details related to instance with id: {} err: {}", instanceId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int deleteCompInstanceHealthWithInstId(int instanceId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteCompInstanceHealthWithId(instanceId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting component instance health details related to instance with id: {} err: {}", instanceId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int deleteCompInstanceKpisHealthWithInstId(int instanceId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteCompInstanceKpisHealthWithId(instanceId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting component instance kpis health details related to instance with id: {} err: {}", instanceId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int deleteCompInstanceKpiConfigurationWithInstId(int instanceId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteCompInstanceKpiConfigurationWithId(instanceId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting component instance kpi configuration details related to instance with id: {} err: {}", instanceId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int deleteComponentClusterMappingWithInstId(int instanceId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteComponentClusterMappingWithInstId(instanceId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting component-cluster mapping related to instance with id: {} err: {}", instanceId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int checkInstExistanceForCluster(int clusterId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.checkInstExistanceForCluster(clusterId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while checking instances mapped or not to cluster with id: {} err: {}", clusterId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int deleteComponentClusterMappingWithClusterId(int clusterId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteComponentClusterMappingWithClusterId(clusterId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting component-cluster mapping related to cluster with id: {} err: {}", clusterId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int deleteTagMappingWithClusterId(int clusterId, int tagId, String table, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteTagMappingWithClusterId(clusterId, tagId, table);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting tag-mapping details related to cluster with id: {} err: {}", clusterId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int deleteTagMappingWithClusterIdandService(int clusterId, int tagId, String table, int serviceId, String serviceIdentifier, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteTagMappingWithClusterIdandService(clusterId, tagId, table, serviceId, serviceIdentifier);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting tag-mapping details related to cluster with id: {} and service {} err: {}", clusterId, serviceIdentifier, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int deleteCompInstanceWithInstId(int instanceId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteCompInstanceWithInstId(instanceId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting instance with id: {} err: {}", instanceId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public List<AttributesViewBean> getAttributeViewDataByComponentAndCommonVersion(int mstComponentId, int mstCommonVersionId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getAttributeViewDataByComponentAndCommonVersion(mstComponentId, mstCommonVersionId);
        } catch (Exception e) {
            LOGGER.error("Error while getting component attributes data", e);
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return null;
    }

    public List<CompInstanceKpiDetailsBean> getDefaultCompInstanceKPIsData(int componentId, int mstCommonVersionId, int mstComponentVersionId, int mstComponentTypeId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getDefaultCompInstanceKPIsData(componentId, mstCommonVersionId, mstComponentVersionId, mstComponentTypeId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting non group data from view common version and producer kpis table", e);
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return null;
    }

    public List<CompInstanceKpiGroupDetailsBean> getDefaultCompInstanceGroupKPIsData(int componentId, int mstCommonVersionId, int mstComponentVersionId, int mstComponentTypeId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getDefaultCompInstanceGroupKPIsData(componentId, mstCommonVersionId, mstComponentVersionId, mstComponentTypeId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting group data from view common version and producer kpis table", e);
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return null;
    }

    public String getCompInstanceIdentifierFromId(int compInstanceId, int accountId, Handle handle) throws ControlCenterException {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getCompInstanceIdentifierFromId(compInstanceId, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting component instance identifier for compInstanceId [{}]", compInstanceId, e);
            throw new ControlCenterException("Error while fetching component instance identifier");
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
    }

    public List<Integer> getCompInstanceIdsUsingComponentDetails(int componentId, int componentTypeId, int commonVersionId, int accountId, Handle handle) throws ControlCenterException {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getCompInstanceIdsUsingComponentDetails(componentId, componentTypeId, commonVersionId, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching component instance Ids for component [{}], componentType [{}], commonVersion [{}] and account [{}]", componentId, componentTypeId, commonVersionId, accountId, e);
            throw new ControlCenterException("Error while fetching component instance IDs");
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
    }

    public List<CompInstClusterDetails> getCompInstanceDetailsForService(int serviceId, int accountId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getCompInstanceDetailsForService(serviceId, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching component instances mapped to serviceId: {}", serviceId, e);
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return new ArrayList<>();
    }

    public Set<Integer> getAgentIdForService(int serviceId, int accountId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getAgentIdForService(serviceId, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching component instances mapped to serviceId: {}", serviceId, e);
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return new HashSet<>();
    }

    public Set<Integer> getAgentIdForServiceFromTagMapping(int serviceId, int accountId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getAgentIdForServiceFromTagMapping(serviceId, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching component instances mapped to serviceId: {}", serviceId, e);
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return new HashSet<>();
    }

    public Set<TagMappingBean> getServiceVsAgentIdsFromTagMapping(int accountId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getServiceVsAgentIdsFromTagMapping(accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching component instances mapped to serviceId", e);
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return new HashSet<>();
    }

    public List<CompInstanceControllerTag> getControllersMappedToCompInstance(int accountId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getControllersMappedToCompInstance(accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching component instances mapped to serviceId", e);
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return new ArrayList<>();
    }

    public void updateEnvDetails(CompInstanceEnvDetailsPojo data, int accId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            componentInstanceDao.updateComponentInstanceEnvDetails(data, accId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while updating component instance env details for component name:{}, acc id:{} err: {}", data.getIdentifier(), accId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
    }

    public int getComponentInstanceEnvDetails(String identifier, Handle handle) throws ControlCenterException {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getComponentInstanceEnvDetails(identifier);
        } catch (Exception e) {
            LOGGER.error("Error occurred while fetching component instance env details for component name:{}. Reason: ", identifier, e);
            throw new ControlCenterException("Error while fetching component instance env details");
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
    }

    public List<HostDetailsBean> getHostTypeFromHostAddress(int accountId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getDetailsForAgents(accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching host details for instances", e);
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return null;
    }

    public List<ComponentInstanceBean> getHostInstance(int accountId, int componentTypeId, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return dao.getHostInstance(accountId, componentTypeId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting host instances from 'comp_instance' table : {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public List<CompInstanceAttributesBean> getInstAttributeMapping(String attributeName, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return dao.getInstAttributeMapping(attributeName);
        } catch (Exception e) {
            LOGGER.error("Exception while getting attribute details for instance: {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public int updateInstanceName(int id, String identifier, String name, String updateTime, String userId, int accountId, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return dao.updateInstanceName(name, updateTime, userId, id, identifier, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while updating instance name in comp_instance table: {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return -1;
    }

    public List<MasterSubTypeBean> getAgentTypes(Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getAgentTypes();
        } catch (Exception e) {
            LOGGER.error("Exception while fetching agent types:-", e);
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return new ArrayList<>();
    }

    public List<Integer> getCompInstIsByAgent(int agentId) {
        ComponentInstanceDao dao = getDaoConnection(null, ComponentInstanceDao.class);
        try {
            return dao.getAgentCompInstMapping(agentId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting agent comp inst mapping data, agentId:{} ", agentId, e);
        } finally {
            closeDaoConnection(null, dao);
        }
        return Collections.emptyList();
    }

    public List<CountBean> getActiveInstanceCountForComponents(int accountId, Handle handle) throws ControlCenterException {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getActiveInstanceCountForComponents(accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching component instance count for components.", e);
            throw new ControlCenterException("Exception while fetching component instance count for components.");
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
    }

    public List<Integer> getInstanceIdsForComponent(int componentId, int accountId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.getInstanceIdsForComponent(componentId, accountId);
        } catch (Exception e) {
            LOGGER.error("Error while fetching component instance ids for component.", e);
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return new ArrayList<>();
    }

    public int updateInstanceEnvDetails(int instanceId, String instanceIdentifier, int environment, String updateTime,
                                        String userId, int accId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.updateInstanceEnvDetails(environment, updateTime, userId, instanceId, instanceIdentifier, accId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while updating component instance env details for component name:{}, acc id:{} err: {}", instanceIdentifier,
                    accId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int deleteComponentClusterMapping(int instanceId, int clusterId, int accountId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteComponentClusterMapping(instanceId, clusterId, accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting instance-cluster mapping for instance id:{}, cluster id:{} acc id:{} err: {}",
                    instanceId, clusterId, accountId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public List<InstanceClusterServicePojo> getInstanceClusterServiceDetails(int serviceId, int accountId, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return dao.getInstanceClusterServiceDetails(serviceId, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting host instance and cluster and service details : {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public int checkHostAddressExistance(int accountId, String hostAddress, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return dao.checkHostAddressExistance(accountId, hostAddress);
        } catch (Exception e) {
            LOGGER.error("Exception while getting host address existance : {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return -1;
    }

    public int checkHostAddressMonitorPortExistance(int accountId, String hostAddress, String monitorPort, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return dao.checkHostAddressMonitorPortExistance(accountId, hostAddress, monitorPort);
        } catch (Exception e) {
            LOGGER.error("Exception while getting host address existance : {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return -1;
    }

    public List<InstanceClusterServicePojo> getClusterServiceDetails(int serviceId, int componentTypeId, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return dao.getClusterServiceDetails(serviceId, componentTypeId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting host cluster and service details : {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public List<ViewComponentInstanceBean> getActiveInstanceDetailsForAccount(int accountId, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return dao.getActiveInstanceDetailsForAccount(accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting host instance details : {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public List<ViewClusterServicesBean> getAllClusterServices(Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return dao.getAllClusterServices();
        } catch (Exception e) {
            LOGGER.error("Exception while getting cluster, service details : {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public List<ViewApplicationServiceMappingBean> getAllServiceApplication(int accId, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return dao.getAllServiceApplication(accId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting service, application details : {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public int deleteInstance(int instanceId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteInstance(instanceId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting instance from comp_instance where instance id:{}, err: {}",
                    instanceId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int activeLicenseHostsCount(int accountId, int status, int mstComponentTypeId, int isCluster, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.activeLicenseHostsCount(accountId, status, mstComponentTypeId, isCluster);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting count of active license hosts from comp_instance, err: {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public int[] addAgentMappingToInstance(List<AgentCompInstMappingBean> agentCompInstMappingBeans, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.addAgentMappingToInstance(agentCompInstMappingBeans);
        } catch (Exception e) {
            LOGGER.error("Exception while adding agent mapping to instance.", e);
            return null;
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
    }

    public int[] deleteAgentMappingFromInstance(List<AgentCompInstMappingBean> agentCompInstMappingBeans, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteAgentMappingFromInstance(agentCompInstMappingBeans);
        } catch (Exception e) {
            LOGGER.error("Exception while deleting agent mapping from instance.", e);
            return null;
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
    }
    public int getCompInsGroupKpiIdUsingKpiId(int kpiId, int groupKpiId, int compInstanceId,  Handle conn) throws ControlCenterException {
        ComponentInstanceDao dao = getDaoConnection(conn, ComponentInstanceDao.class);
        try {
            return dao.getCompInsKpiIdForGroupKpi(kpiId, groupKpiId, compInstanceId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting comp_instance_kpi_group_details id : {}", kpiId, e);
            throw new ControlCenterException("Exception while comp_instance_kpi_group_details.");
        } finally {
            closeDaoConnection(conn, dao);
        }
    }
    public int getCompInsGroupKpiIdUsingKpiId(int kpiId, int compInstanceId,  Handle conn) throws ControlCenterException {
        ComponentInstanceDao dao = getDaoConnection(conn, ComponentInstanceDao.class);
        try {
            return dao.getCompInsKpiIdForNonGroupKpi(kpiId, compInstanceId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting comp_instance_kpi_details for kpiId : {}", kpiId, e);
            throw new ControlCenterException("Exception while comp_instance_kpi_details.");
        } finally {
            closeDaoConnection(conn, dao);
        }
    }

    public List<CompInstKpiMapping> getCompInstKpiMapping(int instanceId,Handle conn) throws ControlCenterException{
        ComponentInstanceDao dao = getDaoConnection(conn, ComponentInstanceDao.class);
        try{
            return dao.getCompInstKpiMapping(instanceId);
        }catch (Exception e){
            LOGGER.error("Error while fetching component instance kpi mapping details for instanceId [{}]", instanceId);
            throw new ControlCenterException("Error while fetching component instance kpi mapping details for instanceId [{}]", String.valueOf(instanceId));
        } finally {
            closeDaoConnection(conn, dao);
        }
    }

    public int deleteCompInstanceMetadataWithInstId(int instanceId, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.deleteCompInstanceMetadataWithInstId(instanceId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting instance metadata with id: {} err: {}", instanceId, e.getMessage());
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return -1;
    }

    public IdPojo getCompInstanceNameAndIdentifierByAccountId(String instanceIdentifier, int accountId, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return dao.getCompInstanceNameAndIdentifierByAccountId(instanceIdentifier, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting component instances for accountId: {}", accountId, e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return null;
    }

    public int[] updateInstanceMetadata(List<InstanceMetadata> instanceMetadata, Handle handle) {
        ComponentInstanceDao componentInstanceDao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return componentInstanceDao.updateInstanceMetadata(instanceMetadata);
        } catch (Exception e) {
            LOGGER.error("Error occurred while updating instanceMetadata", e);
            return new int[1];
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
    }

    public int getInstanceDetailsByHostAddress(int accountId, String hostAddress, int isDR, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return dao.getInstanceDetailsByHostAddress(accountId, hostAddress, isDR);
        } catch (Exception e) {
            LOGGER.error("Exception while getting instance details for host address {}. Details: {}", hostAddress, e.getMessage());
            return -1;
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public int getEnvTypeIdFromTypeName(String environmentType, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return dao.getEnvTypeIdFromTypeName(environmentType);
        } catch (Exception e) {
            LOGGER.error("Exception while getting type details of type {}. Details: {}", environmentType, e.getMessage());
            return 0;
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public List<ObjPojo> getEnvSubTypeDetails(int typeId, Handle handle) {
        ComponentInstanceDao dao = getDaoConnection(handle, ComponentInstanceDao.class);
        try {
            return dao.getEnvSubTypeDetails(typeId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting sub type details for type id {}. Details: {}",typeId, e.getMessage());
            return null;
        } finally {
            closeDaoConnection(handle, dao);
        }
    }
}
