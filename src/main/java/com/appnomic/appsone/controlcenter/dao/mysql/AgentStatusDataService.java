package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.AgentBean;
import com.appnomic.appsone.controlcenter.beans.CommandBeans;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.AgentDao;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.AgentStatusDao;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandTriggerBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.PhysicalAgentBean;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.Date;
import java.util.List;

public class AgentStatusDataService {
    private AgentStatusDataService(){}
    private static final Logger logger = LoggerFactory.getLogger(AgentStatusDataService.class);
    public static List<AgentBean> getCompAgent(){

        AgentStatusDao agentStatusDao =
                MySQLConnectionManager.getInstance().getHandle().open(AgentStatusDao.class);
        try {
            return agentStatusDao.getCompAgentDetails();
        }catch (Exception e){
            logger.error("Error occurred while getting component details" + e.getMessage(), e);
        }
        finally {
            MySQLConnectionManager.getInstance().close(agentStatusDao);
        }
        return Collections.emptyList();
    }

    public static List<Integer> getCompAgentMappings(int agentId){
        AgentStatusDao agentStatusDao = MySQLConnectionManager.getInstance().getHandle().open(AgentStatusDao.class);
        try {
            return agentStatusDao.getCompAgentMappings(agentId);
        }catch (Exception e){
            logger.error("Error occurred while getting component agent mapping details" + e.getMessage(), e);
            return Collections.emptyList();
        } finally {
            MySQLConnectionManager.getInstance().close(agentStatusDao);
        }
    }

    public static List<AgentBean> getAgentCompDetails(int compInstanceId){

        AgentDao agentStatusDao =
                MySQLConnectionManager.getInstance().getHandle().open(AgentDao.class);
        try {
            return agentStatusDao.getAgentCompDetails(compInstanceId);
        }catch (Exception e){
            logger.error("Error occurred while getting agent command details" + e.getMessage(), e);
        }
        finally {
            MySQLConnectionManager.getInstance().close(agentStatusDao);
        }
        return Collections.emptyList();
    }

    public static CommandTriggerBean getAgentCommandTriggerStatus(int physicalAgentId,String commandJobId) {
        AgentStatusDao agentStatusDao = MySQLConnectionManager.getInstance().getHandle().open(AgentStatusDao.class);
        try {
            return agentStatusDao.getAgentCommandTriggerStatus(physicalAgentId, commandJobId);
        } catch (Exception e) {
            logger.error("Error occurred while getting command trigger details" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(agentStatusDao);
        }
        return null;
    }

    public static List<PhysicalAgentBean> getPhysicalAgentsForOngoingCommand(){
        AgentStatusDao agentStatusDao = MySQLConnectionManager.getInstance().getHandle().open(AgentStatusDao.class);
        try {
            return agentStatusDao.getPhysicalAgentsForOngoingCommand();
        }catch (Exception e){
            logger.error("Error occurred while getting component execute details" + e.getMessage(), e);
            return Collections.emptyList();
        } finally {
            MySQLConnectionManager.getInstance().close(agentStatusDao);
        }
    }
    public static String getAccountIdentifier(int physicalAgentId){
        AgentStatusDao agentStatusDao = MySQLConnectionManager.getInstance().getHandle().open(AgentStatusDao.class);
        try {
            return agentStatusDao.getAccountIdentifier(physicalAgentId);
        }catch (Exception e){
            logger.error("Error occurred while getting component execute details" + e.getMessage(), e);
            return null;
        } finally {
            MySQLConnectionManager.getInstance().close(agentStatusDao);
        }
    }
    public static List<String> getSupervisorIdentifier(String hostAddress){

        AgentStatusDao agentStatusDao =
                MySQLConnectionManager.getInstance().getHandle().open(AgentStatusDao.class);
        try {
            return agentStatusDao.getSupervisorIdentifier(hostAddress);
        }catch (Exception e){
            logger.error("Error occurred while getting command trigger details" + e.getMessage(), e);
        }
        finally {
            MySQLConnectionManager.getInstance().close(agentStatusDao);
        }
        return Collections.emptyList();
    }
    public static String getValidCommandJobId(String commandJobId){

        AgentStatusDao agentStatusDao =
                MySQLConnectionManager.getInstance().getHandle().open(AgentStatusDao.class);
        try {
            return agentStatusDao.validCommandJob(commandJobId);
        }catch (Exception e){
            logger.error("Error occurred while getting command job id" + e.getMessage(), e);
        }
        finally {
            MySQLConnectionManager.getInstance().close(agentStatusDao);
        }
        return "";
    }
    public static int getValidCommandId(int commandId){
        AgentStatusDao agentStatusDao =
                MySQLConnectionManager.getInstance().getHandle().open(AgentStatusDao.class);
        try {
            return agentStatusDao.validCommandId(commandId);
        }catch (Exception e){
            logger.error("Error occurred while getting command  id" + e.getMessage(), e);
        }
        finally {
            MySQLConnectionManager.getInstance().close(agentStatusDao);
        }
        return 0;
    }
    public static CommandBeans getAgentCommandStatus(int agentId,String commandJobId){

        AgentStatusDao agentStatusDao =
                MySQLConnectionManager.getInstance().getHandle().open(AgentStatusDao.class);
        try {
            return agentStatusDao.getAgentCommandStatus(agentId,commandJobId);
        }catch (Exception e){
            logger.error("Error occurred while getting command arguments details" + e.getMessage(), e);
        }
        finally {
            MySQLConnectionManager.getInstance().close(agentStatusDao);
        }
        return null;
    }

    private static AgentStatusDao getComponentDaoObject(Handle handle){
        if(handle == null){
            return MySQLConnectionManager.getInstance().open(AgentStatusDao.class);
        }
        else{
            return handle.attach(AgentStatusDao.class);
        }
    }
    private static void closeDaoConnection(Handle handle, AgentStatusDao dao){
        if(handle == null){
            MySQLConnectionManager.getInstance().close(dao);
        }
    }
    public static int addCommandTrigger(CommandTriggerBean commandTriggerBean, Handle handle) {
        AgentStatusDao agentStatusDao = getComponentDaoObject(handle);
        try {
              return agentStatusDao.addCommandTriggerDetails(commandTriggerBean);
        }catch (Exception e){
            logger.error("Error in adding command trigger: {}", commandTriggerBean, e);
        }finally {
            closeDaoConnection(handle, agentStatusDao);
        }
        return -1;
    }

    public static int remCommandTrigger(int physicalAgentId,String commandJobId, Handle handle) {
        AgentStatusDao agentStatusDao = getComponentDaoObject(handle);
        try {
            return agentStatusDao.remCommandTriggerDetails(physicalAgentId,commandJobId);
        }catch (Exception e){
            logger.error("Error in removing command trigger: {}", physicalAgentId, e);
        }finally {
            closeDaoConnection(handle, agentStatusDao);
        }
        return -1;
    }
    public static void updatePhysicalAgentJobStatusCommandExecNULL(int physicalAgentId, Handle handle) {
        AgentStatusDao agentStatusDao = getComponentDaoObject(handle);
        try {
            agentStatusDao.updatePhysicalAgentJobStatusCommandExecNULL(physicalAgentId);
        }catch (Exception e){
            logger.error("Error in updating NULL value for last_job_id,last_status_id,last_command_executed in" +
                    " physical_agent table for : {} agent", physicalAgentId, e);
        }finally {
            closeDaoConnection(handle, agentStatusDao);
        }
    }

    public static List<AgentBean> getCompInstAgentMapping(int agentId){

        AgentStatusDao agentStatusDao =
                MySQLConnectionManager.getInstance().getHandle().open(AgentStatusDao.class);
        try {
            return agentStatusDao.getCompInstAgentMapping(agentId);
        }catch (Exception e){
            logger.error("Error occurred while getting component details" + e.getMessage(), e);
        }
        finally {
            MySQLConnectionManager.getInstance().close(agentStatusDao);
        }
        return Collections.emptyList();
    }

    public static void updateJobId(String physicalAgentIdentifier,String commandJobId,String userId, Handle handle) throws ControlCenterException {
        AgentStatusDao agentStatusDao = getComponentDaoObject(handle);
        try {
            agentStatusDao.updateJobId(physicalAgentIdentifier,commandJobId,userId,new Date(System.currentTimeMillis()));
        } catch (Exception e) {
            logger.error("Exception encountered while updating commandJobId [{}],agent [{}].", commandJobId, physicalAgentIdentifier, e);
            throw new ControlCenterException("Exception encountered while updating commandJobID for agent");
        } finally {
            closeDaoConnection(handle, agentStatusDao);
        }
    }

    public static int addAgentCommandTrigger(CommandTriggerBean commandTriggerBean, Handle handle) {
        AgentStatusDao agentStatusDao = getComponentDaoObject(handle);
        try {
            return agentStatusDao.addAgentCommandTriggerDetails(commandTriggerBean);
        }catch (Exception e){
            logger.error("Error in adding command trigger: {}", commandTriggerBean, e);
        }finally {
            closeDaoConnection(handle, agentStatusDao);
        }
        return -1;
    }

}
