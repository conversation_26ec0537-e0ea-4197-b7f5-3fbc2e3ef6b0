package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.WebHookDataBean;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.sql.Timestamp;

public interface WebHookDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, url, created_time as createdTime, updated_time as updatedTime, user_details_id as userDetailsId, account_id as accountId from webhook_url where account_id = :accountId")
    WebHookDataBean getWebHook(@Bind("accountId") int accountId);

    @SqlUpdate("insert into webhook_url ( url, account_id, user_details_id, created_time, updated_time) VALUES ( :url, :accountId, :userDetailsId, :createdTime, :updatedTime)")
    @GetGeneratedKeys
    int addWebHook(@BindBean WebHookDataBean bean);

    @SqlUpdate("delete from webhook_url where account_id = :accountId")
    int remWebHook(@Bind("accountId") int accountId);

    @SqlUpdate("Update webhook_url set url = :url, updated_time = :updatedTime, user_details_id = :userId where account_id = :accountId")
    int updateWebHook(@Bind("url") String url, @Bind("updatedTime") Timestamp updatedTime, @Bind("accountId") int accountId,  @Bind("userId") String userId);
}
