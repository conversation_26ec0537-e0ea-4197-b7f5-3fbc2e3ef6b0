package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.NotificationSettingsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.NotificationSettings;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;
@UseStringTemplate3StatementLocator
public interface NotificationSettingsDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT notification_type_id typeId, no_of_minutes durationInMin from notification_settings WHERE account_id =:accountId")
    List<NotificationSettingsBean> getNotificationSettings(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("UPDATE notification_settings SET no_of_minutes = :durationInMin, updated_time= :updatedTime, user_details_id = :userId WHERE account_id = :accountId AND notification_type_id = :typeId")
    int[] updateNotificationSettings(@BindBean  List<NotificationSettings> notificationSettings);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into notification_settings ( notification_type_id, no_of_minutes, account_id, created_time, updated_time, user_details_id) values ( :typeId, :durationInMin, :accountId, :createdTime, :updatedTime, :userId)")
    @GetGeneratedKeys
    int[] addNotificationSettings(@BindBean  List<NotificationSettings> notificationSettings);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("UPDATE application_notification_settings SET no_of_minutes = :durationInMin, updated_time = :updatedTime, " +
            "user_details_id = :userId WHERE account_id = :accountId AND notification_type_id = :typeId AND application_id = :applicationId")
    void updateApplicationNotificationSettings(@BindBean List<NotificationSettings> notificationSettings);

}
