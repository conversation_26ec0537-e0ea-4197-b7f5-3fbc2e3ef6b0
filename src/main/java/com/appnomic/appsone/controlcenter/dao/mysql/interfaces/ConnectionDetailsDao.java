package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.ConnectionDetailsBean;
import com.heal.configuration.entities.ConnectionBean;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface ConnectionDetailsDao {
    @SqlBatch("insert into connection_details ( source_id, source_ref_object, destination_id, destination_ref_object," +
            " created_time, updated_time, account_id, user_details_id, is_discovery) values ( :sourceId, :sourceRefObject," +
            " :destinationId, :destinationRefObject, :createdTime, :updatedTime, :accountId, :userDetailsId, :isDiscovery)")
    @GetGeneratedKeys
    int[] addConnection(@BindBean List<ConnectionDetailsBean> connectionDetailsBeanList);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, source_id as sourceId, source_ref_object as sourceRefObject, destination_id as destinationId," +
            " destination_ref_object as destinationRefObject, created_time as createdTime, updated_time as updatedTime," +
            " account_id as accountId, user_details_id as userDetailsId, is_discovery as isDiscovery from connection_details" +
            " where source_id = :srcId and destination_id = :destId and account_id = :accountId")
    ConnectionDetailsBean getConnection(@Bind("srcId") int srcId, @Bind("destId") int destId,  @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, source_id as sourceId, source_ref_object as sourceRefObject, destination_id as destinationId," +
            " destination_ref_object as destinationRefObject, created_time as createdTime, updated_time as updatedTime," +
            " account_id as accountId, user_details_id as userDetailsId, is_discovery as isDiscovery from connection_details" +
            " where account_id = :accountId")
    List<ConnectionDetailsBean> getConnectionWithAccountId(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select cd.source_id as sourceId, cd.destination_id as destinationId, cd.created_time as createdTime, cd.updated_time as updatedTime, cd.user_details_id as modifiedBy, c1.name as destinationName, c1.identifier as destinationIdentifier, " +
            "c2.name as sourceName, c2.identifier as sourceIdentifier from connection_details cd, controller c1, controller c2 where c1.id=cd.destination_id " +
            "and c2.id=cd.source_id and cd.account_id= :accountId")
    List<ConnectionBean> getServiceConnectionBeansForAccount(@Bind("accountId") int accountId);

    @SqlUpdate("delete from connection_details where source_id = :srcId and account_id = :accountId")
    void remConnectionBySourceId(@Bind("srcId") int srcId,  @Bind("accountId") int accountId);

    @SqlUpdate("delete from connection_details where destination_id = :destId and account_id = :accountId")
    void remConnectionByDestinationId(@Bind("destId") int destId,  @Bind("accountId") int accountId);

    @SqlUpdate("delete from connection_details where source_id = :srcId and destination_id = :destId and account_id = :accountId")
    int remConnection(@Bind("srcId") int srcId, @Bind("destId") int destId,  @Bind("accountId") int accountId);

}
