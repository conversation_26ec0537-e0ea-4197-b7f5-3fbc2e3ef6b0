package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import com.appnomic.appsone.controlcenter.pojo.ActionsEnum;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class InstKpiAttrPersistenceSuppressionBean {

    private int compInstanceId;
    private int kpiId;
    private int kpiGroupId;
    private String attributeValue = "ALL";
    private String attributeOldValue;
    @EqualsAndHashCode.Exclude
    private ActionsEnum actionForUpdate;
    @EqualsAndHashCode.Exclude
    private int accountId;
    @EqualsAndHashCode.Exclude
    private Integer persistence;
    @EqualsAndHashCode.Exclude
    private Integer suppression;
    @EqualsAndHashCode.Exclude
    private String userDetailsId;
    @EqualsAndHashCode.Exclude
    private String createdTime;
    @EqualsAndHashCode.Exclude
    private String updatedTime;
    @EqualsAndHashCode.Exclude
    private int isMaintenanceExcluded;
    @EqualsAndHashCode.Exclude
    private String accountIdentifier;
}
