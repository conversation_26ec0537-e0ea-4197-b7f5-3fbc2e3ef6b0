package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.MasterTimezoneBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.TagMappingBean;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.TagMappingDetails;
import com.appnomic.appsone.controlcenter.pojo.TagPreDefinedData;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.Define;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

@UseStringTemplate3StatementLocator
public interface TagsDao {
    @SqlBatch("insert into tag_mapping (tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) " +
            "values (:tagId,:objectId,:objectRefTable,:tagKey,:tagValue,:createdTime,:updatedTime,:accountId,:userDetailsId)")
    void addTagMappingDetails(@BindBean List<TagMappingDetails> tagMappingDetails);

    @SqlUpdate("insert into tag_mapping (tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) " +
            "values (:tagId,:objectId,:objectRefTable,:tagKey,:tagValue,:createdTime,:updatedTime,:accountId,:userDetailsId)")
    @GetGeneratedKeys
    int addTagMappingDetails(@BindBean TagMappingDetails tagMappingDetails);

    @SqlUpdate("insert into tag_mapping (tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) " +
            "values (:tagId,:objectId,:objectRefTable,:tagKey,:tagValue,:createdTime,:updatedTime,:accountId,:userDetailsId)")
    @GetGeneratedKeys
    int addTagMappingDetails(@BindBean TagMappingBean bean);

    @SqlUpdate("INSERT INTO controller ( name, identifier, account_id, user_details_id, created_time, updated_time, controller_type_id) " +
            "VALUES ( :name, :identifier, :accountId, :userDetailsId, :createdTime, :updatedTime, :controllerTypeId)")
    @GetGeneratedKeys
    int addController(@BindBean ControllerBean controllerBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,time_zone_id timeZoneId,timeoffset timeOffset,created_time createdTime,updated_time updatedTime,user_details_id userDetailsId,account_id accountId from mst_timezone where id = :id")
    MasterTimezoneBean getTagValueIdForTimeZone(@Bind("id") int id);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select <selectSection> from <table> where <whereSection>")
    TagPreDefinedData getTagData(@Define("table") String table, @Bind("name") String tagValue, @Define("selectSection") String selectSection, @Define("whereSection") String whereSection);

    @SqlQuery("select id from tag_mapping where tag_id = :tagId and object_id = :objectId and object_ref_table = :objectRefTable and tag_key = :tagKey and tag_value = :tagValue and account_id = :accountId")
    int getTagMappingId(@Bind("tagId") int tagId, @Bind("objectId") int objectId, @Bind("objectRefTable") String objectRefTable, @Bind("tagKey") String tagKey,
                        @Bind("tagValue") String tagValue, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select tag_key tagKey from tag_mapping where  tag_id = :tagId and object_id = :objectId  and account_id = :accountId")
    List<TagMappingDetails> getTagKeyId(@Bind("tagId") int tagId,@Bind("objectId") int objectId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select tag_id tagId,object_id objectId, object_ref_table objectRefTable,tag_key tagKey,tag_value " +
            "tagValue,created_time createdTime,updated_time updatedTime,account_id accountId,user_details_id userDetailsId" +
            " from tag_mapping where id=:id")
    TagMappingDetails getTagMapping(@Bind("id") int id);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select object_id from tag_mapping where tag_id = :tagId and object_ref_table = :objectRefTable and tag_key = :tagKey and tag_value = :tagValue and account_id = :accountId")
    int getTagMappingObjectId(@Bind("tagId") int tagId, @Bind("objectRefTable") String objectRefTable, @Bind("tagKey") String tagKey, @Bind("tagValue") String tagValue, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select tag_key tagKey from tag_mapping where  tag_id = :tagId and object_id = :objectId and object_ref_table = :objectRefTable and tag_key = :tagKey  and account_id = :accountId")
    List<TagMappingDetails> getTagKeyId(@Bind("tagId") int tagId,@Bind("objectId") int objectId,@Bind("objectRefTable") String objectRefTable, @Bind("tagKey") String tagKey, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id, tag_id tagId, object_id objectId, tag_key tagKey, tag_value tagValue, account_id accountId FROM tag_mapping where tag_id = :tagId and object_ref_table = :refTable and account_id = :accountId")
    List<TagMappingBean> getAgentTags(@Bind("tagId") int tagId, @Bind("refTable") String refTable, @Bind("accountId") int accountId);

    @SqlBatch("INSERT INTO controller ( name, identifier, account_id, user_details_id, created_time, updated_time, controller_type_id) " +
            "VALUES ( :name, :identifier, :accountId, :userDetailsId, :createdTime, :updatedTime, :controllerTypeId)")
    @GetGeneratedKeys
    int[] addControllerList(@BindBean List<ControllerBean> controllerBean);

    @SqlUpdate("DELETE FROM application_notification_mapping where application_id = :appId")
    void removeITApplicationNotif(@Bind ("appId") int appId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("Update controller set name = :name" +
            " where id =:id")
    void editControllerName(@Bind("name")String name, @Bind("id")int id);

    @SqlUpdate("DELETE FROM tag_mapping where tag_id = :tagId and object_id = :objId and object_ref_table = :refTable and account_id = :accountId")
    void deleteTagMapping(@Bind("tagId") int tagId, @Bind("objId") int objId,
                          @Bind("refTable") String refTable, @Bind("accountId") int accountId);

    @SqlUpdate("DELETE FROM tag_mapping where tag_id = :tagId and object_id = :objId and object_ref_table = :refTable and " +
            "tag_key = :tagKey and tag_value = :tagValue and account_id = :accountId")
    int deleteAgentServiceTagMapping(@Bind("tagId") int tagId, @Bind("objId") int objId,
                          @Bind("refTable") String refTable, @Bind("tagKey") String tagKey,
                                     @Bind("tagValue") String tagValue, @Bind("accountId") int accountId);

    @SqlUpdate("DELETE FROM tag_mapping where tag_id = :tagId and object_id = :objId and object_ref_table = :refTable and tag_key =:tagKeys and account_id = :accountId and user_details_id=:userId")
    void deleteEntryTagDetails(@Bind("tagId") int tagId, @Bind("objId") int objId, @Bind("tagKeys") String tagKeys,@Bind("refTable") String refTable, @Bind("accountId") int accountId,@Bind("userId") String userId);

    @SqlUpdate("DELETE FROM tag_mapping where tag_id = :tagId and object_id = :objId and object_ref_table = :refTable and tag_key =:tagKey and account_id = :accountId")
    void deleteTag(@Bind("tagId") int tagId, @Bind("objId") int objId, @Bind("tagKey") String tagKey,@Bind("refTable") String refTable, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select object_id id, tag_value name, tag_key identifier from tag_mapping where tag_id = :tagId and object_ref_table = :objectRefTable and account_id = :accountId")
    List<IdPojo> getTagValue(@Bind("tagId") int tagId, @Bind("objectRefTable") String objectRefTable, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select ccm.cluster_id clusterId from comp_instance ci, component_cluster_mapping ccm where ci.id = ccm.comp_instance_id and " +
            "ci.host_address = :hostAddress and ci.account_id = :accountId and ci.is_cluster = 0 and ci.mst_component_type_id = :mstComponentTypeId")
    int getClusterIdForHost(@Bind("hostAddress") String hostAddress, @Bind("accountId") int accountId, @Bind("mstComponentTypeId") int mstComponentTypeId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(*) from comp_instance where id in (select object_id from tag_mapping where tag_id = :tagId and tag_value = :tagValue " +
            "and tag_key= :tagKey and object_ref_table = :objRefTable) and is_cluster = 1 and mst_component_type_id = :mstCompTypeId;")
    int getExistingHostClusterForService(@Bind("tagId") int tagId, @Bind("tagValue") String tagValue, @Bind("tagKey") int tagKey, @Bind("objRefTable") String objRefTable, @Bind("mstCompTypeId") int mstCompTypeId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, tag_id tagId, object_id objectId, object_ref_table objectRefTable, tag_key tagKey, tag_value tagValue, " +
            "created_time createdTime, updated_time updatedTime, account_id accountId, user_details_id userDetailsId from tag_mapping " +
            "where tag_id = :tagId and object_id = :objectId and object_ref_table = :objectRefTable and account_id = :accountId")
    TagMappingDetails getTagMappingData(@Bind("tagId") int tagId, @Bind("objectId") int objectId, @Bind("objectRefTable") String objectRefTable, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("update tag_mapping set tag_key = :tagKey, tag_value = :tagValue, user_details_id = :userDetailsId where id = :id")
    int updateTagMappingDetails(@BindBean TagMappingBean bean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from tag_mapping where id = :id")
    int deleteTagMappingDetails(@Bind("id") int id);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, tag_id tagId, object_id objectId, object_ref_table objectRefTable, tag_key tagKey, tag_value tagValue, " +
            "created_time createdTime, updated_time updatedTime, account_id accountId, user_details_id userDetailsId from tag_mapping " +
            "where tag_id = :tagId and object_id = :objectId and object_ref_table = :objectRefTable and account_id = :accountId and tag_value = :tagValue")
    TagMappingDetails getTagMappingDataByTagVal(@Bind("tagId") int tagId, @Bind("objectId") int objectId, @Bind("objectRefTable") String objectRefTable, @Bind("accountId") int accountId, @Bind("tagValue") String tagValue);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from tag_mapping where tag_id = :tagId and object_id = :objectId")
    int deleteTagMappingByObjectId(@Bind("objectId") int objectId, @Bind("tagId") int tagId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from tag_mapping where tag_id = :tagId and object_id = :objectId and tag_value = :tagValue")
    int deleteTagMappingByTagValue(@Bind("objectId") int objectId, @Bind("tagId") int tagId, @Bind("tagValue") String tagValue);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select tag_value from tag_mapping where tag_id = :tagId and object_id = :objectId and tag_key = :tagKey and object_ref_table = :refTable")
    String getTagValueByKey(@Bind("tagId") int tagId, @Bind("objectId") int objectId, @Bind("tagKey") String tagKey, @Bind("refTable")String refTable);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from tag_mapping where tag_id = :tagId and tag_value = :tagValue and account_id = :accountId")
    void deleteTagMappingByTagValueAndTagId(@Bind("tagId") int tagId, @Bind("tagValue") String tagValue, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from tag_mapping where tag_id = :tagId and object_id = :objectId and tag_value = :tagValue and account_id = :accountId")
    void deleteTagMappingByTagValueObjectIdAndTagId(@Bind("tagId") int tagId, @Bind("objectId") int objectId, @Bind("tagValue") String tagValue, @Bind("accountId") int accountId);
}
