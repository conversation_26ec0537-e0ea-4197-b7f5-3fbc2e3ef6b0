package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.heal.configuration.pojos.ApplicationSettings;
import org.skife.jdbi.v2.sqlobject.BindBean;
import org.skife.jdbi.v2.sqlobject.GetGeneratedKeys;
import org.skife.jdbi.v2.sqlobject.SqlUpdate;
import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;

/**
 * <AUTHOR>
 */
@UseStringTemplate3StatementLocator
public interface ApplicationSettingsDao {

    @SqlUpdate("INSERT INTO application_settings (application_id, signal_anomalies_id, info_signal_close_time_in_mins, ew_signal_close_time_in_mins," +
            " problem_signal_close_time_in_mins, status, account_id, created_time, updated_time, user_details_id) VALUES" +
            " (:applicationId, :signalAnomalyTypeId, :infoSignalCloseTime, :ewSignalCloseTime, :problemSignalCloseTime, :status, :accountId," +
            " :createdTime, :updatedTime, :userDetailsId)")
    @GetGeneratedKeys
    int addApplicationSettings(@BindBean ApplicationSettings applicationSettings);
}
