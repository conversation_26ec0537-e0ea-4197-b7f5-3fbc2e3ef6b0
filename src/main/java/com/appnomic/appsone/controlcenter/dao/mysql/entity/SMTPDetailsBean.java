package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.*;

import java.sql.Timestamp;
@EqualsAndHashCode
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SMTPDetailsBean {

    private int id;
    private String address;
    private int port;
    private String username;
    private String password;
    private int securityId;
    private String security;
    private int accountId;
    private String accIdentifier;
    private String fromRecipient;
    private String userDetailsId;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private int status;
    private int persistEmailNotifications;

}
