package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.CompInstanceKpiGroupDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.ServiceInstanceDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.ServiceInstanceAttributeBean;
import com.heal.configuration.pojos.ServiceInstanceDetails;
import com.heal.configuration.pojos.ServiceInstanceKpiEntity;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.Handle;

import java.util.List;

@Slf4j
public class ServiceInstanceDataService {

    private ServiceInstanceDao getServiceInstanceDao(Handle handle) {
        if (handle == null) {
            return MySQLConnectionManager.getInstance().open(ServiceInstanceDao.class);
        } else {
            return handle.attach(ServiceInstanceDao.class);
        }
    }
    public void closeDaoConnection(Handle handle, ServiceInstanceDao dao){
        if(handle == null) {
            MySQLConnectionManager.getInstance().close(dao);
        }
    }
    public int insertServiceInstance(ServiceInstanceDetails serviceInstanceDetails, Handle handle) throws ControlCenterException {
        ServiceInstanceDao serviceInstanceDao = getServiceInstanceDao(handle);
        try {
            return serviceInstanceDao.insertServiceInstance(serviceInstanceDetails);
        } catch (Exception e) {
            log.error("Exception while inserting service instance details into DB Details ", e);
            throw  new ControlCenterException("Exception while inserting service instance details into DB");
        } finally {
            closeDaoConnection(handle, serviceInstanceDao);
        }
    }


    public void insertServiceInstanceKpiDetailsList(List<ServiceInstanceKpiEntity> serviceInstanceKpiEntities, Handle handle) throws ControlCenterException {
        ServiceInstanceDao serviceInstanceDao = getServiceInstanceDao(handle);
        try {
            serviceInstanceDao.insertServiceInstanceKpiDetailsList(serviceInstanceKpiEntities);
        } catch (Exception e) {
            log.error("Exception while inserting service instance kpi details list into DB Details ", e);
            throw  new ControlCenterException("Exception while service instance kpi details list into DB");
        } finally {
            closeDaoConnection(handle, serviceInstanceDao);
        }
    }

    public void insertServiceInstanceGroupKpiDetailsList(List<CompInstanceKpiGroupDetailsBean> serviceInstanceGroupKpiDetailsList, Handle handle) throws ControlCenterException {
        ServiceInstanceDao serviceInstanceDao = getServiceInstanceDao(handle);
        try {
            serviceInstanceDao.insertServiceInstanceGroupKpiDetailsList(serviceInstanceGroupKpiDetailsList);
        } catch (Exception e) {
            log.error("Exception while inserting service instance group kpi details list into DB Details ", e);
            throw  new ControlCenterException("Exception while service instance group kpi details list into DB");
        } finally {
            closeDaoConnection(handle, serviceInstanceDao);
        }
    }

    public void insertServiceInstanceAttribute(List<ServiceInstanceAttributeBean> serviceInstanceAttributeBean, Handle handle) throws ControlCenterException {
        ServiceInstanceDao serviceInstanceDao = getServiceInstanceDao(handle);
        try {
            serviceInstanceDao.insertServiceInstanceAttribute(serviceInstanceAttributeBean);
        } catch (Exception e) {
            log.error("Exception while inserting Service Instance Attribute details into DB Details ", e);
            throw  new ControlCenterException("Exception while inserting service instance attribute details into DB");
        } finally {
            closeDaoConnection(handle, serviceInstanceDao);
        }
    }


    public int getServiceInstanceIdByServiceId(int serviceId, int accountId, Handle handle) throws ControlCenterException{

        ServiceInstanceDao serviceInstanceDao = getServiceInstanceDao(handle);

        try {
            return serviceInstanceDao.getServiceInstanceIdByServiceId(serviceId, accountId);
        } catch (Exception e){
            log.error("Exception while getting Service Instance details into DB Details for serviceId: [{}] and accountId: [{}]", serviceId, accountId, e);
            throw  new ControlCenterException("Exception while inserting service instance details into DB");
        } finally {
            closeDaoConnection(handle, serviceInstanceDao);
        }

    }


    public void deleteServiceInstanceByServiceId(int serviceId, int accountId, Handle handle) throws ControlCenterException{

        ServiceInstanceDao serviceInstanceDao = getServiceInstanceDao(handle);

        try {
            serviceInstanceDao.deleteServiceInstanceByServiceId(serviceId, accountId);
        } catch (Exception e){
            log.error("Exception while deleting Service Instance details into DB Details for serviceId: [{}] and accountId: [{}]", serviceId, accountId, e);
            throw  new ControlCenterException("Exception while inserting service instance details into DB");
        } finally {
            closeDaoConnection(handle, serviceInstanceDao);
        }

    }

    public void deleteServiceInstanceKpiDetailsByServiceInstanceId(int serviceInstanceId, Handle handle) throws ControlCenterException{

        ServiceInstanceDao serviceInstanceDao = getServiceInstanceDao(handle);

        try {
            serviceInstanceDao.deleteServiceInstanceKpiDetailsByServiceInstanceId(serviceInstanceId);
        } catch (Exception e){
            log.error("Exception while deleting Service Instance kpi details into DB Details for serviceInstanceId: [{}]", serviceInstanceId, e);
            throw  new ControlCenterException("Exception while inserting service instance kpi details into DB");
        } finally {
            closeDaoConnection(handle, serviceInstanceDao);
        }

    }

    public void deleteServiceInstanceGroupKpiDetailsByServiceInstanceId(int serviceInstanceId, Handle handle) throws ControlCenterException{

        ServiceInstanceDao serviceInstanceDao = getServiceInstanceDao(handle);

        try {
            serviceInstanceDao.deleteServiceInstanceGroupKpiDetailsByServiceInstanceId(serviceInstanceId);
        } catch (Exception e){
            log.error("Exception while deleting Service Instance group kpi details into DB Details for serviceInstanceId: [{}]", serviceInstanceId, e);
            throw  new ControlCenterException("Exception while inserting service instance group kpi details into DB");
        } finally {
            closeDaoConnection(handle, serviceInstanceDao);
        }

    }


    public void deleteServiceInstanceAttributeByServiceInstanceId(int serviceInstanceId, Handle handle) throws ControlCenterException{

        ServiceInstanceDao serviceInstanceDao = getServiceInstanceDao(handle);

        try {
            serviceInstanceDao.deleteServiceInstanceAttributeByServiceInstanceId(serviceInstanceId);
        } catch (Exception e){
            log.error("Exception while deleting Service Instance details into DB Details for serviceInstanceId: [{}]", serviceInstanceId, e);
            throw  new ControlCenterException("Exception while inserting service instance attribute details into DB");
        } finally {
            closeDaoConnection(handle, serviceInstanceDao);
        }

    }
}
