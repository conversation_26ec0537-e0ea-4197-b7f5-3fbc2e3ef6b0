package com.appnomic.appsone.controlcenter.dao.mysql.interfaces.connectors;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.ApplicationToKpiMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.AzureKpi;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.AzureResourceToKpiMapping;
import com.appnomic.appsone.controlcenter.pojo.connectors.AzureApplicationDetails;
import com.appnomic.appsone.controlcenter.pojo.connectors.AzureResourceDetails;
import com.appnomic.appsone.controlcenter.pojo.connectors.AzureTokenDetails;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface AzureConnectorDataDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into dataadapter_azure.azure_kpis(kpi_string,kpi_domain,kpi_type,kpi_aggregator)" +
            " values(:kpiName,:kpiDomain,:kpiType,:kpiAggregator);")
    @GetGeneratedKeys
    int[] addAzureKpis(@BindBean List<AzureKpi> azureKpis);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update dataadapter_azure.azure_kpis " +
            "Set kpi_domain = :kpiDomain , kpi_type = :kpiType, kpi_aggregator = :kpiAggregator " +
            " where kpi_string = :kpiName;")
    @GetGeneratedKeys
    void updateAzureKpis(@BindBean List<AzureKpi> azureKpis);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select kpi_string from dataadapter_azure.azure_kpis")
    List<String> getAzureKpis();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select kpi_string from dataadapter_azure.heal_kpis")
    List<String> getHealKpis();


    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from dataadapter_azure.azure_kpis where kpi_domain = :kpiDomain")
    List<Integer> getAzureKpisList(@Bind("kpiDomain") String kpiDomain);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into dataadapter_azure.azure_application(id,application_id,application_key,application_instance, log_agent)" +
            " values(:id,:applicationId,:applicationKey,:applicationInstanceName, :agentIdentifier);")
    @GetGeneratedKeys
    void addAzureApplicationDetails(@BindBean List<AzureApplicationDetails> azureApplicationDetails);

    @SqlUpdate("delete from dataadapter_azure.azure_application")
    void deleteAzureApplicationDetails();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into dataadapter_azure.azure_resource(id,resource_name,resource_group_name,subscription_id, token_id, application_instance)" +
            " values(:id,:resourceName,:resourceGroupName,:subscriptionId, :tokenId, :applicationInstanceName);")
    @GetGeneratedKeys
    void addAzureResourceDetails(@BindBean List<AzureResourceDetails> azureResourceDetails);

    @SqlUpdate("delete from dataadapter_azure.azure_resource")
    void deleteAzureResourceDetails();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into dataadapter_azure.azure_token(id,tenant_id,grant_type,client_id, client_secret, resource)" +
            " values(:id,:tenantId,:grantType,:clientId, :clientSecret, :resourceName);")
    @GetGeneratedKeys
    void addAzureTokenDetails(@BindBean List<AzureTokenDetails> azureTokenDetails);

    @SqlUpdate("delete from dataadapter_azure.azure_token")
    void deleteAzureTokenDetails();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into dataadapter_azure.azure_application_kpi_map (application_id, kpi_id) " +
              "values (:application_id, :kpi_id);")
    @GetGeneratedKeys
    void addAzureApplicationKpiMapping(@BindBean List<ApplicationToKpiMapping> azureApplicationToKpiMappings);

    @SqlUpdate("delete from dataadapter_azure.azure_application_kpi_map")
    void deleteAzureApplicationKpiMapping();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into dataadapter_azure.azure_resource_kpi_map (resource_id, kpi_id) " +
            "values (:resource_id, :kpi_id);")
    @GetGeneratedKeys
    void addAzureResourceKpiMapping(@BindBean List<AzureResourceToKpiMapping> azureResourceToKpiMappings);

    @SqlUpdate("delete from dataadapter_azure.azure_resource_kpi_map")
    void deleteAzureResourceKpiMapping();
}
