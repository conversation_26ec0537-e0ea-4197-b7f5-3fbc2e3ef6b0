package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CompInstanceKPIDetailsBean {

    private int id;
    private int isGroup;
    private String attribute;
    private String aliasName;
    private int attributeStatus;
    private int kpiId;
    private int groupKpiId;
    private int discovery;
    private String groupName;
    private int instanceId;
    private int producerId;
    private int mstProducerKPIMappingId;
    private int collectionInterval;
    private int notification;
    private int status;
    private String userId;
    private String createdTime;
    private String updatedTime;
    private int accountId;
    private int componentId;
    private String accountIdentifier;

}
