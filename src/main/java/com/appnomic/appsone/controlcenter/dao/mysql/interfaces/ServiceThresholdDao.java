package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.ControllerEntity;
import org.skife.jdbi.v2.sqlobject.Bind;
import org.skife.jdbi.v2.sqlobject.SqlQuery;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface ServiceThresholdDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select b.id, b.identifier identifier,b.name,b.status,b.monitor_enabled from " +
            "(select distinct service_id as id from service_kpi_thresholds where account_id = :accountId " +
            "and sor_operation_type_id is not null) a right outer join " +
            "(select id, identifier,name,status,monitor_enabled from controller where account_id = :accountId and " +
            "controller_type_id = :controllerTypeId) b on a.id=b.id " +
            "where a.id is null")
    List<ControllerEntity> getServicesBySOR(@Bind("accountId") Integer accountId,
                                            @Bind("controllerTypeId") Integer cntlTypeId);

}
