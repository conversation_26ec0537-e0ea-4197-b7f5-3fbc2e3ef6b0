package com.appnomic.appsone.controlcenter.dao.mysql.connectors;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.ApplicationToKpiMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.PrometheusHealKpis;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.connectors.KubernetesDataDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.connectors.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class KubernetesDataService {
    public KubernetesDataService(){}

    public void updatePrometheusKpis(PrometheusHealKpis prometheusKpiList)
    {
        KubernetesDataDao kubernetesDataDao = getMsqlDataDao();
        try {
            kubernetesDataDao.updatePrometheusKpis(prometheusKpiList);
        } catch (Exception e) {
            log.error("Error occurred while updating azure KPIs{}", prometheusKpiList, e);
        } finally {
            MySQLConnectionManager.getInstance().close(kubernetesDataDao);
        }
    }

    public void updateDomainToHealKpiMapping(String healIdentifier, int srcId)
    {
        KubernetesDataDao kubernetesDataDao = getMsqlDataDao();
        try {
            kubernetesDataDao.updateDomainToHeal(healIdentifier, srcId);
        } catch (Exception e) {
            log.error("Error occurred while updating  Domain to Heal mapping  - in kubernetes, healIdentifier :{}, src_id : {}, error :{}", healIdentifier, srcId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(kubernetesDataDao);
        }
    }

    public void updateHealKpi(String kpiName, String newHealIdentifier, String oldHealIdentifier)
    {
        KubernetesDataDao kubernetesDataDao = getMsqlDataDao();
        try {
            kubernetesDataDao.updateHealKpi(kpiName, newHealIdentifier, oldHealIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while updating heal_kpis - in kubernetes, healIdentifier :{}, error :{}", newHealIdentifier, e);
        } finally {
            MySQLConnectionManager.getInstance().close(kubernetesDataDao);
        }
    }

    public List<PrometheusHealKpis> getPrometheusKpis()
    {
        KubernetesDataDao kubernetesDataDao = getMsqlDataDao();
        try {
            return kubernetesDataDao.getPrometheusKpis();
        } catch (Exception e) {
            log.error("Error occurred while getting prometheus KPIs{}", (Object) e.getStackTrace());
        } finally {
            MySQLConnectionManager.getInstance().close(kubernetesDataDao);
        }
        return new ArrayList<>();
    }
    public List<String> getHealKpis()
    {
        KubernetesDataDao kubernetesDataDao = getMsqlDataDao();
        try {
            return kubernetesDataDao.getHealKpis();
        } catch (Exception e) {
            log.error("Error occurred while getting heal KPIs{}", (Object) e.getStackTrace());
        } finally {
            MySQLConnectionManager.getInstance().close(kubernetesDataDao);
        }
        return new ArrayList<>();
    }

    public void addKubernetesConfiguration(String schema, List<KubernetesConfiguration> kubernetesConfigurations)
    {
        KubernetesDataDao kubernetesDataDao = getMsqlDataDao();
        try {
            kubernetesDataDao.deleteKubernetesConfig(schema);
            kubernetesDataDao.addKubernetesConfig(schema,kubernetesConfigurations);
        } catch (Exception e) {
            log.error("Error occurred while adding kubernetes configuration {}", kubernetesConfigurations, e);
        } finally {
            MySQLConnectionManager.getInstance().close(kubernetesDataDao);
        }
    }

    public void addPrometheusQueryFilter(String schema, List<PrometheusQueryFilter> prometheusQueryFilters)
    {
        KubernetesDataDao kubernetesDataDao = getMsqlDataDao();
        try {
            kubernetesDataDao.deletePrometheusQueryFilter(schema);
            kubernetesDataDao.addPrometheusQueryFilter(schema,prometheusQueryFilters);
        } catch (Exception e) {
            log.error("Error occurred while adding kubernetes configuration {}", prometheusQueryFilters, e);
        } finally {
            MySQLConnectionManager.getInstance().close(kubernetesDataDao);
        }
    }

    public void addPrometheusKpiResponsePath(String schema, List<PrometheusKpiResponsePath> prometheusKpiResponsePaths)
    {
        KubernetesDataDao kubernetesDataDao = getMsqlDataDao();
        try {
            kubernetesDataDao.deletePrometheusKpiResponsePaths(schema);
            kubernetesDataDao.addPrometheusKpiResponsePaths(schema, prometheusKpiResponsePaths);
        } catch (Exception e) {
            log.error("Error occurred while adding prometheus kpi response paths {}", prometheusKpiResponsePaths, e);
        } finally {
            MySQLConnectionManager.getInstance().close(kubernetesDataDao);
        }
    }

    public void addPrometheusApplication(String schema, List<PrometheusApplication> prometheusApplications)
    {
        KubernetesDataDao kubernetesDataDao = getMsqlDataDao();
        try {
            kubernetesDataDao.deletePrometheusApplications(schema);
            kubernetesDataDao.addPrometheusApplications(schema, prometheusApplications);
        } catch (Exception e) {
            log.error("Error occurred while adding prometheus applications {}", prometheusApplications, e);
        } finally {
            MySQLConnectionManager.getInstance().close(kubernetesDataDao);
        }
    }

    public void addA1LogscanEndpt(String schema, List<A1LogscanEndpoint> a1LogscanEndpoints)
    {
        KubernetesDataDao kubernetesDataDao = getMsqlDataDao();
        try {
            kubernetesDataDao.deleteA1LogscanEndpt(schema);
            kubernetesDataDao.addA1LogscanEndpt(schema, a1LogscanEndpoints);
        } catch (Exception e) {
            log.error("Error occurred while adding A1 logScan endpoints {}", a1LogscanEndpoints, e);
        } finally {
            MySQLConnectionManager.getInstance().close(kubernetesDataDao);
        }
    }

    public void addElasticSearchResponsePath(String schema, List<ElasticSearchResponsePath> elasticSearchResponsePaths)
    {
        KubernetesDataDao kubernetesDataDao = getMsqlDataDao();
        try {
            kubernetesDataDao.deleteElasticSearchResponsePath(schema);
            kubernetesDataDao.addElasticSearchResponsePath(schema, elasticSearchResponsePaths);
        } catch (Exception e) {
            log.error("Error occurred while adding elastic search response paths {}", elasticSearchResponsePaths, e);
        } finally {
            MySQLConnectionManager.getInstance().close(kubernetesDataDao);
        }
    }

    public int [] addPrometheusKpis(String schema, List<PrometheusHealKpis> prometheusHealKpis)
    {
        KubernetesDataDao kubernetesDataDao = getMsqlDataDao();
        try {
            return kubernetesDataDao.addPrometheusKpis(schema, prometheusHealKpis);
        } catch (Exception e) {
            log.error("Error occurred while adding prometheus kpis {}", prometheusHealKpis, e);
        } finally {
            MySQLConnectionManager.getInstance().close(kubernetesDataDao);
        }
        return null;
    }

    public void addKubernetesApplicationKpiMapping(String schema, List<ApplicationToKpiMapping> applicationToKpiMappings)
    {
        KubernetesDataDao kubernetesDataDao = getMsqlDataDao();
        try {
            kubernetesDataDao.addKubernetesApplicationKpiMapping(schema, applicationToKpiMappings);
        } catch (Exception e) {
            log.error("Error while adding Kubernetes applications to kpi mapping.{}", applicationToKpiMappings, e);
        } finally {
            MySQLConnectionManager.getInstance().close(kubernetesDataDao);
        }
    }

    public void addKubernetesHealAccessToken(String schema, List<KubernetesHealAccessToken> kubernetesHealAccessTokens)
    {
        KubernetesDataDao kubernetesDataDao = getMsqlDataDao();
        try {
            kubernetesDataDao.deleteKubernetesHealAccessToken(schema);
            kubernetesDataDao.addKubernetesHealAccessToken(schema, kubernetesHealAccessTokens);
        } catch (Exception e) {
            log.error("Error while adding Kubernetes Heal Access Token.{}", kubernetesHealAccessTokens, e);
        } finally {
            MySQLConnectionManager.getInstance().close(kubernetesDataDao);
        }
    }

    public void addKubernetesHealInstanceCreationPayload(String schema, List<KubernetesHealInstanceCreationPayload> kubernetesHealInstanceCreationPayloads)
    {
        KubernetesDataDao kubernetesDataDao = getMsqlDataDao();
        try {
            kubernetesDataDao.deleteKubernetesHealInstanceCreationPayloads(schema);
            kubernetesDataDao.addKubernetesHealInstanceCreationPayloads(schema, kubernetesHealInstanceCreationPayloads);
        } catch (Exception e) {
            log.error("Error while adding Kubernetes Heal Instance Creation Payloads.{}", kubernetesHealInstanceCreationPayloads, e);
        } finally {
            MySQLConnectionManager.getInstance().close(kubernetesDataDao);
        }
    }

    private KubernetesDataDao getMsqlDataDao(){
        return MySQLConnectionManager.getInstance().open(KubernetesDataDao.class);
    }
}
