package com.appnomic.appsone.controlcenter.dao.mysql.connectors;

import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.connectors.ConnectorsDetailsDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.connectors.ConnectorCommandPojo;
import com.appnomic.appsone.controlcenter.pojo.connectors.ConnectorDetails;
import com.appnomic.appsone.controlcenter.pojo.connectors.ConnectorTemplate;
import com.appnomic.appsone.controlcenter.pojo.IdValuePojo;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ConnectorDetailsDataService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConnectorDetailsDataService.class);

    public ConnectorDetailsDataService() {
    }

    public static List<ConnectorDetails> getConnectorDetails(Handle handle) {
        ConnectorsDetailsDao dao = getConnectionDetailsDao(handle);
        try {
            return dao.getConnectorsDetails();
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting the connectors details for {}", e.getMessage());

        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public static List<IdValuePojo> isConfiguredForAccount(int accountId, Handle handle) {
        ConnectorsDetailsDao dao = getConnectionDetailsDao(handle);
        try {
            return dao.isConfigureForAccount(accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting the connectors details for {}", e.getMessage());

        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public static List<ConnectorDetails.CommandIdName> getConnectorCommands(int connectorId, Handle handle) {
        ConnectorsDetailsDao dao = getConnectionDetailsDao(handle);
        try {
            return dao.getConnectorCommands(connectorId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting the commands for connector id: {}", connectorId, e);

        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public static List<ConnectorDetails.CommandArgs> getConnectorCommandArguments(int commandId, int connectorId, Handle handle) {
        ConnectorsDetailsDao dao = getConnectionDetailsDao(handle);
        try {
            return dao.getConnectorCommandArgs(commandId, connectorId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting the commands arguments for connector id: {} with command id: {}", connectorId, commandId, e);

        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public static ConnectorDetails.CommandArgsValues getConnectorCommandArgumentsValues(int connectorId, int commandId, Handle handle) {
        ConnectorsDetailsDao dao = getConnectionDetailsDao(handle);
        try {
            return dao.getConnectorCommandArgsValue(connectorId, commandId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting the commands for connector id {}", connectorId, e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return null;
    }

    public static List<ConnectorDetails.ConnectorCommandStatus> getConnectorCommandStatus(Handle handle) {
        ConnectorsDetailsDao dao = getConnectionDetailsDao(handle);
        try {
            return dao.getConnectorCommandStatus();
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting the command status for connectors. {}", e.getMessage());

        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public static Integer getConnectorCountForAccount(int accountId) {
        ConnectorsDetailsDao dao = getConnectionDetailsDao(null);
        try {
            return dao.getConnectorCount(accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting the connectors count for {}", e.getMessage());

        } finally {
            closeDaoConnection(null, dao);
        }
        return null;
    }

    public static ConnectorTemplate getConnectorTemplate(Handle handle, Integer accountId, Integer connectorId) {
        ConnectorsDetailsDao dao = getConnectionDetailsDao(handle);
        try {
            return dao.getConnectorTemplate(accountId, connectorId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting the connectors template for {}", e.getMessage());

        } finally {
            closeDaoConnection(handle, dao);
        }
        return null;
    }

    public static ConnectorTemplate getConnectorTemplateConfig(Handle handle, Integer accountId, Integer connectorId) {
        ConnectorsDetailsDao dao = getConnectionDetailsDao(handle);
        try {
            return dao.getConnectorTemplateConfig(accountId, connectorId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting the connectors template for {}", e.getMessage());

        } finally {
            closeDaoConnection(handle, dao);
        }
        return null;
    }

    public static int getTemplateUploadStatus(int accountId, int connectorId) {
        ConnectorsDetailsDao dao = getConnectionDetailsDao(null);
        try {
            return dao.getTemplateUploadStatus(accountId, connectorId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting the connector upload status for {}", e.getMessage());

        } finally {
            closeDaoConnection(null, dao);
        }
        return -1;
    }

    public static void addConnectorCommandsTriggered(ConnectorCommandPojo connectorCommandPojo) {
        ConnectorsDetailsDao dao = getConnectionDetailsDao(null);
        try {
            dao.addConnectorCommandsTriggered(connectorCommandPojo);
            dao.updateConnectorCommandsTriggered(connectorCommandPojo);
        } catch (Exception e) {
            LOGGER.error("Error occurred while adding connector commands triggered. {}", e.getMessage());
        } finally {
            closeDaoConnection(null, dao);
        }
    }


    public static int updateConnectorCommandStatus(String commandJobId, int commandStatus, Handle handle) {
        ConnectorsDetailsDao dao = getConnectionDetailsDao(null);
        try {
            return dao.updateConnectorCommandsStatus(commandJobId, commandStatus);
        } catch (Exception e) {
            LOGGER.error("Error occurred while adding connector commands triggered. {}", e.getMessage());

        } finally {
            closeDaoConnection(handle, dao);
        }
        return -1;
    }

    private static ConnectorsDetailsDao getConnectionDetailsDao(Handle handle) {
        if (handle == null) {
            return MySQLConnectionManager.getInstance().open(ConnectorsDetailsDao.class);
        } else {
            return handle.attach(ConnectorsDetailsDao.class);
        }
    }


    private static void closeDaoConnection(Handle handle, ConnectorsDetailsDao dao) {
        if (handle == null) {
            MySQLConnectionManager.getInstance().close(dao);
        }
    }
}
