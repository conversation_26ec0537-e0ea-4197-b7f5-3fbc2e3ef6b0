package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.Builder;
import lombok.Data;

import java.sql.Timestamp;

@Data
@Builder
public class UserNotificationDetailsBean {
    private boolean smsEnabled;
    private boolean emailEnabled;
    private int forensicEnabled;
    private int accountId;
    private String applicableUserId;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private String userDetailsId;
    private int preferenceId;
    private int forensicNotificationSuppression;
}
