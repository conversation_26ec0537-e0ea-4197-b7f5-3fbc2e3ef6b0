package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentModeConfigBean {

    private int id;
    private int serviceId;
    private int agentTypeId;
    private int commandId;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private String userDetailsId;
    private int accountId;

}
