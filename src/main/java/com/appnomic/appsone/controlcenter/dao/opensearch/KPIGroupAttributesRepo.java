package com.appnomic.appsone.controlcenter.dao.opensearch;

import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.manager.OpenSearchConnectionManager;
import com.appnomic.appsone.opeasearchquery.helper.OpenSearchQueryHelper;
import com.appnomic.appsone.opeasearchquery.queryopts.QueryOptions;
import com.appnomic.appsone.opeasearchquery.results.NameValuePair;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.heal.configuration.util.DateHelper;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> - 12-01-2022
 */
public class KPIGroupAttributesRepo {
    private static final Logger LOGGER = LoggerFactory.getLogger(KPIGroupAttributesRepo.class);

    public Set<String> getGroupKpiAttributesWithDataCollected(String accountIdentifier, String instanceIdentifier,
                                                              Long range, Set<Integer> kpiIds) throws ControlCenterException {
        String indexPrefix = Constants.INDEX_PREFIX_HEAL_COLLATED_KPI + "_" + accountIdentifier.toLowerCase();
        try {
            OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, Constants.INDEX_PREFIX_HEAL_COLLATED_KPI);
            if (openSearchClient == null) {
                return Collections.emptySet();
            }

            long toDate = System.currentTimeMillis();
            long fromDate = toDate - range;

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromDate, toDate).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("compInstanceIdentifier", instanceIdentifier));
            matchFields.add(new NameValuePair("kpiId", String.valueOf(kpiIds.stream().findFirst().get())));

            List<String> columns = new ArrayList<>();
            columns.add("groupAttribute");

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("timeInGMT")
                    .epochFromDate(fromDate)
                    .epochToDate(toDate)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .fetchAllRecords(true)
                    .groupByColumns(Optional.of(columns))
                    .build();

            TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, openSearchClient);
            if (results == null || results.getRowResults() == null || results.getRowResults().isEmpty()) {
                return Collections.emptySet();
            }

            return results.getRowResults().stream()
                    .collect(Collectors.toMap(c -> c.getListOfRows().get(0).getColumnValue(), TabularResults.ResultRow::getCountValue))
                    .keySet();

        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            LOGGER.error("Unable to get core kpi group attributes from [{}]. Details: accountIdentifier [{}], instanceId [{}], kpiId [{}].",
                    indexPrefix, accountIdentifier, instanceIdentifier, kpiIds, e);
            throw new ControlCenterException("Unable to get core kpi group attributes");
        }
    }
}
