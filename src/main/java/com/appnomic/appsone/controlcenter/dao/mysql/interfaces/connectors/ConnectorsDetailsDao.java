package com.appnomic.appsone.controlcenter.dao.mysql.interfaces.connectors;

import com.appnomic.appsone.controlcenter.pojo.connectors.ConnectorCommandPojo;
import com.appnomic.appsone.controlcenter.pojo.connectors.ConnectorDetails;
import com.appnomic.appsone.controlcenter.pojo.connectors.ConnectorTemplate;
import com.appnomic.appsone.controlcenter.pojo.IdValuePojo;
import org.skife.jdbi.v2.sqlobject.Bind;
import org.skife.jdbi.v2.sqlobject.BindBean;
import org.skife.jdbi.v2.sqlobject.SqlQuery;
import org.skife.jdbi.v2.sqlobject.SqlUpdate;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface ConnectorsDetailsDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT mcd.id, mcd.name, mcd.description, mcd.configured, mcd.custom, mcd.is_template_exists templateExists, " +
            "mcd.updated_time createdOn, ua.username createdBy, ctd.upload_error uploadError, ctd.data_collection_error dataCollectionError, " +
            "ctd.upload_error_time uploadErrorTime, ctd.data_collection_error_time dataCollectionErrorTime " +
            "FROM mst_connector_details mcd LEFT JOIN connector_template_data ctd ON mcd.id = ctd.mst_connector_details_id, user_attributes ua  " +
            "WHERE mcd.user_details_id = ua.user_identifier")
    List<ConnectorDetails> getConnectorsDetails();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("Select mst_connector_details_id id, upload_status value " +
            "FROM connector_template_data " +
            "WHERE account_id IN (1, :accountId) " +
            "AND status = 1 AND upload_status = 1;")
    List<IdValuePojo> isConfigureForAccount(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT is_placeholder isPlaceHolder, argument_key `key`, argument_value value " +
            "FROM command_arguments ca, connector_commands_mapping ccm " +
            "WHERE ca.command_id = :commandId AND ccm.connector_id = :connectorId")
    List<ConnectorDetails.CommandArgs> getConnectorCommandArgs(@Bind("commandId") int commandId, @Bind("connectorId") int connectorId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT connector_logs, connector_config, job_name " +
            "FROM connector_commands_mapping WHERE connector_id = :connectorId AND command_id = :commandId")
    ConnectorDetails.CommandArgsValues getConnectorCommandArgsValue(@Bind("connectorId") int connectorId, @Bind("commandId") int commandId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT cd.id commandId, cd.name commandName FROM command_details cd, connector_commands_mapping ccm " +
            "WHERE ccm.command_id = cd.id AND ccm.connector_id = :connectorId AND ccm.is_ui_visible = 1;")
    List<ConnectorDetails.CommandIdName> getConnectorCommands(@Bind("connectorId") int connectorId);

    /*@RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT connector_config, connector_logs FROM connector_commands_mapping " +
            "where connector_id = :connectorId and command_id = :commandId;")
    ConnectorDetails.CommandArgs getConnectorCommandArgs(@Bind("connectorId") int connectorId, @Bind("commandId") int commandId);*/

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT ct.connector_id connectorId, ct.command_id commandId, cd.`name` commandName, max(ct.trigger_time), " +
            "ct.command_job_id, ct.command_status commandStatus, cad.exit_status commandAuditMessage " +
            "FROM commands_triggered ct LEFT JOIN command_details cd ON cd.id = ct.command_id, command_audit_details cad " +
            "WHERE cad.command_job_id = ct.command_job_id GROUP BY ct.connector_id;")
    List<ConnectorDetails.ConnectorCommandStatus> getConnectorCommandStatus();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(id) from mst_connector_details where is_template_exists = 1;")
    Integer getConnectorCount(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT upload_status FROM connector_template_data " +
            "WHERE account_id IN (1, :accountId) AND mst_connector_template_id = :connectorId;")
    Integer getTemplateUploadStatus(@Bind("accountId") int accountId, @Bind("connectorId") int connectorId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT mct.template_content templateContent, mct.mst_connector_details_id connectorId, " +
            "mcd.name connectorName " +
            "FROM mst_connector_template mct, mst_connector_details mcd " +
            "WHERE mct.mst_connector_details_id = :connectorId AND mct.account_id IN (1, :accountId) " +
            "AND  mct.is_default =1 AND mcd.id = :connectorId;")
    ConnectorTemplate getConnectorTemplate(@Bind("accountId")int accountId, @Bind("connectorId") int connectorId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT ctd.mst_connector_details_id connectorId, mcd.name connectorName, template_data_content templateContent " +
            "FROM connector_template_data ctd, mst_connector_details mcd " +
            "WHERE mst_connector_details_id = :connectorId AND ctd.account_id IN (1, :accountId) " +
            "AND  status =1 AND mcd.id = ctd.mst_connector_details_id;")
    ConnectorTemplate getConnectorTemplateConfig(@Bind("accountId")int accountId, @Bind("connectorId") int connectorId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("INSERT INTO commands_triggered " +
            "(connector_id, command_id, trigger_time, command_job_id, command_status, user_details_id)" +
            "VALUES (:connectorId, :commandId, :triggerTime, :commandJobId, :commandStatus, :userDetailsId);")
    void addConnectorCommandsTriggered(@BindBean ConnectorCommandPojo commandPojo);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE mst_connector_details " +
            "SET command_job_id = :commandJobId, command_status = 1 " +
            "WHERE id = :connectorId;")
    void updateConnectorCommandsTriggered(@BindBean ConnectorCommandPojo commandPojo);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE mst_connector_details " +
            "command_status = :commandStatus " +
            "WHERE command_job_id = :commandJobId;")
    int updateConnectorCommandsStatus(@Bind("commandJobId") String commandJobId, @Bind("commandStatus") int commandStatus);
}
