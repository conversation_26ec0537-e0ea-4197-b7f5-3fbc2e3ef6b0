package com.appnomic.appsone.controlcenter.dao.opensearch;

import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.InstanceKpiAttributeThresholdBean;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.manager.OpenSearchConnectionManager;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.heal.configuration.pojos.opensearch.InstanceKpiThresholds;
import com.heal.configuration.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.json.JsonData;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch._types.SortOrder;
import org.opensearch.client.opensearch._types.aggregations.*;
import org.opensearch.client.opensearch._types.query_dsl.Query;
import org.opensearch.client.opensearch.core.*;
import org.opensearch.client.opensearch.core.bulk.BulkResponseItem;
import org.opensearch.client.opensearch.core.search.Hit;

import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.util.*;

/**
 * <AUTHOR> Suman - 05-01-2022
 */
@Slf4j
public class InstanceKpiThresholdRepo {

    private static final int TOTAL_KPIS_FOR_REALTIME_THRESHOLD_AGGR_BUCKET = ConfProperties.getInt(Constants.TOTAL_KPIS_FOR_REALTIME_THRESHOLD_AGGR_BUCKET,
            Constants.TOTAL_KPIS_FOR_REALTIME_THRESHOLD_AGGR_BUCKET_DEFAULT);

    private static final int NUMBER_OF_PAST_DAYS_TO_GET_REALTIME_THRESHOLD =
            ConfProperties.getInt(Constants.NUMBER_OF_PAST_DAYS_TO_GET_REALTIME_THRESHOLD,
                    Constants.NUMBER_OF_PAST_DAYS_TO_GET_REALTIME_THRESHOLD_DEFAULT);


    public void updateThresholds(String accountIdentifier, List<InstanceKpiAttributeThresholdBean> thresholdDetails) throws ControlCenterException {
        try {
            String indexName = Constants.INDEX_PREFIX_HEAL_INSTANCE_KPI_THRESHOLDS + "_" + accountIdentifier.toLowerCase();
            OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, Constants.INDEX_PREFIX_HEAL_INSTANCE_KPI_THRESHOLDS);
            if (openSearchClient == null) {
                return;
            }

            List<UpdateRequest<Object, Object>> updateRequestList = new ArrayList<>();
            List<IndexRequest<InstanceKpiThresholds>> indexRequestList = new ArrayList<>();
            for (InstanceKpiAttributeThresholdBean newThreshold : thresholdDetails) {
                log.debug("Updating instance kpi details in index {}: with details {}, account {}", indexName, newThreshold, accountIdentifier);
                SearchResponse<Object> searchResponse = documentExist(newThreshold, Constants.INDEX_PREFIX_HEAL_INSTANCE_KPI_THRESHOLDS, accountIdentifier);

                if (searchResponse != null && searchResponse.hits() != null && searchResponse.hits().hits() != null) {
                    List<Hit<Object>> hits = searchResponse.hits().hits();
                    if (!hits.isEmpty()) {
                        long updateTime = DateTimeUtil.getGMTToEpochTime(newThreshold.getStartTime());
                        log.debug("OS doc with _id {} in index {} is getting updated with endTime {}", hits.get(0).id(), hits.get(0).index(), updateTime);

                        UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<>()
                                .index(hits.get(0).index())
                                .id(hits.get(0).id())
                                .doc(new HashMap<>() {{
                                    put("endTime", updateTime);
                                }}).build();

                        updateRequestList.add(updateRequest);
                    }

                }

                IndexRequest<InstanceKpiThresholds> indexRequest = addThreshold(newThreshold, indexName);
                indexRequestList.add(indexRequest);
            }

            BulkRequest.Builder bulkRequestBuilder = new BulkRequest.Builder();

            // Add each update request to the bulk request
            updateRequestList.forEach(updateRequest ->
                    bulkRequestBuilder.operations(op ->
                            op.update(updateReq -> updateReq
                                    .index(updateRequest.index())
                                    .id(updateRequest.id())
                                    .document(updateRequest.doc())
                                    .docAsUpsert(false)
                            )));

            // Add each index request to the bulk request
            indexRequestList.forEach(indexRequest ->
                    bulkRequestBuilder.operations(op ->
                            op.index(idx -> idx
                                    .index(indexRequest.index())
                                    .id(indexRequest.id())
                                    .document(indexRequest.document())
                            )));
            // Build the request
            BulkRequest bulkRequest = bulkRequestBuilder.build();

            BulkResponse resp = openSearchClient.bulk(bulkRequest);
            if (resp.errors()) {
                for (BulkResponseItem item : resp.items()) {
                    if (item.error() != null) {
                        log.error("Failures during bulk indexing operation. Exception type: [{}], Reason: [{}]", item.error().type(),
                                item.error().causedBy() != null ? item.error().causedBy().reason() : item.error().reason());
                    }
                }
            }
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error in updating end time of instance kpi attribute. Details: ", e);
        }
    }

    public void createThreshold(List<InstanceKpiAttributeThresholdBean> changedThresholds) throws ControlCenterException {
        try {
            String accountIdentifier = changedThresholds.get(0).getAccountIdentifier();
            OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, Constants.INDEX_PREFIX_HEAL_INSTANCE_KPI_THRESHOLDS);
            if (openSearchClient == null) {
                return;
            }
            String indexName = Constants.INDEX_PREFIX_HEAL_INSTANCE_KPI_THRESHOLDS + "_" + accountIdentifier.toLowerCase();

            List<IndexRequest<InstanceKpiThresholds>> indexRequestList = new ArrayList<>();
            for (InstanceKpiAttributeThresholdBean threshold : changedThresholds) {
                IndexRequest<InstanceKpiThresholds> indexRequest = addThreshold(threshold, indexName);
                indexRequestList.add(indexRequest);
            }

            BulkRequest.Builder bulkRequestBuilder = new BulkRequest.Builder();

            // Add each index request to the bulk request
            indexRequestList.forEach(indexRequest ->
                    bulkRequestBuilder.operations(op ->
                            op.index(idx -> idx
                                    .index(indexRequest.index())
                                    .id(indexRequest.id())
                                    .document(indexRequest.document())
                            )));
            // Build the request
            BulkRequest bulkRequest = bulkRequestBuilder.build();

            BulkResponse resp = openSearchClient.bulk(bulkRequest);
            if (!resp.errors()) {
                log.debug("successfully bulk insert instance kpi thresholds details");
            }
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error in inserting the instance kpi thresholds details. Details: ", e);
            throw new ControlCenterException("Error in inserting the instance kpi thresholds details");
        }
    }

    public void closeExistingThresholds(List<InstanceKpiAttributeThresholdBean> thresholdDetails) throws ControlCenterException {
        try {
            String accountIdentifier = thresholdDetails.get(0).getAccountIdentifier();
            OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, Constants.INDEX_PREFIX_HEAL_INSTANCE_KPI_THRESHOLDS);
            if (openSearchClient == null) {
                return;
            }

            List<UpdateRequest<Object, Object>> updateRequestList = new ArrayList<>();
            for (InstanceKpiAttributeThresholdBean newThreshold : thresholdDetails) {
                log.debug("Deleting instance kpi details in index {}: with details {}, account {}", Constants.INDEX_PREFIX_HEAL_INSTANCE_KPI_THRESHOLDS, newThreshold, accountIdentifier);
                SearchResponse<Object> searchResponse = documentExist(newThreshold, Constants.INDEX_PREFIX_HEAL_INSTANCE_KPI_THRESHOLDS, accountIdentifier);

                if (searchResponse != null && searchResponse.hits() != null && searchResponse.hits().hits() != null) {
                    List<Hit<Object>> hits = searchResponse.hits().hits();
                    if (!hits.isEmpty()) {
                        long updateTime = System.currentTimeMillis();
                        log.debug("OS doc with _id {} in index {} is getting updated with endTime {}", hits.get(0).id(), hits.get(0).index(), updateTime);

                        UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<>()
                                .index(hits.get(0).index())
                                .id(hits.get(0).id())
                                .doc(new HashMap<>() {{
                                    put("endTime", updateTime);
                                }}).build();

                        updateRequestList.add(updateRequest);
                    }
                }

            }

            BulkRequest.Builder bulkRequestBuilder = new BulkRequest.Builder();
            // Add each update request to the bulk request
            updateRequestList.forEach(updateRequest ->
                    bulkRequestBuilder.operations(op ->
                            op.update(updateReq -> updateReq
                                    .index(updateRequest.index())
                                    .id(updateRequest.id())
                                    .document(updateRequest.doc())
                                    .docAsUpsert(false)
                            )));
            // Build the request
            BulkRequest bulkRequest = bulkRequestBuilder.build();

            BulkResponse resp = openSearchClient.bulk(bulkRequest);
            if (resp.errors()) {
                for (BulkResponseItem item : resp.items()) {
                    if (item.error() != null) {
                        log.error("Failures during bulk indexing operation. Exception type: [{}], Reason: [{}]", item.error().type(),
                                item.error().causedBy() != null ? item.error().causedBy().reason() : item.error().reason());
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error in updating end time of instance kpi attribute. Details: ", e);
        }
    }

    public IndexRequest<InstanceKpiThresholds> addThreshold(InstanceKpiAttributeThresholdBean threshold, String indexName) throws JsonProcessingException {
        String compInstId = threshold.getCompInstanceIdentifier();
        String kpiId = String.valueOf(threshold.getKpiId());
        String kpiAttribute = threshold.getAttributeValue();

        long startTime = DateTimeUtil.getGMTToEpochTime(threshold.getStartTime());
        String date = DateHelper.getWeeksAsString(startTime, startTime).get(0);
        String exactIndexName = indexName + "_" + date;
        log.debug("Adding instance kpi details to index {}: with details {}", exactIndexName, threshold);

        String docId = compInstId + "#" + kpiId + "#" + kpiAttribute + "#" + startTime;

        Map<String, Double> thresholds = new HashMap<>();
        thresholds.put("Upper", threshold.getMaxThreshold());
        thresholds.put("Lower", threshold.getMinThreshold());

        InstanceKpiThresholds instanceKpiThreshold = InstanceKpiThresholds.builder()
                .instanceId(compInstId)
                .kpiId(Integer.parseInt(kpiId))
                .kpiAttribute(kpiAttribute)
                .startTime(startTime)
                .endTime(0)
                .serviceIdentifier("")
                .applicationId("")
                .description(threshold.getDescription())
                .operationType(threshold.getOperationName())
                .thresholdType(Constants.STATIC_THRESHOLD)
                .thresholds(thresholds)
                .timestamp(DateTimeUtil.date2GMTConversion(new Date(startTime), Constants.TIMESTAMP_FORMAT_INDEX_PATTERN))
                .build();

        return new IndexRequest.Builder<InstanceKpiThresholds>()
                .index(exactIndexName)
                .id(docId)
                .document(instanceKpiThreshold).build();
    }

    private static SearchResponse<Object> documentExist(InstanceKpiAttributeThresholdBean threshold, String indexName, String accountIdentifier) throws IOException {
        String indexPattern = indexName + "_" + accountIdentifier.toLowerCase() + "_*";

        Query qb = Query.of(c -> c.bool(d -> d.must(e -> e.matchPhrase(f -> f.field("instanceId").query(threshold.getCompInstanceIdentifier())))
                .must(e -> e.matchPhrase(f -> f.field("kpiId").query(String.valueOf(threshold.getKpiId()))))
                .must(e -> e.matchPhrase(f -> f.field("kpiAttribute").query(threshold.getAttributeValue())))
                .must(e -> e.matchPhrase(f -> f.field("thresholdType").query(Constants.STATIC_THRESHOLD)))
                .must(e -> e.matchPhrase(f -> f.field("endTime").query("0")))));

        return OpenSearchRepo.getLimitedSortedDocument(qb, indexPattern, "startTime", true, 1, accountIdentifier, indexName);
    }

    public Map<String, List<InstanceKpiThresholds>> getRealTimeThresholdByInstanceId(String accountIdentifier, String instanceId) {
        String indexPrefix = Constants.INDEX_PREFIX_HEAL_INSTANCE_KPI_THRESHOLDS + "_" + accountIdentifier.toLowerCase();

        Gson gson = new Gson();
        try {
            OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, Constants.INDEX_PREFIX_HEAL_INSTANCE_KPI_THRESHOLDS);
            if (openSearchClient == null) {
                log.error("OpenSearch client is null");
                return Collections.emptyMap();
            }

            // get n days back epoch time from current time
            Instant currentInstant = Instant.now();
            Duration nDaysAgo = Duration.ofDays(NUMBER_OF_PAST_DAYS_TO_GET_REALTIME_THRESHOLD);
            Instant nDaysAgoInstant = currentInstant.minus(nDaysAgo);

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(nDaysAgoInstant.toEpochMilli(), currentInstant.toEpochMilli()).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            Query qb = Query.of(c -> c.bool(d -> d.must(e -> e.matchPhrase(f -> f.field("instanceId").query(instanceId)))
                    .must(e -> e.matchPhrase(f -> f.field("thresholdType").query("REALTIME")))
                    .must(e -> e.matchPhrase(f -> f.field("endTime").query("0")))
                    .must(e -> e.range(f -> f.field("startTime").lte(JsonData.of(currentInstant.toEpochMilli()))))));

            Aggregation.Builder.ContainerBuilder groupByKpiIdTermsAgg = new Aggregation.Builder().terms(new TermsAggregation.Builder().field("kpiId").size(TOTAL_KPIS_FOR_REALTIME_THRESHOLD_AGGR_BUCKET).build());
            Aggregation.Builder.ContainerBuilder groupByAttributeTermsAgg = new Aggregation.Builder().terms(new TermsAggregation.Builder().field("kpiAttribute").size(10).build());
            Aggregation topDocTopHitsAgg = Aggregation.of(c -> c.topHits(d -> d.size(1).sort(e -> e.field(f -> f.field("@timestamp").order(SortOrder.Desc)))));

            Map<String, Aggregation> topDocTopHotsAggMap = new HashMap<>() {{
                put("top_document", topDocTopHitsAgg);
            }};
            groupByAttributeTermsAgg.aggregations(topDocTopHotsAggMap);
            Map<String, Aggregation> groupByAttributeAggMap = new HashMap<>() {{
                put("group_by_attribute", groupByAttributeTermsAgg.build());
            }};
            groupByKpiIdTermsAgg.aggregations(groupByAttributeAggMap);
            Map<String, Aggregation> groupByKpiIdAggMap = new HashMap<>() {{
                put("group_by_kpi", groupByKpiIdTermsAgg.build());
            }};

            SearchRequest searchRequest = new SearchRequest.Builder()
                    .index(indexNames)
                    .ignoreUnavailable(true)
                    .size(0)
                    .query(qb)
                    .aggregations(groupByKpiIdAggMap)
                    .build();


            // Execute the search request
            SearchResponse<Object> searchResponse = openSearchClient.search(searchRequest, Object.class);
            log.debug("os response from index {}, instance {}, look-backdate {} - {}",
                    indexPrefix, instanceId, NUMBER_OF_PAST_DAYS_TO_GET_REALTIME_THRESHOLD, searchResponse);

            Map<String, List<InstanceKpiThresholds>> instanceKpiThresholdsMap = new HashMap<>();

            if (searchResponse.aggregations() != null) {
                Aggregate groupByKpi = searchResponse.aggregations().get("group_by_kpi");
                if (groupByKpi.isLterms()) {
                    for (LongTermsBucket groupByKpiBucket : groupByKpi.lterms().buckets().array()) {
                        if (groupByKpiBucket.aggregations() != null) {
                            Aggregate groupByAttribute = groupByKpiBucket.aggregations().get("group_by_attribute");

                            if (groupByAttribute.isSterms()) {
                                for (StringTermsBucket groupByAttributeBucket : groupByAttribute.sterms().buckets().array()) {
                                    if (groupByAttributeBucket.aggregations() != null) {
                                        Aggregate topDocument = groupByAttributeBucket.aggregations().get("top_document");

                                        if (topDocument.isTopHits()) {
                                            List<Hit<JsonData>> k = topDocument.topHits().hits().hits();
                                            for (Hit<JsonData> hit : k) {
                                                InstanceKpiThresholds instanceKpiThresholds =
                                                        CommonUtils.getObjectMapperWithHtmlEncoder().readValue(gson.toJson(hit.source()), new TypeReference<>() {
                                                        });
                                                if (instanceKpiThresholds != null) {
                                                    if (instanceKpiThresholdsMap.containsKey(groupByKpiBucket.keyAsString())) {
                                                        instanceKpiThresholdsMap.get(groupByKpiBucket.keyAsString()).add(instanceKpiThresholds);
                                                    } else {
                                                        List<InstanceKpiThresholds> instanceKpiThresholdsArrayList = new ArrayList<>();
                                                        instanceKpiThresholdsArrayList.add(instanceKpiThresholds);
                                                        instanceKpiThresholdsMap.put(groupByKpiBucket.keyAsString(), instanceKpiThresholdsArrayList);
                                                    }
                                                }
                                            }
                                        }
                                    } else {
                                        log.debug("group by attribute bucket is empty, index {}, instance {}, look-backdate {} - {}",
                                                indexPrefix, instanceId, NUMBER_OF_PAST_DAYS_TO_GET_REALTIME_THRESHOLD, searchResponse);
                                    }
                                }
                            }
                        } else {
                            log.debug("no aggregation for top 1st timestamp, index {}, instance {}, look-backdate {} - {}",
                                    indexPrefix, instanceId, NUMBER_OF_PAST_DAYS_TO_GET_REALTIME_THRESHOLD, searchResponse);
                        }
                    }
                }
            } else {
                log.debug("no aggregation found at kpi level, index {}, instance {}, look-backdate  {} - {}",
                        indexPrefix, instanceId, NUMBER_OF_PAST_DAYS_TO_GET_REALTIME_THRESHOLD, searchResponse);
            }
            return instanceKpiThresholdsMap;
        } catch (Exception ex) {
            log.error("Exception occurred while querying the os index {}, instance id - {}, time range - {}",
                    Constants.INDEX_PREFIX_HEAL_INSTANCE_KPI_THRESHOLDS, instanceId, NUMBER_OF_PAST_DAYS_TO_GET_REALTIME_THRESHOLD, ex);
        }
        return Collections.emptyMap();
    }
}
