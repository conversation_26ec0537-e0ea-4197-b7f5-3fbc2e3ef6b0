package com.appnomic.appsone.controlcenter.dao.redis;

import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.Category;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

@Slf4j

public class CategoryRepo {

    String ACCOUNTS_KEY = "/accounts";
    String CATEGORY_KEY = "/categories";
    String ACCOUNTS_HASH = "ACCOUNTS_";
    String CATEGORY_HASH = "_CATEGORIES";




    public List<Category> getCategoryDetails(String accountIdentifier) {
        String hashKey = ACCOUNTS_KEY + "/" + accountIdentifier + CATEGORY_KEY;
        String hashValue = ACCOUNTS_HASH + accountIdentifier + CATEGORY_HASH;
        try {
            String categoryObject = RedisUtilities.getKey(hashKey, hashValue);
            if (categoryObject == null) {
                log.debug("Category details not found for account: [{}]", accountIdentifier);
                return Collections.emptyList();
            }

            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(categoryObject, new TypeReference<List<Category>>() {
            });
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while getting categories details for account [{}]. Details: ", accountIdentifier, e);
            return Collections.emptyList();
        }

    }

    public List<Category> updateCategoryDetails(String accountIdentifier, List<Category> Category) {
        try {
            RedisUtilities.updateKey(ACCOUNTS_KEY + "/" + accountIdentifier + CATEGORY_KEY,
                    ACCOUNTS_HASH  + accountIdentifier + CATEGORY_HASH, Category);
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while updating  the category details for accountIdentifier: {}", accountIdentifier, e);
        }

        return Category;
    }
    public void updateCategory( String accountIdentifier, Category categoryBean){
        String CATEGORY_HASH = "_CATEGORIES_";
        try {
            RedisUtilities.updateKey(ACCOUNTS_KEY + "/" + accountIdentifier + CATEGORY_KEY + "/" + categoryBean.getIdentifier() ,
                    ACCOUNTS_HASH + accountIdentifier + CATEGORY_HASH + categoryBean.getIdentifier(), categoryBean);
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while updating the category details for the accountIdentifier: {} and categoryIdentifier: {}",accountIdentifier, categoryBean.getIdentifier(),e);
        }
    }

    public void deleteCategoryInRedis(String accountIdentifier, String categoryIdentifier){
        try{
            RedisUtilities.deleteKey(ACCOUNTS_KEY + "/" + accountIdentifier + CATEGORY_KEY + "/" + categoryIdentifier,
                    ACCOUNTS_HASH + accountIdentifier + CATEGORY_HASH + "_" + categoryIdentifier);
        }catch (Exception e){
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Exception encountered while deleteAgentServiceMapping details for the accountIdentifier: {} and category: {}.", accountIdentifier, categoryIdentifier, e);
        }
    }

}

