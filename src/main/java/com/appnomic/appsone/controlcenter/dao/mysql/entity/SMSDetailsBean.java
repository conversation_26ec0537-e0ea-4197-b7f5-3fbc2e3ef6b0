package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.*;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class SMSDetailsBean {

    private int id;
    private String address;
    private int port;
    private String countryCode;
    private int protocolId;
    private String httpMethod;
    private String httpRelativeUrl;
    private int accountId;
    private String postData;
    private Integer postDataFlag;
    private String userDetailsId;
    private Date createdTime;
    private Date updatedTime;
    private int status;
    private int isMultiRequest;
    private String username;
    private String password;
    private int persistSmsNotifications;
}
