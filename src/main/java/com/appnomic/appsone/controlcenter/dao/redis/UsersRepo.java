package com.appnomic.appsone.controlcenter.dao.redis;

import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.entities.UserAccessDetails;
import com.heal.configuration.pojos.User;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class UsersRepo {
    String USERS_KEY = "/users";
    String USERS_HASH = "USERS_";
    String USER_ACCESS_DETAILS_KEY = "/accessDetails";
    String USER_ACCESS_DETAILS_HASH = "_ACCESSDETAILS";

    public void deleteUser(String userIdentifier) {
        try {
            RedisUtilities.deleteKey(USERS_KEY + "/" + userIdentifier, USERS_HASH + userIdentifier);
            log.debug("User with {} is removed from redis", userIdentifier);
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while fetching details for user {}", userIdentifier, e);
        }
    }

    public void addUser(User userDetail) {
        try {
            RedisUtilities.updateKey(USERS_KEY + "/" + userDetail.getUserDetailsId(), USERS_HASH + userDetail.getUserDetailsId(), userDetail);
            log.debug("User {} added to redis", userDetail.getUserDetailsId());
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while adding details for user with identifier {}", userDetail.getUserDetailsId(), e);
        }
    }

    public User getUser(String userIdentifier) {
        try {
            String userDetails = RedisUtilities.getKey(USERS_KEY + "/" + userIdentifier, USERS_HASH + userIdentifier);
            if (userDetails == null) {
                log.debug("User details unavailable for user {}", userIdentifier);
                return null;
            }

            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(userDetails, new TypeReference<User>() {
            });
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while getting details for userId {}", userIdentifier, e);
            return null;
        }
    }

    public List<UserAccessDetails> getUserAccessDetails(String userIdentifier) {
        try {
            String userDetails = RedisUtilities.getKey(USERS_KEY + "/" + userIdentifier + USER_ACCESS_DETAILS_KEY, USERS_HASH + userIdentifier + USER_ACCESS_DETAILS_HASH);
            if (userDetails == null) {
                log.debug("Access details unavailable for user {}", userIdentifier);
                return null;
            }

            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(userDetails, new TypeReference<List<UserAccessDetails>>() {
            });
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while fetching access details for user {}", userIdentifier, e);
            return null;
        }
    }

    public void updateUserAccessDetails(String userIdentifier, List<UserAccessDetails> userAccessDetails) {
        try {
            RedisUtilities.updateKey(USERS_KEY + "/" + userIdentifier + USER_ACCESS_DETAILS_KEY, USERS_HASH + userIdentifier + USER_ACCESS_DETAILS_HASH, userAccessDetails);
            log.debug("Access details added/updated for user {}", userIdentifier);
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while adding/updating access details for user {}", userIdentifier, e);
        }
    }

    public void deleteUserAccessDetails(String userIdentifier) {
        try {
            RedisUtilities.deleteKey(USERS_KEY + "/" + userIdentifier + USER_ACCESS_DETAILS_KEY,
                    USERS_HASH + userIdentifier + USER_ACCESS_DETAILS_HASH);

            log.debug("Access details for user {} is deleted", userIdentifier);
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while deleting access details key for user {}", userIdentifier, e);
        }
    }
}



