package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.ServiceConfigurationBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.ServiceConfigurationDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> on 21/8/19
 */

public class ServiceConfigurationDataService {
    private static final Logger logger = LoggerFactory.getLogger(ServiceConfigurationDataService.class);


    public static List<ServiceConfigurationBean> getServiceConfiguration(int accountId, int serviceId) {
        ServiceConfigurationDao serviceConfigurationDao = MySQLConnectionManager.getInstance()
                .open(ServiceConfigurationDao.class);
        try {
            return serviceConfigurationDao.getServiceConfiguration(accountId, serviceId);
        } catch (Exception e) {
            logger.error("Exception while getting service configuration" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(serviceConfigurationDao);
        }
        return new ArrayList<>();
    }


    //------------------------------------------------------------------------------------------------------------

    public static int[] addService(List<ServiceConfigurationBean> serviceConfigurationList, Handle handle) {
        ServiceConfigurationDao serviceConfigurationDao = getServiceConfigurationDao(handle);
        try {
            return serviceConfigurationDao.addServiceConfiguration(serviceConfigurationList);
        } catch (Exception e) {
            logger.error("Error occurred while adding values for persistence and suppression" + e.getMessage(), e);
            logger.debug("trace:", e);
        } finally {
            closeDaoConnection(handle, serviceConfigurationDao);
        }
        return null;
    }

    public static void updateService(List<ServiceConfigurationBean> serviceBeanList) throws ControlCenterException {
        ServiceConfigurationDao serviceConfigurationDao = MySQLConnectionManager.getInstance().open(ServiceConfigurationDao.class);
        try {
            serviceConfigurationDao.updateServiceConfiguration(serviceBeanList);
        } catch (Exception e) {
            logger.error("Error occurred while updating values for persistence and suppression" + e.getMessage(), e);
            throw new ControlCenterException("Failed to update persistence and suppression values for service.");
        } finally {
            MySQLConnectionManager.getInstance().close(serviceConfigurationDao);
        }
    }

    private static ServiceConfigurationDao getServiceConfigurationDao(Handle handle) {
        if (handle == null) {
            return MySQLConnectionManager.getInstance().open(ServiceConfigurationDao.class);
        } else {
            return handle.attach(ServiceConfigurationDao.class);
        }
    }

    private static void closeDaoConnection(Handle handle, ServiceConfigurationDao dao) {
        if (handle == null) {
            MySQLConnectionManager.getInstance().close(dao);
        }
    }

    public static List<ServiceConfigurationBean> getServiceConfiguration(int accountId) {
        ServiceConfigurationDao serviceConfigurationDao = MySQLConnectionManager.getInstance()
                .open(ServiceConfigurationDao.class);
        try {
            return serviceConfigurationDao.getServiceConfiguration(accountId);
        } catch (Exception e) {
            logger.error("Exception while getting service configuration" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(serviceConfigurationDao);
        }
        return new ArrayList<>();
    }
}