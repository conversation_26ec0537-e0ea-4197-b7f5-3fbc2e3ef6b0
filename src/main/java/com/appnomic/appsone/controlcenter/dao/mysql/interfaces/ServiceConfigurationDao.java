package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;


import com.appnomic.appsone.controlcenter.dao.mysql.entity.ServiceConfigurationBean;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface ServiceConfigurationDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, account_id accountId,service_id serviceId,user_details_id userDetailsId," +
            "created_time createdTime,updated_time updatedTime,start_collection_interval " +
            "startCollectionInterval,end_collection_interval endCollectionInterval," +
            "sor_persistence sorPersistence,sor_suppression sorSuppression, " +
            "nor_persistence norPersistence, nor_suppression norSuppression " +
            "from service_configurations where account_id = :accountId and service_id = :serviceId")
    List<ServiceConfigurationBean> getServiceConfiguration(@Bind("accountId") int accountId,
                                                           @Bind("serviceId") int serviceId);


    @SqlBatch("INSERT INTO service_configurations ( service_id,account_id, user_details_id, created_time, " +
            "updated_time, start_collection_interval, end_collection_interval, sor_persistence, sor_suppression, " +
            "nor_persistence, nor_suppression) " +
            "VALUES ( :serviceId,:accountId, :userDetailsId, :createdTime, :updatedTime, :startCollectionInterval, " +
            ":endCollectionInterval, :sorPersistence, :sorSuppression, :norPersistence, :norSuppression)")
    @GetGeneratedKeys
    int[] addServiceConfiguration(@BindBean List<ServiceConfigurationBean> serviceDetails);


    @SqlBatch("UPDATE service_configurations SET user_details_id = :userDetailsId,sor_persistence = :sorPersistence, " +
            "sor_suppression = :sorSuppression, nor_persistence = :norPersistence, nor_suppression =:norSuppression ,updated_time = :updatedTime " +
            "where account_id = :accountId and service_id = :serviceId and id = :id")
    void updateServiceConfiguration(@BindBean List<ServiceConfigurationBean> serviceDetails);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, account_id accountId,service_id serviceId,user_details_id userDetailsId," +
            "created_time createdTime,updated_time updatedTime,start_collection_interval " +
            "startCollectionInterval,end_collection_interval endCollectionInterval," +
            "sor_persistence sorPersistence,sor_suppression sorSuppression, " +
            "nor_persistence norPersistence, nor_suppression norSuppression " +
            "from service_configurations where account_id = :accountId;")
    List<ServiceConfigurationBean> getServiceConfiguration(@Bind("accountId") int accountId);
}