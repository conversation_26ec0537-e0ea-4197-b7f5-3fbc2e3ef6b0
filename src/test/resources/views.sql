-- All types and subtypes(view_types)
DROP VIEW if exists view_types;
CREATE VIEW view_types AS
select t.type, t.id typeid, st.name, st.id subtypeid
from mst_type t, mst_sub_type st
where t.id = st.mst_type_id;

-- All component, component type, component version(view_components)
DROP VIEW if exists view_components;
CREATE VIEW view_components AS
select ct.id component_type_id,ct.name component_type_name, ct.status component_type_status, c.id component_id, c.name component_name,
c.is_custom, c.status component_status, cv.id common_version_id, cv.name common_version_name, v.id component_version_id,
v.name component_version_name, v.is_custom is_custom_version, v.status is_version_status, v.version_from, v.version_to
from mst_component_type ct, mst_component c, mst_component_mapping cm,
mst_common_version cv, mst_component_version v
where ct.id = cm.mst_component_type_id and cm.mst_component_id = c.id and c.id = v.mst_component_id
and v.mst_common_version_id=cv.id;

-- All component attributes for each component common version.(view_attributes)
DROP VIEW if exists view_attributes;
CREATE VIEW view_attributes AS
select cam.id mst_component_attribute_mapping_id, cv.id mst_common_version_id, cv.mst_component_id, cv.name mst_common_name, ca.id attribute_id, ca.attribute_name,
ca.is_custom, ca.status, cam.is_mandatory, cam.default_value, cam.is_ui_visible, ca.attribute_type, cam.min_length, cam.max_length, cam.regex,
cam.error_message errorMessage
from mst_common_attributes ca, mst_component_attribute_mapping cam,
mst_common_version cv
where ca.id = cam.mst_common_attributes_id and cam.mst_common_version_id = cv.id;


-- All kpi group and kpis of each group.(view_kpi_groups)
DROP VIEW if exists view_kpi_groups;
CREATE VIEW view_kpi_groups AS
select g.id groupid,g.name groupname,g.identifier group_identifier, g.discovery, g.is_custom, g.status groupstatus, k.id kpiid,
k.name kpiname, k.identifier, k.value_type, g.account_id, k.status kpistatus, st.name kpitype, k.measure_units, k.cluster_operation,
k.data_type,k.rollup_operation,k.cluster_aggregation_type,k.instance_aggregation_type
from mst_kpi_group g, mst_kpi_details k, mst_sub_type st
where k.kpi_group_id = g.id and g.kpi_type_id = st.id;


-- All kpis without the group.(view_kpis)
DROP VIEW if exists view_kpis;
CREATE VIEW view_kpis AS
select k.id kpiid,
k.name kpi_name, k.identifier, k.value_type, k.account_id, k.status kpistatus,
st.name kpi_type, k.is_custom, k.measure_units, k.cluster_operation, k.data_type,k.rollup_operation,
k.cluster_aggregation_type,k.instance_aggregation_type
from mst_kpi_details k, mst_sub_type st
where k.kpi_group_id = 0 and k.kpi_type_id = st.id;


-- All kpis.(view_all_kpis)
DROP VIEW if exists view_all_kpis;
CREATE VIEW view_all_kpis AS
select st.name kpi_type, g.id group_id,g.name group_name,g.identifier group_identifier, g.discovery, g.is_custom, g.status group_status, k.id kpiid,
k.name kpi_name, k.identifier, k.value_type, k.account_id, k.status kpi_status, k.measure_units, k.cluster_operation,
k.data_type,k.rollup_operation,k.cluster_aggregation_type,k.instance_aggregation_type,k.description, k.is_informative
from mst_kpi_group g right join mst_kpi_details k on k.kpi_group_id = g.id, mst_sub_type st where st.id = k.kpi_type_id;





-- All common version component and kpis.(view_common_version_kpis)
DROP VIEW if exists view_common_version_kpis;
CREATE VIEW view_common_version_kpis AS
select cv.mst_component_id, k.id kpi_id, k.name kpi_name, k.identifier kpi_identifier,
m.mst_common_version_id, k.kpi_group_id,k.kpi_type_id,
m.default_collection_interval, m.status, m.default_operation_id, m.default_threshold, k.account_id,
m.do_analytics, ifnull((select discovery from mst_kpi_group g where g.id = k.kpi_group_id), 0) is_discovery
from mst_component_version_kpi_mapping m, mst_common_version cv,
mst_kpi_details k
where m.mst_common_version_id = cv.id and m.mst_kpi_details_id = k.id;

-- All producer details.(view_producers)
DROP VIEW if exists view_producers;
CREATE VIEW view_producers AS
select p.id producer_id, p.name producer_name, p.is_custom, p.status,
p.account_id, p.is_kpi_group, st.name kpi_type, pt.type producer_type, pt.classname
from mst_producer p, mst_sub_type st, mst_producer_type pt
where st.id = p.mst_sub_type_id and pt.id= p.mst_producer_type_id;


-- All producer and kpi details.(view_producer_kpis)
DROP VIEW if exists view_producer_kpis;
CREATE VIEW view_producer_kpis AS
select p.id producer_id, p.name producer_name, p.is_custom, p.status,
p.account_id, p.is_kpi_group, st.name kpi_type, pt.type producer_type, pt.classname, pkm.is_default,
pkm.mst_kpi_details_id, pkm.mst_component_version_id, k.name kpi_name, pkm.mst_component_id,
pkm.mst_component_type_id, pkm.id mst_producer_kpi_mapping_id, k.identifier kpi_identifier
from mst_producer p, mst_sub_type st, mst_producer_type pt,
mst_producer_kpi_mapping pkm, mst_kpi_details k
where st.id = p.mst_sub_type_id and pt.id= p.mst_producer_type_id and pkm.producer_id = p.id
and pkm.mst_kpi_details_id = k.id;


-- All component instance.(view_component_instance)
DROP VIEW if exists view_component_instance;
CREATE VIEW view_component_instance AS
select c.id, c.name, c.status, c.identifier,
c.host_id,
(select name from comp_instance where id=c.host_id) host_name,
c.is_cluster,
c.account_id,
c.user_details_id, c.discovery, c.host_address, c.mst_component_id, vc.component_name,
c.mst_component_type_id, vc.component_type_name, c.mst_component_version_id, vc.component_version_name,
vc.common_version_id, vc.common_version_name, c.created_time, c.updated_time, c.supervisor_id
from comp_instance c, view_components vc
where c.mst_component_id=vc.component_id and c.mst_component_type_id=vc.component_type_id
and c.mst_component_version_id = vc.component_version_id;

-- All coverage window details.(view_coverage_window_profile_details)
DROP VIEW if exists view_coverage_window_profile_details;
CREATE VIEW view_coverage_window_profile_details AS
select cwp.id profile_id, cwp.name profile_name, cwp.day_option_id,st.name day_option_name, cwp.status, cwp.account_id, cw.day, cw.start_hour, cw.start_minute, cw.end_hour, cw.end_minute,cw.is_business_hour from coverage_window cw, coverage_window_profile cwp, mst_sub_type st where cw.coverage_window_profile_id=cwp.id and st.id = cwp.day_option_id;

-- All kpi with category mapping.
DROP VIEW if exists view_kpi_category_details;
CREATE VIEW view_kpi_category_details AS
select object_id kpi_id, c.id category_id, c.name, c.identifier category_identifier, c.is_workload
from tag_mapping tm, mst_kpi_details kp, mst_category_details c
where tm.object_id = kp.id and tm.object_ref_table='mst_kpi_details' and tm.tag_key = c.id and c.status=1;


-- All component instance attributes data
DROP VIEW if exists view_comp_instance_attributes;
CREATE VIEW view_comp_instance_attributes AS
select c.id,c.attribute_value attributeValue,c.comp_instance_id compInstanceId,
c.mst_component_attribute_mapping_id mstComponentAttributeMappingId,
c.created_time createdTime,c.updated_time updatedTime,c.user_details_id userDetailsId,
c.mst_common_attributes_id mstCommonAttributesId,c.attribute_name attributeName,
m.is_mandatory isMandatory, a.name displayName,m.is_ui_visible as isUiVisible,
m.min_length minLength, m.max_length maxLength, m.regex, a.attribute_type attributeType, m.error_message errorMessage
from comp_instance_attribute_values c, mst_component_attribute_mapping m,
mst_common_attributes a
where
c.mst_common_attributes_id = m.mst_common_attributes_id and
c.mst_component_attribute_mapping_id = m.id
and m.mst_common_attributes_id = a.id;

-- All component instance kpis data
DROP VIEW if exists view_comp_instance_kpis;
CREATE VIEW view_comp_instance_kpis AS
select k.kpi_type, k.kpiid, k.identifier kpi_identifier, k.kpi_name, c.id instance_id,
k.group_name, k.group_id, k.discovery, null attribute_value,c.id comp_instance_id
from comp_instance c, comp_instance_kpi_details ck, view_all_kpis k
where c.id = ck.comp_instance_id and ck.mst_kpi_details_id = k.kpiid
union
select k.kpi_type, k.kpiid, k.identifier kpi_identifier, k.kpi_name, c.id instance_id,
k.group_name, k.group_id, k.discovery, ck.attribute_value,c.id comp_instance_id
from comp_instance c, comp_instance_kpi_group_details ck, view_all_kpis k
where c.id = ck.comp_instance_id and ck.mst_kpi_details_id = k.kpiid;

DROP VIEW if exists view_access_details;
create view view_access_details as select a.name access_profile_name,
a.id access_profile_id, r.name role_name, r.id role_id,
b.name big_feature_name, b.id big_feature_id, b.ui_visible feature_ui_visible
from mst_roles r, mst_big_features b, mst_access_profiles a, mst_access_profile_mapping am
where r.id = a.mst_role_id and a.id = am.mst_access_profile_id and am.mst_big_feature_id = b.id
and r.status=1 and a.status = 1 and b.status=1;

DROP VIEW if exists view_route_details;
create view view_route_details as select p.id action_id, p.name action_name, p.identifier action_identifier, r.id route_id, r.request_method, r.base_url, r.path_info,
r.path_info_regex, b.id big_feature_id, b.identifier big_feature_identifier, b.name big_feature_name, b.dashboard_name
from mst_page_actions p, mst_routes r, mst_routes_mapping rm, mst_big_features b
where rm.mst_route_id = r.id and rm.mst_big_feature_id = b.id and rm.mst_page_action_id = p.id;


DROP VIEW if exists view_application_service_mapping;
CREATE VIEW view_application_service_mapping as SELECT a.id AS application_id, a.name AS application_name, a.identifier AS application_identifier, s.id AS service_id, s.name AS service_name,
s.identifier AS service_identifier, tm.account_id
FROM controller a, tag_mapping tm, controller s
WHERE tm.tag_id = 1 AND a.account_id = tm.account_id AND tm.object_ref_table = 'controller' AND tm.object_id = a.id
AND a.status = 1 AND a.controller_type_id = 191 AND tm.account_id = s.account_id AND tm.tag_key = s.id AND s.status = 1 AND s.controller_type_id = 192;

DROP VIEW if exists view_cluster_applications;
CREATE VIEW view_cluster_applications AS
SELECT c.id AS id, c.name AS name, c.identifier AS identifier, c.host_id host_cluster_id, c.mst_component_id, c.mst_component_type_id, c.mst_component_version_id,
cnt.id AS application_id, cnt.name AS application_name,cnt.identifier AS application_identifier
FROM comp_instance c, tag_mapping tm, controller cnt
WHERE c.status = 1 AND c.is_cluster = 1 AND c.account_id = tm.account_id AND tm.tag_id = 1 AND tm.object_ref_table = 'comp_instance' AND tm.object_id = c.id
AND tm.tag_key = cnt.id AND cnt.controller_type_id = 191 AND c.status = 1;

DROP VIEW if exists view_cluster_services;
CREATE VIEW view_cluster_services AS
SELECT c.id AS id, c.name AS name, c.identifier AS identifier, c.host_id host_cluster_id, c.mst_component_id, c.mst_component_type_id, c.mst_component_version_id,
cnt.id AS service_id,cnt.name AS service_name, cnt.identifier AS service_identifier
FROM comp_instance c, tag_mapping tm, controller cnt
WHERE c.status = 1 AND c.is_cluster = 1 AND c.account_id = tm.account_id AND tm.tag_id = 1 AND tm.object_ref_table = 'comp_instance'
AND tm.object_id = c.id AND tm.tag_key = cnt.id AND cnt.controller_type_id = 192 AND c.status = 1;

DROP VIEW if exists view_transaction_service;
CREATE VIEW view_transaction_service AS
select t.*,c.id serviceId, c.identifier serviceIdentifier, c.name serviceName
from transaction t, tag_mapping tm, controller c where
t.status=1 and t.id = tm.object_id and tm.tag_id=1 and tm.object_ref_table ='transaction'
and tm.account_id = t.account_id and tm.tag_key = c.id and
c.status = 1 and c.controller_type_id = 192 and c.account_id= tm.account_id;