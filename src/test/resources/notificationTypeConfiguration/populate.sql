INSERT INTO `account` (`id`,`name`,`created_time`,`updated_time`,`status`,`private_key`,`public_key`,`user_details_id`,`identifier`) VALUES (1,'Global','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIAAWoEJHwA9hOkSIOEjRfjhLg+JM5F43Hy8TfQ0YXUIyV/3dMS+JnNf+mBS4RfTsfEb1i2uL6hn7cXU491iuLt54f1BLshQjfoAcGBSuBBAAnoYGVA4GSAAQBPxDDWJdKBdAjgQoC62jaS9YGt4ZIQNjswquEtORgAScGqobCarv5D6UTQuxae/GY4EFR+jXulYFNG96+TF6hNe2oSV/5K6AFzg4uGgoCwXD7PJS1E48Peh85aP7fNiKJaqbjw7LlmkFEAPZlhLBddQVxkj8zBU1aRDKodyviw8v4TCLjhqNRPnm1mIq9qSM=','MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQBPxDDWJdKBdAjgQoC62jaS9YGt4ZIQNjswquEtORgAScGqobCarv5D6UTQuxae/GY4EFR+jXulYFNG96+TF6hNe2oSV/5K6AFzg4uGgoCwXD7PJS1E48Peh85aP7fNiKJaqbjw7LlmkFEAPZlhLBddQVxkj8zBU1aRDKodyviw8v4TCLjhqNRPnm1mIq9qSM=','7640123a-fbde-4fe5-9812-581cd1e3a9c1','e573f852-5057-11e9-8fd2-b37b61e52317');
INSERT INTO `account` (`id`,`name`,`created_time`,`updated_time`,`status`,`private_key`,`public_key`,`user_details_id`,`identifier`) VALUES (2,'INDIA','2020-04-10 12:00:10','2020-04-10 12:00:10',1,'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIAAOxBOeiCVO04kbWju45hZvjhoT5cyQA2jg3DU51yjx48WGKuiJXeXYajB+pXHbWUcCyAX7Eeo5UsCDJ2/QBzcocl+vPqECgoAcGBSuBBAAnoYGVA4GSAAQFtdTb89vNgdj+5AyxF5bOwKxrAOQsDQkHhB/QffmXuQyD73eEyT5NKr5nJV9agfG7s5rs5HFJwr62xVwfs0gUhAHGxbpw3fMAOK6V46DFDIePKSxp+8w5dvPp5VD0aSGmVYVD3FOK1MdcgPLkPFeiFc6RM8VNKtfL0w7aC0fRVejHH6P6+OM75as+nC4p5wE=','MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQFtdTb89vNgdj+5AyxF5bOwKxrAOQsDQkHhB/QffmXuQyD73eEyT5NKr5nJV9agfG7s5rs5HFJwr62xVwfs0gUhAHGxbpw3fMAOK6V46DFDIePKSxp+8w5dvPp5VD0aSGmVYVD3FOK1MdcgPLkPFeiFc6RM8VNKtfL0w7aC0fRVejHH6P6+OM75as+nC4p5wE=','7640123a-fbde-4fe5-9812-581cd1e3a9c1','d681ef13-d690-4917-jkhg-6c79b-1');
INSERT INTO `account` (`id`,`name`,`created_time`,`updated_time`,`status`,`private_key`,`public_key`,`user_details_id`,`identifier`) VALUES (3,'Singapore','2020-04-10 12:01:56','2020-04-10 12:01:56',1,'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIAub9hjB9WTz+5FtGPYGtoAiUdS11lGC+UhinIzirG9BT3dsJEEALyW3xDHLMERoEMSt3U9EwZHnRYW35G9H49I1HluTyJtimoAcGBSuBBAAnoYGVA4GSAAQEmoko+HZRzIsU6lFyzOG/GVGzfc6ATuMy2M3Pdk9FOzkJxwGx6fQN/1MjHp76ks4EQ52umfOgGVoX/lAbeX7P8ajUOphohpoG99jVsof+k6jAk42/0KeBlw2RMvN0Q+w6ByNGP4+VPDBz7OTu3PVR0hnJ8FivyJl391hXkliK+S6efNIYB+HBZQox5fDwhJ0=','MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQEmoko+HZRzIsU6lFyzOG/GVGzfc6ATuMy2M3Pdk9FOzkJxwGx6fQN/1MjHp76ks4EQ52umfOgGVoX/lAbeX7P8ajUOphohpoG99jVsof+k6jAk42/0KeBlw2RMvN0Q+w6ByNGP4+VPDBz7OTu3PVR0hnJ8FivyJl391hXkliK+S6efNIYB+HBZQox5fDwhJ0=','7640123a-fbde-4fe5-9812-581cd1e3a9c1','d681ef13-d690-4917-jkhg-6c79b-2');
INSERT INTO `account` (`id`,`name`,`created_time`,`updated_time`,`status`,`private_key`,`public_key`,`user_details_id`,`identifier`) VALUES (4,'Alaska','2020-04-10 12:02:46','2020-04-10 12:02:46',1,'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIAX/K7OJ1UEVkERkdvWOKSdLRcAxW4FubQRAn4OeyWSZhTNMeeaOYDZWokhhJ0GnLTkXmKhwFFj0+EUTKSz0ArsZa4WJXwg/3oAcGBSuBBAAnoYGVA4GSAAQAB4YxkRBci0KF5bxK2BckHWBR15dkv00no30ZB5zem+DMzQMY6FSqvxohu+onDKVXfki2XLSoE0KkRQ2ISJmYwDWW6qef9gIGTWfgTvcspZ2XboBxJcX5/uYW5CVpIiz3ewOI7iRfJqrXTXnLUvakTiLDCS4QrR7MQl8OPKBfzQbicKIZ1VJQR0jkRJ8ef9M=','MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQAB4YxkRBci0KF5bxK2BckHWBR15dkv00no30ZB5zem+DMzQMY6FSqvxohu+onDKVXfki2XLSoE0KkRQ2ISJmYwDWW6qef9gIGTWfgTvcspZ2XboBxJcX5/uYW5CVpIiz3ewOI7iRfJqrXTXnLUvakTiLDCS4QrR7MQl8OPKBfzQbicKIZ1VJQR0jkRJ8ef9M=','7640123a-fbde-4fe5-9812-581cd1e3a9c1','d681ef13-d690-4917-jkhg-6c79b-3');
INSERT INTO `account` (`id`,`name`,`created_time`,`updated_time`,`status`,`private_key`,`public_key`,`user_details_id`,`identifier`) VALUES (5,'ACME-EStore','2020-04-10 12:05:37','2020-04-10 12:05:37',1,'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIASrD6pbY1YgxSstXlTFUBl+lBDfSgkxY9era3twXl9dB9qGuOfl9+Ig8ko2VegiJ06LOJcxlfwef0BEE0zBTGH+4bZCXIS3CoAcGBSuBBAAnoYGVA4GSAAQFKwTxE+Qda84d8vh+MGic9FuL7Id0B7rd1K+R/J1SZeFeKyhMpV0nF5UbxHmfnb9XOo9Ib+JJhQSwYxK32bKfcmBXL05Tm+IBjCo5z1pm80TMXsKa/2ajEufEUcI2S4nVRPBRCoPxjmcfZKdkFWISiqF/0ufJNblKQVAOYUF83Gokisyt7viEs2LKYUTJLT4=','MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQFKwTxE+Qda84d8vh+MGic9FuL7Id0B7rd1K+R/J1SZeFeKyhMpV0nF5UbxHmfnb9XOo9Ib+JJhQSwYxK32bKfcmBXL05Tm+IBjCo5z1pm80TMXsKa/2ajEufEUcI2S4nVRPBRCoPxjmcfZKdkFWISiqF/0ufJNblKQVAOYUF83Gokisyt7viEs2LKYUTJLT4=','7640123a-fbde-4fe5-9812-581cd1e3a9c1','c681ef13-d690-4917-jkhg-6c79b-1');
INSERT INTO `account` (`id`,`name`,`created_time`,`updated_time`,`status`,`private_key`,`public_key`,`user_details_id`,`identifier`) VALUES (6,'Bangalore-India','2020-04-17 12:45:04','2020-04-17 12:45:04',1,'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIAEGCauHyfuApTYe/drmifvdv5uFWqGx2PZObkKbjQHfG7bAp8JmU13pJ1kstrfZKKAo0hcZOVzZXHUOZOnYdqWrW4ZuI39GRoAcGBSuBBAAnoYGVA4GSAAQDNoLpxBcNLjiH1A6Hd6UEQjpQcyFWTiQ/JWizM1jQWEFtXcowGZMiKzUWYyMgFkZKLvZeOjlN7mtlGkcRtxW5UoUawBRQaN0BOH+MTFG8dn7bIEuGq1vAB0KqH4Atm5tOKZMRiKu7//ZvR5ZL3w+RukNMQQ9Pj1WTbzYLSeqqH9z5YWeAlHBy90+uDX0dfpM=','MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQDNoLpxBcNLjiH1A6Hd6UEQjpQcyFWTiQ/JWizM1jQWEFtXcowGZMiKzUWYyMgFkZKLvZeOjlN7mtlGkcRtxW5UoUawBRQaN0BOH+MTFG8dn7bIEuGq1vAB0KqH4Atm5tOKZMRiKu7//ZvR5ZL3w+RukNMQQ9Pj1WTbzYLSeqqH9z5YWeAlHBy90+uDX0dfpM=','7640123a-fbde-4fe5-9812-581cd1e3a9c1','3bd72c00-8a73-413a-a378-662f7158c0d4');
INSERT INTO `account` (`id`,`name`,`created_time`,`updated_time`,`status`,`private_key`,`public_key`,`user_details_id`,`identifier`) VALUES (7,'Bangalore-India-alt','2020-04-17 13:00:41','2020-04-17 13:00:41',1,'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIAK1ApLfw+u3R5s0E44roQoLaM0+teyBoieRUVtqK9LXnQcE6GdFzkzizvmkrRtwxj1i/1NQ87AORGCd5sa2RtD3P1YY3OoLFoAcGBSuBBAAnoYGVA4GSAAQDYRf599XtYLGAT1B0JP61Bs9zD9hhmCpInBZg8AuadbDuCbkAJkOl38OkWcDyAW+NfVBaxFxWFUkNDOMjXAtKrX65uH8/i9ACN8BqGrahVW5nSi8Gtb7cTiangvf8snghmVzmdiYel+ZUgDj2eH3Y9twcq3Y4d/rCvmZFFs5/dzzjbUvYX2gUK48fZSl1BCg=','MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQDYRf599XtYLGAT1B0JP61Bs9zD9hhmCpInBZg8AuadbDuCbkAJkOl38OkWcDyAW+NfVBaxFxWFUkNDOMjXAtKrX65uH8/i9ACN8BqGrahVW5nSi8Gtb7cTiangvf8snghmVzmdiYel+ZUgDj2eH3Y9twcq3Y4d/rCvmZFFs5/dzzjbUvYX2gUK48fZSl1BCg=','7640123a-fbde-4fe5-9812-581cd1e3a9c1','831455cf-ca2b-49c8-86bb-8298b8a22023');

insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (1,'Agent','Agent type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (2,'Application','Application type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values
(931, 'ForensicCmds', 'Type of forenisc commands.', '2019-12-12 00:00:00', '2019-12-12 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (3,'Attribute_Type','Attributes field type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (4,'Availability_DataType','Availability KPI datatype','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (5,'Cluster_Operation','Cluster operations','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (6,'Communication_Endpoint','Communication endpoints of the data server','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (7,'Core_DataType','Core KPI datatype','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (8,'Forensic_DataType','Forensic KPI datatype','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (9,'HTTPMethod','HTTP Method types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (10,'KPI','KPI type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (11,'IntegerKpiUnits','Integer type KPI units','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (12,'FloatKpiUnits','Float type KPI units','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (13,'TextKpiUnits','Text type KPI units','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (14,'Transaction','Transaction types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (15,'Transaction_Attributes','Transaction attribute types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (16,'Transaction_Respone_Type','Transaction response time types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (17,'Object_Type','Object types for tags','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (18,'Apache Protocol','Types of protocol for Apache HTTPD','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (19,'Connect With','Oracle connectivity type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (20,'JDBC_Parameter_Type','JDBC Parameter Type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (21,'SCRIPT_Parameter_Type','SSH Parameter Type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (22,'WMI_Parameter_Type','Shell Parameter Type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (23,'JMX_Parameter_Type','JMX Parameter Type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (24,'ConfigWatch_DataType','Config watch KPI datatype','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (25,'FileWatch_DataType','File watch KPI datatype','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (26,'QueryResult','Result type of JDBC queries','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (27,'PSAgentOperationMode','Operation modes for PSAgent','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (28,'ComponentAgentOperationMode','Operation modes for Component Agent','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (29,'ResponseOptions','Options for PSAgent response data','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (30,'ServerDetailProtocols','PSAgent supported protocols for data capture','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (31,'HTTPProxyProtocol','PSAgent proxy protocols','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (32,'HTTPDataParts','Transaction data parts for HTTP','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (33,'TCPDataParts','Transaction data parts for TCP','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (34,'QueryParam','Extractors for query parameters','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (35,'Header','Extractors for headers','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (36,'HTTPRequestBody','Extractors for httprequest body','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (37,'HTTPResponseBody','Extractors for httpresponse body','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (38,'TCPRequestBody','Extractors for tcprequest body','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (39,'TCPResponseBody','Extractors for tcpresponse body','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (40,'UIDashboardTypes','Various dashboard types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (41,'UIPodOperation','Operation for UI Pods','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (42,'MethodType','Method type for JIM transactions','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (43,'TransformationType','Transformation type for JIM transactions','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (44,'ResponseTimeType','Transaction response time type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (45,'CommonPlaceholders','Placeholders for sms/email notification','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (46,'DayOptions','Option for all days in a week','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (47,'Days','All days in a week','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (48,'Operations','Operations type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (49,'SMTP Security','SMTP Security','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (50,'TransactionStatus','Status of a transaction','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (51,'TransactionKPITypes','Various thresholds type for a transaction','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (52,'SMSGatewayProtocols','SMS Gateway Protocols','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (53,'HTTPSMSRequestMethods','HTTP SMS Request Parameter Types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (54,'SMSParameterTypes','SMS Parameter Types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (57,'SMSPlaceHolders','SMS Placeholders','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (58,'CorePlaceHolders','Placeholder for core kpi type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (59,'AvailabilityPlaceHolders','Placeholder for availability kpi type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (60,'FileWatchPlaceHolders','Placeholder for file watch kpi type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (61,'ConfigWatchPlaceHolders','Placeholder for config watch kpi type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (62,'TransactionPlaceHolders','Placeholder for transactions','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (63,'UserRegistrationType','User registration type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (64,'UserRegistrationTypePlaceHolders','Placeholder for user registration type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (65,'UserLockStatus','User lock status type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (66,'JMXAttributeDataType','JMX attribute type for different set of attribute values','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (67,'SecuredType','Security type for JPPF, value should be either TRUE or FALSE','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (68,'JPPFType','JPPF type for server and node','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (69,'TagType','Data details of tag types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (70,'ControllerType','Controller types based on installation','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (71,'AggregationType','Group KPI collation based on types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (72,'XPTFlowDropDown','KPIs for XPT flow level','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (73,'XPTStepDropDown','KPIs for XPT step level','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (74,'RuleType','Rule pattern types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (75,'PayloadType','HTTP Payload types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (76,'PairType','HTTP key value types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (77,'JIMExitType','JIM exit types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (78,'SupervisorType','Type of supervisors','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (79,'CommandExecType','Execution method to run commands','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (80,'CommandType','Command type for execution','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (81,'StandardType','Type for distinction between custom or OOB','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (82,'CommandOutputType','Type of output of the command','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (83,'DownloadType','Type of download files','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (84,'JIMExitFramework','Type of JIM exit frameworks','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (85,'ForensicExecType','Type of execution by forensics','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (86,'AppsoneComponents','List of Appsone components','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (87,'AgentDataType','Type of agents based on load/behaviour KPI type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (88,'ThresholdType', 'Type of thresholds for data points', '2019-08-09 00:00:00','2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (89,'AvailabilityOperations', 'Operations type for availability', '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (90,'JIMAgentMode', 'Type of modes for JIM Agent.', '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (91,'TemplateType', 'Type of notification templates.', '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (92,'MaintenanceCmds', 'Type of maintenance commands.', '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (93,'ForensicCmds', 'Type of forenisc commands.', '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (94,'DiscoveryCmds', 'Type of discovery commands.', '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (95,'CannedCmds', 'Type of canned commands.', '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (96,'ConfigurationCmds', 'Type of configuration commands.', '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (97,'HealthCmds', 'Type of health commands.', '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (98,'AgentOperationCmds', 'Type of agent operation commands.', '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
insert into mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) values (99,'RollUpOperation','RollUp operations','2020-01-06 00:00:00','2020-01-06 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);

insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (1,'ComponentAgent',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Component Agent is the agent to collect the KPI data for Components',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (2,'JIMAgent',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Java Intrusive Monitoring Agent is the agent to collect the KPI data for the Java Applications',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (3,'PSAgent',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Protocol Stack Agent is the agent to collect the KPI data for the Transactions',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (4,'NoOpAgent',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dummy agent for no use',0,0);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (5,'SyntheticAgent',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent for synthetic monitoring',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (6,'DotNet',2,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dotnet based Application',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (7,'Finacle10',2,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Finacle_10 based Applications',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (8,'Finacle7',2,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Finacle_7 based Application',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (9,'Flexcube',2,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Flexcube based Application',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (10,'Java',2,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Java based Application',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (11,'WebServices',2,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Web services based Application',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (12,'TextBox',3,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Attribute field type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (13,'DropDown',3,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Attribute field type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (14,'CheckBox',3,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Attribute field type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (15,'Password',3,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Attribute field type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (16,'Integer',4,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Availability datatype',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (17,'Sum',5,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Sum the KPI values for Cluster Operation',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (18,'Average',5,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Average the KPI values for the Cluster Operation',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (19,'None',5,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Do nothing on the KPI values for the Cluster Operation',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (20,'GRPC',6,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GRPC will be used for the Communication Endpoint',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (21,'Float',7,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Float datatype for Core KPIs with Decimal values',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (22,'Integer',7,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Integer datatype for Core KPIs with Numeric values',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (23,'Text',7,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Text datatype for Core KPIs with Alphanumeric values',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (24,'Text',8,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Text datatype for Forensic KPIs with Alphanumeric values',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (25,'GET',9,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP GET Method type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (26,'POST',9,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP POST Method type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (27,'UPDATE',9,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP UPDATE Method type',0,0);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (28,'DELETE',9,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP DELETE Method type',0,0);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (29,'Availability',10,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI Type to collect the availability data',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (30,'Core',10,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI Type to collect the core data',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (31,'Forensic',10,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI Type to collect the forensic data',0,0);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (32,'ConfigWatch',10,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI Type to collect the configuration data',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (33,'FileWatch',10,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI Type to collect the data for file changes',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (34,'Bytes',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (35,'Count',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (36,'Gigabytes',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (37,'Kilobytes',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (38,'Megabytes',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (39,'Microseconds',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (40,'Milliseconds',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (41,'Minutes',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (42,'Seconds',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (43,'Percentage',12,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (44,'Text',13,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (45,'TCP',14,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP Transaction type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (46,'HTTP',14,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP Transaction type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (47,'Body',15,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP Body Patterns',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (48,'Header',15,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP Header Patterns',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (49,'QueryParams',15,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP Query Parameters',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (50,'TCPData',15,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP Data Patterns',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (51,'DC',16,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'DC Response Type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (52,'EUE',16,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'EUE Response Type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (53,'RENDER',16,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'RENDER Response Type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (54,'Application',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Application tag type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (55,'Component',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Components tag type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (56,'ComponentInstance',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Component Instances tag type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (57,'KPI',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPIs tag type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (58,'Cluster',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Clusters tag type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (59,'Producer',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Procuers tag type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (60,'Transaction',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Transactions tag type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (61,'Agents',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agents tag type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (62,'http',18,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP will be used for collecting Apache HTTP KPIs',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (63,'https',18,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTPS will be used for collecting Apache HTTP KPIs',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (64,'ServiceName',19,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'ServiceName will be used for collecting Oracle KPIs',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (65,'SID',19,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'SID will be used for collecting Oracle KPIs',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (66,'KEY_VALUE',20,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Key value pair argument for JDBC',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (67,'COMMANDLINE',21,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command line argument for SSH',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (68,'STANDARDINPUT',21,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Standard input argument for SSH',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (69,'COMMANDLINE',22,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command line argument for Shell',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (70,'KEY_VALUE',23,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Key value pair argument for JMX',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (71,'Float',24,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Float datatype for Config KPIs with Decimal values',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (72,'Integer',24,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Integer datatype for Config KPIs with Numeric values',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (73,'Text',24,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Text datatype for Config watch KPIs with Alphanumeric values',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (74,'Text',25,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Text datatype for File watch KPIs with Alphanumeric values',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (75,'NAMEVALUE',26,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Query result type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (76,'RESULTSET',26,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Query result type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (77,'Remote',27,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Online operation mode for PSAgent',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (78,'Local',27,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Offline operation mode for PSAgent',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (79,'Remote',28,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Online operation mode for Component Agent',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (80,'Local',28,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Offline operation mode for Component Agent',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (81,'ResponseHeader',29,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Response header option',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (82,'ResponseBody',29,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Response body option',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (83,'StatusLine',29,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'No response data',0,0);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (84,'HTTP',30,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Protocol for HTTP data',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (85,'HTTPS',30,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Protocol for HTTPS data',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (86,'TCP-DIR',30,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Protocol for TCP data',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (87,'FINICORE',30,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Protocol for Finacle data',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (88,'HTTP',31,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'PSAgent proxy protocol',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (89,'QueryParam',32,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP Query parameters',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (90,'Header',32,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP headers',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (91,'RequestBody',32,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP request body',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (92,'ResponseBody',32,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP response body',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (93,'RequestBody',33,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP request body',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (94,'ResponseBody',33,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP response body',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (95,'Query Param Extractor',34,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Query param extractor',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (96,'Grouped Regex Extractor',35,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Header extractor',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (97,'Grouped Regex Extractor',36,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP request body regex extractor',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (98,'Form Data Extractor',36,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP request body form extractor',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (99,'Grouped Regex Extractor',37,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP response body regex extractor',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (100,'Grouped Regex Extractor',38,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP request body regex extractor',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (101,'Position And Length Extractor',38,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP request body position length extractor',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (102,'Grouped Regex Extractor',39,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP response body regex extractor',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (103,'Position And Length Extractor',39,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP response body position length extractor',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (104,'Application Dashboard',40,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dashboard for applications',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (105,'NOC Dashboard',40,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dashboard for NOC',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (106,'JIM Dashboard',40,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dashboard for JIM',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (107,'Synthetic Monitoring',40,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dashboard for Synthetic Monitoring',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (108,'BVE',40,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dashboard for BVE',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (109,'Default',41,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Default pod operation',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (110,'Custom',41,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Custom pod operation',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (111,'ENTRY',42,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Entry type method',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (112,'EXIT',42,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Exit type method',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (113,'GENERIC',43,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Generic transformation type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (114,'SERVLET',43,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Servlet transformation type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (115,'SQL',43,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Query transformation type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (116,'Transaction',10,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Transaction KPI Type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (117,'SeverityProfile',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Severity alert profile type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (118,'NotificationContentProfile',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Notification alert profile type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (119,'EscalationProfile',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Esacalation alert profile type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (120,'AlertProfile',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Alert profile',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (121,'DC',44,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'DC transaction time type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (122,'EUM',44,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'EUM transaction time type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (123,'BOTH',44,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Both transaction time type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (124,'{Account}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Account',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (125,'{Application}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Application',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (126,'{Severity}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Severity',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (127,'{ComponentInstance}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Component Instance',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (128,'{Component}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Component',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (129,'{ComponentType}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Component Type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (130,'{KPI}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for KPI',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (131,'{KPIType}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for KPI Type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (132,'{ActualValue}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Actual Value',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (133,'{ThresholdValue}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Threshold Value',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (134,'{Time}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Time',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (135,'{AlertID}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Alert Id',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (136,'{EscalationLevel}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Escalation Level',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (137,'{ViolationDetails}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Violation Details',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (138,'{KPI Group}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for KPI Group',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (139,'Daily',46,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'All days in a week',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (140,'Days',46,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Day in a week',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (141,'Monday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'1st day of the week',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (142,'Tuesday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'2nd day of the week',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (143,'Wednesday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'3rd day of the week',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (144,'Thursday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'4th day of the week',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (145,'Friday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'5th day of the week',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (146,'Saturday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'6th day of the week',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (147,'Sunday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'7th day of the week',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (148,'TimeProfile',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Tags for coverage window',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (149,'greater than',48,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Greater than operation',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (150,'lesser than',48,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Less than operation',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (151,'NONE',49,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'SMTP security as NONE',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (152,'SSL',49,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'SMTP security as SSL',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (153,'TLS',49,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'SMTP security as TLS',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (154,'Good',50,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Good status of a transaction',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (155,'Slow',50,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Slow status of a transaction',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (156,'Fail',50,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Fail status of a transaction',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (157,'Unknown',50,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Unknown status of a transaction',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (158,'Timedout',50,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Timedout status of a transaction',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (159,'Slow Percentage',51,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Total volume threshold for transactions',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (160,'Fail Percentage',51,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Transaction status threshold for transactions',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (161,'HTTP',52,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP SMS Gateway Protocol',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (162,'TCP',52,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP SMS Gateway Protocol',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (163,'GET',53,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP GET SMS Request Method',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (164,'POST',53,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP POST SMS Request Method',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (165,'QueryParameter',54,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Query parameter for SMS Request',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (166,'RequestParameter',54,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Request parameter for SMS Request',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (167,'TcpParameter',54,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP parameter for SMS Request',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (168,'{MobileNumber}',57,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Mobile number placeholder for SMS',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (169,'{SMSContent}',57,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'SMS content placeholder for SMS',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (170,'UserRegistration',63,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'User registration',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (171,'UserResetPassword',63,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Password reset for users',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (172,'{Username}',64,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for username',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (173,'{Password}',64,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for user password',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (174,'{Account}',64,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for user account',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (175,'Locked',65,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'User account is locked',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (176,'Unlocked',65,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'User account is unlocked',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (177,'GrpcSettings',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Grpc Settings',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (178,'KPIGroup',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI Group',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (179,'HolidayProfiles',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Holiday Profiles',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (180,'HTTPS',52,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTPS SMS Gateway Protocol',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (181,'DirectValue',66,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'DirectValue type for JMX attributes',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (182,'CompositeValue',66,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'CompositeValue type for JMX attributes',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (183,'Yes',67,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Security type TRUE for JPPF security enable',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (184,'No',67,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Security type FALSE for JPPF security enable',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (185,'Server',68,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JPPF type is server',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (186,'Node',68,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JPPF type is node',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (187,'ENV_PARAM',22,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Standard input argument for Shell',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (188,'LogForwarder',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Log forwarder agent',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (189,'KeyValue',69,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KeyValue tag type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (190,'String',69,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Plain text tag type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (191,'Application',70,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Application controller type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (192,'Services',70,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Service controller type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (193,'not between',48,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Less than minimum value and greater than maximum value',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (194,'Total Volume',51,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Total volume of transactions',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (195,'SingleValue',71,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Single value aggregation',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (196,'MultiValue',71,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Multi value aggregation',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (197,'None',71,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'No aggregation',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (198,'Transacted Value',72,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Transacted KPI',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (199,'Conversion',72,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Conversion KPI',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (200,'Business Error',72,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Business error KPI',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (201,'Arrival Rate',72,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Arrival rate KPI',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (202,'Response Time (ms)',73,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Response time KPI',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (203,'Pass throughs (%)',73,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Passthrough KPI',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (204,'OPTIONS',9,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP OPTIONS Method type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (205,'PUT',9,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP PUT Method type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (206,'Regex',74,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Regex rule type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (207,'Request Data',74,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Request data rule type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (208,'Form Data',75,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Form data http payload type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (209,'XML Data',75,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'XML data http payload type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (210,'JSON Data',75,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JSON data http payload type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (211,'Cookie',76,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Cookie key value pair type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (212,'Query Parameters',76,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Query parameter key value pair type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (213,'HTTP Header',76,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP Header key value pair type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (214,'PayloadType',76,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Payload content type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (215,'JDBC',77,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JDBC exit type for JIM',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (216,'HTTP',77,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP exit type for JIM',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (217,'Queue',77,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Queue exit type for JIM',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (218,'ForensicAgent',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent for foresic actions',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (219,'WindowsSupervisor',78,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Windows based supervisor',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (220,'UnixSupervisor',78,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Unix based supervisor',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (221,'RestClient',85,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'REST client based command execution',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (222,'Script',85,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Script based command execution',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (223,'LongPolling',79,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Long polling based command execution',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (224,'Install',92,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform installation',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (225,'Upgrade',92,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform upgradeation',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (226,'UnInstall',92,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform uninstall',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (227,'Running',98,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform stop',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (228,'Stopped',98,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform start',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (229,'Restart',98,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform restart',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (230,'Execute',93,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform execute',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (231,'Custom',81,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Type to identify custom component',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (232,'OOB',81,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Type to identify OOB component',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (233,'Blob',82,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Output contents in blob',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (234,'File',82,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Output contents in a file',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (235,'PDF',83,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'PDF download type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (236,'CSV',83,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'CSV download type',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (237,'JDBC',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Oracle JIM exit framework',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (240,'JAX_WS_CLIENT',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JAX WS client JIM exit framework',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (241,'IBM_MQ',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'IBM MQ JIM exit framework',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (242,'APACHE_HTTP_CLIENT1',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Apache HTTP client JIM exit framework',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (243,'APACHE_HTTP_CLIENT2',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Apache HTTP client JIM exit framework',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (244,'AXIS2_CLIENT',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'AXIS2 client JIM exit framework',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (245,'JDBC_CONNECTION',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'MSSQL JIM exit framework',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (246,'JMS',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JMS JIM exit framework',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (247,'Supervisor',86,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Appsone supervisor',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (248,'SupervisorController',86,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Appsone supervisor controller',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (249,'Workload',87,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agents for load KPIs',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (250,'Behaviour',87,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agents for behaviour KPIs',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (251,'WorkNBehaviour',87,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent for both load and behaviour KPIs',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (252,'Others',87,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent for other than load and behaviour KPIs',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (253,'Baseline', 88, '2019-08-09 00:00:00','2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'Baseline threshold to be discovered by MLE', 0, 1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (254,'Static', 88, '2019-08-09 00:00:00','2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'Static threshold to be defined by users', 0, 1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (255,'in between', 48, '2019-08-09 00:00:00','2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'In Between operation', 0, 1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (256,'not equals', 89, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'Value is not equals.', 0, 1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (257,'Auto', 96, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'Agent will be running as Auto mode.', 0, 1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (258,'Verbose', 96, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'Agent will be running in Verbose mode.', 0, 1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (259,'SwitchOff', 96, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'Agent will be running in SwitchOff mode.', 0, 1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (260,'Problem', 91, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'Agent will be running as Auto mode.', 0, 1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (261,'ProblemImapacts', 91, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'Agent will be running in Verbose mode.', 0, 1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (262,'Anomaly', 91, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'Agent will be running in SwitchOff mode.', 0, 1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (263,'Realtime', 88, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'Realtime threshold to be discovered by MLE', 0, 1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (264,'EJB_WEBLOGIC_REMOTE', 84, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'EJB Weblogic exit framework', 0, 1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (265,'Weblogic_EJB', 77, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'EJB Weblogic exit type for JIM', 0, 1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (266,'EJB Data', 74, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'EJB rule type', 0, 1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (267,'MYSQL_STATEMENT', 84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'MySQL Statement JIM exit framework',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (268,'MYSQL_PREPARED_STATEMENT', 84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'MySQL Prepared Statement JIM exit framework',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (269,'ORACLE', 84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Oracle JIM exit framework',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (270,'MSSQL', 84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'MSSQL JIM exit framework',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (271,'NEW-JDBC',77,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JDBC exit type for JIM',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (272,'AgentOperations',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent operations type for command type.',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (273,'Maintenance',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Maintenance type for command type.',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (274,'Forensic',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Forensic type for command type.',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (275,'Discovery',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Discovery type for command type.',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (276,'CannedCommands',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Canned type for command type.',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (277,'Configuration',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Configuration type for command type.',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (278,'Health',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Health type for command type.',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (279,'SelfHeal',98,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'SelfHeal for command type.',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (280,'Sum',99,'2020-01-06 00:00:00','2020-01-06 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Sum the KPI values for rollup Operation',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (281,'Average',99,'2020-01-06 00:00:00','2020-01-06 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Average the KPI values for the rollup Operation',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (282,'None',99,'2020-01-06 00:00:00','2020-01-06 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Do nothing on the KPI values for the rollup Operation',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (283,'Max',99,'2020-01-06 00:00:00','2020-01-06 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Max of KPI values for the rollup Operation',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (284,'Last',99,'2020-01-06 00:00:00','2020-01-06 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Last fo KPI values for the rollup Operation',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (285,'COMMAND_OPTIONS',21,'2020-01-24 00:00:00', '2020-01-24 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Standard input argument for SSH',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (286,'COMMAND_OPTIONS',22,'2020-01-24 00:00:00', '2020-01-24 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command line argument for Shell',0,1);
insert into mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) values (287,'Custom',1,'2020-01-06 00:00:00','2020-01-06 00:00:00' ,'7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent for custom',0,1);
insert into `smtp_details` (`id`,`address`,`port`,`username`,`password`,`security_id`,`created_time`,`updated_time`,`account_id`,`from_recipient`,`user_details_id`,`status`) VALUES (10,'www.appnomic.com',9090,'<EMAIL>','YXBwbm9taWNfMQ==',151,'2020-05-07 12:27:30','2020-05-07 12:28:35',2,'<EMAIL>','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
insert into `smtp_details` (`id`,`address`,`port`,`username`,`password`,`security_id`,`created_time`,`updated_time`,`account_id`,`from_recipient`,`user_details_id`,`status`) VALUES (11,'www.appnomic.com',9090,'<EMAIL>','YXBwbm9taWNfMQ==',151,'2020-05-07 12:27:30','2020-05-07 12:28:35',4,'<EMAIL>','7640123a-fbde-4fe5-9812-581cd1e3a9c1',0);
insert into `sms_details` (`id`,`address`,`port`,`country_code`,`protocol_id`,`http_method`,`http_relative_url`,`created_time`,`updated_time`,`account_id`,`user_details_id`,`post_data`,`post_data_flag`,`status`, `is_multi_request`) VALUES (9,'www.appnomic.com',9090,'IND',161,'GET','/home/<USER>','2020-05-07 12:29:09','2020-05-07 12:29:09',2,'7640123a-fbde-4fe5-9812-581cd1e3a9c1',NULL,0,1,1);
insert into `sms_details` (`id`,`address`,`port`,`country_code`,`protocol_id`,`http_method`,`http_relative_url`,`created_time`,`updated_time`,`account_id`,`user_details_id`,`post_data`,`post_data_flag`,`status`, `is_multi_request`) VALUES (10,'www.appnomic.com',9090,'IND',161,'GET','/home/<USER>','2020-05-07 12:29:09','2020-05-07 12:29:09',4,'7640123a-fbde-4fe5-9812-581cd1e3a9c1',NULL,0,0,1);
insert into `sms_parameters` (`id`,`parameter_name`,`parameter_value`,`sms_details_id`,`created_time`,`updated_time`,`parameter_type_id`,`user_details_id`,`default_value`,`is_placeholder`) VALUES (11,'{mobileNumber}','**********',9,'2020-05-07 12:29:09','2020-05-07 12:29:09',165,'7640123a-fbde-4fe5-9812-581cd1e3a9c1',NULL,1);
insert into `sms_parameters` (`id`,`parameter_name`,`parameter_value`,`sms_details_id`,`created_time`,`updated_time`,`parameter_type_id`,`user_details_id`,`default_value`,`is_placeholder`) VALUES (12,'{mobileNumber}','**********',9,'2020-05-07 12:29:09','2020-05-07 12:29:09',165,'7640123a-fbde-4fe5-9812-581cd1e3a9c1',NULL,1);
insert into `rules` VALUES (1,2,1,1,'First 2 Segments','2020-02-20 12:39:32','2020-02-20 12:39:32','7640123a-fbde-4fe5-9812-581cd1e3a9c1',207,1),
(2,2,1,1,'EJB Data Rule','2020-02-20 12:39:32','2020-02-20 13:28:57','7640123a-fbde-4fe5-9812-581cd1e3a9c1',266,1),
(3,2,1,1,'First 2 Segment','2020-02-20 12:39:32','2020-02-20 13:13:21','7640123a-fbde-4fe5-9812-581cd1e3a9c1',207,1),
(4,2,1,1,'EJB Data Rule','2020-02-20 12:39:32','2020-02-20 12:39:32','7640123a-fbde-4fe5-9812-581cd1e3a9c1',266,1),
(5,2,1,1,'First & Second Segment','2020-02-20 12:39:33','2020-02-20 13:39:01','7640123a-fbde-4fe5-9812-581cd1e3a9c1',207,1);