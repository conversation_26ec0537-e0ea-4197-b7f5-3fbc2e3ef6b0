package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.NotificationSettingsBean;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.NotificationSettings;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.dao.mysql.NotificationSettingsDataService;
import com.appnomic.appsone.controlcenter.util.NotificationSettingsTest;
import org.junit.BeforeClass;
import org.junit.Test;

import java.util.List;

import static org.junit.Assert.*;

public class NotificationSettingsServiceTest {

    @BeforeClass
    public static void setUp() {
        MySQLConnectionManager.INSTANCE.setDbi(null);

        String H2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/create.sql'\\;RUNSCRIPT FROM './src/test/resources/views.sql'\\;RUNSCRIPT FROM './src/test/resources/populate.sql'";

        MySQLConnectionManager.getInstance().setH2URL(H2URL);
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true);
        MySQLConnectionManager.getInstance().getHandle();

        try {
            Thread.sleep(30000);
        } catch (InterruptedException e) {
            fail("Failed in sleeping!!!");
        }
    }

    @Test
    public void getNotificationSettings() {

        List<NotificationSettingsBean> notificationSettings = NotificationSettingsDataService.getNotificationSetting(NotificationSettingsTest.accountId);

        for (NotificationSettingsBean setting : notificationSettings) {

             //settings retrieved from service layer must be of type NotificationSettingsBean

            assertNotNull(setting);

            String typeName = MasterCache.getMstSubTypeForSubTypeId(setting.getTypeId()).getSubTypeName();

            // For a given account once notification settings are updated it must have the ids

            assertTrue(typeName.equals(NotificationSettingsTest.typeNameTooLong) || typeName.equals(NotificationSettingsTest.typeNameLong));
        }
    }

    /* typeName retrieved for the given typeId must be either:
      Open for too long
      Open for long
 */
    @Test
    public void updateNotificationSettings() {

        List<NotificationSettings> settings = NotificationSettingsTest.generateNotificationSettingsPojo();

        int[] ids = NotificationSettingsDataService.updateNotificationSetting(settings);

        assertTrue(ids.length > 0);

    }
}
