package com.appnomic.appsone.controlcenter.businesslogic;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;

public class MockHttpRequest extends HttpServletRequestWrapper {

    MockHttpRequest(HttpServletRequest request) {
        super(request);
    }

    private MockInputStream inputStream = null;
    private String contentType = null;
    private int contentLength = -1;

    void setInputStream(MockInputStream inputStream) {
        this.inputStream = inputStream;
    }

    public MockInputStream getInputStream() {
        return this.inputStream;
    }

    @Override
    public String getHeader(String header) {
        if (header != null) {
            if (header.equalsIgnoreCase("Content-Type")) {
                return this.contentType;
            } else if (header.equalsIgnoreCase("Content-length")) {
                return this.contentLength + "";
            }
            return super.getHeader(header);
        }
        return null;
    }

    public String getCharacterEncoding() {
        return "UTF-8";
    }

    public String getContentType() {
        return this.contentType;
    }

    public void setContentLength(long contentLength) {
        this.contentLength = (int) contentLength;
    }

    public int getContentLength() {
        return this.contentLength;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

}
