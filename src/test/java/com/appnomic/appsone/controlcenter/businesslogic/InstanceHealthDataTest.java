package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.InstanceHealthDetails;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class InstanceHealthDataTest {

    @Test
    public void getSortedList1() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true);
        InstanceHealthData service = new InstanceHealthData();
        List<InstanceHealthDetails> serviceBreakageList= new ArrayList<>();

            serviceBreakageList.add(InstanceHealthDetails.builder()
            .dataPostStatus(0)
            .lastPostedTime(null)
            .instanceName("Rhel")
            .type("OS")
            .build());

            serviceBreakageList.add(InstanceHealthDetails.builder()
            .dataPostStatus(1)
            .lastPostedTime(1569510000000L)
            .instanceName("Tomcat")
            .type("Server")
            .build());

            serviceBreakageList.add(InstanceHealthDetails.builder()
            .dataPostStatus(0)
            .lastPostedTime(1566510000000L)
            .instanceName("Oracle")
            .type("DB")
            .build());

            List<InstanceHealthDetails> sortedList = service.getSortedList(serviceBreakageList);
            assertTrue(sortedList.get(0).getLastPostedTime() == null && sortedList.get(0).getDataPostStatus()==0);
            assertTrue(sortedList.get(1).getLastPostedTime()!= null && sortedList.get(1).getDataPostStatus()==0);
            assertTrue(sortedList.get(2).getLastPostedTime() != null && sortedList.get(2).getDataPostStatus() == 1);
    }

    @Test
    public void getSortedList2(){
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true);
        List<InstanceHealthDetails> list = null;
        list= new InstanceHealthData().getSortedList(list);
        assertEquals(list, new ArrayList<>());
    }
}