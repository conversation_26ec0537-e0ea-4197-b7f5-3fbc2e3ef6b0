package com.appnomic.appsone.controlcenter.dao;

import com.appnomic.appsone.controlcenter.beans.AgentBean;
import com.appnomic.appsone.controlcenter.beans.AgentCompInstMappingBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.AgentDao;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.BindInDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.AccountAgentMapping;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import org.junit.BeforeClass;
import org.junit.Test;

import java.sql.Timestamp;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;

public class AgentDaoTest {

    @BeforeClass
    public static void setUp() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true);
        MySQLConnectionManager.getInstance().getHandle();

        try {
            Thread.sleep(20000);
        } catch (InterruptedException e) {
            System.out.println();
        }
    }


    @Test
    public void getAgentBeanData() {
        String agentUId = "f2d43605-b93e-4fdf-a363-057a062f9154";
        AgentBean bean = MySQLConnectionManager.getInstance().open(AgentDao.class).getAgentBeanData(agentUId);
        assertNotNull(bean);
    }

    @Test
    public void getAgentBeanDataForName() {
        String agentName = "NoOpAgent";
        AgentBean bean = MySQLConnectionManager.getInstance().open(AgentDao.class).getAgentBeanDataForName(agentName);
        assertNotNull(bean);
    }

    @Test
    public void getAgentList() {
        List<AgentBean> list = MySQLConnectionManager.getInstance().open(AgentDao.class).getAgentList();
        assertNotEquals(list.size(),0);
    }

    @Test
    public void getAgentAccountMappingByAgentId() {
        String identifier = "f2d43605-b93e-4fdf-a363-057a062f9154";
        List<AccountAgentMapping> list = MySQLConnectionManager.getInstance().open(AgentDao.class).getAgentAccountMappingByAgentId(identifier);
        assertNotEquals(list.size(),0);
    }

    @Test
    public void getAgentCompInstMapping() {
        List<Integer> agentIds = new ArrayList<>();
        agentIds.add(4);
        List<AgentCompInstMappingBean> list = MySQLConnectionManager.getInstance().open(BindInDao.class).getAgentCompInstMapping(agentIds);
        assertNotEquals(list.size(),0);
    }

    @Test
    public void addAgent() throws ParseException{

        Timestamp timestamp = new java.sql.Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());

        AgentBean agentBean = new AgentBean();
        agentBean.setUniqueToken(UUID.randomUUID().toString());
        agentBean.setName("agent");
        agentBean.setAgentTypeId(23);
        agentBean.setCreatedTime(timestamp);
        agentBean.setUpdatedTime(timestamp);
        agentBean.setUserDetailsId("user");
        agentBean.setStatus(1);
        agentBean.setHostAddress("***********");
        agentBean.setDescription("");
        agentBean.setMode("passive");
        agentBean.setPhysicalAgentId(1);

        int agentId = MySQLConnectionManager.getInstance().open(AgentDao.class).addAgent(agentBean);
        assertNotEquals(-1,agentId);
    }
}