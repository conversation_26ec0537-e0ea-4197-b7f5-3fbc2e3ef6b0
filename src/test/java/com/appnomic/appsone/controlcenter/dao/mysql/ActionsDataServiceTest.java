package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.ActionCategoryMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.Actions;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import org.junit.Test;
import org.skife.jdbi.v2.Handle;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

public class ActionsDataServiceTest {

    private Actions getActions(){
        return Actions.builder()
                .name("test")
                .accountId(1)
                .agentType(218)
                .standardType(232)
                .userDetailsId("user")
                .identifier("675456")
                .createdTime(String.valueOf(Timestamp.valueOf(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))))
                .updatedTime(String.valueOf(Timestamp.valueOf(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))))
                .build();
    }

    private ActionCategoryMapping getActionCategory(){
        return ActionCategoryMapping.builder()
                .actionId(7)
                .categoryId(1)
                .ttlInSecs(300)
                .retries(3)
                .actionExecTypeId(222)
                .downloadTypeId(235)
                .timeWindowInSecs(300)
                .commandExecTypeId(223)
                .objectRefTable("command_details")
                .objectId(1)
                .userDetailsId("user")
                .createdTime(String.valueOf(Timestamp.valueOf(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))))
                .updatedTime(String.valueOf(Timestamp.valueOf(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))))
                .build();
    }

    /*@Test
    void addForensic1() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true);
        try {
            Handle handle = MySQLConnectionManager
                    .getInstance().getHandle().open();
            Thread.sleep(10000);
            ForensicDetailsBean f = getForensics(),bean;
            int id = ForensicDataService.addForensic(f, handle);
            f.setId(id);
            Thread.sleep(10000);
            bean = ForensicDataService.getForensicDetails(f.getAccountId()).stream()
                    .filter(c-> c.getId()==id).findAny().get();
            assertTrue(bean.equals(f));
            handle.close();
        }catch(Exception e){
            System.out.println(e);
            fail();
        }
     }*/

    @Test
    public void addActions2() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true);
        try {
            Handle handle = MySQLConnectionManager
                    .getInstance().getHandle().open();
            Actions bean = getActions();
            bean.setIdentifier(null);
            ActionScriptDataService.addActionScript(bean, handle);
            handle.close();
        }catch(Exception e){
            assertTrue(true);
        }
    }

    @Test
    public void addActionCategoryMapping1() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true);
        List<ActionCategoryMapping> list = new ArrayList<>();
        ActionCategoryMapping fc1 = getActionCategory(),fc2 = getActionCategory();
        list.add(fc1);
        fc2.setCategoryId(2);
        list.add(fc2);
        try {
            Handle handle = MySQLConnectionManager
                    .getInstance().getHandle().open();
            Thread.sleep(10000);
            ActionScriptDataService.addActionScriptCategoryMapping(list, handle);
            handle.close();
            List<ActionCategoryMapping> l = ActionScriptDataService.getActionCategoryDetails(7,null);
            l.forEach(c -> c.setId(0));
            assertTrue(l.size() > 0);
            assertTrue(l.contains(fc1) && l.contains(fc2));
        }catch(Exception e){
            System.out.println(e);
             fail();
        }
    }

    @Test
    public void addActionCategoryMapping2() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true);
        List<ActionCategoryMapping> list = new ArrayList<>();
        ActionCategoryMapping fc1 = getActionCategory(),fc2 = getActionCategory();
        list.add(fc1);
        fc2.setActionExecTypeId(6557);
        list.add(fc2);
        try {
            Handle handle = MySQLConnectionManager
                    .getInstance().getHandle().open();
            ActionScriptDataService.addActionScriptCategoryMapping(list, handle);
            handle.close();
        }catch(Exception e){
            assertTrue(true);
        }
    }
}