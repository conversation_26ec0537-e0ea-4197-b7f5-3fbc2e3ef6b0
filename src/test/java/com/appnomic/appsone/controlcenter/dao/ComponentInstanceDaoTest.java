package com.appnomic.appsone.controlcenter.dao;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.ComponentInstanceDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.ComponentKpiDetail;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import org.junit.BeforeClass;
import org.junit.Test;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

public class ComponentInstanceDaoTest {

    @BeforeClass
    public static void setUp() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true);
        MySQLConnectionManager.getInstance().getHandle();

        try {
            Thread.sleep(20000);
        } catch (InterruptedException e) {
            fail("Failed in sleeping");
        }
    }

    public ComponentInstanceDao setup() {
        return MySQLConnectionManager.getInstance().open(ComponentInstanceDao.class);
    }

    @Test
    public void addComponentInstance() {

        ComponentInstanceBean componentInstanceBean = new ComponentInstanceBean();
        componentInstanceBean.setName("test");
        componentInstanceBean.setStatus(1);
        componentInstanceBean.setIsDR(0);
        componentInstanceBean.setIsCluster(0);
        componentInstanceBean.setMstComponentVersionId(1);
        componentInstanceBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        componentInstanceBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        componentInstanceBean.setAccountId(2);
        componentInstanceBean.setMstComponentId(1);
        componentInstanceBean.setMstComponentTypeId(1);
        componentInstanceBean.setUserDetailsId("abc");
        componentInstanceBean.setIdentifier("testing");
        componentInstanceBean.setMstCommonVersionId(1);
        componentInstanceBean.setDiscovery(1);

        int id = setup().addComponentInstance(componentInstanceBean);

        assertNotEquals(id,-1);
    }

    @Test
    public void addAttributesForComponentInstance() {
        CompInstanceAttributesBean compInstanceAttributesBean = new CompInstanceAttributesBean();
        compInstanceAttributesBean.setAttributeName("test");
        compInstanceAttributesBean.setAttributeValue("abc");
        compInstanceAttributesBean.setCompInstanceId(1);
        compInstanceAttributesBean.setMstComponentAttributeMappingId(1);
        compInstanceAttributesBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        compInstanceAttributesBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        compInstanceAttributesBean.setUserDetailsId("testing");
        compInstanceAttributesBean.setMstCommonAttributesId(1);
        int id = setup().addAttributesForComponentInstance(compInstanceAttributesBean);
        assertNotEquals(id,-1);
    }

    @Test
    public void addNonGroupCompInstanceKpiDetails() {
        CompInstanceKpiDetailsBean compInstanceKpiDetailsBean = new CompInstanceKpiDetailsBean();
        compInstanceKpiDetailsBean.setCompInstanceId(2);
        compInstanceKpiDetailsBean.setMstProducerKpiMappingId(1);
        compInstanceKpiDetailsBean.setCollectionInterval(100);
        compInstanceKpiDetailsBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        compInstanceKpiDetailsBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        compInstanceKpiDetailsBean.setStatus(1);
        compInstanceKpiDetailsBean.setUserDetailsId("testing");
        compInstanceKpiDetailsBean.setMstKpiDetailsId(1);
        compInstanceKpiDetailsBean.setMstProducerId(3);
        int id = setup().addNonGroupCompInstanceKpiDetails(compInstanceKpiDetailsBean);
        assertNotEquals(id,-1);
    }

    @Test
    public void addGroupCompInstanceKpiDetails() {
        CompInstanceKpiGroupDetailsBean compInstanceKpiGroupDetailsBean = new CompInstanceKpiGroupDetailsBean();
        compInstanceKpiGroupDetailsBean.setStatus(1);
        compInstanceKpiGroupDetailsBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        compInstanceKpiGroupDetailsBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        compInstanceKpiGroupDetailsBean.setUserDetailsId("abc");
        compInstanceKpiGroupDetailsBean.setCompInstanceId(2);
        compInstanceKpiGroupDetailsBean.setMstProducerKpiMappingId(1);
        compInstanceKpiGroupDetailsBean.setCollectionInterval(200);
        compInstanceKpiGroupDetailsBean.setMstKpiDetailsId(1);
        compInstanceKpiGroupDetailsBean.setIsDiscovery(0);
        compInstanceKpiGroupDetailsBean.setKpiGroupName("abc");
        compInstanceKpiGroupDetailsBean.setMstKpiGroupId(1);
        compInstanceKpiGroupDetailsBean.setMstProducerId(1);
        int id = setup().addGroupCompInstanceKpiDetails(compInstanceKpiGroupDetailsBean);
        assertNotEquals(id,-1);
    }

    @Test
    public void getComponentInstance() {
        String identifier = "";
        String name = "AIX_Cluster";
        int accountId = 2;
        ComponentInstanceBean instanceBean = setup().getComponentInstance(identifier,name,accountId);
        assertNotNull(instanceBean);
    }

    @Test
    public void getComponentInstanceById() {
        int id = 2;
        int accountId = 2;
        ComponentInstanceBean instanceBean = setup().getComponentInstanceById(id, accountId);
        assertNotNull(instanceBean);
    }

    @Test
    public void getCompInstBasedInAttributeValues() {
        List<String> attributeList = new ArrayList<>();
        attributeList.add("192.168.13.123");
        attributeList.add("");
        int accountId = 2;
        int mstComponentId = 8;
        List<ComponentInstanceAttributesBean> list = new BindInDataService().getCompInstBasedInAttributeValues(null, attributeList, accountId, mstComponentId);
        assertEquals(list.size(), 0);
    }

    @Test
    public void addAgentCompInstMapping() {
        AgentCompInstMappingBean agentCompInstMappingBean = new AgentCompInstMappingBean();
        agentCompInstMappingBean.setCompInstanceId(2);
        agentCompInstMappingBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        agentCompInstMappingBean.setAgentId(2);
        int id = setup().addAgentCompInstMapping(agentCompInstMappingBean);
        assertNotEquals(id,-1);
    }

    @Test
    public void addAgentAccountMapping() throws ParseException {
        AgentAccountMappingBean agentAccountMappingBean = new AgentAccountMappingBean();
        agentAccountMappingBean.setAgentId(2);
        agentAccountMappingBean.setAccountId(2);
        agentAccountMappingBean.setUserDetailsId("abc");
        agentAccountMappingBean.setCreatedTime(new java.sql.Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime()));
        int id = setup().addAgentAccountMapping(agentAccountMappingBean);
        assertNotEquals(id,-1);
    }

    @Test
    public void addCompClusterMapping() {
        CompClusterMappingBean compClusterMappingBean = new CompClusterMappingBean();
        compClusterMappingBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        compClusterMappingBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        compClusterMappingBean.setUserDetailsId("abc");
        compClusterMappingBean.setAccountId(2);
        compClusterMappingBean.setCompInstanceId(3);
        compClusterMappingBean.setClusterId(1);
        int id = setup().addCompClusterMapping(compClusterMappingBean);
        assertNotEquals(id,-1);
    }

    @Test
    public void getComponentInstances() {
        List<String> identifiers = new ArrayList<>();
        identifiers.add("AIX_Cluster");
        identifiers.add("RHEL_Cluster_1");
        int accountid = 2;
        List<ComponentInstanceBean> list = new BindInDataService().getComponentInstanceBeans(identifiers,accountid, null);
        assertNotEquals(list.size(),-1);
    }

    @Test
    public void getComponentDetails() {
        int id = 1;
        ComponentKpiDetail detail = setup().getComponentDetails(id);
        assertNotNull(detail);
    }
}