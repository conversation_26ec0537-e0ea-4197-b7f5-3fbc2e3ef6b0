package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import spock.lang.Specification

class AutoDiscoveryIgnoredEntitiesBLTest extends Specification {
    def "Client validation failure - NULL request object"() {
        when:
        new AutoDiscoveryIgnoredEntitiesBL().clientValidation(null)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Request object is null or empty."
    }

    def "Client validation failure - NULL authorization token"() {
        setup:
        RequestObject requestObject = new RequestObject()
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", null)
        requestObject.setHeaders(headers)

        when:
        new AutoDiscoveryIgnoredEntitiesBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.AUTH_KEY_INVALID
    }

    def "Client validation failure - NULL or empty authorization token"() {
        setup:
        RequestObject requestObject = new RequestObject()
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "")
        requestObject.setHeaders(headers)

        when:
        new AutoDiscoveryIgnoredEntitiesBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.AUTH_KEY_INVALID
    }

    def "Client validation failure - NULL or empty accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", null)
        requestObject.setParams(params)
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AutoDiscoveryIgnoredEntitiesBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.ACCOUNT_NULL_OR_EMPTY
    }

    def "Client validation success"() {
        setup:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean ub = new AutoDiscoveryIgnoredEntitiesBL().clientValidation(requestObject)

        then:
        ub.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        ub.getAuthToken() == "testing-with-dummy-authorization-header"
    }
}
