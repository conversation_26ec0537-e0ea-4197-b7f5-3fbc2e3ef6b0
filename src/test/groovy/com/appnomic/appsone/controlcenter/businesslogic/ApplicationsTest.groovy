package com.appnomic.appsone.controlcenter.businesslogic


import com.appnomic.appsone.controlcenter.exceptions.RequestException
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.AddApplication
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spock.lang.Specification

import static com.appnomic.appsone.controlcenter.businesslogic.Applications.addClientValidations
import static com.appnomic.appsone.controlcenter.businesslogic.Applications.editClientValidations

class ApplicationsTest extends Specification {

    Request request = Spy(Request.class)
    private static final String DEFAULT_TIMEZONE = "(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi"


    def "add client validations json invalid"()
    {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        request.body() >> ""

        when:
        addClientValidations(request)

        then:
        thrown(RequestException)

    }

    def  "add client validations invalid userId"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        request.headers("Authorization") >> ""
        request.body() >> "{\n" +
                "\"name\": \"App-test1\" ,\n" +
                "\"services\" : [\"BrAnchTransactions  \",\"Core-DB-Service\"]\n" +
                "}"

        when:
        addClientValidations(request)

        then:
        thrown(RequestException)
    }

    def "add client validations name null"()
    {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "\"name\": \"\" ,\n" +
                "\"services\" : [\"BrAnchTransactions  \",\"Core-DB-Service\"]\n" +
                "}"

        when:
        addClientValidations(request)

        then:
        thrown(RequestException)
    }

    def "add client validations identifier invalid"()
    {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> "{\n" +
                "\"name\": \"test_app\" ,\n" +
                "\"identifier\": \"bjkbskjajiljfd;kl;ikdopeimkdnuhdfjasnjkashciosdjiojamsdknoifhiowjmciodufiodjvmdviofuviojebfuiehfouierfiojeriojhfgierugiojgeriogu9ogu9tgujiortjghiotujh8iu9rtgj\" ,\n" +
                "\"services\" : [\"BrAnchTransactions  \",\"Core-DB-Service\"]\n" +
                "}"

        when:
        addClientValidations(request)

        then:
        thrown(RequestException)
    }

    def "add client validations services null"()
    {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> "{\n" +
                "\"name\": \"test_app\" ,\n" +
                "\"services\" : []\n" +
                "}"

        when:
        addClientValidations(request)

        then:
        noExceptionThrown()

    }

    def "add client validations service empty"()
    {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> "{\n" +
                "\"name\": \"test_app\" ,\n" +
                "\"services\" : [\"     \",\"Core-DB-Service\"]\n" +
                "}"

        when:
        addClientValidations(request)

        then:
        thrown(RequestException)
    }

    def "add client validations valid"()
    {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> "{\n" +
                "\"name\": \"test_app\" ,\n" +
                "\"services\" : [\"BrAnchTransactions  \",\"Core-DB-Service\"]\n" +
                "}"
        when:
        AddApplication app = addClientValidations(request)

        then:
        app.timezone== DEFAULT_TIMEZONE
        app.name == "test_app"
    }

    def "add client validations valid timezone"()
    {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> "{\n" +
                "\"name\": \"test_app\" ,\n" +
                "\"services\" : [\"BrAnchTransactions  \",\"Core-DB-Service\"],\n" +
                "\"timezone\" : \"(GMT+08:00) Perth\"\n" +
                "}"
        when:
        AddApplication app = addClientValidations(request)

        then:
        app.timezone == "(GMT+08:00) Perth"
        app.name == "test_app"
    }

    def "Edit client validation request body null"() {
        setup :
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        request.body() >> null

        when :
        editClientValidations(request)

        then :
        thrown(RequestException)
    }


}