package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.common.autodiscovery.Entity
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.AutoDiscoveryMapEntityPojo
import spock.lang.Specification

class AutoDiscoveryMapEntityBLTest extends Specification {
    def "Client validation failure - NULL request object"() {
        when:
        new AutoDiscoveryMapEntityBL().clientValidation(null)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Request object is null or empty."
    }

    def "Client validation failure - Invalid request body"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("testing-with-dummy-body")
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AutoDiscoveryMapEntityBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.INVALID_REQUEST_BODY
    }

    def "Client validation failure - NULL accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", null)
        requestObject.setParams(params)
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AutoDiscoveryMapEntityBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.ACCOUNT_NULL_OR_EMPTY
    }

    def "Client validation failure - NULL authorization token"() {
        setup:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", null)
        requestObject.setHeaders(headers)

        when:
        new AutoDiscoveryMapEntityBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.AUTH_KEY_INVALID
    }

    def "Client validation failure - empty authorization token"() {
        setup:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "")
        requestObject.setHeaders(headers)

        when:
        new AutoDiscoveryMapEntityBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.AUTH_KEY_INVALID
    }

    def "Client validation success - for a single entity"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "        \"entityType\": \"Host\",\n" +
                "        \"serviceIdentifiers\": [25, 26], \n" +
                "        \"serviceMappingIdentifiers\": [\"test-entity-1\"] \n" +
                "    }\n")
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<AutoDiscoveryMapEntityPojo> data = new AutoDiscoveryMapEntityBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getBatchName() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().getEntityType() == Entity.Host
        data.getPojoObject().getServiceMappingIdentifiers() == ["test-entity-1"]
        data.getPojoObject().getServiceIdentifiers() == [25, 26]
    }

}
