package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.common.enums.DiscoveryStatus
import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.GetHost
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.IdNamePojo
import spock.lang.Specification

class GetHostBLTest extends Specification{

    def "Client validation failure - NULL request object"() {
        when:
        new GetHostBL().clientValidation(null)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Request object is null or empty."
    }

    def "Client validation failure - NULL accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", null)
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new GetHostBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid account identifier"
    }

    def "Client validation failure - Empty accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new GetHostBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid account identifier"
    }

    def "Client validation failure - Empty authorization token"() {
        setup:
        RequestObject requestObject = new RequestObject()

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "")
        requestObject.setHeaders(headers)

        when:
        new GetHostBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - NULL authorization token"() {
        setup:
        RequestObject requestObject = new RequestObject()

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", null)
        requestObject.setHeaders(headers)

        when:
        new GetHostBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - Missing authorization key"() {
        setup:
        RequestObject requestObject = new RequestObject()

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        requestObject.setHeaders(headers)

        when:
        new GetHostBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Success Case"() {
        given:
        RequestObject requestObject = new RequestObject()

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<Object> data = new GetHostBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getBatchName() == null
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getPojoObject() == null
    }

    def "Check for duplicates - Remove from stagingTable List"() {
        given:
        List<GetHost> duplicatesFromSDMTable = new ArrayList<>()
        List<GetHost> duplicatesFromStagingTable = new ArrayList<>()

        List<IdNamePojo> services = new ArrayList<>()
        services.add(IdNamePojo.builder().id(1).identifier("Service1").name("Service1").build())
        services.add(IdNamePojo.builder().id(2).identifier("Service2").name("Service2").build())
        List<GetHost> preExistingData = new ArrayList<>()
        GetHost getHost = GetHost.builder()
        .hostIdentifier("**************")
        .service(services)
                .build();
        preExistingData.add(getHost)

        List<GetHost> stagingTableData = new ArrayList<>()
        GetHost getHost1 = GetHost.builder()
                .hostIdentifier("**************")
        .service(new ArrayList<IdNamePojo>())
                .build()
        stagingTableData.add(getHost1)

        when:
        GetHostBL getHostBL = new GetHostBL();
        getHostBL.removeDuplicates(preExistingData, stagingTableData, duplicatesFromSDMTable, duplicatesFromStagingTable)

        then:
        duplicatesFromStagingTable.size() == 1
        duplicatesFromSDMTable.size() == 0
    }

    def "Check for duplicates - Remove from preExistingTable List"() {
        given:
        List<GetHost> duplicatesFromSDMTable = new ArrayList<>()
        List<GetHost> duplicatesFromStagingTable = new ArrayList<>()

        List<IdNamePojo> services = new ArrayList<>()
        services.add(IdNamePojo.builder().id(1).identifier("Service1").name("Service1").build())
        services.add(IdNamePojo.builder().id(2).identifier("Service2").name("Service2").build())
        List<GetHost> preExistingData = new ArrayList<>()
        List<String> hostAddress = new ArrayList<>();
        hostAddress.add("**************")
        GetHost getHost = GetHost.builder()
                .hostIdentifier("**************")
                .hostAddress(hostAddress)
                .service(new ArrayList<IdNamePojo>())
                .status(DiscoveryStatus.ADDED_TO_SYSTEM)
                .build();
        preExistingData.add(getHost)

        List<GetHost> stagingTableData = new ArrayList<>()
        GetHost getHost1 = GetHost.builder()
                .hostIdentifier("**************")
                .hostAddress(hostAddress)
                .service(services)
                .status(DiscoveryStatus.ADDED_TO_SYSTEM)
                .build()
        stagingTableData.add(getHost1)

        when:
        GetHostBL getHostBL = new GetHostBL();
        getHostBL.removeDuplicates(preExistingData, stagingTableData, duplicatesFromSDMTable, duplicatesFromStagingTable)

        then:
        duplicatesFromStagingTable.size() == 0
        duplicatesFromSDMTable.size() == 1
    }

    def "Check for duplicates - Remove from StagingTable based on status"() {
        given:
        List<GetHost> duplicatesFromSDMTable = new ArrayList<>()
        List<GetHost> duplicatesFromStagingTable = new ArrayList<>()

        List<IdNamePojo> services = new ArrayList<>()
        services.add(IdNamePojo.builder().id(1).identifier("Service1").name("Service1").build())
        services.add(IdNamePojo.builder().id(2).identifier("Service2").name("Service2").build())
        List<GetHost> preExistingData = new ArrayList<>()
        List<String> hostAddress = new ArrayList<>();
        hostAddress.add("**************")
        GetHost getHost = GetHost.builder()
                .hostIdentifier("**************")
                .hostAddress(hostAddress)
                .service(new ArrayList<IdNamePojo>())
                .status(DiscoveryStatus.ADDED_TO_SYSTEM)
                .build();
        preExistingData.add(getHost)

        List<GetHost> stagingTableData = new ArrayList<>()
        GetHost getHost1 = GetHost.builder()
                .hostIdentifier("**************")
                .hostAddress(hostAddress)
                .service(services)
                .status(DiscoveryStatus.DISCOVERED_NOT_ADDED_TO_SYSTEM)
                .build()
        stagingTableData.add(getHost1)

        when:
        GetHostBL getHostBL = new GetHostBL();
        getHostBL.removeDuplicates(preExistingData, stagingTableData, duplicatesFromSDMTable, duplicatesFromStagingTable)

        then:
        duplicatesFromStagingTable.size() == 1
        duplicatesFromSDMTable.size() == 1
    }
}
