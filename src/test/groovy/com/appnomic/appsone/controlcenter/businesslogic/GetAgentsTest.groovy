package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import spock.lang.Specification

class GetAgentsTest extends Specification{

    String[] Id1=["abc"]
    String[] Id2=["1","2","3"]
    String[] Id3=["0"]
    String[] Id4=["1"]

    def "Auth Token Invalid"(){
        setup:
        RequestObject requestObject=new RequestObject()
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", null)
        requestObject.setHeaders(headers)
        when:
        new GetAgents().clientValidation(requestObject)
        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid Authorization Token."
    }
    def "Empty Account Identifier"(){
        setup:
        RequestObject requestObject=new RequestObject()
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "authorization-token")
        requestObject.setHeaders(headers)
        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", null)
        requestObject.setParams(params)

        when:
        new GetAgents().clientValidation(requestObject)
        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Account identifier is null or empty."


    }
    def "AgentTypeId Negative integer"(){
        setup:
        RequestObject requestObject=new RequestObject()
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "authorization-token")
        requestObject.setHeaders(headers)
        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)
        Map<String,String[]> queryParams = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        queryParams.put("agentTypeId",Id3)
        requestObject.setQueryParams(queryParams)
        when:
        new GetAgents().clientValidation(requestObject)
        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : AgentTypeId should be a positive non-zero integer."


    }
    def "Multiple AgentTypeId "(){
        setup:
        RequestObject requestObject=new RequestObject()
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "authorization-token")
        requestObject.setHeaders(headers)
        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)
        Map<String,String[]> queryParams = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        queryParams.put("agentTypeId",Id2)
        requestObject.setQueryParams(queryParams)
        when:
        new GetAgents().clientValidation(requestObject)
        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Multiple AgentTypeIds provided."


    }

    def "AgentTypeId should be positive integer"(){
        setup:
        RequestObject requestObject=new RequestObject()
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "authorization-token")
        requestObject.setHeaders(headers)
        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)
        Map<String,String[]> queryParams = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        queryParams.put("agentTypeId",Id1)
        requestObject.setQueryParams(queryParams)
        when:
        new GetAgents().clientValidation(requestObject)
        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : AgentTypeId should be positive integer."


    }
    def "Success Case"() {
        setup:
        RequestObject requestObject=new RequestObject()
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "authorization-token")
        requestObject.setHeaders(headers)
        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)
        Map<String,String[]> queryParams = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        queryParams.put("agentTypeId",Id4)
        int agentTypeId=1
        requestObject.setQueryParams(queryParams)
        when:
        UtilityBean<Object> data = new GetAgents().clientValidation(requestObject)


        then:
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getPojoObject() == agentTypeId
        data.getAuthToken() == "authorization-token"

    }
}
