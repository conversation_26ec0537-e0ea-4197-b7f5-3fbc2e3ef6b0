package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.CompInstAttributesBean
import com.appnomic.appsone.controlcenter.beans.HierarchyBean
import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.exceptions.ServerException
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import com.appnomic.appsone.controlcenter.service.KeyCloakAuthService
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spock.lang.Shared
import spock.lang.Specification

class EditCompInstAttributesBLIT extends Specification{
    @Shared
    String oldH2URL

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(false)
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }


    def cleanupSpec() {
        DBTestCache.rollback()
    }

    String authToken = KeycloakConnectionManager.getAccessToken();

    def "client validation serviceId :empty"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier","d681ef13-d690-4917-jkhg-6c79b-12")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        EditCompInstAttributeBL editCompInstAttributeBL = new EditCompInstAttributeBL()
        editCompInstAttributeBL.clientValidation(requestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Instance Id should not be empty."
    }

    def "client validation accountId :empty"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        EditCompInstAttributeBL editCompInstAttributeBL = new EditCompInstAttributeBL()
        editCompInstAttributeBL.clientValidation(requestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Account Identifier should not be empty."
    }

    def "server validation invalid serviceId"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier","d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":serviceId","8")
        params.put(":instanceId","51")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        EditCompInstAttributeBL editCompInstAttributeBL = new EditCompInstAttributeBL()
        when:
        UtilityBean<HierarchyBean> beanUtilityBean = editCompInstAttributeBL.clientValidation(requestObject)

        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Exception encountered when parsing request body"
    }

    def "client validation for instance id empty"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier","d681ef13-d690-4917-jkhg-6c79b-12")
        params.put(":serviceId","2")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        EditCompInstAttributeBL editCompInstAttributeBL = new EditCompInstAttributeBL()
        when:
        UtilityBean<HierarchyBean> beanUtilityBean = editCompInstAttributeBL.clientValidation(requestObject)

        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Instance Id should not be empty."
    }
    /*def "invalid attributeId for instance check"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier","d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":serviceId","8")
        params.put(":instanceId","53")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization",authToken)
        requestObject.setHeaders(headers)

        requestObject.setBody("[\n" +
                "        {\n" +
                "            \"attributeId\": 179,\n" +
                "            \"attributeValue\": \"89SSFaPZjtHK3Hia5Q7KA==\",\n" +
                "            \"attributeKey\": \"password\"\n" +
                "        }]")
        EditCompInstAttributeBL editCompInstAttributeBL = new EditCompInstAttributeBL()
        UtilityBean<HierarchyBean> beanUtilityBean = editCompInstAttributeBL.clientValidation(requestObject)
        when:
        List<CompInstAttributesBean> compInstAttributesBeans = editCompInstAttributeBL.serverValidation(beanUtilityBean)
        then:
        final e = thrown(ServerException)
        e.getMessage() == "ServerException : The attributeId '179' doesn't map to this attribute key Password"
    }*/

    def "invalid attribute key mapped for instance check"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier","d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":serviceId","8")
        params.put(":instanceId","53")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization",authToken)
        requestObject.setHeaders(headers)

        requestObject.setBody("[\n" +
                "        {\n" +
                "            \"attributeId\": 178,\n" +
                "            \"attributeValue\": \"89SSFaPZjtHK3Hia5Q7KA==\",\n" +
                "            \"attributeKey\": \"passord\"\n" +
                "        }]")
        EditCompInstAttributeBL editCompInstAttributeBL = new EditCompInstAttributeBL()
        UtilityBean<HierarchyBean> beanUtilityBean = editCompInstAttributeBL.clientValidation(requestObject)
        when:
        List<CompInstAttributesBean> compInstAttributesBeans = editCompInstAttributeBL.serverValidation(beanUtilityBean)
        then:
        final e = thrown(ServerException)
        e.getMessage() == "ServerException : The attribute key 'passord' doesn't exist to this instance"
    }

    /*def "invalid password encryption for instance attribute check"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier","d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":serviceId","8")
        params.put(":instanceId","53")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization",authToken)
        requestObject.setHeaders(headers)

        requestObject.setBody("[\n" +
                "        {\n" +
                "            \"attributeId\": 178,\n" +
                "            \"attributeValue\": \"89SSFaPZjtHK3Hia5Q7KA==\",\n" +
                "            \"attributeKey\": \"password\"\n" +
                "        }]")
        EditCompInstAttributeBL editCompInstAttributeBL = new EditCompInstAttributeBL()
        UtilityBean<HierarchyBean> beanUtilityBean = editCompInstAttributeBL.clientValidation(requestObject)
        when:
        List<CompInstAttributesBean> compInstAttributesBeans = editCompInstAttributeBL.serverValidation(beanUtilityBean)
        then:
        final e = thrown(ServerException)
        e.getMessage() == "ServerException : Password is not properly encrypted."
    }

    def "non-editable attribute check for instance attribute "() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier","d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":serviceId","8")
        params.put(":instanceId","53")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization",authToken)
        requestObject.setHeaders(headers)

        requestObject.setBody("[\n" +
                "        {\n" +
                "            \"attributeId\": 175,\n" +
                "            \"attributeValue\": \"*************\",\n" +
                "            \"attributeKey\": \"hostaddress\"\n" +
                "        }]")
        EditCompInstAttributeBL editCompInstAttributeBL = new EditCompInstAttributeBL()
        UtilityBean<HierarchyBean> beanUtilityBean = editCompInstAttributeBL.clientValidation(requestObject)
        when:
        List<CompInstAttributesBean> compInstAttributesBeans = editCompInstAttributeBL.serverValidation(beanUtilityBean)
        then:
        final e = thrown(ServerException)
        e.getMessage() == "ServerException : The attribute key 'hostaddress' is not editable"
    }
    def "update valid instance attribute"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier","d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":serviceId","8")
        params.put(":instanceId","53")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization",authToken)
        requestObject.setHeaders(headers)

        requestObject.setBody("[\n" +
                "        {\n" +
                "            \"attributeId\": 176,\n" +
                "            \"attributeValue\": \"1522\",\n" +
                "            \"attributeKey\": \"monitorport\"\n" +
                "        }]")
        EditCompInstAttributeBL editCompInstAttributeBL = new EditCompInstAttributeBL()
        UtilityBean<HierarchyBean> beanUtilityBean = editCompInstAttributeBL.clientValidation(requestObject)
        List<CompInstAttributesBean> compInstAttributesBeans = editCompInstAttributeBL.serverValidation(beanUtilityBean)
        when:
        List<CompInstAttributesBean> comptInstancePojoList = editCompInstAttributeBL.process(compInstAttributesBeans)
        then:
        comptInstancePojoList.size() == 1
    }*/

}
