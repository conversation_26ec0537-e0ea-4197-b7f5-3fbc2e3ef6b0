package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.exceptions.ServerException
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import com.appnomic.appsone.controlcenter.pojo.Supervisor
import spock.lang.Specification

class UpdateSupervisorBLTest extends Specification {

    def "update supervisor - verification of IOException"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "  \"supervisorName\": \"Test_20\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":supervisorIdentifier", "e6a66a9f-ab0a-49e4-89c0-8fcd2f8143e7")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new UpdateSupervisorBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Exception encountered while parsing the JSON request body."
    }

    def "update supervisor - invalid Supervisor attributes"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "  \"name\": \"Test_20_eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA_eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA_eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":supervisorIdentifier", "e6a66a9f-ab0a-49e4-89c0-8fcd2f8143e7")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new UpdateSupervisorBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Input parameter(s) 'name' invalid"
    }

    def "update supervisor - invalid authorization token"() {
        setup:
        UtilityBean<Supervisor> utilityBean = UtilityBean.<Supervisor> builder()
                .authToken("testing-with-dummy-authorization-header")
                .build()

        when:
        new UpdateSupervisorBL().serverValidation(utilityBean)

        then:
        final ServerException e = thrown()
        e.getMessage() == "ServerException : Error in retrieving user identifier"
    }

    def "update supervisor - no Supervisor identifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "  \"name\": \"Test_20\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new UpdateSupervisorBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Supervisor identifier is invalid"
    }

    def "update supervisor - empty Supervisor identifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "  \"name\": \"Test_20\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":supervisorIdentifier", " ")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new UpdateSupervisorBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Supervisor identifier is invalid"
    }
}
