package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.exceptions.StaticThresholdException
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

class StaticThresholdTest extends Specification {
    Request request = Spy(Request.class)
    Response response = Spy(Response.class)

    def setup(){
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
    }

    def cleanup(){
        DBTestCache.rollback()
    }
    def "Client validation without exception"(){
        setup:
        setup()
        StaticThreshold staticThreshold = new StaticThreshold()
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":serviceId") >> "3"
        request.queryParams("kpiType") >> "Core"
        request.params(":threshold-type") >> "static"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.f4vVXGo3h3l6IP1Y9OXPR-UWYpY2SgIS9o-uAjzA5wjrpCrh-LmQrvqs-Apo0LSbkfvbSYKLVWGwAW16VrCH2eyx25NW64kgEs6PP9AovJM9uL6Nw_BzRi5dO_BwtFnT2NlC2yGC7fmE47oYkC9c8-wPp3IuPtrXvaJ1CfxPlqHbTYWJTGI4tPzCYTRA1ppCwqLFOw_0eod_p3z-RLSFoyw5MWlKFy1PjvvNrGYRY-HFOriDvjIuT7lOkAxnOPVm3cL6rJSKgC4Ur3kOejGtpUjy0S_KRL1-BmZMbRaGa7Y7CD0b6ZJFgNVhYgcvBxmDEAdOL8SnCYzSDiHKBbd8tw"
        when:
        staticThreshold.validateRequestParameters(request)
        then:
        noExceptionThrown()
    }
    def "Client validation with exception"(){
        setup:
        setup()
        StaticThreshold staticThreshold = new StaticThreshold()
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":serviceId") >> "3"
        request.queryParams("kpiType") >> "Core"
        request.params(":threshold-type") >> "nor"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.f4vVXGo3h3l6IP1Y9OXPR-UWYpY2SgIS9o-uAjzA5wjrpCrh-LmQrvqs-Apo0LSbkfvbSYKLVWGwAW16VrCH2eyx25NW64kgEs6PP9AovJM9uL6Nw_BzRi5dO_BwtFnT2NlC2yGC7fmE47oYkC9c8-wPp3IuPtrXvaJ1CfxPlqHbTYWJTGI4tPzCYTRA1ppCwqLFOw_0eod_p3z-RLSFoyw5MWlKFy1PjvvNrGYRY-HFOriDvjIuT7lOkAxnOPVm3cL6rJSKgC4Ur3kOejGtpUjy0S_KRL1-BmZMbRaGa7Y7CD0b6ZJFgNVhYgcvBxmDEAdOL8SnCYzSDiHKBbd8tw"
        when:
        staticThreshold.validateRequestParameters(request)
        then:
        thrown(StaticThresholdException)
    }
    def "Server validation with exception"(){
        setup:
        setup()
        StaticThreshold staticThreshold = new StaticThreshold()
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-12"
        request.params(":serviceId") >> "4"
        request.queryParams("kpiType") >> "Core"
        request.params(":threshold-type") >> "static"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.f4vVXGo3h3l6IP1Y9OXPR-UWYpY2SgIS9o-uAjzA5wjrpCrh-LmQrvqs-Apo0LSbkfvbSYKLVWGwAW16VrCH2eyx25NW64kgEs6PP9AovJM9uL6Nw_BzRi5dO_BwtFnT2NlC2yGC7fmE47oYkC9c8-wPp3IuPtrXvaJ1CfxPlqHbTYWJTGI4tPzCYTRA1ppCwqLFOw_0eod_p3z-RLSFoyw5MWlKFy1PjvvNrGYRY-HFOriDvjIuT7lOkAxnOPVm3cL6rJSKgC4Ur3kOejGtpUjy0S_KRL1-BmZMbRaGa7Y7CD0b6ZJFgNVhYgcvBxmDEAdOL8SnCYzSDiHKBbd8tw"
        when:
        def k =staticThreshold.serverValidation(request)
        then:
        thrown(StaticThresholdException)
    }
}
