package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.pojo.AgentTriggerStatusPojo
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import spock.lang.Specification

class GetAgentCommandTriggerStatusBLTest extends Specification{
    def "Client validation failure - NULL request object"() {
        when:
        new GetAgentCommandTriggerStatusBL().clientValidation(null)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Request object is null or empty."
    }

    def "Client validation failure - NULL request body"() {
        setup:
        RequestObject requestObject = new RequestObject()

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        requestObject.setQueryParams(queryMapData)

        when:
        new GetAgentCommandTriggerStatusBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid request body. Reason: Request body is either NULL or empty."
    }

    def "Client validation failure - Garbage request body"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("testing-with-dummy-body")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new GetAgentCommandTriggerStatusBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid JSON."
    }

    def "Client validation failure - NULL accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandJobId\": \"c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 48,\n" +
                "        \"commandJobId\": \"df4c6052-8c33-4cce-b461-e79ddfb758b0\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", null)
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new GetAgentCommandTriggerStatusBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid account identifier"
    }

    def "Client validation failure - Empty accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandJobId\": \"c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 48,\n" +
                "        \"commandJobId\": \"df4c6052-8c33-4cce-b461-e79ddfb758b0\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new GetAgentCommandTriggerStatusBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid account identifier"
    }

    def "Client validation failure - NULL authorization token"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandJobId\": \"c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 48,\n" +
                "        \"commandJobId\": \"df4c6052-8c33-4cce-b461-e79ddfb758b0\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", null)
        requestObject.setHeaders(headers)

        when:
        new GetAgentCommandTriggerStatusBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - empty authorization token"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandJobId\": \"c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 48,\n" +
                "        \"commandJobId\": \"df4c6052-8c33-4cce-b461-e79ddfb758b0\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "")
        requestObject.setHeaders(headers)

        when:
        new GetAgentCommandTriggerStatusBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - empty authorization key"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandJobId\": \"c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 48,\n" +
                "        \"commandJobId\": \"df4c6052-8c33-4cce-b461-e79ddfb758b0\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        requestObject.setHeaders(headers)

        when:
        new GetAgentCommandTriggerStatusBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - Invalid datatype of request body keys"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"dummy-string-in place of-int\",\n" +
                "        \"commandJobId\": \"c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 48,\n" +
                "        \"commandJobId\": 99\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new GetAgentCommandTriggerStatusBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid JSON."
    }

    def "Client validation failure - request body key 'physicalAgentId' <= 0 value"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": -10,\n" +
                "        \"commandJobId\": \"c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 48,\n" +
                "        \"commandJobId\": \"df4c6052-8c33-4cce-b461-e79ddfb758b0\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new GetAgentCommandTriggerStatusBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Input validation failure"
    }

    def "Client validation failure - request body key 'physicalAgentId' null value"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": null,\n" +
                "        \"commandJobId\": \"c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 48,\n" +
                "        \"commandJobId\": \"df4c6052-8c33-4cce-b461-e79ddfb758b0\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new GetAgentCommandTriggerStatusBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Input validation failure"
    }

    def "Client validation failure - request body key 'commandJobId' null value"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 10,\n" +
                "        \"commandJobId\": null\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 48,\n" +
                "        \"commandJobId\": \"df4c6052-8c33-4cce-b461-e79ddfb758b0\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new GetAgentCommandTriggerStatusBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Input validation failure"
    }

    def "Client validation failure - request body key 'commandJobId' empty value"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 10,\n" +
                "        \"commandJobId\": \"c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 48,\n" +
                "        \"commandJobId\": \"\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new GetAgentCommandTriggerStatusBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Input validation failure"
    }

    def "Client validation failure - both request body key 'physicalAgentId' & 'commandJobId' wrong value"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": -13,\n" +
                "        \"commandJobId\": null\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 48,\n" +
                "        \"commandJobId\": \"df4c6052-8c33-4cce-b461-e79ddfb758b0\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new GetAgentCommandTriggerStatusBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Input validation failure"

    }

    def "Success Case - With distinct physicalAgentId and commandJobId pair"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandJobId\": \"c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 48,\n" +
                "        \"commandJobId\": \"df4c6052-8c33-4cce-b461-e79ddfb758b0\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<List<AgentTriggerStatusPojo>> data = new GetAgentCommandTriggerStatusBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().size() == 2
        data.getPojoObject().get(0).getPhysicalAgentId() == 13
        data.getPojoObject().get(0).getCommandJobId() == "c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e"

    }

    def "Success Case - duplicate physicalAgentId and commandJobId pair"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandJobId\": \"c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandJobId\": \"c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 48,\n" +
                "        \"commandJobId\": \"df4c6052-8c33-4cce-b461-e79ddfb758b0\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandJobId\": \"c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandJobId\": \"c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandJobId\": \"c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 48,\n" +
                "        \"commandJobId\": \"df4c6052-8c33-4cce-b461-e79ddfb758b0\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandJobId\": \"c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 48,\n" +
                "        \"commandJobId\": \"df4c6052-8c33-4cce-b461-e79ddfb758b0\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<List<AgentTriggerStatusPojo>> data = new GetAgentCommandTriggerStatusBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().size() == 2
        data.getPojoObject().get(0).getPhysicalAgentId() == 13
        data.getPojoObject().get(0).getCommandJobId() == "c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e"
    }
}
