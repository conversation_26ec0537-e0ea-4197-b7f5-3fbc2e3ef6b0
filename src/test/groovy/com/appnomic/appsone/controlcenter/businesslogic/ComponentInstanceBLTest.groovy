package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ServiceKpiThreshold
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.ComponentInstance
import com.appnomic.appsone.controlcenter.pojo.IdPojo
import com.appnomic.appsone.controlcenter.pojo.StaticThresholdRules
import com.appnomic.appsone.controlcenter.pojo.Tags
import com.appnomic.appsone.controlcenter.dao.mysql.ImportServicesDataService
import com.appnomic.appsone.controlcenter.exceptions.RequestException
import spark.Request
import spark.Response
import spock.lang.Specification

class ComponentInstanceBLTest extends Specification{

    Request request = Spy(Request.class)
    Response response = Spy(Response.class)

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        Thread.sleep(20000)
    }

    def "Method Success"(){
        given:
        ComponentInstance componentInstance = new ComponentInstance()
        List<Tags> tags = new ArrayList<>()
        tags.add(Tags.builder().identifier("WebLogic-Service").subTypeName("Services").name("Controller").layer("app_service").build())
        tags.add(Tags.builder().identifier("Appsone_fi").subTypeName("Application").name("Controller").build())
        componentInstance.setIdentifier("Logic-Cluster")
        componentInstance.setMstComponentName("Weblogic")
        componentInstance.setKpi(null)
        componentInstance.setDiscovery(1)
        componentInstance.setName("Logic-Cluster")
        componentInstance.setMstComponentVersion("12.1.x")
        componentInstance.setMstComponentType("Application Server")
        componentInstance.setGroupKpiDiscovery(null)
        componentInstance.setGroupKpi(null)
        componentInstance.setTags(tags)
        Map<String,IdPojo> map = new HashMap<>()
        IdPojo idPojo = new IdPojo()
        idPojo.setId(1)
        map.put("WebLogic-Service", idPojo)
        when:
        StaticThreshold.createKpiThresholdForAvailabilityKpi(componentInstance,2,"7640123a-fbde-4fe5-9812-581cd1e3a9c1",null,map, new HashSet<ServiceKpiThreshold>())
        then:
        noExceptionThrown()
        def thresholdRules = new StaticThresholdRules()
        thresholdRules.setKpiId("255")
        thresholdRules.setKpiLevel("instances")
        thresholdRules.setKpiAttribute("ALL")
        ServiceKpiThreshold obj = ImportServicesDataService.getStaticThreshold(thresholdRules,2,3)
        obj.getId()!=0
    }
    def "Method Failure"(){
        given:
        ComponentInstance componentInstance = new ComponentInstance()
        List<Tags> tags = new ArrayList<>()
        tags.add(Tags.builder().identifier("Logic-Service").subTypeName("Services").name("Controller").layer("app_service").build())
        tags.add(Tags.builder().identifier("Appsone_fi").subTypeName("Application").name("Controller").build())
        componentInstance.setIdentifier("Logic-Cluster")
        componentInstance.setMstComponentName("Weblogic")
        componentInstance.setKpi(null)
        componentInstance.setDiscovery(1)
        componentInstance.setName("Logic-Cluster")
        componentInstance.setMstComponentVersion("12.1.x")
        componentInstance.setMstComponentType("Application Server")
        componentInstance.setGroupKpiDiscovery(null)
        componentInstance.setGroupKpi(null)
        componentInstance.setTags(tags)
        when:
        StaticThreshold.createKpiThresholdForAvailabilityKpi(componentInstance,2,"7640123a-fbde-4fe5-9812-581cd1e3a9c1",null,new HashMap<String, IdPojo>(), new HashSet<ServiceKpiThreshold>())
        then:
        final e = thrown(ControlCenterException)
        e.getMessage() == "Service not exist for identifier: Logic-Service"
    }

    def "clientValidation failure :Account Identifier should not be empty."() {
        given:
        request.params(":identifier") >> null
        request.headers("Authorization") >> "eyJhbGciOiJSUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3TWFqem5objVlcXM4ZThfMW80djZ5cXVDZFdUQUdKWVNpRE1hejk5dTFvIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cStjosMGnZaQnXycz9NuI0aL82leJU_VCjqnAadmMA4slC_YECbbCKcDDprOIeR11C009tLJCx5aak9cuaQrotvbTROwlrDi3J4PJcjU5gyRA9wpZ5QyZ0EnfuStqYQqCZ3GW1yrpwVUkitKA7pZ97u1AwsKaphyRPzru6YGiSRPLe93H1J9FDOsgqf-XwbPcDAA-MsFIEfycbmNkkQ2F7S-sipUGcCm0STk5xSc8jRCafmvKKsqiTRagq2YjRNwEAykTAvaSOhVDKPg89tbklQdKN1ooKD_4ZhIOJq-mKIsBkQFr7UvR9GiOgir3h-wWLliEb5bZ-67VgPBevOhbQ"
        request.body() >> null
        when:
        ComponentInstanceBL.addClientValidations(request);
        then:
        final e = thrown(RequestException)
        e.getMessage() == "Account Identifier should not be empty."
    }

    def "clientValidation failure :Authorization Token is empty"() {
        given:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> null
        request.body() >> null
        when:
        ComponentInstanceBL.addClientValidations(request)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "Authorization Token is empty."
    }

    def "clientValidation failure :invalid body"() {
        given:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3TWFqem5objVlcXM4ZThfMW80djZ5cXVDZFdUQUdKWVNpRE1hejk5dTFvIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cStjosMGnZaQnXycz9NuI0aL82leJU_VCjqnAadmMA4slC_YECbbCKcDDprOIeR11C009tLJCx5aak9cuaQrotvbTROwlrDi3J4PJcjU5gyRA9wpZ5QyZ0EnfuStqYQqCZ3GW1yrpwVUkitKA7pZ97u1AwsKaphyRPzru6YGiSRPLe93H1J9FDOsgqf-XwbPcDAA-MsFIEfycbmNkkQ2F7S-sipUGcCm0STk5xSc8jRCafmvKKsqiTRagq2YjRNwEAykTAvaSOhVDKPg89tbklQdKN1ooKD_4ZhIOJq-mKIsBkQFr7UvR9GiOgir3h-wWLliEb5bZ-67VgPBevOhbQ"
        request.body() >> null
        when:
        ComponentInstanceBL.addClientValidations(request)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "Request object is null or empty."
    }

    def "clientValidation failure :invalid JSON"() {
        given:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3TWFqem5objVlcXM4ZThfMW80djZ5cXVDZFdUQUdKWVNpRE1hejk5dTFvIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cStjosMGnZaQnXycz9NuI0aL82leJU_VCjqnAadmMA4slC_YECbbCKcDDprOIeR11C009tLJCx5aak9cuaQrotvbTROwlrDi3J4PJcjU5gyRA9wpZ5QyZ0EnfuStqYQqCZ3GW1yrpwVUkitKA7pZ97u1AwsKaphyRPzru6YGiSRPLe93H1J9FDOsgqf-XwbPcDAA-MsFIEfycbmNkkQ2F7S-sipUGcCm0STk5xSc8jRCafmvKKsqiTRagq2YjRNwEAykTAvaSOhVDKPg89tbklQdKN1ooKD_4ZhIOJq-mKIsBkQFr7UvR9GiOgir3h-wWLliEb5bZ-67VgPBevOhbQ"
        request.body() >> "[\n " +
                " {\n  " +
                "  \"name\": \"ContainerGroovy\"," +
                "\n    " +
                "\"id\": \"4f51f8b0-b6c9-4375-8729-b21bc6d877777\"," +
                "\n    " +
                "\"componentName\": \"AIX\"," +
                "\n    " +
                "\"componentVersion\": \"6.1\"," +
                "\n    " +
                "\"serviceIdentifiers\": [" +
                "\n      " +
                "\"4f51f8b0-b6c9-4375-8729-b21bc6d89999\"" +
                "\n    " +
                "]," +
                "\n   " +
                " \"agentIdentifiers\": [" +
                "\n      " +
                "\"e570de02-c585-4917-bbb7-5c97b35e-53\"" +
                "\n    " +
                "]," +
                "\n    " +
                "\"discovery\": 1," +
                "\n    " +
                "\"attributes\": [" +
                "\n      {" +
                "\n        " +
                "\"name\": \"HostAddress\"," +
                "\n        " +
                "\"value\": \"************\"" +
                "\n      }\n " +
                "   ]" +
                "\n  " +
                "}\n" +
                "]"
        when:
        ComponentInstanceBL.addClientValidations(request)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "Invalid JSON."
    }

    def "clientValidation failure :duplicate component instance"() {
        given:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3TWFqem5objVlcXM4ZThfMW80djZ5cXVDZFdUQUdKWVNpRE1hejk5dTFvIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cStjosMGnZaQnXycz9NuI0aL82leJU_VCjqnAadmMA4slC_YECbbCKcDDprOIeR11C009tLJCx5aak9cuaQrotvbTROwlrDi3J4PJcjU5gyRA9wpZ5QyZ0EnfuStqYQqCZ3GW1yrpwVUkitKA7pZ97u1AwsKaphyRPzru6YGiSRPLe93H1J9FDOsgqf-XwbPcDAA-MsFIEfycbmNkkQ2F7S-sipUGcCm0STk5xSc8jRCafmvKKsqiTRagq2YjRNwEAykTAvaSOhVDKPg89tbklQdKN1ooKD_4ZhIOJq-mKIsBkQFr7UvR9GiOgir3h-wWLliEb5bZ-67VgPBevOhbQ"
        request.body() >> "[\n " +
                " {\n  " +
                "  \"name\": \"ContainerGroovy\"," +
                "\n    " +
                "\"identifier\": \"4f51f8b0-b6c9-4375-8729-b21bc6d877777\"," +
                "\n    " +
                "\"componentName\": \"AIX\"," +
                "\n    " +
                "\"componentVersion\": \"6.1\"," +
                "\n    " +
                "\"serviceIdentifiers\": [" +
                "\n      " +
                "\"4f51f8b0-b6c9-4375-8729-b21bc6d89999\"" +
                "\n    " +
                "]," +
                "\n   " +
                " \"agentIdentifiers\": [" +
                "\n      " +
                "\"e570de02-c585-4917-bbb7-5c97b35e-53\"" +
                "\n    " +
                "]," +
                "\n    " +
                "\"discovery\": 1," +
                "\n    " +
                "\"attributes\": [" +
                "\n      {" +
                "\n        " +
                "\"name\": \"HostAddress\"," +
                "\n        " +
                "\"value\": \"************\"" +
                "\n      }\n " +
                "   ]" +
                "\n  " +
                "},\n" +
                " {\n  " +
                "  \"name\": \"ContainerGroovy\"," +
                "\n    " +
                "\"identifier\": \"4f51f8b0-b6c9-4375-8729-b21bc6d877777\"," +
                "\n    " +
                "\"componentName\": \"AIX\"," +
                "\n    " +
                "\"componentVersion\": \"6.1\"," +
                "\n    " +
                "\"serviceIdentifiers\": [" +
                "\n      " +
                "\"4f51f8b0-b6c9-4375-8729-b21bc6d89999\"" +
                "\n    " +
                "]," +
                "\n   " +
                " \"agentIdentifiers\": [" +
                "\n      " +
                "\"e570de02-c585-4917-bbb7-5c97b35e-53\"" +
                "\n    " +
                "]," +
                "\n    " +
                "\"discovery\": 1," +
                "\n    " +
                "\"attributes\": [" +
                "\n      {" +
                "\n        " +
                "\"name\": \"HostAddress\"," +
                "\n        " +
                "\"value\": \"************\"" +
                "\n      }\n " +
                "   ]" +
                "\n  " +
                "}\n" +
                "]"
        when:
        ComponentInstanceBL.addClientValidations(request)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "Duplicate Component Instance identifier or name"
    }

    def "clientValidation failure :Name is empty"() {
        given:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3TWFqem5objVlcXM4ZThfMW80djZ5cXVDZFdUQUdKWVNpRE1hejk5dTFvIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cStjosMGnZaQnXycz9NuI0aL82leJU_VCjqnAadmMA4slC_YECbbCKcDDprOIeR11C009tLJCx5aak9cuaQrotvbTROwlrDi3J4PJcjU5gyRA9wpZ5QyZ0EnfuStqYQqCZ3GW1yrpwVUkitKA7pZ97u1AwsKaphyRPzru6YGiSRPLe93H1J9FDOsgqf-XwbPcDAA-MsFIEfycbmNkkQ2F7S-sipUGcCm0STk5xSc8jRCafmvKKsqiTRagq2YjRNwEAykTAvaSOhVDKPg89tbklQdKN1ooKD_4ZhIOJq-mKIsBkQFr7UvR9GiOgir3h-wWLliEb5bZ-67VgPBevOhbQ"
        request.body() >> "[\n " +
                " {\n  " +
                "  \"name\": \"\"," +
                "\n    " +
                "\"identifier\": \"4f51f8b0-b6c9-4375-8729-b21bc6d877777\"," +
                "\n    " +
                "\"componentName\": \"AIX\"," +
                "\n    " +
                "\"componentVersion\": \"6.1\"," +
                "\n    " +
                "\"serviceIdentifiers\": [" +
                "\n      " +
                "\"4f51f8b0-b6c9-4375-8729-b21bc6d89999\"" +
                "\n    " +
                "]," +
                "\n   " +
                " \"agentIdentifiers\": [" +
                "\n      " +
                "\"e570de02-c585-4917-bbb7-5c97b35e-53\"" +
                "\n    " +
                "]," +
                "\n    " +
                "\"discovery\": 1," +
                "\n    " +
                "\"attributes\": [" +
                "\n      {" +
                "\n        " +
                "\"name\": \"HostAddress\"," +
                "\n        " +
                "\"value\": \"************\"" +
                "\n      }\n " +
                "   ]" +
                "\n  " +
                "}\n" +
                "]"
        when:
        ComponentInstanceBL.addClientValidations(request)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "{name=Name is empty}"
    }

    def "clientValidation failure :componentName is empty"() {
        given:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3TWFqem5objVlcXM4ZThfMW80djZ5cXVDZFdUQUdKWVNpRE1hejk5dTFvIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cStjosMGnZaQnXycz9NuI0aL82leJU_VCjqnAadmMA4slC_YECbbCKcDDprOIeR11C009tLJCx5aak9cuaQrotvbTROwlrDi3J4PJcjU5gyRA9wpZ5QyZ0EnfuStqYQqCZ3GW1yrpwVUkitKA7pZ97u1AwsKaphyRPzru6YGiSRPLe93H1J9FDOsgqf-XwbPcDAA-MsFIEfycbmNkkQ2F7S-sipUGcCm0STk5xSc8jRCafmvKKsqiTRagq2YjRNwEAykTAvaSOhVDKPg89tbklQdKN1ooKD_4ZhIOJq-mKIsBkQFr7UvR9GiOgir3h-wWLliEb5bZ-67VgPBevOhbQ"
        request.body() >> "[\n " +
                " {\n  " +
                "  \"name\": \"ContainerGroovy\"," +
                "\n    " +
                "\"identifier\": \"4f51f8b0-b6c9-4375-8729-b21bc6d877777\"," +
                "\n    " +
                "\"componentName\": \"\"," +
                "\n    " +
                "\"componentVersion\": \"6.1\"," +
                "\n    " +
                "\"serviceIdentifiers\": [" +
                "\n      " +
                "\"4f51f8b0-b6c9-4375-8729-b21bc6d89999\"" +
                "\n    " +
                "]," +
                "\n   " +
                " \"agentIdentifiers\": [" +
                "\n      " +
                "\"e570de02-c585-4917-bbb7-5c97b35e-53\"" +
                "\n    " +
                "]," +
                "\n    " +
                "\"discovery\": 1," +
                "\n    " +
                "\"attributes\": [" +
                "\n      {" +
                "\n        " +
                "\"name\": \"HostAddress\"," +
                "\n        " +
                "\"value\": \"************\"" +
                "\n      }\n " +
                "   ]" +
                "\n  " +
                "}\n" +
                "]"
        when:
        ComponentInstanceBL.addClientValidations(request)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "{componentName=Component Name is empty for ContainerGroovy}"
    }

    def "clientValidation failure :componentVersion is empty"() {
        given:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3TWFqem5objVlcXM4ZThfMW80djZ5cXVDZFdUQUdKWVNpRE1hejk5dTFvIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cStjosMGnZaQnXycz9NuI0aL82leJU_VCjqnAadmMA4slC_YECbbCKcDDprOIeR11C009tLJCx5aak9cuaQrotvbTROwlrDi3J4PJcjU5gyRA9wpZ5QyZ0EnfuStqYQqCZ3GW1yrpwVUkitKA7pZ97u1AwsKaphyRPzru6YGiSRPLe93H1J9FDOsgqf-XwbPcDAA-MsFIEfycbmNkkQ2F7S-sipUGcCm0STk5xSc8jRCafmvKKsqiTRagq2YjRNwEAykTAvaSOhVDKPg89tbklQdKN1ooKD_4ZhIOJq-mKIsBkQFr7UvR9GiOgir3h-wWLliEb5bZ-67VgPBevOhbQ"
        request.body() >> "[\n " +
                " {\n  " +
                "  \"name\": \"ContainerGroovy\"," +
                "\n    " +
                "\"identifier\": \"4f51f8b0-b6c9-4375-8729-b21bc6d877777\"," +
                "\n    " +
                "\"componentName\": \"AIX\"," +
                "\n    " +
                "\"componentVersion\": \"\"," +
                "\n    " +
                "\"serviceIdentifiers\": [" +
                "\n      " +
                "\"4f51f8b0-b6c9-4375-8729-b21bc6d89999\"" +
                "\n    " +
                "]," +
                "\n   " +
                " \"agentIdentifiers\": [" +
                "\n      " +
                "\"e570de02-c585-4917-bbb7-5c97b35e-53\"" +
                "\n    " +
                "]," +
                "\n    " +
                "\"discovery\": 1," +
                "\n    " +
                "\"attributes\": [" +
                "\n      {" +
                "\n        " +
                "\"name\": \"HostAddress\"," +
                "\n        " +
                "\"value\": \"************\"" +
                "\n      }\n " +
                "   ]" +
                "\n  " +
                "}\n" +
                "]"
        when:
        ComponentInstanceBL.addClientValidations(request)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "{componentVersion=Component Version is empty for ContainerGroovy}"
    }

    def "clientValidation failure :Service Identifier is empty"() {
        given:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3TWFqem5objVlcXM4ZThfMW80djZ5cXVDZFdUQUdKWVNpRE1hejk5dTFvIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cStjosMGnZaQnXycz9NuI0aL82leJU_VCjqnAadmMA4slC_YECbbCKcDDprOIeR11C009tLJCx5aak9cuaQrotvbTROwlrDi3J4PJcjU5gyRA9wpZ5QyZ0EnfuStqYQqCZ3GW1yrpwVUkitKA7pZ97u1AwsKaphyRPzru6YGiSRPLe93H1J9FDOsgqf-XwbPcDAA-MsFIEfycbmNkkQ2F7S-sipUGcCm0STk5xSc8jRCafmvKKsqiTRagq2YjRNwEAykTAvaSOhVDKPg89tbklQdKN1ooKD_4ZhIOJq-mKIsBkQFr7UvR9GiOgir3h-wWLliEb5bZ-67VgPBevOhbQ"
        request.body() >> "[\n " +
                " {\n  " +
                "  \"name\": \"ContainerGroovy\"," +
                "\n    " +
                "\"identifier\": \"4f51f8b0-b6c9-4375-8729-b21bc6d877777\"," +
                "\n    " +
                "\"componentName\": \"AIX\"," +
                "\n    " +
                "\"componentVersion\": \"6.1\"," +
                "\n    " +
                "\"serviceIdentifiers\": [" +
                "\n      " +
                "]," +
                "\n   " +
                " \"agentIdentifiers\": [" +
                "\n      " +
                "\"e570de02-c585-4917-bbb7-5c97b35e-53\"" +
                "\n    " +
                "]," +
                "\n    " +
                "\"discovery\": 1," +
                "\n    " +
                "\"attributes\": [" +
                "\n      {" +
                "\n        " +
                "\"name\": \"HostAddress\"," +
                "\n        " +
                "\"value\": \"************\"" +
                "\n      }\n " +
                "   ]" +
                "\n  " +
                "}\n" +
                "]"
        when:
        ComponentInstanceBL.addClientValidations(request)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "{serviceIdentifier=Service Identifier is empty for ContainerGroovy }"
    }
}
