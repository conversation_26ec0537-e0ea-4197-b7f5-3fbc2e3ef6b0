package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.exceptions.KpiException
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.AddKpiRequest
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class KpiTest extends Specification {

    @Shared
    String oldH2URL

    def setupSpec() {
        oldH2URL = MySQLConnectionManager.INSTANCE.getH2URL()

        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2URL()
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        Thread.sleep(30000)
    }

    def cleanupSpec() {
        DBTestCache.rollback()
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        //the following line was added ONLY to ensure that the existing test cases are not impacted.
        MySQLConnectionManager.INSTANCE.getHandle()
    }

    @Shared
    def k = new AddKpiRequest()

    class DummyRequest extends Request {
        boolean nullCheck = true
        String body

        @Override
        String body() {
            if (body != null) {
                return body
            }

            if (nullCheck) {
                return null
            } else {
                return " "
            }
        }
    }

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    def "addKpi client validation failure request body NULL"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)

        Request request = Spy(Request.class)
        request.params(":identifier") >> "e573f852-5057-11e9-8fd2-b37b61e52317"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> ""

        when:
        Kpi kpi = new Kpi()
        kpi.clientValidation(request.body())

        then:
        final KpiException e = thrown()
        e.getMessage() == "KpiException : Invalid request body. Reason: Request body is either NULL or empty. Refer more details, refer application log file."
    }

    def "addKpi client validation failure invalid parameters"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)

        Request request = Spy(Request.class)
        request.params(":identifier") >> "e573f852-5057-11e9-8fd2-b37b61e52317"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> """{"kpiName": "KPI_NAME", 
                            "kpiIdentifier": "KPI_UNIQUE_IDENTIFIER", 
                            "description": "Description of the KPI", 
                            "groupKpiIdentifier" : "GROUP_KPI_NAME", 
                            "dataType" : "Integer", 
                            "clusterOperation" : "SUM", 
                            "rollupOperation" : "SUM", 
                            "instanceAggregation" : "MULTIVALUE", 
                            "clusterAggregation" : "MULTIVALUE", 
                            "collectionIntervalSeconds" : 60, 
                            "measureUnits" : "percentile", 
                            "componentName" : "COMPONENT_UNIQUE_IDENTIFIER", 
                            "componentType" : "COMPONENT_TYPE", 
                            "componentVersion" : "10.x", 
                            "categoryName": "CATEGORY_NAME", 
                            "enableAnalytics": 1}"""

        when:
        Kpi kpi = new Kpi()
        kpi.clientValidation(request.body())

        then:
        final KpiException e = thrown()
        e.getMessage() == "KpiException : Invalid request. Reason: Invalid values for one or more attributes. Refer more details, refer application log file."
    }

    def "addKpi client validation successful with auto-generated kpiIdentifier"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)

        Request request = Spy(Request.class)
        request.params(":identifier") >> "e573f852-5057-11e9-8fd2-b37b61e52317"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> """{"kpiName": "KPI_NAME", 
                            "description": "Description of the KPI", 
                            "groupKpiIdentifier" : "GROUP_KPI_NAME", 
                            "dataType" : "Integer", 
                            "kpiType": "CORE",
                            "clusterOperation" : "SUM", 
                            "rollupOperation" : "SUM", 
                            "instanceAggregation" : "MULTIVALUE", 
                            "clusterAggregation" : "MULTIVALUE", 
                            "collectionIntervalSeconds" : 60, 
                            "measureUnits" : "percentile", 
                            "componentName" : "COMPONENT_UNIQUE_IDENTIFIER", 
                            "componentType" : "COMPONENT_TYPE", 
                            "componentVersion" : "10.x", 
                            "categoryName": "CATEGORY_NAME", 
                            "enableAnalytics": 1}"""

        when:
        Kpi kpi = new Kpi()
        AddKpiRequest addKpiRequest = kpi.clientValidation(request.body())

        then:
        addKpiRequest.getKpiIdentifier() != null
        addKpiRequest.getKpiIdentifier().startsWith("KPI_NAME-")
    }

    def "addKpi client validation successful"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)

        Request request = Spy(Request.class)
        request.params(":identifier") >> "e573f852-5057-11e9-8fd2-b37b61e52317"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> """{"kpiName": "KPI_NAME", 
                            "kpiIdentifier": "KPI_UNIQUE_IDENTIFIER", 
                            "description": "Description of the KPI", 
                            "groupKpiIdentifier" : "GROUP_KPI_NAME", 
                            "dataType" : "Integer", 
                            "kpiType": "CORE", 
                            "clusterOperation" : "SUM", 
                            "rollupOperation" : "SUM", 
                            "instanceAggregation" : "MULTIVALUE", 
                            "clusterAggregation" : "MULTIVALUE", 
                            "collectionIntervalSeconds" : 60, 
                            "measureUnits" : "percentile", 
                            "componentName" : "COMPONENT_UNIQUE_IDENTIFIER", 
                            "componentType" : "COMPONENT_TYPE", 
                            "componentVersion" : "10.x", 
                            "categoryName": "CATEGORY_NAME", 
                            "enableAnalytics": 1}"""

        when:
        Kpi kpi = new Kpi()
        def addKpiRequest = kpi.clientValidation(request.body())

        then:
        noExceptionThrown()
        addKpiRequest.getKpiName() == "KPI_NAME"
        addKpiRequest.getKpiIdentifier() == "KPI_UNIQUE_IDENTIFIER"
        addKpiRequest.getDescription() == "Description of the KPI"
        addKpiRequest.getGroupKpiIdentifier() == "GROUP_KPI_NAME"
        addKpiRequest.getDataType() == "Integer"
        addKpiRequest.getKpiType() == "CORE"
        addKpiRequest.getClusterOperation() == "SUM"
        addKpiRequest.getRollupOperation() == "SUM"
        addKpiRequest.getInstanceAggregation() == "MULTIVALUE"
        addKpiRequest.getClusterAggregation() == "MULTIVALUE"
        addKpiRequest.getCollectionIntervalSeconds() == 60
        addKpiRequest.getMeasureUnits() == "percentile"
        addKpiRequest.getComponentName() == "COMPONENT_UNIQUE_IDENTIFIER"
        addKpiRequest.getComponentType() == "COMPONENT_TYPE"
        addKpiRequest.getComponentVersion() == "10.x"
        addKpiRequest.getCategoryName() == "CATEGORY_NAME"
        addKpiRequest.getEnableAnalytics() == 1
    }

    def "addKpi server validation invalid scenarios"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        Kpi kpi = new Kpi()

        when:
        kpi.serverValidation(k, 1)

        then:
        def error = thrown(expectedException)
        error.message == expectedMessage

        where:
        k                                            || expectedException | expectedMessage
        /*KPI with AliasName already exists*/
        new AddKpiRequest(1, 2, 60, "Disk IO Read", "DISK_IOREAD", "Description", "Integer",
                "Core", "Sum", "Sum", "MultiValue", "MultiValue", "percentile", "GROUP_KPI_ID", "COMP_NAME",
                "COMP_VER", "10.x", "CATEGORY_NAME", null, 1, 1)  | KpiException      | "KpiException : Kpi with provided alias name already exists"

        /*KPI without AliasName already exists*/
        new AddKpiRequest(1, 2, 60, "Disk IO Read", null, "Description", "Integer",
                "Core", "Sum", "Sum", "MultiValue", "MultiValue", "percentile", "GROUP_KPI_ID", "COMP_NAME",
                "COMP_VER", "10.x", "CATEGORY_NAME", null, 1, 1) || KpiException      | "KpiException : Kpi with provided name already exists"

        /*Group KPI doesn't exist*/
        new AddKpiRequest(1, 2, 60, "KPI_NAME", "KPI_UNIQUE_IDENTIFIER", "Description", "Integer",
                "Core", "Sum", "Sum", "MultiValue", "MultiValue", "percentile", "GROUP_KPI_ID", "COMP_NAME",
                "COMP_VER", "10.x", "CATEGORY_NAME", null, 1, 1) || KpiException      | "KpiException : KPI with groupKpiName is not available"

        /*Invalid Kpi type*/
        new AddKpiRequest(1, 2, 60, "KPI_NAME", "KPI_UNIQUE_IDENTIFIER", "Description", "Integer",
                "Core+Core", "Sum", "Sum", "MultiValue", "MultiValue+MultiValue", "percentile", "GROUP_KPI_ID", "COMP_NAME",
                "COMP_VER", "10.x", "CATEGORY_NAME", null, 1, 1) || KpiException      | "KpiException : kpiType of the KPI is invalid"

        /*KpiType mismatch between Group Kpi and individual Kpi*/
        new AddKpiRequest(1, 2, 60, "KPI_NAME", "KPI_UNIQUE_IDENTIFIER", "Description", "Integer",
                "Availability", "Sum", "Sum", "MultiValue", "MultiValue+MultiValue", "percentile", "PortUtilization", "COMP_NAME",
                "COMP_VER", "10.x", "CATEGORY_NAME", null, 1, 1) || KpiException      | "KpiException : kpiType of given KPI and corresponding group KPI does not match"

        /*Invalid cluster operation*/
        new AddKpiRequest(1, 2, 60, "KPI_NAME", "KPI_UNIQUE_IDENTIFIER", "Description", "Integer",
                "Core", "Sum+Sum", "Sum", "MultiValue", "MultiValue", "percentile", "DiskSpace", "COMP_NAME",
                "COMP_VER", "10.x", "CATEGORY_NAME", null, 1, 1) || KpiException      | "KpiException : clusterOperation of the KPI is invalid"

        /*Invalid rollup operation*/
        new AddKpiRequest(1, 2, 60, "KPI_NAME", "KPI_UNIQUE_IDENTIFIER", "Description", "Integer",
                "Core", "Sum", "Sum+Sum", "MultiValue", "MultiValue", "percentile", "DiskSpace", "COMP_NAME",
                "COMP_VER", "10.x", "CATEGORY_NAME", null, 1, 1) || KpiException      | "KpiException : rollupOperation of the KPI is invalid"

        /*Invalid cluster aggregation*/
        new AddKpiRequest(1, 2, 60, "KPI_NAME", "KPI_UNIQUE_IDENTIFIER", "Description", "Integer",
                "Core", "Sum", "Sum", "MultiValue+MultiValue", "MultiValue", "percentile", "DiskSpace", "COMP_NAME",
                "COMP_VER", "10.x", "CATEGORY_NAME", null, 1, 1) || KpiException      | "KpiException : clusterAggregation of the KPI is invalid"

        /*Invalid instance aggregation*/
        new AddKpiRequest(1, 2, 60, "KPI_NAME", "KPI_UNIQUE_IDENTIFIER", "Description", "Integer",
                "Core", "Sum", "Sum", "MultiValue", "MultiValue+MultiValue", "percentile", "DiskSpace", "COMP_NAME",
                "COMP_VER", "10.x", "CATEGORY_NAME", null, 1, 1) || KpiException      | "KpiException : instanceAggregation of the KPI is invalid"

        /*Invalid dataType for Core KpiType*/
        new AddKpiRequest(1, 2, 60, "KPI_NAME", "KPI_UNIQUE_IDENTIFIER", "Description", "Double",
                "Core", "Sum", "Sum", "MultiValue", "MultiValue", "percentile", "DiskSpace", "COMP_NAME",
                "COMP_VER", "10.x", "CATEGORY_NAME", null, 1, 1) || KpiException      | "KpiException : dataType of the KPI is invalid"

        /*Invalid dataType for Availability KpiType*/
        new AddKpiRequest(1, 2, 60, "KPI_NAME", "KPI_UNIQUE_IDENTIFIER", "Description", "Text",
                "Availability", "Sum", "Sum", "MultiValue", "MultiValue", "percentile", "CommunicationStatus", "COMP_NAME",
                "COMP_VER", "10.x", "CATEGORY_NAME", null, 1, 1) || KpiException      | "KpiException : dataType of the KPI is invalid"

        /*Invalid dataType for FileWatch KpiType*/
        new AddKpiRequest(1, 2, 60, "KPI_NAME", "KPI_UNIQUE_IDENTIFIER", "Description", "Integer",
                "FileWatch", "Sum", "Sum", "MultiValue", "MultiValue", "percentile", "FileWatcher", "COMP_NAME",
                "COMP_VER", "10.x", "CATEGORY_NAME", null, 1, 1) || KpiException      | "KpiException : dataType of the KPI is invalid"

        /*Invalid dataType for ConfigWatch KpiType*/
        new AddKpiRequest(1, 2, 60, "KPI_NAME", "KPI_UNIQUE_IDENTIFIER", "Description", "Double",
                "ConfigWatch", "Sum", "Sum", "MultiValue", "MultiValue", "percentile", "FetchFileProperties", "COMP_NAME",
                "COMP_VER", "10.x", "CATEGORY_NAME", null, 1, 1) || KpiException      | "KpiException : dataType of the KPI is invalid"

        /*Invalid kpiType*/
        new AddKpiRequest(1, 2, 60, "KPI_NAME", "KPI_UNIQUE_IDENTIFIER", "Description", "Double",
                "ConfigWatch+ConfigWatch", "Sum", "Sum", "MultiValue", "MultiValue", "percentile", "FetchFileProperties", "COMP_NAME",
                "COMP_VER", "10.x", "CATEGORY_NAME", null, 1, 1) || KpiException      | "KpiException : kpiType of the KPI is invalid"


        /*Invalid componentName and componentVersion for Core KpiType*/
        new AddKpiRequest(1, 2, 60, "KPI_NAME", "KPI_UNIQUE_IDENTIFIER", "Description", "Integer",
                "Core", "Sum", "Sum", "MultiValue", "MultiValue", "percentile", "DiskSpace", "COMP_NAME",
                "COMP_VER", "10.x", "CATEGORY_NAME", null, 1, 1) || KpiException      | "KpiException : Combination of commonVersionId and componentId of the KPI is invalid"

        /*Invalid componentType*/
        new AddKpiRequest(1, 2, 60, "KPI_NAME", "KPI_UNIQUE_IDENTIFIER", "Description", "Integer",
                "Core", "Sum", "Sum", "MultiValue", "MultiValue", "percentile", "DiskSpace", "CentOS",
                "6.x", "6.x/7.x", "CATEGORY_NAME", null, 1, 1)   || KpiException      | "KpiException : ComponentTypeId of the KPI is invalid"

        /*Invalid Category*/
        new AddKpiRequest(1, 2, 60, "KPI_NAME", "KPI_UNIQUE_IDENTIFIER", "Description", "Integer",
                "Core", "Sum", "Sum", "MultiValue", "MultiValue", "percentile", "DiskSpace", "CentOS",
                "6.x", "Host", "CATEGORY_NAME", null, 1, 1)      || KpiException      | "KpiException : categoryName of the KPI is invalid"
    }

    /*def "addKpi server validation success"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        Kpi kpi = new Kpi()
        k = new AddKpiRequest(1, 2, 60, "KPI_NAME", "KPI_UNIQUE_IDENTIFIER", "Description", "Integer",
                "Core", "Sum", "Sum", "MultiValue", "MultiValue", "percentile", "DiskSpace", "CentOS",
                "6.x", "Host", "Disk Space")

        when:
        kpi.serverValidation(k, 1)

        then:
        noExceptionThrown()
    }

    def "Process addKpiRequest negative cases"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        Request request = Spy(Request.class)
        request.params(":identifier") >> "e573f852-5057-11e9-8fd2-b37b61e52317"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> ""

        Kpi kpi = new Kpi()

        when:
        kpi.processAddKpiRequest(k, "userId", 1)

        then:
        def error = thrown(expectedException)
        error.message == expectedMessage

        where:
        k                                            || expectedException | expectedMessage
        *//*Invalid KpiType*//*
        new AddKpiRequest(1, 2, 60, "Disk IO Read", "DISK_IOREAD", "Description", "Integer",
                "Core+Core", "Sum", "Sum", "MultiValue", "MultiValue", "percentile", "GROUP_KPI_ID", "COMP_NAME",
                "COMP_VER", "10.x", "CATEGORY_NAME")  | KpiException      | "KpiException : Error while adding KPI details"

        *//*Invalid DataType*//*
        new AddKpiRequest(1, 2, 60, "Disk IO Read", "DISK_IOREAD", "Description", "Double",
                "Core", "Sum", "Sum", "MultiValue", "MultiValue", "percentile", "GROUP_KPI_ID", "COMP_NAME",
                "COMP_VER", "10.x", "CATEGORY_NAME")  | KpiException      | "KpiException : Error while adding KPI details"

        *//*Invalid DataType*//*
        new AddKpiRequest(1, 2, 60, "Disk IO Read", "DISK_IOREAD", "Description", "Integer",
                "Core", "Sum", "Sum", "MultiValue", "MultiValue", "percentile", "GROUP_KPI_ID", "COMP_NAME",
                "COMP_VER", "10.x", "CATEGORY_NAME")  | KpiException      | "KpiException : Error while mapping KPI to category"

        *//*Invalid component type*//*
        new AddKpiRequest(1, 2, 60, "Disk IO Read", "DISK_IOREAD", "Description", "Integer",
                "Core", "Sum", "Sum", "MultiValue", "MultiValue", "percentile", "GROUP_KPI_ID", "COMP_NAME",
                "COMP_VER", "10.x", "Disk Space")  | KpiException      | "KpiException : ComponentTypeId of the KPI is invalid"

        *//*Invalid component name and version*//*
        new AddKpiRequest(1, 2, 60, "KPI_NAME", "KPI_UNIQUE_IDENTIFIER", "Description", "Integer",
                "Core", "Sum", "Sum", "MultiValue", "MultiValue", "percentile", "DiskSpace", "CentOS",
                "6.x/7.x", "Host", "Disk Space")  | KpiException      | "KpiException : Error while mapping KPI to component version"
    }
*/
    /*def "Process addKpiRequest positive case"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        Request request = Spy(Request.class)
        request.params(":identifier") >> "e573f852-5057-11e9-8fd2-b37b61e52317"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> ""

        Kpi kpi = new Kpi()
        k = new AddKpiRequest(1, 2, 60, "KPI_NAME", "KPI_UNIQUE_IDENTIFIER", "Description", "Integer",
                "Core", "Sum", "Sum", "MultiValue", "MultiValue", "percentile", "DiskSpace", "CentOS",
                "6.x", "Host", "Disk Space")

        when:
        IdPojo kpiDetails = kpi.processAddKpiRequest(k, "userId", 1)

        then:
        noExceptionThrown()
        List<AllKpiList> addedKpi = MasterDataService.getAllKpisList().stream()
        .filter({it -> it.kpiId > 400}).collect(Collectors.toList())
               // .filter({it -> it.getKpiName() == "KPI_NAME" && it.getIdentifier() == "KPI_UNIQUE_IDENTIFIER"})
        //.findAny()
        addedKpi.get(0).getKpiName() == kpiDetails.name
        addedKpi.get(0).getIdentifier() == kpiDetails.identifier
        addedKpi.get(0).getGroupId() == 3
        addedKpi.get(0).getKpiType() == "Core"
    }*/
}

/*   kpiName                  | aliasName                               | description                                | dataType                         | kpiType              | cluserOperation              | rollupOperation             | clusterAggregation                    | instanceAggregation                    | collIntervalSec                    | measurementUnits                | groupKpiName                              | compName                                          | compVersion                  | compType                             | categoryName                       | enableAnalytics         || expectedException | expectedMessage
        k.setKpiName("KPI_NAME") | k.setAliasName("KPI_UNIQUE_IDENTIFIER") | k.setDescription("Description of the KPI") | k.setDataType("Integer")         | k.setKpiType("Core") | k.setClusterOperation("Sum") | k.setRollupOperation("Sum") | k.setClusterAggregation("MultiValue") | k.setInstanceAggregation("MultiValue") | k.setCollectionIntervalSeconds(60) | k.setMeasureUnits("percentile") | k.setGroupKpiIdentifier("GROUP_KPI_NAME") | k.setComponentName("COMPONENT_UNIQUE_IDENTIFIER") | k.setComponentVersion("7.x") | k.setComponentType("COMPONENT_TYPE") | k.setCategoryName("CATEGORY_NAME") | k.setEnableAnalytics(1) || KpiException      | "KpiException : Server validation failure."
        k.setKpiName("KPI_NAME") | k.setAliasName("KPI_UNIQUE_IDENTIFIER") | k.setDescription("Description of the KPI") | k.setDataType("Integer+Integer") | k.setKpiType("Core") | k.setClusterOperation("Sum") | k.setRollupOperation("Sum") | k.setClusterAggregation("MultiValue") | k.setInstanceAggregation("MultiValue") | k.setCollectionIntervalSeconds(60) | k.setMeasureUnits("percentile") | k.setGroupKpiIdentifier("DiskSpace")      | k.setComponentName("COMPONENT_UNIQUE_IDENTIFIER") | k.setComponentVersion("7.x") | k.setComponentType("COMPONENT_TYPE") | k.setCategoryName("CATEGORY_NAME") | k.setEnableAnalytics(1) || KpiException      | "KpiException : DataType of the KPI is invalid"
    */