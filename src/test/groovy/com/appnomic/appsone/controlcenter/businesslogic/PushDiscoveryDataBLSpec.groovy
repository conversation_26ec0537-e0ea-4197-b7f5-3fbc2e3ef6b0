package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.common.beans.discovery.Host
import com.appnomic.appsone.controlcenter.beans.UserAccountBean
import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import spock.lang.Shared
import spock.lang.Specification

class PushDiscoveryDataBLSpec extends Specification{
    PushDiscoveryDataBL bl = new PushDiscoveryDataBL()

    @Shared
    AccountBean account = new AccountBean()
    @Shared
    UserAccountBean accountBean = new UserAccountBean()
    String AUTH_TOKEN = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        account.id = 2
        account.identifier = "d681ef13-d690-4917-jkhg-6c79b-1"
        account.name= "INDIA"
        account.userIdDetails = "7640123a-fbde-4fe5-9812-581cd1e3a9c1"
        accountBean.setAccount(account)
        accountBean.setUserId("1")
        Thread.sleep(20000)
    }

    def cleanupSpec() {
        DBTestCache.rollback()
    }

    def "Client validation without exception"(){
        given:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-12")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", AUTH_TOKEN)
        requestObject.setHeaders(headers)

        requestObject.setBody("{\"hostIdentifier\":\"4D971B24CD8EBDD100F75E72F3660C48\",\"operatingSystem\":\"CentOS Linux\",\"operatingSystemVersion\":\"7\",\"hostname\":\"CentOS7\",\"platform\":\"amd64\",\"lastDiscoveryRunTime\":*************,\"discoveryStatus\":\"DISCOVERED_NOT_ADDED_TO_SYSTEM\",\"currentUser\":\"root\",\"endpoints\":[{\"endpointIdentifier\":\"018C1997F23D87DB7C75B70B53E380BD\",\"ipAddress\":\"127.0.0.1\",\"portNo\":8664},{\"endpointIdentifier\":\"A1A69FD607897BCE070F951D2F25C31D\",\"ipAddress\":\"0.0.0.0\",\"portNo\":22},{\"endpointIdentifier\":\"7A702AF9DC5BB9600111058F710ACD3E\",\"ipAddress\":\"**************\",\"portNo\":8082},{\"endpointIdentifier\":\"FF39C925380537AA5F3D13169F99C00A\",\"ipAddress\":\"**************\",\"portNo\":8445},{\"endpointIdentifier\":\"********************************\",\"ipAddress\":\"127.0.0.1\",\"portNo\":25},{\"endpointIdentifier\":\"96081F84FCF931FF25A8608A8ACCA836\",\"ipAddress\":\"::ffff:127.0.0.1\",\"portNo\":8005},{\"endpointIdentifier\":\"5B528326C5A69344167FD035C253D3BE\",\"ipAddress\":\"0.0.0.0\",\"portNo\":80},{\"endpointIdentifier\":\"F18769D6C332F91F057248E387752D6D\",\"ipAddress\":\"::1\",\"portNo\":25},{\"endpointIdentifier\":\"D73E29E5DA9C2F15092D42EAFFBECA39\",\"ipAddress\":\"**************\",\"portNo\":9992},{\"endpointIdentifier\":\"71F92A0C2BC2A3100C62BE0B8B169D6A\",\"ipAddress\":\"::1\",\"portNo\":8009},{\"endpointIdentifier\":\"DF1A2BF107CF36E5A7909F5FF1EAF982\",\"ipAddress\":\"0.0.0.0\",\"portNo\":8080}],\"connections\":[{\"connectionIdentifier\":\"617033F32D38DD3C60FD998AB0930BDB\",\"connectionType\":\"CONN_TCP\",\"direction\":\"OUTGOING_CONNECTION\",\"localIP\":\"**************\",\"localPort\":22,\"remoteIP\":\"************\",\"remotePort\":61694},{\"connectionIdentifier\":\"5FD7388DD111F036608B1A1FCEB12FA1\",\"connectionType\":\"CONN_TCP\",\"direction\":\"OUTGOING_CONNECTION\",\"localIP\":\"**************\",\"localPort\":43038,\"remoteIP\":\"**************\",\"remotePort\":9998},{\"connectionIdentifier\":\"0A7BA05D6C82385A04E83B2A4B263DA4\",\"connectionType\":\"CONN_TCP\",\"direction\":\"OUTGOING_CONNECTION\",\"localIP\":\"**************\",\"localPort\":54908,\"remoteIP\":\"**************\",\"remotePort\":9998},{\"connectionIdentifier\":\"56AC5ADFAEA212527D09EB1696D70C64\",\"connectionType\":\"CONN_TCP\",\"direction\":\"OUTGOING_CONNECTION\",\"localIP\":\"**************\",\"localPort\":22,\"remoteIP\":\"************\",\"remotePort\":51276},{\"connectionIdentifier\":\"58247DFA2AA06666C01D1A223BC2011F\",\"connectionType\":\"CONN_TCP\",\"direction\":\"OUTGOING_CONNECTION\",\"localIP\":\"**************\",\"localPort\":54912,\"remoteIP\":\"**************\",\"remotePort\":9998},{\"connectionIdentifier\":\"12294E922266D3FE0837F25C502BC156\",\"connectionType\":\"CONN_TCP\",\"direction\":\"OUTGOING_CONNECTION\",\"localIP\":\"**************\",\"localPort\":54910,\"remoteIP\":\"**************\",\"remotePort\":9998},{\"connectionIdentifier\":\"A17D04DDF89BC91745B63CEB294A1195\",\"connectionType\":\"CONN_TCP\",\"direction\":\"OUTGOING_CONNECTION\",\"localIP\":\"**************\",\"localPort\":54914,\"remoteIP\":\"**************\",\"remotePort\":9998},{\"connectionIdentifier\":\"4C8EECE4C2CBC299524425E5804B9766\",\"connectionType\":\"CONN_TCP\",\"direction\":\"OUTGOING_CONNECTION\",\"localIP\":\"**************\",\"localPort\":22,\"remoteIP\":\"************\",\"remotePort\":54463},{\"connectionIdentifier\":\"CE7B12252EDC07A15AF2DACAB7A22B84\",\"connectionType\":\"CONN_TCP\",\"direction\":\"OUTGOING_CONNECTION\",\"localIP\":\"**************\",\"localPort\":41236,\"remoteIP\":\"**************\",\"remotePort\":1521},{\"connectionIdentifier\":\"93802B7BC47A1C4E60AE992BC3570652\",\"connectionType\":\"CONN_TCP\",\"direction\":\"OUTGOING_CONNECTION\",\"localIP\":\"**************\",\"localPort\":41224,\"remoteIP\":\"**************\",\"remotePort\":1521},{\"connectionIdentifier\":\"EF153AF8C44676AFB9C19EFB66A11EEF\",\"connectionType\":\"CONN_TCP\",\"direction\":\"OUTGOING_CONNECTION\",\"localIP\":\"**************\",\"localPort\":41222,\"remoteIP\":\"**************\",\"remotePort\":1521},{\"connectionIdentifier\":\"0BEBDD693FC5D01231F3EB05CA81AED9\",\"connectionType\":\"CONN_TCP\",\"direction\":\"OUTGOING_CONNECTION\",\"localIP\":\"**************\",\"localPort\":41212,\"remoteIP\":\"**************\",\"remotePort\":1521},{\"connectionIdentifier\":\"0D5F703BB79F2FAEACAABBFDD54FE954\",\"connectionType\":\"CONN_TCP\",\"direction\":\"OUTGOING_CONNECTION\",\"localIP\":\"**************\",\"localPort\":41234,\"remoteIP\":\"**************\",\"remotePort\":1521},{\"connectionIdentifier\":\"F7359488A1DE72D767FA639EC6047020\",\"connectionType\":\"CONN_TCP\",\"direction\":\"OUTGOING_CONNECTION\",\"localIP\":\"**************\",\"localPort\":41230,\"remoteIP\":\"**************\",\"remotePort\":1521},{\"connectionIdentifier\":\"334BF6D09BA2BD8EE23D1B9AA57A17C9\",\"connectionType\":\"CONN_TCP\",\"direction\":\"OUTGOING_CONNECTION\",\"localIP\":\"**************\",\"localPort\":41214,\"remoteIP\":\"**************\",\"remotePort\":1521},{\"connectionIdentifier\":\"15CE0B8FC9350251835F503B194416B5\",\"connectionType\":\"CONN_TCP\",\"direction\":\"OUTGOING_CONNECTION\",\"localIP\":\"**************\",\"localPort\":41216,\"remoteIP\":\"**************\",\"remotePort\":1521},{\"connectionIdentifier\":\"C3A4E44B8FAB8655D5B5712B1960D095\",\"connectionType\":\"CONN_TCP\",\"direction\":\"OUTGOING_CONNECTION\",\"localIP\":\"**************\",\"localPort\":41242,\"remoteIP\":\"**************\",\"remotePort\":1521},{\"connectionIdentifier\":\"6ECB9A272494B24CF61FDCA66AD1D5BE\",\"connectionType\":\"CONN_TCP\",\"direction\":\"OUTGOING_CONNECTION\",\"localIP\":\"**************\",\"localPort\":45296,\"remoteIP\":\"**************\",\"remotePort\":11000},{\"connectionIdentifier\":\"38E3283F01693C65792C005F38FB2433\",\"connectionType\":\"CONN_TCP\",\"direction\":\"OUTGOING_CONNECTION\",\"localIP\":\"**************\",\"localPort\":41208,\"remoteIP\":\"**************\",\"remotePort\":1521}],\"runningProcesses\":[{\"processIdentifier\":\"B9058BC307DEB52E49C322948958BAF3\",\"processName\":\"/usr/lib/systemd/systemd\",\"processArgs\":\"/usr/lib/systemd/systemd --switched-root --system --deserialize 22 \",\"pid\":\"1\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"8D41E6E0084B58B0C374D69DE6377C88\",\"processName\":\"/usr/lib/systemd/systemd-journald\",\"processArgs\":\"/usr/lib/systemd/systemd-journald \",\"pid\":\"498\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"435E1C3A424E28B5B0866EB6A9F13E87\",\"processName\":\"/usr/sbin/lvmetad\",\"processArgs\":\"/usr/sbin/lvmetad -f \",\"pid\":\"524\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"AA77237C267C9165681082AF56583538\",\"processName\":\"/usr/lib/systemd/systemd-udevd\",\"processArgs\":\"/usr/lib/systemd/systemd-udevd \",\"pid\":\"530\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"7950BEB018CD6E57D7CEE3B0E98E6713\",\"processName\":\"/usr/sbin/auditd\",\"processArgs\":\"/sbin/auditd \",\"pid\":\"649\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"D5DD74247C3F2CBAAE518A00235D5783\",\"processName\":\"/usr/sbin/irqbalance\",\"processArgs\":\"/usr/sbin/irqbalance --foreground \",\"pid\":\"671\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"210904D248A5142DB6F784E34C4D4558\",\"processName\":\"/usr/bin/VGAuthService\",\"processArgs\":\"/usr/bin/VGAuthService -s \",\"pid\":\"673\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"C311A17FCB182470E5AD2F71E5854800\",\"processName\":\"/usr/bin/vmtoolsd\",\"processArgs\":\"/usr/bin/vmtoolsd \",\"pid\":\"674\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"8A59443CA90EDA40DFB9C128ABA87DAF\",\"processName\":\"/usr/lib/systemd/systemd-logind\",\"processArgs\":\"/usr/lib/systemd/systemd-logind \",\"pid\":\"675\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"5A1CAC595B635CD38AA2E81BEBE45E3D\",\"processName\":\"/usr/lib/polkit-1/polkitd\",\"processArgs\":\"/usr/lib/polkit-1/polkitd --no-debug \",\"pid\":\"677\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"A8818A7962EDA40A38633D83688FD582\",\"processName\":\"/usr/bin/dbus-daemon\",\"processArgs\":\"/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation \",\"pid\":\"678\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"55682D012C21C659D3B6DACBFFC4A981\",\"processName\":\"/usr/sbin/crond\",\"processArgs\":\"/usr/sbin/crond -n \",\"pid\":\"686\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"AC93BB2C1BEEA7D79A0F09C305D1DAF5\",\"processName\":\"/usr/sbin/agetty\",\"processArgs\":\"/sbin/agetty --noclear tty1 linux \",\"pid\":\"694\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"B0851ABDD5926093BF29310FC77B3DB8\",\"processName\":\"/usr/bin/python2.7\",\"processArgs\":\"/usr/bin/python2 -Es /usr/sbin/firewalld --nofork --nopid \",\"pid\":\"713\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"F3EB914F7DFCF52614C76EB38ABD2277\",\"processName\":\"/usr/sbin/NetworkManager\",\"processArgs\":\"/usr/sbin/NetworkManager --no-daemon \",\"pid\":\"739\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"D4108BE24FF96B56165F718B8298E626\",\"processName\":\"/usr/sbin/sshd\",\"processArgs\":\"/usr/sbin/sshd -D \",\"pid\":\"1044\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"94F4A97D435F0A8042E166BB0B94C889\",\"processName\":\"/usr/bin/python2.7\",\"processArgs\":\"/usr/bin/python2 -Es /usr/sbin/tuned -l -P \",\"pid\":\"1045\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"51463A3FCE08A1A3C50F92D2B8D2726D\",\"processName\":\"/usr/sbin/rsyslogd\",\"processArgs\":\"/usr/sbin/rsyslogd -n \",\"pid\":\"1048\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"B5349A3A5730CAE6797DF1946FB477C2\",\"processName\":\"/usr/libexec/postfix/master\",\"processArgs\":\"/usr/libexec/postfix/master -w \",\"pid\":\"1303\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/var/spool/postfix\",\"attributes\":null},{\"processIdentifier\":\"374450B6BCC013945C727026D9E0FA40\",\"processName\":\"/usr/libexec/postfix/qmgr\",\"processArgs\":\"qmgr -l -t unix -u \",\"pid\":\"1324\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/var/spool/postfix\",\"attributes\":null},{\"processIdentifier\":\"4EC8BC517C47A7D467810C556BA62BD1\",\"processName\":\"/usr/bin/bash\",\"processArgs\":\"/bin/sh ./standalone.sh \",\"pid\":\"9043\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/opt/JBOSS/jboss-eap-7.1/bin\",\"attributes\":null},{\"processIdentifier\":\"AC0341A4D351F88A51F85598CAB5FCEC\",\"processName\":\"/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.292.b10-1.el7_9.x86_64/jre/bin/java\",\"processArgs\":\"java -D[Standalone] -server -verbose:gc -Xloggc:/opt/JBOSS/jboss-eap-7.1/standalone/log/gc.log -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=5 -XX:GCLogFileSize=3M -XX:-TraceClassUnloading -Xms1303m -Xmx1303m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true -Dorg.jboss.boot.log.file=/opt/JBOSS/jboss-eap-7.1/standalone/log/server.log -Dlogging.configuration=file:/opt/JBOSS/jboss-eap-7.1/standalone/configuration/logging.properties -jar /opt/JBOSS/jboss-eap-7.1/jboss-modules.jar -mp /opt/JBOSS/jboss-eap-7.1/modules org.jboss.as.standalone -Djboss.home.dir=/opt/JBOSS/jboss-eap-7.1 -Djboss.server.base.dir=/opt/JBOSS/jboss-eap-7.1/standalone \",\"pid\":\"9106\",\"componentId\":14,\"componentVersion\":\"7.1.0.\",\"componentTypeId\":3,\"processCurrentWorkingDirectory\":\"/opt/JBOSS/jboss-eap-7.1/bin\",\"attributes\":{\"MonitorPort\":\"8445\",\"HostAddress\":\"**************\"}},{\"processIdentifier\":\"0922E178376A1037302E3FA1E4247231\",\"processName\":\"/home/<USER>/jdk1.8.0_181/bin/java\",\"processArgs\":\"/home/<USER>/jdk1.8.0_181/bin/java -Djava.util.logging.config.file=/opt/apache-tomcat-8.5.55/conf/logging.properties -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager -javaagent:/home/<USER>/opentelemetry-javaagent-1.4.0-SNAPSHOT-all.jar -Dotel.traces.exporter=jaeger -Dotel.exporter.jaeger.endpoint=http://**************:14250 -Dotel.resource.attributes=service.name=tomcat1,agentUid=JIMAgent_233 -Djdk.tls.ephemeralDHKeySize=2048 -Djava.protocol.handler.pkgs=org.apache.catalina.webresources -Dorg.apache.catalina.security.SecurityListener.UMASK=0027 -Dignore.endorsed.dirs= -classpath /opt/apache-tomcat-8.5.55/bin/bootstrap.jar:/opt/apache-tomcat-8.5.55/bin/tomcat-juli.jar -Dcatalina.base=/opt/apache-tomcat-8.5.55 -Dcatalina.home=/opt/apache-tomcat-8.5.55 -Djava.io.tmpdir=/opt/apache-tomcat-8.5.55/temp org.apache.catalina.startup.Bootstrap start \",\"pid\":\"12199\",\"componentId\":13,\"componentVersion\":\"8.5.55\",\"componentTypeId\":3,\"processCurrentWorkingDirectory\":\"/opt/apache-tomcat-8.5.55/bin\",\"attributes\":{\"MonitorPort\":\"8080\",\"HostAddress\":\"**************\"}},{\"processIdentifier\":\"D35571D91B7E8B0994A2D12FDFD99FA8\",\"processName\":\"/usr/sbin/sshd\",\"processArgs\":\"sshd: root@pts/0     \",\"pid\":\"12849\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"344365034C9BCC09FC3E1E2E54AAB178\",\"processName\":\"/usr/bin/bash\",\"processArgs\":\"-bash \",\"pid\":\"12861\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/home/<USER>",\"attributes\":null},{\"processIdentifier\":\"0884E3748FF4900542029781B026138B\",\"processName\":\"/home/<USER>/jdk1.8.0_181/bin/java\",\"processArgs\":\"/home/<USER>/jdk1.8.0_181//bin/java -XX:+UseConcMarkSweepGC -XX:-HeapDumpOnOutOfMemoryError -Xms300M -Xmx500M -Djavax.net.ssl.trustStore=/home/<USER>/ComponentAgent/CA-Tomcat-233-physical/config/keyfiles/cacerts -Djavax.net.ssl.trustStorePassword=changeit -jar /home/<USER>/ComponentAgent/CA-Tomcat-233-physical/ComponentAgent-5.8.26-SNAPSHOT.jar \",\"pid\":\"13553\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/home/<USER>/ComponentAgent/ComponentAgent-5.8.26-1/ComponentAgent_5.8.26/selfheal\",\"attributes\":null},{\"processIdentifier\":\"B6C2AB077C18642254DCD5285B78CC69\",\"processName\":\"/usr/bin/bash\",\"processArgs\":\"bash /home/<USER>/ComponentAgent/ComponentAgent-5.8.26-1/ComponentAgent_5.8.26/producers/Check_URL_Status.sh \",\"pid\":\"14486\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/home/<USER>/ComponentAgent/ComponentAgent-5.8.26-1/ComponentAgent_5.8.26/selfheal\",\"attributes\":null},{\"processIdentifier\":\"A5182EC41A0B8821C32CC09F16F5A391\",\"processName\":\"/usr/bin/wget\",\"processArgs\":\"wget -t 1 --timeout=10 ALL_Check_URL_Content_Wget -q -O - \",\"pid\":\"14539\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/home/<USER>/ComponentAgent/ComponentAgent-5.8.26-1/ComponentAgent_5.8.26/selfheal\",\"attributes\":null},{\"processIdentifier\":\"0959B7EE063093D353D3B1EF2DCA1813\",\"processName\":\"/usr/bin/sleep\",\"processArgs\":\"sleep 30 \",\"pid\":\"14556\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/home/<USER>/Supervisor/Supervisor_Linux_2.1.0.12\",\"attributes\":null},{\"processIdentifier\":\"B476998FBFEA00F5533262E67B325E47\",\"processName\":\"/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.292.b10-1.el7_9.x86_64/jre/bin/java\",\"processArgs\":\"java -jar auto-discovery-agent-5.8.27-SNAPSHOT-jar-with-dependencies.jar -m offline -p . \",\"pid\":\"14559\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/home/<USER>",\"attributes\":null},{\"processIdentifier\":\"BEB5282F1B65938707840751B7EB8E44\",\"processName\":\"/usr/sbin/httpd\",\"processArgs\":\"/usr/sbin/httpd -DFOREGROUND \",\"pid\":\"21185\",\"componentId\":7,\"componentVersion\":\"2.4.6\",\"componentTypeId\":2,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":{\"MonitorPort\":\"80\",\"HostAddress\":\"**************\"}},{\"processIdentifier\":\"365AA9646215087E3EA35C10C3053FE3\",\"processName\":\"/usr/sbin/sshd\",\"processArgs\":\"sshd: root@notty     \",\"pid\":\"22304\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"B85E4A8A3E2EAA29BF78378CFEF5013F\",\"processName\":\"/usr/libexec/openssh/sftp-server\",\"processArgs\":\"/usr/libexec/openssh/sftp-server \",\"pid\":\"22323\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/root\",\"attributes\":null},{\"processIdentifier\":\"E2BA880D00BE6312A466C8B8419FBB82\",\"processName\":\"/usr/libexec/postfix/pickup\",\"processArgs\":\"pickup -l -t unix -u \",\"pid\":\"26604\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/var/spool/postfix\",\"attributes\":null},{\"processIdentifier\":\"CD7D94B09B2AC2947D329986FB677F02\",\"processName\":\"/usr/sbin/sshd\",\"processArgs\":\"sshd: root@notty     \",\"pid\":\"28013\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"9C1F5652BD475BAAECB1B83FA2A8F85F\",\"processName\":\"/usr/libexec/openssh/sftp-server\",\"processArgs\":\"/usr/libexec/openssh/sftp-server \",\"pid\":\"28019\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/root\",\"attributes\":null},{\"processIdentifier\":\"202594738A1F173B44EC9545709027D8\",\"processName\":\"/usr/sbin/httpd\",\"processArgs\":\"/usr/sbin/httpd -DFOREGROUND \",\"pid\":\"30013\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"1F5365ECE47F276B7A0EB0B194A68ADC\",\"processName\":\"/usr/sbin/httpd\",\"processArgs\":\"/usr/sbin/httpd -DFOREGROUND \",\"pid\":\"30014\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"F2ABF8A385723AA54A58C0AF409BF5DA\",\"processName\":\"/usr/sbin/httpd\",\"processArgs\":\"/usr/sbin/httpd -DFOREGROUND \",\"pid\":\"30015\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"08761C949BA5DAC041416A6180A30B35\",\"processName\":\"/usr/sbin/httpd\",\"processArgs\":\"/usr/sbin/httpd -DFOREGROUND \",\"pid\":\"30016\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"B144D931B489FD85F2BCC16D602F6E26\",\"processName\":\"/usr/sbin/httpd\",\"processArgs\":\"/usr/sbin/httpd -DFOREGROUND \",\"pid\":\"30017\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/\",\"attributes\":null},{\"processIdentifier\":\"9A052AA11A711978682D6390C040A3F4\",\"processName\":\"/home/<USER>/Supervisor/Supervisor_Linux_2.1.0.12/Supervisor\",\"processArgs\":\"/home/<USER>/Supervisor/supervisor/Supervisor /home/<USER>/Supervisor/supervisor/config/config.json /home/<USER>/Supervisor/supervisor/schemas/config.schema.json \",\"pid\":\"31977\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/home/<USER>/Supervisor/Supervisor_Linux_2.1.0.12\",\"attributes\":null},{\"processIdentifier\":\"28FC9011A03EFA5539264E2B3B1A63A8\",\"processName\":\"/usr/bin/bash\",\"processArgs\":\"/bin/sh /home/<USER>/Supervisor/supervisor/supervisor_monitor.sh \",\"pid\":\"32010\",\"componentId\":0,\"componentVersion\":null,\"componentTypeId\":0,\"processCurrentWorkingDirectory\":\"/home/<USER>/Supervisor/Supervisor_Linux_2.1.0.12\",\"attributes\":null}],\"networkInterfaces\":[{\"networkInterfaceIdentifier\":\"A8089FA70C04837535087D37010A09E0\",\"interfaceName\":\"ens160\",\"interfaceType\":\"Ethernet\",\"interfaceIP\":\"**************\",\"hardwareAddress\":\"00:50:56:92:E8:B3\"}],\"mountPoints\":[{\"mountPointIdentifier\":\"0136F4F57B9D49364AA57FDB196494D8\",\"dirName\":\"/sys\",\"devName\":\"sysfs\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"0958C7242F87DE54495AAA985D2280EB\",\"dirName\":\"/proc\",\"devName\":\"proc\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"E6895D2504D342F01DD177CDD6385345\",\"dirName\":\"/dev\",\"devName\":\"devtmpfs\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"6171311464B5FCFC2E9D5D59D46D5AB3\",\"dirName\":\"/sys/kernel/security\",\"devName\":\"securityfs\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"9F77BF76D05A92F4D47E9422C375083D\",\"dirName\":\"/dev/shm\",\"devName\":\"tmpfs\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"B0F1ECA337516C4206CA7E6D2939FE0D\",\"dirName\":\"/dev/pts\",\"devName\":\"devpts\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"E4485D93F7C7AFDD95F4FD95EF3E160C\",\"dirName\":\"/run\",\"devName\":\"tmpfs\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"9B3E264B6B19CB017B9866FEFB376604\",\"dirName\":\"/sys/fs/cgroup\",\"devName\":\"tmpfs\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"EA1D4E916E6CAB12D922F86B1137C237\",\"dirName\":\"/sys/fs/cgroup/systemd\",\"devName\":\"cgroup\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"C5C3C82D7C3CBEE02EAFA0E198B8AF07\",\"dirName\":\"/sys/fs/pstore\",\"devName\":\"pstore\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"A11BDB540BF6343A0A8E9D8C49CF97F1\",\"dirName\":\"/sys/fs/cgroup/net_cls,net_prio\",\"devName\":\"cgroup\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"462DBEA2A1F9677ADA0FD12A3F7E362C\",\"dirName\":\"/sys/fs/cgroup/freezer\",\"devName\":\"cgroup\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"4E41F840350B8CFABFA159B5C41A2E3C\",\"dirName\":\"/sys/fs/cgroup/cpuset\",\"devName\":\"cgroup\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"EF1A7906E10AC20C95782EBF5BE37877\",\"dirName\":\"/sys/fs/cgroup/perf_event\",\"devName\":\"cgroup\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"9DAC1AF8732067F4F784426C70089A60\",\"dirName\":\"/sys/fs/cgroup/hugetlb\",\"devName\":\"cgroup\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"BBCB3730B1089477A075F64D63824B0D\",\"dirName\":\"/sys/fs/cgroup/blkio\",\"devName\":\"cgroup\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"6F17EFA68905E502D02A4205247EA37C\",\"dirName\":\"/sys/fs/cgroup/pids\",\"devName\":\"cgroup\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"548E299929E47B86F1D19B083145AD38\",\"dirName\":\"/sys/fs/cgroup/memory\",\"devName\":\"cgroup\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"8CDA4C6677F3AA7D5BBE88ED5DCB27EB\",\"dirName\":\"/sys/fs/cgroup/cpu,cpuacct\",\"devName\":\"cgroup\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"E7BBF55BA199C948456B4C925E218819\",\"dirName\":\"/sys/fs/cgroup/devices\",\"devName\":\"cgroup\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"602B364FFADDA5364E2C8DD773AF39BE\",\"dirName\":\"/sys/kernel/config\",\"devName\":\"configfs\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"C105533608039E087E8BB7E2E3363734\",\"dirName\":\"/\",\"devName\":\"/dev/mapper/centos-root\",\"typeName\":\"local\"},{\"mountPointIdentifier\":\"551115534534D664E7902AB9FC1B2CD0\",\"dirName\":\"/sys/fs/selinux\",\"devName\":\"selinuxfs\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"01408B60D51DEA023694D0C19091BD8A\",\"dirName\":\"/proc/sys/fs/binfmt_misc\",\"devName\":\"systemd-1\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"B4FF3A9AFC77CF24E24A7BFECD8C6E82\",\"dirName\":\"/sys/kernel/debug\",\"devName\":\"debugfs\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"DAED1F08C5015E497BF9AC7775E61F42\",\"dirName\":\"/sys/fs/fuse/connections\",\"devName\":\"fusectl\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"750F4C43F5046E2476B4ECFF4BA651F3\",\"dirName\":\"/dev/hugepages\",\"devName\":\"hugetlbfs\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"116CA66802966AF1986FD9AF2D46EB63\",\"dirName\":\"/dev/mqueue\",\"devName\":\"mqueue\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"D50FD9FEACA9DEA10CD5A8B94117529E\",\"dirName\":\"/boot\",\"devName\":\"/dev/sda1\",\"typeName\":\"local\"},{\"mountPointIdentifier\":\"FA95832C5F3C4A5A091F5CBDFF16FD08\",\"dirName\":\"/proc/sys/fs/binfmt_misc\",\"devName\":\"binfmt_misc\",\"typeName\":\"none\"},{\"mountPointIdentifier\":\"4666164F81B085DDB79C8A571355607D\",\"dirName\":\"/run/user/0\",\"devName\":\"tmpfs\",\"typeName\":\"none\"}],\"discoveryErrors\":[{\"errorMessage\":\"org.hyperic.sigar.SigarFileNotFoundException-No such file or directory\",\"stackTrace\":\"org.hyperic.sigar.ProcExe.gather(Native Method)\\norg.hyperic.sigar.ProcExe.fetch(ProcExe.java:30)\\norg.hyperic.sigar.Sigar.getProcExe(Sigar.java:544)\\ncom.appnomic.appsone.disco.ProcessDiscovery.discover(ProcessDiscovery.java:52)\\ncom.appnomic.appsone.disco.HealAutoDiscovery.runDiscovery(HealAutoDiscovery.java:165)\\ncom.appnomic.appsone.Main.main(Main.java:29)\\n\",\"errorCount\":79,\"variables\":[\"2\",\"4\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\",\"13\",\"14\",\"16\",\"18\",\"19\",\"20\",\"21\",\"22\",\"23\",\"24\",\"25\",\"26\",\"27\",\"28\",\"29\",\"35\",\"36\",\"37\",\"38\",\"46\",\"48\",\"49\",\"51\",\"53\",\"66\",\"102\",\"284\",\"285\",\"286\",\"295\",\"296\",\"298\",\"299\",\"300\",\"301\",\"303\",\"305\",\"381\",\"391\",\"392\",\"407\",\"408\",\"409\",\"410\",\"411\",\"412\",\"413\",\"414\",\"415\",\"416\",\"417\",\"418\",\"609\",\"620\",\"621\",\"622\",\"623\",\"624\",\"625\",\"626\",\"627\",\"2393\",\"7066\",\"7228\",\"10195\",\"11372\",\"17002\",\"25197\",\"30048\",\"32422\"]}],\"hostAttributes\":{\"HostUsername\":\"root\",\"SshPort\":\"22\",\"HostAddress\":\"**************\"}}")

        when:

        UtilityBean<Host> utilityBean = bl.clientValidation(requestObject)

        then:
        noExceptionThrown()
        utilityBean.getPojoObject().getHostIdentifier() == "4D971B24CD8EBDD100F75E72F3660C48"
    }

    def "Client validation no exception"(){
        given:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-12")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", AUTH_TOKEN)
        requestObject.setHeaders(headers)

        when:

        UtilityBean<Host> utilityBean = bl.clientValidation(null)

        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : "+ UIMessages.REQUEST_NULL
    }

    def "Client validation exception :  Request NULL"(){
        given:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-12")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", AUTH_TOKEN)
        requestObject.setHeaders(headers)

        when:
        UtilityBean<Host> utilityBean = bl.clientValidation(null)

        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : "+ UIMessages.REQUEST_NULL
    }

    def "Client validation exception :  Request empty"(){
        given:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-12")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", AUTH_TOKEN)
        requestObject.setHeaders(headers)
        requestObject.setBody("")

        when:
        UtilityBean<Host> utilityBean = bl.clientValidation(requestObject)

        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Invalid request body. Reason: Request body is either NULL or empty."
    }

    def "Client validation exception : JSON PARSER"(){
        given:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-12")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", AUTH_TOKEN)
        requestObject.setHeaders(headers)
        requestObject.setBody("[]")

        when:
        UtilityBean<Host> utilityBean = bl.clientValidation(requestObject)

        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : "+Constants.JSON_PARSE_ERROR
    }
}
