package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.exceptions.ServerException
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.OptInRequestPojo
import com.appnomic.appsone.controlcenter.pojo.OptInWebhookRequestPojo
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import spock.lang.Shared
import spock.lang.Specification

class WhatsappOptInWebhookBLSpec extends Specification{
    @Shared
    String oldH2URL

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)

        oldH2URL = MySQLConnectionManager.INSTANCE.getH2URL()

        String H2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/whatsappOptIn/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/whatsappOptIn/populate.sql'"

        MySQLConnectionManager.INSTANCE.setH2URL(H2URL)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2URL()
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        Thread.sleep(5000)
    }

    def cleanupSpec() {
        DBTestCache.rollback()
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        MySQLConnectionManager.INSTANCE.getHandle()
    }


    def "client validation : noException"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier","d681ef13-d690-4917-jkhg-6c79b-12")

        requestObject.setBody("{\"messages\":[{\"from\":\"1234567890\",\"id\":\"ABGGhSkIc2B_Ago-sDy5BNm-1gI5\",\"text\":{\"body\":\"Yes\"},\"timestamp\":\"1529381066\",\"type\":\"text\"}]}")
        requestObject.setParams(params)

        when:
        WhatsappOptInWebhookBL bl = new WhatsappOptInWebhookBL()
        bl.clientValidation(requestObject)

        then:
        noExceptionThrown()
    }

    def "client validation : RequestBodyException"(){
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier","d681ef13-d690-4917-jkhg-6c79b-12")

        requestObject.setBody("")
        requestObject.setParams(params)

        when:
        WhatsappOptInWebhookBL bl = new WhatsappOptInWebhookBL()
        bl.clientValidation(requestObject)

        then:
        thrown(ClientException)
    }

    def "Server validation : from"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-12")

        requestObject.setBody("{\"messages\":[{\"from\":\"\",\"id\":\"ABGGhSkIc2B_Ago-sDy5BNm-1gI5\",\"text\":{\"body\":\"Yes\"},\"timestamp\":\"1529381066\",\"type\":\"text\"}]}")
        requestObject.setParams(params)

        WhatsappOptInWebhookBL bl = new WhatsappOptInWebhookBL()
        UtilityBean<OptInWebhookRequestPojo> clientValidation = bl.clientValidation(requestObject)

        when:

        bl.serverValidation(clientValidation)

        then:
        thrown(ServerException)
    }

    def "Server validation : noException"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-12")

        requestObject.setBody("{\"messages\":[{\"from\":\"919999999999\",\"id\":\"ABGGhSkIc2B_Ago-sDy5BNm-1gI5\",\"text\":{\"body\":\"Yes\"},\"timestamp\":\"1529381066\",\"type\":\"text\"}]}")
        requestObject.setParams(params)

        WhatsappOptInWebhookBL bl = new WhatsappOptInWebhookBL()
        UtilityBean<OptInWebhookRequestPojo> clientValidation = bl.clientValidation(requestObject)

        when:

        bl.serverValidation(clientValidation)

        then:
        noExceptionThrown()
    }

    def "Process : noException"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-12")

        requestObject.setBody("{\"messages\":[{\"from\":\"919999999999\",\"id\":\"ABGGhSkIc2B_Ago-sDy5BNm-1gI5\",\"text\":{\"body\":\"Yes\"},\"timestamp\":\"1529381066\",\"type\":\"text\"}]}")
        requestObject.setParams(params)

        WhatsappOptInWebhookBL bl = new WhatsappOptInWebhookBL()
        UtilityBean<OptInWebhookRequestPojo> clientValidation = bl.clientValidation(requestObject)
        UtilityBean<OptInRequestPojo> serverValidation = bl.serverValidation(clientValidation)


        when:

        bl.process(serverValidation)

        then:
        noExceptionThrown()
    }
}
