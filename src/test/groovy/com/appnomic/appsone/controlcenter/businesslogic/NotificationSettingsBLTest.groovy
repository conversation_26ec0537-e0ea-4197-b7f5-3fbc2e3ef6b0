package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UserAccountBean
import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.dao.mysql.NotificationSettingsDataService
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean
import com.appnomic.appsone.controlcenter.dao.mysql.entity.NotificationSettings
import com.appnomic.appsone.controlcenter.exceptions.RequestException
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.NotificationSettingsPojo
import spark.Request
import spock.lang.Shared
import spock.lang.Specification

class NotificationSettingsBLTest extends Specification {
    Request request = Spy(Request.class)


    @Shared
    String oldH2URL

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)

        oldH2URL = MySQLConnectionManager.INSTANCE.getH2URL()

        String H2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/NotificationSettingsConfiguration/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/NotificationSettingsConfiguration/populate.sql'"


        MySQLConnectionManager.INSTANCE.setH2URL(H2URL)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2URL()
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        Thread.sleep(5000)
    }

    def cleanupSpec() {
        DBTestCache.rollback()
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        MySQLConnectionManager.INSTANCE.getHandle()
    }

    def "addDefaultNotificationSettings success"() {
        given:
        int accountId = 2
        String userId = "7640123a-fbde-4fe5-9812-581cd1e3a9c1"
        when:
        def ids = new NotificationSettingsBL().addDefaultNotificationSettings(accountId, userId)
        then:
        noExceptionThrown()
        then:
        ids.size() == 2
    }

    def "get notification settings success"() {
        given:
        int accountId = 2
        when:
        NotificationSettingsDataService.getNotificationSetting(accountId)
        then:
        noExceptionThrown()
    }

    def "Client validation Success"() {
        given:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-12"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> "[" +
                "{" +
                "\"typeId\":292,\"typeName\":\"Open for long\",\"durationInMin\":300" +
                "}," +
                "{" +
                "\"typeId\":293,\"typeName\":\"Open for too long\",\"durationInMin\":300" +
                "}" +
                "]"
        when:
        NotificationSettingsBL.addClientValidations(request)
        then:
        noExceptionThrown()
    }

    def "Client validation failure"() {
        given:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-12"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> null
        when:
        NotificationSettingsBL.addClientValidations(request)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "Request object is null or empty."
    }

    def "Client validation invalid json"() {
        given:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-12"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> "[" +
                "{" +
                "\"typeId\":292,\"typeName\":\"Open for long\",\"durationInMin\":300" +
                "}," +
                "{" +
                "\"typeId\":293,\"typeNames\":\"Open for too long\",\"durationInMin\":300" +
                "}" +
                "]"
        when:
        NotificationSettingsBL.addClientValidations(request)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "{typeName=typeName is empty}"
    }

    def "Client validation null values"() {
        given:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-12"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> "[" +
                "{" +
                "\"typeId\": null,\"typeName\": null ,\"durationInMin\":0" +
                "}" +
                "]"
        when:
        NotificationSettingsBL.addClientValidations(request)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "{durationInMin=durationInMin is empty, typeName=typeName is empty, typeId=typeId is empty}"
    }

    def "Client validation duplicate objects"() {
        given:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-12"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> "[" +
                "{" +
                "\"typeId\":292,\"typeName\":\"Open for long\",\"durationInMin\":300" +
                "}," +
                "{" +
                "\"typeId\":292,\"typeName\":\"Open for long\",\"durationInMin\":300" +
                "}" +
                "]"
        when:
        NotificationSettingsBL.addClientValidations(request)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "Duplicate Notification Setting"
    }

    def "Client validation empty json"() {
        given:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-12"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> "[]"
        when:
        NotificationSettingsBL.addClientValidations(request)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "Request object is null or empty."
    }

    def "Validate data method Success"() {
        given:
        def notificationSettingsList = new ArrayList<NotificationSettingsPojo>()
        def settingForLong = new NotificationSettingsPojo()
        def settingForTooLong = new NotificationSettingsPojo()
        settingForLong.setDurationInMin(300)
        settingForLong.setTypeId(292)
        settingForLong.setTypeName("Open for long")
        settingForTooLong.setDurationInMin(300)
        settingForTooLong.setTypeId(293)
        settingForTooLong.setTypeName("Open for too long")
        notificationSettingsList.add(settingForLong)
        notificationSettingsList.add(settingForTooLong)
        when:
        NotificationSettingsBL.validateData(notificationSettingsList)
        then:
        noExceptionThrown()
    }

    def "Validate data method failure due to typeName and type Id combination is incorrect"() {
        given:
        def notificationSettingsList = new ArrayList<NotificationSettingsPojo>()
        def settingForLong = new NotificationSettingsPojo()
        def settingForTooLong = new NotificationSettingsPojo()
        settingForLong.setDurationInMin(300)
        settingForLong.setTypeId(292)
        settingForLong.setTypeName("Open for longer")
        settingForTooLong.setDurationInMin(300)
        settingForTooLong.setTypeId(293)
        settingForTooLong.setTypeName("Open for too long")
        notificationSettingsList.add(settingForLong)
        notificationSettingsList.add(settingForTooLong)
        when:
        NotificationSettingsBL.validateData(notificationSettingsList)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "{Type name/id combination=Invalid Combination of Type Id and Type Name, Type name=Invalid Type Name}"
    }

    def "Validate data method failure due to typeName  is incorrect"() {
        given:
        def notificationSettingsList = new ArrayList<NotificationSettingsPojo>()
        def settingForLong = new NotificationSettingsPojo()
        def settingForTooLong = new NotificationSettingsPojo()
        settingForLong.setDurationInMin(300)
        settingForLong.setTypeId(292)
        settingForLong.setTypeName("Open for longer")
        settingForTooLong.setDurationInMin(300)
        settingForTooLong.setTypeId(293)
        settingForTooLong.setTypeName("Open for too longer")
        notificationSettingsList.add(settingForLong)
        notificationSettingsList.add(settingForTooLong)
        when:
        NotificationSettingsBL.validateData(notificationSettingsList)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "{Type name/id combination=Invalid Combination of Type Id and Type Name, Type name=Invalid Type Name}"
    }

    def "Validate data method failure due to duration is 0"() {
        given:
        def notificationSettingsList = new ArrayList<NotificationSettingsPojo>()
        def settingForLong = new NotificationSettingsPojo()
        def settingForTooLong = new NotificationSettingsPojo()
        settingForLong.setDurationInMin(300)
        settingForLong.setTypeId(292)
        settingForLong.setTypeName("Open for long")
        settingForTooLong.setDurationInMin(00)
        settingForTooLong.setTypeId(293)
        settingForTooLong.setTypeName("Open for too long")
        notificationSettingsList.add(settingForLong)
        notificationSettingsList.add(settingForTooLong)
        when:
        NotificationSettingsBL.validateData(notificationSettingsList)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "{Duration In Minutes=Invalid Duration}"
    }

    def "Server validation failure due to absent account in DB"() {
        given:
        def notificationSettingsList = new ArrayList<NotificationSettingsPojo>()
        def settingForLong = new NotificationSettingsPojo()
        def settingForTooLong = new NotificationSettingsPojo()
        def userAccBean = new UserAccountBean()
        def account = new AccountBean()
        settingForLong.setDurationInMin(300)
        settingForLong.setTypeId(292)
        settingForLong.setTypeName("Open for long")
        settingForTooLong.setDurationInMin(300)
        settingForTooLong.setTypeId(293)
        settingForTooLong.setTypeName("Open for too long")
        notificationSettingsList.add(settingForLong)
        notificationSettingsList.add(settingForTooLong)
        account.setId(3)
        userAccBean.setAccount(account)
        userAccBean.setUserId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
        when:
        NotificationSettingsBL.addServerValidations(notificationSettingsList, userAccBean)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "Notification Settings not found for the account"
    }

    def "Server validation success"() {
        given:
        def notificationSettingsList = new ArrayList<NotificationSettingsPojo>()
        def settingForLong = new NotificationSettingsPojo()
        def settingForTooLong = new NotificationSettingsPojo()
        def userAccBean = new UserAccountBean()
        def account = new AccountBean()
        settingForLong.setDurationInMin(300)
        settingForLong.setTypeId(292)
        settingForLong.setTypeName("Open for long")
        settingForTooLong.setDurationInMin(300)
        settingForTooLong.setTypeId(293)
        settingForTooLong.setTypeName("Open for too long")
        notificationSettingsList.add(settingForLong)
        notificationSettingsList.add(settingForTooLong)
        account.setId(1)
        userAccBean.setAccount(account)
        userAccBean.setUserId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
        when:
        NotificationSettingsBL.addServerValidations(notificationSettingsList, userAccBean)
        then:
        noExceptionThrown()
    }
    def "Server validation: Open for long is not present in the POJO"() {
        given:
        def notificationSettingsList = new ArrayList<NotificationSettingsPojo>()
        def settingForLong = new NotificationSettingsPojo()
        def settingForTooLong = new NotificationSettingsPojo()
        def userAccBean = new UserAccountBean()
        def account = new AccountBean()
        settingForLong.setTypeId(292)
        settingForLong.setTypeName("Open for long")
        settingForTooLong.setDurationInMin(301)
        settingForTooLong.setTypeId(293)
        settingForTooLong.setTypeName("Open for too long")
        notificationSettingsList.add(settingForLong)
        notificationSettingsList.add(settingForTooLong)
        account.setId(1)
        userAccBean.setAccount(account)
        userAccBean.setUserId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
        when:
        List<NotificationSettings> res = NotificationSettingsBL.addServerValidations(notificationSettingsList, userAccBean)
        then:
        res.get(1).getDurationInMin() % res.get(0).getDurationInMin() == 0
        noExceptionThrown()
    }
    def "Server validation: Open for long is not multiple of open for too long"() {
        given:
        def notificationSettingsList = new ArrayList<NotificationSettingsPojo>()
        def settingForLong = new NotificationSettingsPojo()
        def settingForTooLong = new NotificationSettingsPojo()
        def userAccBean = new UserAccountBean()
        def account = new AccountBean()
        settingForLong.setDurationInMin(30)
        settingForLong.setTypeId(292)
        settingForLong.setTypeName("Open for long")
        settingForTooLong.setDurationInMin(301)
        settingForTooLong.setTypeId(293)
        settingForTooLong.setTypeName("Open for too long")
        notificationSettingsList.add(settingForLong)
        notificationSettingsList.add(settingForTooLong)
        account.setId(1)
        userAccBean.setAccount(account)
        userAccBean.setUserId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
        when:
        List<NotificationSettings> res = NotificationSettingsBL.addServerValidations(notificationSettingsList, userAccBean)
        then:
        res.get(1).getDurationInMin() % res.get(0).getDurationInMin() == 0
        noExceptionThrown()
    }

}
