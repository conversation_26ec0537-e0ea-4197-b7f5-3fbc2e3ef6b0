package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.common.beans.discovery.Host
import com.appnomic.appsone.controlcenter.beans.UserAccountBean
import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import spock.lang.Shared
import spock.lang.Specification

class AddToSystemBLSpec extends Specification {
    AddToSystemBL bl = new AddToSystemBL()

    @Shared
    AccountBean account = new AccountBean()
    @Shared
    UserAccountBean accountBean = new UserAccountBean()
    String AUTH_TOKEN = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
    String userDetailId = "7640123a-fbde-4fe5-9812-581cd1e3a9c1"

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        account.id = 2
        account.identifier = "d681ef13-d690-4917-jkhg-6c79b-1"
        account.name = "INDIA"
        account.userIdDetails = "7640123a-fbde-4fe5-9812-581cd1e3a9c1"
        accountBean.setAccount(account)
        accountBean.setUserId("1")
        Thread.sleep(20000)
    }

    def cleanupSpec() {
        DBTestCache.rollback()
    }

    def "Client validation requestObject null"() {
        when:

        UtilityBean<Host> utilityBean = bl.clientValidation(null)

        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : " + UIMessages.REQUEST_NULL;
    }

    def "Client validation auth token null"() {
        given:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-12")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", null)
        requestObject.setHeaders(headers)

        requestObject.setBody("[\"0C80EDC33C53ED469F6806B18540FD04\"]")

        when:

        UtilityBean<Host> utilityBean = bl.clientValidation(requestObject)

        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : " + UIMessages.AUTH_KEY_INVALID;
    }

    def "Client validation identifier null"() {
        given:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        requestObject.setParams(params)
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", AUTH_TOKEN)
        requestObject.setHeaders(headers)

        requestObject.setBody("[\"0C80EDC33C53ED469F6806B18540FD04\"]")

        when:

        UtilityBean<Host> utilityBean = bl.clientValidation(requestObject)

        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : " + UIMessages.ACCOUNT_NULL_OR_EMPTY;
    }

    def "Client validation JSON_PARSE_ERROR"() {
        given:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-12")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", AUTH_TOKEN)
        requestObject.setHeaders(headers)

        requestObject.setBody("[0C80EDC33C53ED469F6806B18540FD04]")

        when:

        UtilityBean<Host> utilityBean = bl.clientValidation(requestObject)

        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : " + Constants.JSON_PARSE_ERROR
    }

    def "Client validation without exception"() {
        given:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-12")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", AUTH_TOKEN)
        requestObject.setHeaders(headers)

        requestObject.setBody("[\"0C80EDC33C53ED469F6806B18540FD04\"]")

        when:

        UtilityBean<Host> utilityBean = bl.clientValidation(requestObject)

        then:
        noExceptionThrown()
        utilityBean.getPojoObject()[0] == "0C80EDC33C53ED469F6806B18540FD04"
    }

    def "Server validation without exception"() {
        setup:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", AUTH_TOKEN)
        requestObject.setHeaders(headers)

        requestObject.setBody("[\"0C80EDC33C53ED469F6806B18540FD04\"]")

        when:

        UtilityBean<Host> clientValidation = bl.clientValidation(requestObject)
        UtilityBean<List<String>> serverValidation = bl.serverValidation(clientValidation)

        then:
        noExceptionThrown()
        serverValidation.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
    }
}
