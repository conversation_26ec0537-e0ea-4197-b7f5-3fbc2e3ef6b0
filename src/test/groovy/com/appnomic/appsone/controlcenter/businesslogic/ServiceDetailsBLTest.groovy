package com.appnomic.appsone.controlcenter.businesslogic


import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import spock.lang.Shared
import spock.lang.Specification

class ServiceDetailsBLTest extends Specification {

    @Shared
    String oldH2URL

    def test_setup(String changableH2URL) {
        String H2URL = changableH2URL

        MySQLConnectionManager.INSTANCE.setH2URL(H2URL)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2URL()

        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        Thread.sleep(10000)
    }

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)

        oldH2URL = MySQLConnectionManager.INSTANCE.getH2URL()
    }

    def cleanupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        //the following line was added ONLY to ensure that the existing test cases are not impacted.
        MySQLConnectionManager.INSTANCE.getHandle()
    }

    /*def "getServiceProcessList_valid_agentStatus_false"() {
        given:
        String changableH2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/serviceDetailsUseCases/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/serviceDetailsUseCases/populate-valid.sql'"
        test_setup(changableH2URL)
        ServiceDetailsBL serviceDetailsBL = new ServiceDetailsBL()
        com.appnomic.appsone.controlcenter.pojo.Account account = ValidationUtils.validAndGetAccount("d681ef13-d690-4917-jkhg-6c79b-1")
        TagDetailsBean controllerTag = MasterCache.getTagDetails(Constants.CONTROLLER_TAG)

        when:
        List<ServiceListPage> output = serviceDetailsBL.getServiceProcessList(account, false, controllerTag,"ef9f7600-0905-4bcb-9504-f724cdfb6b9c")

        then:
        output.size() == 1
        output.get(0).name == "NB-Web-Service"
        output.get(0).identifier == "NB-Web-Service"
        output.get(0).id == 2
        output.get(0).status == "1"
        output.get(0).application.size() == 2
        output.get(0).application.get("count") == 1
        ((Set<String>) output.get(0).application.get("name")).toArray()[0] == "NetBanking"
        output.get(0).componentInstance.size() == 2
        output.get(0).componentInstance.get("count") == 1
        ((Set<String>) output.get(0).componentInstance.get("name")).toArray()[0] == "RHEL_NB_Web_Host_154_Inst_1"
        output.get(0).type == ""
        output.get(0).agentStatus == null
    }*/

    /*def "getServiceProcessList_Missing_tagMappingDetails_agentStatus_true"() {
        given:
        String changableH2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/serviceDetailsUseCases/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/serviceDetailsUseCases/populate-missing_tag_details.sql'"
        test_setup(changableH2URL)
        ServiceDetailsBL serviceDetailsBL = new ServiceDetailsBL()
        TagDetailsBean controllerTag = MasterCache.getTagDetails(Constants.CONTROLLER_TAG)
        com.appnomic.appsone.controlcenter.pojo.Account account = ValidationUtils.validAndGetAccount("d681ef13-d690-4917-jkhg-6c79b-1")

        when:
        List<ServiceListPage> output = serviceDetailsBL.getServiceProcessList(account, true, controllerTag,"ef9f7600-0905-4bcb-9504-f724cdfb6b9c")

        then:
        output.isEmpty()
    }*/

    /*def "getServiceProcessList_Missing_tagMappingDetails_agentStatus_false"() {
        given:
        String changableH2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/serviceDetailsUseCases/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/serviceDetailsUseCases/populate-missing_tag_details.sql'"
        test_setup(changableH2URL)
        ServiceDetailsBL serviceDetailsBL = new ServiceDetailsBL()
        com.appnomic.appsone.controlcenter.pojo.Account account = ValidationUtils.validAndGetAccount("d681ef13-d690-4917-jkhg-6c79b-1")
        TagDetailsBean controllerTag = MasterCache.getTagDetails(Constants.CONTROLLER_TAG)

        when:
        List<ServiceListPage> output = serviceDetailsBL.getServiceProcessList(account, false, controllerTag,"ef9f7600-0905-4bcb-9504-f724cdfb6b9c")

        then:
        output.isEmpty()
    }

    def "getServiceProcessList_Missing_tagMappingDetails_with_controllers_agentStatus_true"() {
        given:
        String changableH2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/serviceDetailsUseCases/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/serviceDetailsUseCases/populate-with-controllers.sql'"
        test_setup(changableH2URL)
        ServiceDetailsBL serviceDetailsBL = new ServiceDetailsBL()
        com.appnomic.appsone.controlcenter.pojo.Account account = ValidationUtils.validAndGetAccount("d681ef13-d690-4917-jkhg-6c79b-1")
        TagDetailsBean controllerTag = MasterCache.getTagDetails(Constants.CONTROLLER_TAG)

        when:
        List<ServiceListPage> output = serviceDetailsBL.getServiceProcessList(account, true, controllerTag,"ef9f7600-0905-4bcb-9504-f724cdfb6b9c")

        then:
        output.size() == 0
    }*/
}
