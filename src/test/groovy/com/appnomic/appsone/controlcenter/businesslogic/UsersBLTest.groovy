package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.AccessDetailsBean
import com.appnomic.appsone.controlcenter.beans.UserInfoBean
import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException
import com.appnomic.appsone.controlcenter.exceptions.RequestException
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.IdPojo
import com.appnomic.appsone.controlcenter.pojo.UserInfo
import com.appnomic.appsone.controlcenter.dao.mysql.UserDataService
import com.appnomic.appsone.controlcenter.util.CommonUtils
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import spock.lang.Shared
import spock.lang.Specification

class UsersBLTest extends Specification {

    private static final ObjectMapper OBJECT_MAPPER = CommonUtils.getObjectMapperWithHtmlEncoder()


    @Shared
    String oldH2URL
    UsersBL usersBL = new UsersBL();

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)

        oldH2URL = MySQLConnectionManager.INSTANCE.getH2URL()

        String H2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/userManagement/masterData.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/userManagement/testData.sql'"

        MySQLConnectionManager.INSTANCE.setH2URL(H2URL)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2URL()
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        Thread.sleep(5000)
    }

    def cleanupSpec() {
        DBTestCache.rollback()
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        MySQLConnectionManager.INSTANCE.getHandle()
    }

    def "ValidateRoleId"() {
        when:
        int id = usersBL.validateRoleId("3")

        then:
        noExceptionThrown()
        id == 3
    }

    def "ValidateRoleId invalid"() {
        when:
        usersBL.validateRoleId("abc")

        then:
        thrown(RequestException)

    }

    def "ValidateRoleId incorrect"() {
        when:
        usersBL.validateRoleId("37")

        then:
        thrown(RequestException)
    }

    def "GetProfilesForRoleId"() {
        when:
        List<IdPojo> list = usersBL.getProfilesForRoleId(3)

        then:
        list.size() == 1
        list.get(0).name == 'User Manager'

    }

    def "GetProfilesForRoleId invalid"() {
        when:
        usersBL.getProfilesForRoleId(31)

        then:
        thrown(ControlCenterException)

    }

    def "populateAccessDetails userManager"() {

        setup:
        UserInfoBean userInfoBean = new UserDataService().getUserDetails("usermanager")
        AccessDetailsBean bean = OBJECT_MAPPER.readValue(userInfoBean.getAccessDetailsJSON(),
                new TypeReference<AccessDetailsBean>() {
                })

        when:
        List<UserInfo.AccessDetails> accessDetails = usersBL.populateAccessDetails(bean)

        then:
        accessDetails != null
        accessDetails.get(0).accountId == "*"
        accessDetails.get(0).applications == null
    }

    def "populateAccessDetails appsoneAdmin"() {

        setup:
        UserInfoBean userInfoBean = new UserDataService().getUserDetails("appsoneadmin")
        AccessDetailsBean bean = OBJECT_MAPPER.readValue(userInfoBean.getAccessDetailsJSON(),
                new TypeReference<AccessDetailsBean>() {
                })

        when:
        List<UserInfo.AccessDetails> accessDetails = usersBL.populateAccessDetails(bean)

        then:
        accessDetails != null
        accessDetails.get(0).accountId == "*"
        accessDetails.get(0).applications == null
    }

    def "validateUserDetailsId invalid attribute bean"() {

        when:
        usersBL.validateUserDetailsId("userDetailsId")

        then:
        thrown(RequestException)
    }

    def "validateUserDetailsId unauthorized user"() {

        when:
        usersBL.validateUserDetailsId("9feea7db-c6eb-e8c4c018-4eb4-43da-b5b0-dc8409bacbe1")

        then:
        thrown(RequestException)
    }

    def "validateUserDetailsId user manager"() {

        when:
        usersBL.validateUserDetailsId("9feea7db-c6eb-4071-828a-ee9f40016dff")

        then:
        noExceptionThrown()
    }

    def "validateRoleAndProfile invalid role"() {

        when:
        usersBL.validateRoleAndProfile(67432,3)

        then:
        thrown(RequestException)
    }

    def "validateRoleAndProfile invalid profile"() {

        when:
        usersBL.validateRoleAndProfile(1,3)

        then:
        thrown(RequestException)
    }

    def "validateRoleAndProfile valid"() {

        when:
        usersBL.validateRoleAndProfile(4,5)

        then:
        noExceptionThrown()
    }

}
