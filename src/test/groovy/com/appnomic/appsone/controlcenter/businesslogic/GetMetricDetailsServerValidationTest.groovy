package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UserAccountBean
import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo
import com.appnomic.appsone.controlcenter.exceptions.ServerException
import com.appnomic.appsone.controlcenter.pojo.Controller
import com.appnomic.appsone.controlcenter.pojo.MetricDetailsRequest
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import com.appnomic.appsone.controlcenter.pojo.ServiceConfig
import com.appnomic.appsone.controlcenter.util.CommonUtils
import com.appnomic.appsone.controlcenter.util.ValidationUtils
import com.fasterxml.jackson.core.type.TypeReference
import com.heal.configuration.pojos.CompInstClusterDetails
import spock.lang.Specification

import java.util.function.Function
import java.util.stream.Collectors

class GetMetricDetailsServerValidationTest extends Specification {

    def "Server Validation:Auth Token Validation"() {
        setup:
        GetMetricDetails getMetricDetails = new GetMetricDetails()
        UtilityBean utilityBean=getUtilityBean()

        def utility = UtilityBean.<Integer> builder().accountIdentifier("d681ef13-d690-4917-jkhg-6c79b-1").authToken(null).build()
        when:

       getMetricDetails.serverValidation(utility)
        then:
        final ServerException e = thrown()
        e.getMessage() == "ServerException : Invalid Authorization Token."
    }

    private static Controller getController() {

        Controller controller = new Controller()
        controller.setAppId("123")
        controller.setName("controllerName")
        controller.setAccountId(1)
        controller.setControllerTypeId(12)
        controller.setTimeOffset(1L)
        controller.setCreatedBY("create")
        controller.setCreatedOn("cretedon")
        controller.setUpdatedTime("update")

        ServiceConfig serviceConfig = new ServiceConfig()
        serviceConfig.setServiceId("123")
        serviceConfig.setServiceName("serviceName")

        controller.setPluginSuppressionInterval(1)
        controller.setPluginWhitelisted(true)
        controller.setServiceDetails(serviceConfig)
        return controller

    }


    private static UserAccountBean getUserAccBean() {
        AccountBean accountBean = new AccountBean()

        accountBean.setId(1)
        accountBean.setName("abc")
        accountBean.setCreatedTime(null)
        accountBean.setUpdatedTime(null)
        accountBean.setStatus(2)
        accountBean.setPrivateKey("privacykey")
        accountBean.setPublicKey("publicKey")
        accountBean.setUserIdDetails("details")
        accountBean.setIdentifier("identifier")

        return UserAccountBean.builder()
                .userId("1")
                .account(accountBean)
                .build()
    }
    private static CompInstClusterDetails getCompInstClusterDetails(){
        return CompInstClusterDetails.builder()

    }

    private static UtilityBean getUtilityBean() {
        AccountBean accountBean = new AccountBean()

        accountBean.setId(1)
        accountBean.setName("abc")
        accountBean.setCreatedTime(null)
        accountBean.setUpdatedTime(null)
        accountBean.setStatus(2)
        accountBean.setPrivateKey("privacykey")
        accountBean.setPublicKey("publicKey")
        accountBean.setUserIdDetails("details")
        accountBean.setIdentifier("identifier")
        List<Integer> instanceIds = new ArrayList<>()
        instanceIds.add(1)
        instanceIds.add(2)
        instanceIds.add(3)
        return UtilityBean.builder()


                .pojoObject(instanceIds)
                .accountIdentifier("d681ef13-d690-4917-jkhg-6c79b-1")
                .serviceId("123")
                .authToken("testing-identifier-using-dummy-token")
                .applicationId("applicationid")
                .userId("userid")
                .batchName("batchname")
                .account(accountBean)
                .build()


    }

    def "Server Validation:Invalid ServiceId"() {
        setup:
        GetMetricDetails getMetricDetails = new GetMetricDetails()
        UserAccountBean userAccBean=new UserAccountBean()
        UtilityBean utilityBean=getUtilityBean()
//        userAccBean = getUserAccBean()
        userAccBean.setUserId("123")
        userAccBean.setAccount(getUserAccBean().getAccount())

        int accountId=  userAccBean.getAccount().getId()
        int serviceId=Integer.parseInt(utilityBean.getServiceId())
        Controller serviceDetails= ValidationUtils.getServiceDetails(accountId, serviceId)
        when:

        getMetricDetails.serverValidation(utilityBean)
        then:
        final e = thrown(ServerException)
        e.getMessage() == "RequestException : The service id provided is invalid in this case as it does not exist or is not mapped to the concerned account"
    }

    def "Server Validation: Invalid Instance Id"() {
        setup:
        InstanceRepo instanceRepo = new InstanceRepo();
        MetricDetailsRequest metricDetailsRequest
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\"instanceIds\":[1,2,3]}")
        metricDetailsRequest = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestObject.getBody(),
                new TypeReference<MetricDetailsRequest>() {
                })
        List<Integer> instanceIds = metricDetailsRequest.getInstanceIds();
        UtilityBean utilityBean=getUtilityBean()

        when:
        UtilityBean<Object> data = new GetMetricDetails().serverValidation(utilityBean)
        data.getPojoObject() == instanceIds
        then:
        final e = thrown(ServerException)
        e.getMessage() == "ServerException : Invalid instanceIds provided for the service."

    }

    def "Server Validation: Invalid instanceIds provided for the service."() {
        setup:
        InstanceRepo instanceRepo = new InstanceRepo();
        UtilityBean utilityBean=getUtilityBean()
        Set<Integer> instanceIds = new HashSet<>(utilityBean.getPojoObject())
        CompInstClusterDetails compInstClusterDetails=new CompInstClusterDetails()
        Map<Integer, CompInstClusterDetails> instancesMap = instanceRepo.getInstances(userAccBean.getAccount().getIdentifier())
                .parallelStream()
                .filter({ i -> instanceIds.contains(i.getId()) })
                .collect(Collectors.toMap(com.heal.configuration.pojos.CompInstClusterDetails.&getId, Function.identity()));
        Set<Integer> componentIds = instancesMap.values().parallelStream().map(CompInstClusterDetails.&getComponentId).collect(Collectors.toSet())



        when:
        new GetMetricDetails().serverValidation(utilityBean)

        then:
        final e = thrown(ServerException)
        e.getMessage() == "ServerException : Invalid instanceIds provided for the service."

    }
}
