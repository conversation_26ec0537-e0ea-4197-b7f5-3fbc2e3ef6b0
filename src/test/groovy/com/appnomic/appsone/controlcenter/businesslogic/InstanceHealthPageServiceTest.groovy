package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.service.InstanceHealthPageService
import spark.Request
import spark.Response
import spock.lang.Specification

class InstanceHealthPageServiceTest extends Specification {
    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }


    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def cleanupSpec() {
        DBTestCache.rollback()
    }

    def "getInstanceHealthPageDetails failure "() {
        setup:
        MySQLConnectionManager.INSTANCE.setDbi(null)

        def correctDataDbUrl = MySQLConnectionManager.INSTANCE.getH2URL()

        String incorrectDataDbUrl = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/NotificationSettingsConfiguration/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/NotificationSettingsConfiguration/populate.sql'"


        MySQLConnectionManager.INSTANCE.setH2URL(incorrectDataDbUrl)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2URL()
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        Thread.sleep(5000)
        when:
        def result = InstanceHealthPageService.getInstanceHealthPageDetails(request, response)
        then:
        result.getMessage() == "Error while getting Page List"
        cleanup:
        DBTestCache.rollback()
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(correctDataDbUrl)
        MySQLConnectionManager.INSTANCE.getHandle()
    }
}
