package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import spock.lang.Specification

class GetForensicActionBLTest extends Specification {
    def "Request object is NULL"() {
        when:
        new GetForensicActionsBL().clientValidation(null)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Request object is null or empty."
    }
    def"Auth token is NULL"(){
        setup:
        RequestObject requestObject=new RequestObject()
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", null)
        requestObject.setHeaders(headers)
        when:
        new GetForensicActionsBL().clientValidation(requestObject)
        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Authorization Token is empty."
    }
    def "Empty Account Identifier"(){
        setup:
        RequestObject requestObject=new RequestObject()
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "authorization-token")
        requestObject.setHeaders(headers)
        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", null)
        requestObject.setParams(params)

        when:
        new GetForensicActionsBL().clientValidation(requestObject)
        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Account Identifier should not be empty."


    }
    def "Client Validation: success "(){
        setup:
        RequestObject requestObject=new RequestObject()
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "authorization-token")
        requestObject.setHeaders(headers)
        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "identifier")
        requestObject.setParams(params)

        when:
        UtilityBean<Object> data = new GetForensicActionsBL().clientValidation(requestObject)
        then:
        data.getAccountIdentifier()=="identifier"
        data.getAuthToken()=="authorization-token"
    }
}
