package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.exceptions.KpiException
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommonVersionCompIdMapping
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CompVersionKpiMappingBean
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.AddKpiRequest
import com.appnomic.appsone.controlcenter.dao.mysql.MasterDataService
import spock.lang.Specification

class CompVersionKpiMappingTest extends Specification {

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        Thread.sleep(20000)
    }

    def "CompVersionKpiMapping compIdMapping is NULL"() {
        when:
        CompVersionKpiMapping.addCompVerKpiMappingBean(null, null, null, 0, 0, null)

        then:
        def error = thrown(KpiException)
        error.message == "KpiException : Common version & component ID are invalid"
    }

    def "CompVersionKpiMapping NULL attributes"() {
        when:
        CompVersionKpiMapping.addCompVerKpiMappingBean(null, null, new CommonVersionCompIdMapping(), -1, -1, null)

        then:
        def error = thrown(KpiException)
        error.message == "KpiException : Error while creating CompVersionKpiMappingBean"
    }

    def "CompVersionKpiMapping insertion failure"() {
        when:
        AddKpiRequest addKpiRequest = AddKpiRequest.builder()
                .custom(1)
                .enableAnalytics(1)
                .collectionIntervalSeconds(60)
                .kpiName("KPIName")
                .kpiIdentifier("AliasName")
                .description("Kpi description")
                .dataType("Text")
                .kpiType("Core")
                .clusterOperation("Sum")
                .rollupOperation("Average")
                .clusterAggregation("SingleValue")
                .instanceAggregation("SingleValue")
                .measureUnits("DisplayPurpose")
                .groupKpiIdentifier("AliasName")
                .componentName("ComponentName")
                .componentVersion("ComponentVersion")
                .componentType("ComponentType")
                .categoryName("CategoryName")
                .build()
        CompVersionKpiMapping.addCompVerKpiMappingBean("user-identifier", addKpiRequest, new CommonVersionCompIdMapping(), -1, -1, null)

        then:
        def error = thrown(KpiException)
        error.message == "KpiException : Error while mapping KPI to Component version."
    }

    def "CompVersionKpiMapping insertion successful"() {
        when:
        AddKpiRequest addKpiRequest = AddKpiRequest.builder()
                .custom(1)
                .enableAnalytics(1)
                .collectionIntervalSeconds(60)
                .kpiName("KPIName-test")
                .kpiIdentifier("AliasName-test")
                .description("Kpi description")
                .dataType("Text")
                .kpiType("Core")
                .clusterOperation("Sum")
                .rollupOperation("Average")
                .clusterAggregation("SingleValue")
                .instanceAggregation("SingleValue")
                .measureUnits("DisplayPurpose")
                .groupKpiIdentifier("AliasName")
                .componentName("ComponentName")
                .componentVersion("ComponentVersion")
                .componentType("ComponentType")
                .categoryName("CategoryName")
                .build()
        def retVal = CompVersionKpiMapping.addCompVerKpiMappingBean("user-identifier-test", addKpiRequest,
                CommonVersionCompIdMapping.builder().commonVersionId(14).componentId(13).build(),
                3, 12, null)

        then:
        Optional<CompVersionKpiMappingBean> data = MasterDataService.getComponentVersionKpiMapping()
                .stream()
                .filter({ c -> c.getId() == retVal })
                .findAny()

        if(data.isPresent()) {
            data.get().getComponentTypeId() == 3
            data.get().getKpiDetailsId() == 12
            data.get().getUserDetailsId() == "user-identifier-test"
        }
    }
}
