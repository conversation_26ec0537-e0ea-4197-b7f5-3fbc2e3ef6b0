package com.appnomic.appsone.controlcenter.businesslogic


import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException
import com.appnomic.appsone.controlcenter.exceptions.ServerException
import com.appnomic.appsone.controlcenter.pojo.ParentApplicationPojo
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import com.heal.configuration.entities.ParentApplicationBean
import spock.lang.Specification


class AddParentApplicationBLTest extends Specification {

    def "Client validation failure - NULL request object"() {
        when:
        new AddParentApplicationBL().clientValidation(null)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Request object is null or empty."
    }

    def "Client validation failure - NULL accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "   {\n" +
                "      \"parentApplicationIdentifier\":\"20bef44d-f521-4c8c-8455-3ec2facf14c8\"\n" +
                "     }\n" +
                "   ]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", null)
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddParentApplicationBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Account identifier is invalid"
    }

    def "Client validation failure - Empty accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "   {\n" +
                "      \"parentApplicationIdentifier\":\"20bef44d-f521-4c8c-8455-3ec2facf14c8\"\n" +
                "     }\n" +
                "   ]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddParentApplicationBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Account identifier is invalid"
    }

    def "Client validation failure - Empty authorization token"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "   {\n" +
                "      \"parentApplicationIdentifier\":\"20bef44d-f521-4c8c-8455-3ec2facf14c8\"\n" +
                "     }\n" +
                "   ]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "")
        requestObject.setHeaders(headers)

        when:
        new AddParentApplicationBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - NULL authorization token"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "   {\n" +
                "      \"parentApplicationIdentifier\":\"20bef44d-f521-4c8c-8455-3ec2facf14c8\"\n" +
                "     }\n" +
                "   ]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", null)
        requestObject.setHeaders(headers)

        when:
        new AddParentApplicationBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - Missing authorization key"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "   {\n" +
                "      \"parentApplicationIdentifier\":\"20bef44d-f521-4c8c-8455-3ec2facf14c8\"\n" +
                "     }\n" +
                "   ]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        requestObject.setHeaders(headers)

        when:
        new AddParentApplicationBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "add parent application - verification of IOException"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "   {\n" +
                "       identifier}\n" +
                "     ]\n")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddParentApplicationBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid JSON."
    }

    def "Client validation Success"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "   {\n" +
                "      \"parentApplication\":\"20bef44d-f521-4c8c-8455-3ec2facf14c8\"\n" +
                "     }\n" +
                "   ]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<ParentApplicationPojo> data = new AddParentApplicationBL().clientValidation(requestObject) as UtilityBean<ParentApplicationPojo>

        then:
        data.getAccount() == null
        data.getBatchName() == null
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getPojoObject().parentApplication.contains("20bef44d-f521-4c8c-8455-3ec2facf14c8")
    }


    def "add parent application - invalid authorization token"() {
        setup:
        UtilityBean<ParentApplicationPojo> utilityBean = UtilityBean.<ParentApplicationPojo> builder()
                .authToken("testing-with-dummy-authorization-header")
                .build()

        when:
        new AddParentApplicationBL().serverValidation(utilityBean as UtilityBean<List<ParentApplicationPojo>>)

        then:
        final ServerException e = thrown()
        e.getMessage() == "ServerException : Invalid Account Identifier."
    }

    def "add parent application - invalid account identifier"() {
        setup:
        UtilityBean<ParentApplicationPojo> utilityBean = UtilityBean.<ParentApplicationPojo> builder()
                .accountIdentifier("heal_health11")
                .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.B805lrlTlBzyIN_Vhzef4wSQeVQB75Hwf0bDB4ymsveDOKEWasiKo0lsgKbDO8h0XHBiEWS3Am7t3hrAeQU1FfQ3fxV7Nlyyi8muiv0aj8xqjBiUhwbmmyhWknkGrTR6vOQ3oYBF-Unr9UDjynNiNVClmrkgo0EzYWVSPiZ1maX5AuOKpw1VbRyw_QdQlbwq_Uc9TLzrNFso1Alb5GIEv_Jkl-K13d5Apj4YHVXVMSOJDs-mKcEoFo8cvxpsUQRveXA7Q-JH5NVX3Hp6-vIFATFB_Uuv8eHULk51wSqYAJLJP1D_S1zsxBUZSyltCEyeKO_mKv8AIGk4Kui0A0Hk_Q")
                .build()

        when:
        new AddParentApplicationBL().serverValidation(utilityBean as UtilityBean<List<ParentApplicationPojo>>)

        then:
        final ServerException e = thrown()
        e.getMessage() == "ServerException : Invalid Account Identifier."
    }

    def "add parent application - server validation success case"() {
        setup:
        List<ParentApplicationPojo> parentApplication = new ArrayList<>()
        parentApplication.add(ParentApplicationPojo.builder().parentApplication("20bef44d-f521-4c8c-8455-3ec2facf14c8").build())
        UtilityBean<List<ParentApplicationPojo>> utilityBean = UtilityBean.<List<ParentApplicationPojo>> builder()
                .accountIdentifier("heal_health")
                .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.B805lrlTlBzyIN_Vhzef4wSQeVQB75Hwf0bDB4ymsveDOKEWasiKo0lsgKbDO8h0XHBiEWS3Am7t3hrAeQU1FfQ3fxV7Nlyyi8muiv0aj8xqjBiUhwbmmyhWknkGrTR6vOQ3oYBF-Unr9UDjynNiNVClmrkgo0EzYWVSPiZ1maX5AuOKpw1VbRyw_QdQlbwq_Uc9TLzrNFso1Alb5GIEv_Jkl-K13d5Apj4YHVXVMSOJDs-mKcEoFo8cvxpsUQRveXA7Q-JH5NVX3Hp6-vIFATFB_Uuv8eHULk51wSqYAJLJP1D_S1zsxBUZSyltCEyeKO_mKv8AIGk4Kui0A0Hk_Q")
                .pojoObject(parentApplication)
                .build()

        when:
        List<ParentApplicationBean> beanList = new AddParentApplicationBL().serverValidation(utilityBean as UtilityBean<List<ParentApplicationPojo>>)

        then:
        beanList.get(0).name == "20bef44d-f521-4c8c-8455-3ec2facf14c8"
    }

    def "Add Parent Application - failure"() {
        when:
        new AddParentApplicationBL().process(null)

        then:
        final DataProcessingException e = thrown()
        e.getMessage() == "DataProcessingException : Error Adding Parent Application"
    }
}
