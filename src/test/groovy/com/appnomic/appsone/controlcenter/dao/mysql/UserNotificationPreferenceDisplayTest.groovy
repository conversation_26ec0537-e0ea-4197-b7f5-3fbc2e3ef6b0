package com.appnomic.appsone.controlcenter.dao.mysql

import com.appnomic.appsone.controlcenter.beans.UserBean
import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.beans.NotificationSettingsBean
import com.appnomic.appsone.controlcenter.pojo.User
import com.appnomic.appsone.controlcenter.service.NotificationPreferencesDataService
import spock.lang.Shared
import spock.lang.Specification

class UserNotificationPreferenceDisplayTest extends Specification {
    @Shared
    String oldH2URL

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)

        oldH2URL = MySQLConnectionManager.INSTANCE.getH2URL()

        String H2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/userNotificationPreferencesDisplay/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/userNotificationPreferencesDisplay/populate.sql'"

        MySQLConnectionManager.INSTANCE.setH2URL(H2URL)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2URL()
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        Thread.sleep(5000)
    }

    def cleanupSpec() {
        DBTestCache.rollback()
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        //the following line was added ONLY to ensure that the existing test cases are not impacted.
        MySQLConnectionManager.INSTANCE.getHandle()
    }

    class DummyUserBean extends UserBean {
        @Override
        User getUser(String accountName) {
            User user = new User()
            user.setUserId("5e897286-6781-49c7-9847-942e8039a7f6")
            return user
        }

        @Override
        List<String> getApplicationNamesForUser(String accountName) {
            List<String> appNames = new ArrayList<>()
            appNames.add("enetbanking_1")
            appNames.add("ApplicationHost733")

            return appNames
        }
    }

    def "getNotificationSettings"() {
        when:
        //Valid data set
        List<NotificationSettingsBean> settings1 = NotificationPreferencesDataService.getNotificationSettingsForAccount(2)
        //No data available for account ID 1
        List<NotificationSettingsBean> settings2 = NotificationPreferencesDataService.getNotificationSettingsForAccount(1)

        then:
        settings1.size() == 2
        for(NotificationSettingsBean set : settings1) {
            if(set.getTypeId() == 292) {
                set.getTypeName() == "Open for long"
                set.getDurationInMin() == 120
            } else {
                set.getTypeId() == 293
                set.getDurationInMin() == 240
                set.getTypeName() == "Open for too long"
            }
        }

        settings2.isEmpty()
    }
/*
    def "getUsersTest-output list empty"() {
        given:
        UserBean userBeanMock = Spy(DummyUserBean.class)

        List<UserBean> userBeans = new ArrayList<>()
        userBeans.add(userBeanMock)

        Account account = ValidationUtils.validAndGetAccount("d681ef13-d690-4917-jkhg-6c79b-123")

        when:
        Method method = ConfigurationDataService.class.getDeclaredMethod("getUsers", List.class, Account.class)
        method.setAccessible(true)
        List<User> users = (List<User>) method.invoke(ConfigurationDataService.class, userBeans, account)

        then:
        users.isEmpty()
    }
    def "getUsersTest-output list populated"() {
        given:
        UserBean userBeanMock = Spy(DummyUserBean.class)    

        List<UserBean> userBeans = new ArrayList<>()
        userBeans.add(userBeanMock)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

        Account account = ValidationUtils.validAndGetAccount("d681ef13-d690-4917-jkhg-6c79b-12")

        when:                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           
        Method method = ConfigurationDataService.class.getDeclaredMethod("getUsers", List.class, Account.class)
        method.setAccessible(true)
        List<User> users = (List<User>) method.invoke(ConfigurationDataService.class, userBeans, account)

        then:
        users.size() == 1
        users.get(0).emailEnabled == 1
        users.get(0).smsEnabled == 0

        List<ApplicationDetails> appDetails = users.get(0).applicationDetails
        appDetails.size() == 2

        for(ApplicationDetails appDet : appDetails) {
            if(appDet.applicationId == 1) {
                appDet.applicationId == 1
                appDet.applicationName == "enetbanking_1"
                appDet.applicationIdentifier == "ENetbanking"

                List<UserNotificationPreferenceBean> notifPrefs = appDet.notificationPreferences
                notifPrefs.size() == 4

                notifPrefs.get(0).signalSeverity == "Severe"
                notifPrefs.get(0).signalSeverityId == 295
                notifPrefs.get(0).signalType == "Signal"
                notifPrefs.get(0).signalTypeId == 297

                notifPrefs.get(1).signalSeverity == "Severe"
                notifPrefs.get(1).signalSeverityId == 295
                notifPrefs.get(1).signalType == "Early Warning"
                notifPrefs.get(1).signalTypeId == 298

                notifPrefs.get(2).signalSeverity == "Default"
                notifPrefs.get(2).signalSeverityId == 296
                notifPrefs.get(2).signalType == "Signal"
                notifPrefs.get(2).signalTypeId == 297

                notifPrefs.get(3).signalSeverity == "Default"
                notifPrefs.get(3).signalSeverityId == 296
                notifPrefs.get(3).signalType == "Early Warning"
                notifPrefs.get(3).signalTypeId == 298

                for(UserNotificationPreferenceBean notifPref : notifPrefs) {
                    notifPref.notificationType == "Immediately"
                    notifPref.notificationTypeId == 291
                }
            } else {
                appDet.applicationId == 7
                appDet.applicationName == "ApplicationHost733"
                appDet.applicationIdentifier == "245a8d1c-e7b5-434a-9e33-127a81e28050"

                List<UserNotificationPreferenceBean> notifPrefs1 = appDetails.get(1).notificationPreferences
                notifPrefs1.size() == 4

                notifPrefs1.get(0).signalSeverity == "Severe"
                notifPrefs1.get(0).signalSeverityId == 295
                notifPrefs1.get(0).signalType == "Signal"
                notifPrefs1.get(0).signalTypeId == 297
                notifPrefs1.get(0).notificationType == "Immediately"
                notifPrefs1.get(0).notificationTypeId == 291

                notifPrefs1.get(1).signalSeverity == "Severe"
                notifPrefs1.get(1).signalSeverityId == 295
                notifPrefs1.get(1).signalType == "Early Warning"
                notifPrefs1.get(1).signalTypeId == 298
                notifPrefs1.get(1).notificationType == "Open for long"
                notifPrefs1.get(1).notificationTypeId == 292

                notifPrefs1.get(2).signalSeverity == "Default"
                notifPrefs1.get(2).signalSeverityId == 296
                notifPrefs1.get(2).signalType == "Signal"
                notifPrefs1.get(2).signalTypeId == 297
                notifPrefs1.get(2).notificationType == "Open for too long"
                notifPrefs1.get(2).notificationTypeId == 293

                notifPrefs1.get(3).signalSeverity == "Default"
                notifPrefs1.get(3).signalSeverityId == 296
                notifPrefs1.get(3).signalType == "Early Warning"
                notifPrefs1.get(3).signalTypeId == 298
                notifPrefs1.get(3).notificationType == "Off"
                notifPrefs1.get(3).notificationTypeId == 294
            }
        }
    }*/
}
