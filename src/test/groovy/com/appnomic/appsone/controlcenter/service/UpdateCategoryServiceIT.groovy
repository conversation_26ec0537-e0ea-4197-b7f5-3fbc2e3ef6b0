package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.beans.CategoryDetailBean
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.dao.mysql.CategoryDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.IdPojo
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class UpdateCategoryServiceIT extends Specification {


    UpdateCategoryService service = new UpdateCategoryService()
    CategoryDataService categoryDataService = new CategoryDataService()

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    class EmptyTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return null
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    @Shared
    Set<String> header = new HashSet<>()
    Map<String, String> parameters = new HashMap<>()
    String identifier = "d681ef13-d690-4917-jkhg-6c79b-1"

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def addTestCategory() {
        DummyRequest request = Spy(DummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"test-category\"\n," +
                "  \"description\": \"test-category-description\",\n" +
                "  \"subType\": \"Info\",\n" +
                "  \"status\": 1\n" +
                "}"
        GenericResponse<IdPojo> genericResponse = new AddCategoryService().addCategory(request, response)
        return genericResponse.getData()
    }

    def  remTestCategory(int id) {
        categoryDataService.deleteCategoryById(id,null)
    }

    def "UpdateCategory"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        IdPojo category = addTestCategory() as IdPojo
        parameters.put(Constants.CATEGORY_ID, String.valueOf(category.getId()))
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"test-category-updated\"\n," +
                "  \"description\": \"test-category-description-updated\",\n" +
                "  \"subType\": \"Workload\",\n" +
                "  \"status\": 0\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = service.updateCategory(request, response)

        then:
        response.status == 200
        res.getMessage() == UIMessages.CATEGORY_UPDATE_SUCCESS

        then:
        CategoryDetailBean updatedCategory = categoryDataService.getCategory(category.getIdentifier())
        updatedCategory.getName() == "test-category-updated"
        updatedCategory.getDescription() == "test-category-description-updated"
        updatedCategory.getIsWorkLoad() == 1
        updatedCategory.getIsInformative() == 0
        updatedCategory.getStatus() == 0

        cleanup:
        remTestCategory(category.getId())
    }

    def "UpdateCategory KPIs mapped"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.CATEGORY_ID, "1")
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"CPU\"\n," +
                "  \"description\": \"test-category-description-updated\",\n" +
                "  \"subType\": \"Non-info\",\n" +
                "  \"status\": 0\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = service.updateCategory(request, response)

        then:
        response.status == 400
        res.getMessage() == "ServerException : Category status can only be modified if no KPIs are mapped to the category."
    }

    def "UpdateCategory standard invalid"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.CATEGORY_ID, "1")
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"test-category-updated\"\n," +
                "  \"description\": \"test-category-description-updated\",\n" +
                "  \"subType\": \"Workload\",\n" +
                "  \"status\": 0\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = service.updateCategory(request, response)

        then:
        response.status == 400
        res.getMessage() == "ServerException : Category status can only be modified if no KPIs are mapped to the category."

    }

    def "UpdateCategory standard only des"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.CATEGORY_ID, "76")
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"Success\"\n," +
                "  \"description\": \"test-category-description-updated\",\n" +
                "  \"subType\": \"Info\",\n" +
                "  \"status\": 0\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = service.updateCategory(request, response)

        then:
        response.status == 400
        res.getMessage() == "ServerException : Only category description can be modified for Standard categories."
    }

    def "UpdateCategory standard"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.CATEGORY_ID, "76")
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"name\": \"Success\",\n" +
                "  \"description\": \"test-category-description-updated\",\n" +
                "  \"subType\": \"Non-info\",\n" +
                "  \"status\": 0\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = service.updateCategory(request, response)

        then:
        response.status == 200
        res.getMessage() == UIMessages.CATEGORY_UPDATE_SUCCESS
    }

    def "UpdateCategory standard des null"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.CATEGORY_ID, "76")
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"name\": \"Success\",\n" +
                "  \"description\": null,\n" +
                "  \"subType\": \"Non-info\",\n" +
                "  \"status\": 0\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = service.updateCategory(request, response)

        then:
        response.status == 400
        res.getMessage() == "ServerException : Description provided is null or empty for the standard category."
    }

    def "UpdateCategory category id invalid"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.CATEGORY_ID, "769999")
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"name\": \"Success\",\n" +
                "  \"description\": \"test-category-description-updated\",\n" +
                "  \"status\": 1\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = service.updateCategory(request, response)

        then:
        response.status == 400
        res.getMessage() == "ServerException : Category is not present in the specified account."
    }

    def "UpdateCategory category name duplicate"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.CATEGORY_ID, "1")
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"name\": \"CPU\",\n" +
                "  \"description\": \"CPU-test-category-description\",\n" +
                "  \"status\": 1,\n" +
                " \"subType\":\"Non-info\"" +
                "}"
        response.status(200)

        when:
        service.updateCategory(request, response)

        then:
        response.status == 200
    }

    def "update category validate name null"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        parameters.put(Constants.CATEGORY_ID, "5")
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": null\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = service.updateCategory(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Invalid request. Kindly check the logs."
    }

    def "update category validate name empty"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        parameters.put(Constants.CATEGORY_ID, "7")
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \" \"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = service.updateCategory(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Invalid request. Kindly check the logs."

    }

    def "update category request null"() {

        given:
        response.status(200)

        when:
        GenericResponse res = service.updateCategory(null, response)

        then:
        response.status == 400
        res.message.contains(UIMessages.INVALID_REQUEST_BODY)
    }

    def "update category token null"() {

        given:

        EmptyTokenDummyRequest request = Spy(EmptyTokenDummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        parameters.put(Constants.CATEGORY_ID, "76")
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"testCategory\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = service.updateCategory(request, response)

        then:
        response.status == 400
        res.message.contains(UIMessages.AUTH_KEY_INVALID)
    }

    def "update category categoryId null"() {

        given:

        DummyRequest request = Spy(DummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        parameters.put(Constants.CATEGORY_ID, null)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"testCategory\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = service.updateCategory(request, response)

        then:
        response.status == 400
        res.message.contains("Category Id is null or empty.")
    }

    def "update category categoryId error"() {

        given:

        DummyRequest request = Spy(DummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        parameters.put(Constants.CATEGORY_ID, "invalid-id")
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"testCategory\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = service.updateCategory(request, response)

        then:
        response.status == 400
        res.message.contains("Category Id should be a positive integer.")
    }

    def "update category user invalid"() {

        given:

        InvalidDummyRequest request = Spy(InvalidDummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        parameters.put(Constants.CATEGORY_ID, "8")
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"testCategory\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = service.updateCategory(request, response)

        then:
        response.status == 400
        res.message == "ServerException : Error while extracting user details from authorization token"
    }

    def "update category account invalid"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.CATEGORY_ID, "6")
        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"testCategory\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = service.updateCategory(request, response)

        then:
        response.status == 400
        res.message == "ServerException : Account identifier is invalid"
    }

    def "update category account null"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.CATEGORY_ID, "3")
        parameters.put(Constants.ACCOUNT_IDENTIFIER, null)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"testCategory\"\n," +
                "\"identifier\": \"testCategory\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = service.updateCategory(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Account identifier is null or empty."

    }

    def "update category request body null"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        parameters.put(Constants.CATEGORY_ID, "2")
        request.params() >> parameters
        request.body() >> null
        response.status(200)

        when:
        GenericResponse res = service.updateCategory(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Invalid request body. Reason: Request body is either NULL or empty."
    }

    def "update category request body empty"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        parameters.put(Constants.CATEGORY_ID, "9")
        request.params() >> parameters
        request.body() >> " "
        response.status(200)

        when:
        GenericResponse res = service.updateCategory(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Invalid request body. Reason: Request body is either NULL or empty."
    }

    def "update category request parse error"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        parameters.put(Constants.CATEGORY_ID,"7")
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\"\"testCategory1\"\n," +
                "}"
        response.status(200)

        when:
        GenericResponse res = service.updateCategory(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Invalid JSON."
    }
}
