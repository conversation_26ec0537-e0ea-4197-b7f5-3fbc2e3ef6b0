package com.appnomic.appsone.controlcenter.service


import spock.lang.Specification

class GetInstanceLevelKpiThresholdServiceIT extends Specification {

  /*  abstract class DummyHttpServletRequest implements HttpServletRequest {
        @Override
        Map<String, String[]> getParameterMap() {
            return new HashMap<String, String[]>()
        }

        @Override
        Enumeration<String> getHeaderNames() {
            return new Hashtable<String, String>().keys()
        }

    }

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def "Server validation failure - invalid KPI ID"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> "Dummy"
        request.body() >> "{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"sshd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]}"
        when:
        GenericResponse res = new GetInstanceLevelKpiThresholdService().getThresholds(request, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ClientException : Invalid KPI ID. It should be a non-zero integer"
    }

    def "Server validation failure - invalid userId"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> "Dummy"
        request.body() >> "{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"sshd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]}"
        when:
        GenericResponse res = new GetInstanceLevelKpiThresholdService().getThresholds(request, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Error while extracting user details from authorization token"
    }

    def "Server validation failure - invalid accountIdentifier"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.ACCOUNT_IDENTIFIER, "dummy")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"sshd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]}"
        when:
        GenericResponse res = new GetInstanceLevelKpiThresholdService().getThresholds(request, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Account identifier is invalid"
    }

    def "Success case"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> ""
        Map<String, String> queryMap = new HashMap<>()
        String[] strs = new String[1]
        strs[0] = "26"
        queryMap.put("instanceIds", strs)
        queryMap.put("kpiId", 32)
        request.queryMap() >> queryMap

        Request req = Spy(Request.class)
        req.headers() >> headers
        req.raw() >> Spy(DummyHttpServletRequest.class)
        req.params() >> params
        req.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        req.body() >> "{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"sshd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]}"
        new AddInstanceLevelKpiThresholdService().addThresholds(req, response)

        when:
        GenericResponse res = new GetInstanceLevelKpiThresholdService().getThresholds(request, response)

        then:
        res.getResponseStatus() == "SUCCESS"
        res.getData().size() > 0

        InstanceLevelKpiAttributeThreshold threshold = InstanceLevelKpiAttributeThreshold.builder()
        .kpiId(32)
        .groupKpiId(4)
        .attributeValue("sshd")
        .operation("lesser than")
        .minThreshold(2)
        .maxThreshold(3)
        .severity(1)
        .status(1)
        .build()

        res.getData().contains(threshold)

        cleanup:
        new KPIDataService().deleteCompInstanceThresholdDetails(26)
    }*/

}
