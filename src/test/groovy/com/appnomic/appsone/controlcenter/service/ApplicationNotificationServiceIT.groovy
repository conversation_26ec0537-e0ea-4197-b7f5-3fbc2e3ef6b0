package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.ApplicationNotificationDetails
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

class ApplicationNotificationServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def "GetNotificationConfiguration user null"() {

        given:
        request.headers("Authorization") >> null
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        response.status(200)

        when:
        GenericResponse res = ApplicationNotificationService.getNotificationConfiguration(request, response)

        then:
        response.status == 400
        res.message == "Authorization Token is empty."
    }

    def "GetNotificationConfiguration user invalid"() {

        given:
        request.headers("Authorization") >> "abc"
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        response.status(200)

        when:
        GenericResponse res = ApplicationNotificationService.getNotificationConfiguration(request, response)

        then:
        response.status == 400
        res.message == "Invalid Authorization Token."
    }

    def "GetNotificationConfiguration user empty"() {

        given:
        request.headers("Authorization") >> ""
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        response.status(200)

        when:
        GenericResponse res = ApplicationNotificationService.getNotificationConfiguration(request, response)

        then:
        response.status == 400
        res.message == "Authorization Token is empty."
    }

    def "GetNotificationConfiguration account empty"() {

        given:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params(Constants.ACCOUNT_IDENTIFIER) >> ""
        response.status(200)

        when:
        GenericResponse res = ApplicationNotificationService.getNotificationConfiguration(request, response)

        then:
        response.status == 400
        res.message == "Account Identifier should not be empty."
    }

    def "GetNotificationConfiguration account invalid"() {

        given:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "invalid-acc-1"
        response.status(200)

        when:
        GenericResponse res = ApplicationNotificationService.getNotificationConfiguration(request, response)

        then:
        response.status == 400
        res.message == "Invalid Account Identifier."
    }

    def "GetNotificationConfiguration account null"() {

        given:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params(Constants.ACCOUNT_IDENTIFIER) >> null
        response.status(200)

        when:
        GenericResponse res = ApplicationNotificationService.getNotificationConfiguration(request, response)

        then:
        response.status == 400
        res.message == "Account Identifier should not be empty."
    }

    def "GetNotificationConfiguration success"() {

        given:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        response.status(200)

        when:
        GenericResponse res = ApplicationNotificationService.getNotificationConfiguration(request, response)

        then:
        response.status == 200
        res != null
        res.data != null
        ApplicationNotificationDetails appn = res.data as ApplicationNotificationDetails
        appn.getMetaData() != null
        appn.getMetaData().getNotificationSubType().size == 4
        appn.getPreferences() != null


    }
}
