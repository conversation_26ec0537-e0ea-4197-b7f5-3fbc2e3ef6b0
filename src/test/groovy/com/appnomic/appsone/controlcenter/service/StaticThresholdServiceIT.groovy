package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.StatusResponse
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.StaticThresholdRules
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

class StaticThresholdServiceIT extends Specification {
    class DummyResponse extends Response {
        int status

        @Override
        void status(int i) {
            status = i
        }

        @Override
        int status() {
            return this.status
        }
    }
    def setupSpec(){
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(false)
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def cleanupSpec(){
        DBTestCache.rollback()
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def"getStaticThresholds client validation failure"(){
        setup:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":serviceId") >> "2"
        request.queryParams("kpiType") >> "Core"
        request.params(":threshold-type") >> "nor"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        when:
        def genericResponse =  StaticThresholdService.getStaticThresholds(request,response)
        then:
        genericResponse.responseStatus == StatusResponse.FAILURE.name()
        genericResponse.message == "StaticThresholdException : Invalid thresholdType. Reason: thresholdType is undefined in the request."
        genericResponse.getData()== null
        response.status == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
    }
    def"getStaticThresholds server validation failure"(){
        setup:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d68f13-d690-4917-jkhg-6c79b-1-1"
        request.params(":serviceId") >> "2"
        request.queryParams("kpiType") >> "Core"
        request.params(":threshold-type") >> "static"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        when:
        def genericResponse =  StaticThresholdService.getStaticThresholds(request,response)
        then:
        genericResponse.responseStatus == StatusResponse.FAILURE.name()
        genericResponse.message == "StaticThresholdException : Invalid Account Identifier."
        genericResponse.getData()== null
        response.status == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
    }
    def"getStaticThresholds server validation pass"() {
        setup:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":serviceId") >> "2"
        request.queryParams("kpiType") >> "Core"
        request.queryParams("isSystem") >> "false"
        request.params(":threshold-type") >> "static"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        when:
        def genericResponse = StaticThresholdService.getStaticThresholds(request, response)
        then:
        response.getStatus() == Constants.SUCCESS_STATUS_CODE
        genericResponse.getMessage() == "List is fetched successfully."
        genericResponse.getData().size() != 0
    }
    def "create thresholds Core"(){
        setup:
        
        
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":serviceId") >> "2"
        request.queryParams("kpiType") >> "Core"
        request.queryParams("isSystem") >> "false"
        request.params(":threshold-type") >> "static"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        List<StaticThresholdRules> list = StaticThresholdService.getStaticThresholds(request, response).data
        int id = list.parallelStream()
                .filter({ s -> s.kpiId == "8" && s.kpiLevel == "clusters" })
                .findAny().get().dataId
        request.body() >> "[\n" +
                "{\n" +
                "            \"dataId\": "+ id +",\n" +
                "            \"kpiId\": \"8\",\n" +
                "            \"categoryId\": 1,\n" +
                "            \"categoryName\": \"CPU\",\n" +
                "            \"kpiDataType\": \"Float\",\n" +
                "            \"kpiUnit\": \"Percentage\",\n" +
                "            \"kpiLevel\": \"clusters\",\n" +
                "            \"kpiName\": \"CPU Busy\",\n" +
                "            \"kpiAttribute\": \"ALL\",\n" +
                "            \"userDefinedOperationType\": \"lesser than\",\n" +
                "            \"systemOperationType\": null,\n" +
                "            \"generateAnomaly\": false,\n" +
                "            \"userDefinedSOR\": true,\n" +
                "            \"userRuleSeverity\": false,\n" +
                "            \"systemRuleSeverity\": false,\n" +
                "            \"systemThresholds\": {\n" +
                "                \"MIN\": null,\n" +
                "                \"MAX\": null\n" +
                "            },\n" +
                "            \"userThresholds\": {\n" +
                "                \"MIN\": 10,\n" +
                "                \"MAX\": null\n" +
                "            },\n" +
                "            \"errorMessage\": {}\n" +
                "        }\n" +
                "]"
        when:
        def genericResponse =  StaticThresholdService.createThresholds(request,response)
        then:
        genericResponse.getResponseStatus() == Constants.SUCCESS_MESSAGE
        response.getStatus() == Constants.SUCCESS_STATUS_CODE
        genericResponse.getMessage() == StatusResponse.SUCCESS.name()
    }
    def "create thresholds Availability"(){
        setup:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":serviceId") >> "2"
        request.queryParams("kpiType") >> "Availability"
        request.queryParams("isSystem") >> "false"
        request.params(":threshold-type") >> "static"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        List<StaticThresholdRules> list = StaticThresholdService.getStaticThresholds(request, response).data
        int id = list.parallelStream()
                .filter({ s -> s.kpiId == "252" && s.kpiLevel == "instances" })
                .findAny().get().dataId
        request.body() >> "[\n" +
                "{\n" +
                "            \"dataId\": "+ id +",\n" +
                "            \"kpiId\": \"252\",\n" +
                "            \"categoryId\": 53,\n" +
                "            \"categoryName\": \"DBAvailability\",\n" +
                "            \"kpiDataType\": \"Integer\",\n" +
                "            \"kpiUnit\": \"Count\",\n" +
                "            \"kpiLevel\": \"instances\",\n" +
                "            \"kpiName\": \"Cluster Status\",\n" +
                "            \"kpiAttribute\": \"ALL\",\n" +
                "            \"userDefinedOperationType\": \"null\",\n" +
                "            \"systemOperationType\": null,\n" +
                "            \"generateAnomaly\": false,\n" +
                "            \"userDefinedSOR\": false,\n" +
                "            \"userRuleSeverity\": false,\n" +
                "            \"systemRuleSeverity\": false,\n" +
                "            \"systemThresholds\": {\n" +
                "                \"MIN\": null,\n" +
                "                \"MAX\": null\n" +
                "            },\n" +
                "            \"userThresholds\": {\n" +
                "                \"MIN\": null,\n" +
                "                \"MAX\": null\n" +
                "            },\n" +
                "            \"errorMessage\": {}\n" +
                "        }\n" +
                "]"
        when:
        def genericResponse =  StaticThresholdService.createThresholds(request,response)
        then:
        genericResponse.getResponseStatus() == Constants.SUCCESS_MESSAGE
        response.getStatus() == Constants.SUCCESS_STATUS_CODE
        genericResponse.getMessage() == StatusResponse.SUCCESS.name()
    }
    def "create thresholds Core with null body"(){
        setup:


        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":serviceId") >> "2"
        request.queryParams("kpiType") >> "Core"
        request.queryParams("isSystem") >> "false"
        request.params(":threshold-type") >> "static"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        List<StaticThresholdRules> list = StaticThresholdService.getStaticThresholds(request, response).data
        int id = list.parallelStream()
                .filter({ s -> s.kpiId == "8" && s.kpiLevel == "clusters" })
                .findAny().get().dataId
        request.body() >> null
        when:
        def genericResponse =  StaticThresholdService.createThresholds(request,response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "StaticThresholdException : Request body can not be null."
    }
    def "create thresholds  test fail"(){
        setup:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":serviceId") >> "2"
        request.queryParams("kpiType") >> "Core"
        request.queryParams("isSystem") >> "false"
        request.params(":threshold-type") >> "static"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "[\n" +
                "{\n" +
                "            \"dataId\": 16,\n" +
                "            \"kpiId\": \"40\",\n" +
                "            \"categoryId\": 78,\n" +
                "            \"categoryName\": \"FinacleServerLogs\",\n" +
                "            \"kpiDataType\": \"Integer\",\n" +
                "            \"kpiUnit\": \"Count\",\n" +
                "            \"kpiLevel\": \"clusters\",\n" +
                "            \"kpiName\": \"Finacle Errors\",\n" +
                "            \"kpiAttribute\": \"ALL\",\n" +
                "            \"userDefinedOperationType\": \"lesser than\",\n" +
                "            \"systemOperationType\": null,\n" +
                "            \"generateAnomaly\": false,\n" +
                "            \"userDefinedSOR\": false,\n" +
                "            \"systemThresholds\": {\n" +
                "                \"MIN\": null,\n" +
                "                \"MAX\": null\n" +
                "            },\n" +
                "            \"userThresholds\": {\n" +
                "                \"MIN\": 10,\n" +
                "                \"MAX\": null\n" +
                "            },\n" +
                "            \"errorMessage\": {}\n" +
                "        }\n" +
                "]"
        when:
        def genericResponse =  StaticThresholdService.createThresholds(request,response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "All the KPI static thresholds provided are invalid. please refer error messages in data."
    }
    def "create thresholds for system threshold test fail"(){
        setup:
        
        
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":serviceId") >> "2"
        request.queryParams("kpiType") >> "Core"
        request.queryParams("isSystem") >> "true"
        request.params(":threshold-type") >> "static"
        request.headers("Authorization") >> "Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.f4vVXGo3h3l6IP1Y9OXPR-UWYpY2SgIS9o-uAjzA5wjrpCrh-LmQrvqs-Apo0LSbkfvbSYKLVWGwAW16VrCH2eyx25NW64kgEs6PP9AovJM9uL6Nw_BzRi5dO_BwtFnT2NlC2yGC7fmE47oYkC9c8-wPp3IuPtrXvaJ1CfxPlqHbTYWJTGI4tPzCYTRA1ppCwqLFOw_0eod_p3z-RLSFoyw5MWlKFy1PjvvNrGYRY-HFOriDvjIuT7lOkAxnOPVm3cL6rJSKgC4Ur3kOejGtpUjy0S_KRL1-BmZMbRaGa7Y7CD0b6ZJFgNVhYgcvBxmDEAdOL8SnCYzSDiHKBbd8tw"
        request.body() >> "[\n" +
                "{\n" +
                "            \"dataId\": 16,\n" +
                "            \"kpiId\": \"409\",\n" +
                "            \"categoryId\": 78,\n" +
                "            \"categoryName\": \"FinacleServerLogs\",\n" +
                "            \"kpiDataType\": \"Integer\",\n" +
                "            \"kpiUnit\": \"Count\",\n" +
                "            \"kpiLevel\": \"clusters\",\n" +
                "            \"kpiName\": \"Finacle Errors\",\n" +
                "            \"kpiAttribute\": \"ALL\",\n" +
                "            \"systemOperationType\": \"lesse than\",\n" +
                "            \"userDefinedOperationType\": null,\n" +
                "            \"generateAnomaly\": false,\n" +
                "            \"userDefinedSOR\": false,\n" +
                "            \"systemThresholds\": {\n" +
                "                \"MIN\": 10,\n" +
                "                \"MAX\": null\n" +
                "            },\n" +
                "            \"userThresholds\": {\n" +
                "                \"MIN\": null,\n" +
                "                \"MAX\": null\n" +
                "            },\n" +
                "            \"errorMessage\": {}\n" +
                "        }\n" +
                "]"
        when:
        def genericResponse =  StaticThresholdService.createThresholds(request,response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "All the KPI static thresholds provided are invalid. please refer error messages in data."
    }

    def"getStaticThresholds Core"() {
        setup:


        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":serviceId") >> "2"
        request.queryParams("kpiType") >> "Core"
        request.queryParams("isSystem") >> "false"
        request.params(":threshold-type") >> "static"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        when:
        def genericResponse = StaticThresholdService.getStaticThresholds(request, response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.SUCCESS.name()
        response.getStatus() == Constants.SUCCESS_STATUS_CODE
        genericResponse.getMessage() == "List is fetched successfully."
        genericResponse.getData().size() > 0
    }
    def"getStaticThresholds Core where no kpi threshold is configured to service id"() {
        setup:


        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":serviceId") >> "3"
        request.queryParams("kpiType") >> "Core"
        request.queryParams("isSystem") >> "false"
        request.params(":threshold-type") >> "static"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        when:
        def genericResponse = StaticThresholdService.getStaticThresholds(request, response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.SUCCESS.name()
        response.getStatus() == Constants.SUCCESS_STATUS_CODE
        genericResponse.getMessage() == "Static threshold list unavailable for the account identifier: d681ef13-d690-4917-jkhg-6c79b-1,service id: 3"
        genericResponse.getData() == null
    }
    def"getStaticThresholds Availability"() {
        setup:
        
        
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":serviceId") >> "2"
        request.queryParams("kpiType") >> "Availability"
        request.queryParams("isSystem") >> "false"
        request.params(":threshold-type") >> "static"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        when:
        def genericResponse = StaticThresholdService.getStaticThresholds(request, response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.SUCCESS.name()
        response.getStatus() == Constants.SUCCESS_STATUS_CODE
        genericResponse.getMessage() == "List is fetched successfully."
        genericResponse.getData().size() > 0

    }

    def "update severity Core"(){
        setup:
        
        
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":serviceId") >> "2"
        request.queryParams("kpiType") >> "Core"
        request.queryParams("isSystem") >> "false"
        request.params(":threshold-type") >> "static"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        List<StaticThresholdRules> list = StaticThresholdService.getStaticThresholds(request, response).data
        int id = list.parallelStream()
                .filter({ s -> s.kpiId == "8" && s.kpiLevel == "clusters" })
                .findAny().get().dataId
        request.body() >> "[\n" +
                "{\n" +
                "            \"dataId\": "+ id +",\n" +
                "            \"kpiId\": \"8\",\n" +
                "            \"categoryId\": 1,\n" +
                "            \"categoryName\": \"CPU\",\n" +
                "            \"kpiDataType\": \"Float\",\n" +
                "            \"kpiUnit\": \"Percentage\",\n" +
                "            \"kpiLevel\": \"clusters\",\n" +
                "            \"kpiName\": \"CPU Busy\",\n" +
                "            \"kpiAttribute\": \"ALL\",\n" +
                "            \"userDefinedOperationType\": \"lesser than\",\n" +
                "            \"systemOperationType\": null,\n" +
                "            \"generateAnomaly\": false,\n" +
                "            \"userDefinedSOR\": true,\n" +
                "            \"userRuleSeverity\": true,\n" +
                "            \"systemRuleSeverity\": false,\n" +
                "            \"systemThresholds\": {\n" +
                "                \"MIN\": null,\n" +
                "                \"MAX\": null\n" +
                "            },\n" +
                "            \"userThresholds\": {\n" +
                "                \"MIN\": 10,\n" +
                "                \"MAX\": null\n" +
                "            },\n" +
                "            \"errorMessage\": {}\n" +
                "        }\n" +
                "]"
        when:
        def genericResponse =  StaticThresholdService.createThresholds(request,response)

        then:
        genericResponse.getResponseStatus() == Constants.SUCCESS_MESSAGE
        StaticThresholdService.getStaticThresholds(request, response).data
                .parallelStream().anyMatch({ s -> s.kpiId == "8" &&
                s.kpiLevel == "clusters" && s.userRuleSeverity})
    }

    def "update severity Availability"(){
        setup:
        
        
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":serviceId") >> "2"
        request.queryParams("kpiType") >> "Availability"
        request.queryParams("isSystem") >> "false"
        request.params(":threshold-type") >> "static"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        List<StaticThresholdRules> list = StaticThresholdService.getStaticThresholds(request, response).data
        int id = list.parallelStream()
                .filter({ s -> s.kpiId == "252" && s.kpiLevel == "instances" })
                .findAny().get().dataId
        request.body() >> "[\n" +
                "{\n" +
                "            \"dataId\": "+ id +",\n" +
                "            \"kpiId\": \"252\",\n" +
                "            \"categoryId\": 53,\n" +
                "            \"categoryName\": \"DBAvailability\",\n" +
                "            \"kpiDataType\": \"Integer\",\n" +
                "            \"kpiUnit\": \"Count\",\n" +
                "            \"kpiLevel\": \"instances\",\n" +
                "            \"kpiName\": \"Cluster Status\",\n" +
                "            \"kpiAttribute\": \"ALL\",\n" +
                "            \"userDefinedOperationType\": \"null\",\n" +
                "            \"systemOperationType\": null,\n" +
                "            \"generateAnomaly\": false,\n" +
                "            \"userDefinedSOR\": false,\n" +
                "            \"userRuleSeverity\": true,\n" +
                "            \"systemRuleSeverity\": false,\n" +
                "            \"systemThresholds\": {\n" +
                "                \"MIN\": null,\n" +
                "                \"MAX\": null\n" +
                "            },\n" +
                "            \"userThresholds\": {\n" +
                "                \"MIN\": null,\n" +
                "                \"MAX\": null\n" +
                "            },\n" +
                "            \"errorMessage\": {}\n" +
                "        }\n" +
                "]"
        when:
        def genericResponse =  StaticThresholdService.createThresholds(request,response)
        then:
        genericResponse.getResponseStatus() == Constants.SUCCESS_MESSAGE
        StaticThresholdService.getStaticThresholds(request, response).data
                .parallelStream().anyMatch({ s -> s.kpiId == "252" &&
                s.kpiLevel == "instances" && s.userRuleSeverity})
    }

    def"getStaticThresholds Availability + Computed KPIs"() {
        setup:


        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":serviceId") >> "2"
        request.queryParams("kpiType") >> "Availability"
        request.queryParams("isSystem") >> "false"
        request.params(":threshold-type") >> "static"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        when:
        def genericResponse = StaticThresholdService.getStaticThresholds(request, response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.SUCCESS.name()
        response.getStatus() == Constants.SUCCESS_STATUS_CODE
        System.out.println(genericResponse.data)
        genericResponse.getMessage() == "List is fetched successfully."
        genericResponse.getData().size() > 0

    }
}
