package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.dao.mysql.SupervisorDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

import javax.servlet.http.HttpServletRequest

class UpdateSupervisorServiceIT extends Specification {

    abstract class DummyHttpServletRequest implements HttpServletRequest {
        @Override
        Map<String, String[]> getParameterMap() {
            return new HashMap<String, String[]>()
        }

        @Override
        Enumeration<String> getHeaderNames() {
            return new Hashtable<String, String>().keys()
        }

    }

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
        KeycloakConnectionManager.getLogin()
    }

    def "update supervisor success"() {
        setup:
        Request req = Spy(Request.class)
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        req.headers() >> headers
        req.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        req.params() >> params
        req.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        req.body() >> "{\n" +
                "  \"name\": \"Windows_584-Dummy\",\n" +
                "  \"supervisorId\": \"Windows_584-Identifier\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true,\n" +
                "  \"mode\": \"LOCAL\"\n" +
                "}"
        new AddSupervisorService().addSupervisor(req, response)

        Request req2 = Spy(Request.class)
        Set<String> headers2 = new HashSet<>()
        headers2.add("Authorization")
        req2.headers() >> headers2
        req2.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params2 = new HashMap<>()
        params2.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        params2.put(":supervisorIdentifier", "Windows_584-Identifier")
        req2.params() >> params2
        req2.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        req2.body() >> "{\n" +
                "  \"name\": \"Windows_584-Dummy-1\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"WindowsSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"status\": true\n" +
                "}"
        when:
        GenericResponse res = new UpdateSupervisorService().updateSupervisor(req2, response)
        then:
        println res
        response.status == Constants.SUCCESS_STATUS_CODE
        res.message == "Supervisor details successfully updated"
        cleanup:
        new SupervisorDataService().deleteSupervisor("Windows_584-Dummy-1", null)
    }

    def "update supervisor failure - missing identifier"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":supervisorIdentifier", "d52f2ddb-1e2f-4063-a366-e138c825c7192")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "  \"name\": \"Windows_54\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true\n" +
                "}"
        when:
        GenericResponse res = new UpdateSupervisorService().updateSupervisor(request, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Supervisor with identifier d52f2ddb-1e2f-4063-a366-e138c825c7192 is unavailable"
    }

    def "update supervisor failure - no change in name"() {
        setup:
        Request req = Spy(Request.class)
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        req.headers() >> headers
        req.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        req.params() >> params
        req.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        req.body() >> "{\n" +
                "  \"name\": \"Windows_584-Dummy\",\n" +
                "  \"supervisorId\": \"Windows_584-Identifier\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************1\",\n" +
                "  \"status\": true,\n" +
                "  \"mode\": \"LOCAL\"\n" +
                "}"
        new AddSupervisorService().addSupervisor(req, response)

        Request req2 = Spy(Request.class)
        Set<String> headers1 = new HashSet<>()
        headers1.add("Authorization")
        req2.headers() >> headers1
        req2.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params1 = new HashMap<>()
        params1.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        params1.put(":supervisorIdentifier", "Windows_584-Identifier")
        req2.params() >> params1
        req2.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        req2.body() >> "{\n" +
                "  \"name\": \"Windows_584-Dummy\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************2\",\n" +
                "  \"status\": true\n" +
                "}"
        when:
        GenericResponse res = new UpdateSupervisorService().updateSupervisor(req2, response)
        then:
        res.getResponseStatus() == "SUCCESS"
        res.getMessage() == "Supervisor details successfully updated"
        cleanup:
        new SupervisorDataService().deleteSupervisor("Windows_584-Dummy", null)
    }

    def "update supervisor failure - duplicate name"() {
        setup:
        Request req = Spy(Request.class)
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        req.headers() >> headers
        req.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        req.params() >> params
        req.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        req.body() >> "{\n" +
                "  \"name\": \"Windows_584-Dummy\",\n" +
                "  \"supervisorId\": \"Windows_584-Identifier\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************1\",\n" +
                "  \"status\": true,\n" +
                "  \"mode\": \"LOCAL\"\n" +
                "}"
        new AddSupervisorService().addSupervisor(req, response)

        Request req2 = Spy(Request.class)
        Set<String> headers1 = new HashSet<>()
        headers1.add("Authorization")
        req2.headers() >> headers1
        req2.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params1 = new HashMap<>()
        params1.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        params1.put(":supervisorIdentifier", "Windows_584-Identifier")
        req2.params() >> params1
        req2.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        req2.body() >> "{\n" +
                "  \"name\": \"Linux_10\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************2\",\n" +
                "  \"status\": true\n" +
                "}"
        when:
        GenericResponse res = new UpdateSupervisorService().updateSupervisor(req2, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Supervisor with name Linux_10 already exists"
        cleanup:
        new SupervisorDataService().deleteSupervisor("Windows_584-Dummy", null)
    }

    def "update supervisor failure - duplicate host address"() {
        setup:
        Request req = Spy(Request.class)
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        req.headers() >> headers
        req.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        req.params() >> params
        req.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        req.body() >> "{\n" +
                "  \"name\": \"Windows_584-Dummy\",\n" +
                "  \"supervisorId\": \"Windows_584-Identifier\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************1\",\n" +
                "  \"status\": true,\n" +
                "  \"mode\": \"LOCAL\"\n" +
                "}"
        new AddSupervisorService().addSupervisor(req, response)

        Request req2 = Spy(Request.class)
        Set<String> headers1 = new HashSet<>()
        headers1.add("Authorization")
        req2.headers() >> headers1
        req2.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params1 = new HashMap<>()
        params1.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        params1.put(":supervisorIdentifier", "Windows_584-Identifier")
        req2.params() >> params1
        req2.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        req2.body() >> "{\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************1\",\n" +
                "  \"status\": true\n" +
                "}"
        when:
        GenericResponse res = new UpdateSupervisorService().updateSupervisor(req2, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Supervisor for host address **************1 already exists"
        cleanup:
        new SupervisorDataService().deleteSupervisor("Windows_584-Dummy", null)
    }

    def "update supervisor failure - invalid supervisorTypeName"() {
        setup:
        Request req = Spy(Request.class)
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        req.headers() >> headers
        req.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        req.params() >> params
        req.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        req.body() >> "{\n" +
                "  \"name\": \"Windows_584-Dummy\",\n" +
                "  \"supervisorId\": \"Windows_584-Identifier\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************1\",\n" +
                "  \"status\": true,\n" +
                "  \"mode\": \"LOCAL\"\n" +
                "}"
        new AddSupervisorService().addSupervisor(req, response)

        Request req2 = Spy(Request.class)
        Set<String> headers1 = new HashSet<>()
        headers1.add("Authorization")
        req2.headers() >> headers1
        req2.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params1 = new HashMap<>()
        params1.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        params1.put(":supervisorIdentifier", "Windows_584-Identifier")
        req2.params() >> params1
        req2.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        req2.body() >> "{\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor-Dummy\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************2\",\n" +
                "  \"status\": true\n" +
                "}"
        when:
        GenericResponse res = new UpdateSupervisorService().updateSupervisor(req2, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Supervisor type UnixSupervisor-Dummy is invalid"
        cleanup:
        new SupervisorDataService().deleteSupervisor("Windows_584-Dummy", null)
    }

    def "update supervisor failure - invalid mode"() {
        setup:
        Request req = Spy(Request.class)
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        req.headers() >> headers
        req.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        req.params() >> params
        req.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        req.body() >> "{\n" +
                "  \"name\": \"Windows_584-Dummy\",\n" +
                "  \"supervisorId\": \"Windows_584-Identifier\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************1\",\n" +
                "  \"status\": true,\n" +
                "  \"mode\": \"LOCAL\"\n" +
                "}"
        new AddSupervisorService().addSupervisor(req, response)

        Request req2 = Spy(Request.class)
        Set<String> headers1 = new HashSet<>()
        headers1.add("Authorization")
        req2.headers() >> headers1
        req2.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params1 = new HashMap<>()
        params1.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        params1.put(":supervisorIdentifier", "Windows_584-Identifier")
        req2.params() >> params1
        req2.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        req2.body() >> "{\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************2\",\n" +
                "  \"status\": true,\n" +
                "  \"mode\": \"LOCAL-Invalid\"\n" +
                "}"
        when:
        GenericResponse res = new UpdateSupervisorService().updateSupervisor(req2, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ClientException : Input parameter(s) 'mode' invalid"
        cleanup:
        new SupervisorDataService().deleteSupervisor("Windows_584-Dummy", null)
    }

    def "update supervisor failure - duplicate LOCAL mode"() {
        setup:
        Request req = Spy(Request.class)
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        req.headers() >> headers
        req.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        req.params() >> params
        req.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        req.body() >> "{\n" +
                "  \"name\": \"Windows_584\",\n" +
                "  \"supervisorId\": \"Windows_584-Identifier\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true,\n" +
                "  \"mode\": \"LOCAL\"\n" +
                "}"
        new AddSupervisorService().addSupervisor(req, response)

        Request req2 = Spy(Request.class)
        Set<String> headers2 = new HashSet<>()
        headers2.add("Authorization")
        req2.headers() >> headers2
        req2.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params2 = new HashMap<>()
        params2.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        params2.put(":supervisorIdentifier", "Windows_584-Identifier")
        req2.params() >> params2
        req2.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        req2.body() >> "{\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"status\": true,\n" +
                "  \"mode\": \"REMOTE\"\n" +
                "}"

        when:
        GenericResponse res = new UpdateSupervisorService().updateSupervisor(req2, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Supervisor with mode REMOTE already exists"
        cleanup:
        new SupervisorDataService().deleteSupervisor("Windows_584", null)
    }

    def "update supervisor failure - invalid authorization token"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":supervisorIdentifier", "d52f2ddb-1e2f-4063-a366-e138c825c719")
        request.params() >> params
        request.headers("Authorization") >> ""
        request.body() >> "{\n" +
                "  \"name\": \"Test_20\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true\n" +
                "}"
        when:
        GenericResponse res = new UpdateSupervisorService().updateSupervisor(request, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ClientException : Invalid authorization token"

    }

    def "update supervisor api invalid account"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-11")
        params.put(":supervisorIdentifier", "d52f2ddb-1e2f-4063-a366-e138c825c719")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "  \"name\": \"Test_20\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true\n" +
                "}"
        when:
        GenericResponse res = new UpdateSupervisorService().updateSupervisor(request, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Supervisor with identifier d52f2ddb-1e2f-4063-a366-e138c825c719 is unavailable"
    }

    def "add supervisor api name size"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":supervisorIdentifier", "8823da9e-a0db-46c9-8e14-98f0ffb128f5")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "  \"name\": \"Test_20_eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA_eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA_eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true\n" +
                "}"
        when:
        GenericResponse res = new UpdateSupervisorService().updateSupervisor(request, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ClientException : Input parameter(s) 'name' invalid"
    }
}
