package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.dao.mysql.BatchProcessDataService
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ProcessDetailsBean
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.IdPojo
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

import static com.appnomic.appsone.controlcenter.common.Constants.*

class UpdateBatchProcessDetailsBLServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    class EmptyUserDummyRequest extends Request {

        @Override
        String headers(String header) {
            return ""
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    @Shared
    Set<String> header = new HashSet<>()
    Map<String, String> parameters = new HashMap<>()
    String identifier = "d681ef13-d690-4917-jkhg-6c79b-1"
    def batchProcessDataService = new BatchProcessDataService()
    def service = new UpdateBatchProcessService()

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def addProcessIT() {
        Request req = Spy(Request.class)

        Map<String, String> params = new HashMap<>()
        params.put(ACCOUNT_IDENTIFIER, identifier)
        params.put(BATCH_JOB_NAME, "batch_job_test")
        req.params() >> params

        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        req.headers() >> headers
        req.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()

        req.queryMap() >> new HashMap<>()

        req.body() >> "{\n" +
                "  \"processName\": \"process-test\",\n" +
                "  \"processIdentifier\": \"process-test-identifier\",\n" +
                "  \"batchJobs\": [\"batchJob1\",\"batchJob1\"],\n" +
                "  \"hostDetails\": [\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1\",\n" +
                "          \"value\": \"value_1\",\n" +
                "          \"defaultValue\": \"default_value_1\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2\",\n" +
                "          \"value\": \"value_2\",\n" +
                "          \"defaultValue\": \"default_value_2\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1\",\n" +
                "          \"value\": \"value_1\",\n" +
                "          \"defaultValue\": \"default_value_1\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2\",\n" +
                "          \"value\": \"value_2\",\n" +
                "          \"defaultValue\": \"default_value_2\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        return new AddBatchProcessService().addBatchProcess(req, response).getData() as IdPojo
    }

    def deleteProcessIT(int id) {
        batchProcessDataService.deleteProcessIT(id, null)
    }


    def "UpdateBatchProcessDetails"() {

        given:
        IdPojo process = addProcessIT()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(BATCH_JOB_NAME,"batch_job_test")
        parameters.put(PROCESS_DETAILS_ID,process.getId() as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"processName\": \"process-test-upd\",\n" +
                "  \"batchJobsToBeDeleted\": [\"batchJob1\",\"batchJob1\"],\n" +
                "  \"batchJobsToBeAdded\": [\"batchJob3\",\"batchJob4\"],\n" +
                "  \"hostDetails\": [\n" +
                "    {\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"id\": 7,\n" +
                "          \"name\": \"name_1-updated\",\n" +
                "          \"value\": \"value_1-updated\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"id\": 8,\n" +
                "          \"name\": \"name_2-upd\",\n" +
                "          \"value\": \"value_2\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        service.updateBatchProcessDetails(request,response)

        then:
        response.status == SUCCESS_STATUS_CODE
        ProcessDetailsBean bean = batchProcessDataService.getBatchProcessDetailsByNameAndAccount(2, "process-test-upd")
        bean != null
        bean.getName() == "process-test-upd"
        bean.getIdentifier() == process.identifier

        cleanup:
        deleteProcessIT(process.getId())
    }

    def "UpdateBatchProcessDetails empty user"() {

        given:
        EmptyUserDummyRequest request = Spy(EmptyUserDummyRequest.class)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(BATCH_JOB_NAME, "batch_job_test")
        parameters.put(PROCESS_DETAILS_ID, "3")
        request.params() >> parameters
        request.body() >> "{}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.updateBatchProcessDetails(request, response) as GenericResponse<IdPojo>

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Invalid Authorization Token."
    }

    def "UpdateBatchProcessDetails request empty"() {

        given:
        DummyRequest request = Spy(DummyRequest.class)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(BATCH_JOB_NAME, "batch_job_test")
        parameters.put(PROCESS_DETAILS_ID, "3")
        request.params() >> parameters
        request.body() >> "{}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.updateBatchProcessDetails(null, response) as GenericResponse<IdPojo>

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Request body is either NULL or empty."
    }

    def "UpdateBatchProcessDetails JSON invalid"() {

        given:
        DummyRequest request = Spy(DummyRequest.class)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(BATCH_JOB_NAME, "batch_job_test")
        parameters.put(PROCESS_DETAILS_ID, "3")
        request.params() >> parameters
        request.body() >> "{abc}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.updateBatchProcessDetails(request, response) as GenericResponse<IdPojo>

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Invalid JSON."
    }

    def "UpdateBatchProcessDetails identifier empty"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, "")
        parameters.put(BATCH_JOB_NAME, "batch_job_test1")
        parameters.put(PROCESS_DETAILS_ID, "3")
        request.params() >> parameters
        request.body() >> "{}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.updateBatchProcessDetails(request, response) as GenericResponse<IdPojo>

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Account identifier is null or empty."
    }

    def "UpdateBatchProcessDetails identifier null"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, null)
        parameters.put(BATCH_JOB_NAME, "batch_job_test1")
        parameters.put(PROCESS_DETAILS_ID, "3")
        request.params() >> parameters
        request.body() >> "{}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.updateBatchProcessDetails(request, response) as GenericResponse<IdPojo>

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Account identifier is null or empty."
    }

    def "UpdateBatchProcessDetails process id empty"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(BATCH_JOB_NAME, "batch-job-name")
        parameters.put(PROCESS_DETAILS_ID, "")
        request.params() >> parameters
        request.body() >> "{}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.updateBatchProcessDetails(request, response) as GenericResponse<IdPojo>

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Process details id is NULL or empty"
    }

    def "UpdateBatchProcessDetails process id null"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(BATCH_JOB_NAME, "batch-job-name")
        parameters.put(PROCESS_DETAILS_ID, null)
        request.params() >> parameters
        request.body() >> "{}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.updateBatchProcessDetails(request, response) as GenericResponse<IdPojo>

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Process details id is NULL or empty"
    }

    def "UpdateBatchProcessDetails process id not integer"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(BATCH_JOB_NAME, "batch-job-name")
        parameters.put(PROCESS_DETAILS_ID, "abc")
        request.params() >> parameters
        request.body() >> "{}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.updateBatchProcessDetails(request, response) as GenericResponse<IdPojo>

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Batch process id is not a positive integer."
    }

    def "UpdateBatchProcessDetails invalid token"() {

        given:
        InvalidDummyRequest request = Spy(InvalidDummyRequest.class)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(BATCH_JOB_NAME, "batch_job_test")
        parameters.put(PROCESS_DETAILS_ID, "3")
        request.params() >> parameters
        request.body() >> "{}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.updateBatchProcessDetails(request, response) as GenericResponse<IdPojo>

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ServerException : Error while extracting user details from authorization token"
    }

    def "UpdateBatchProcessDetails account identifier invalid"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, "invalid-account-identifier")
        parameters.put(BATCH_JOB_NAME, "batch_job_test")
        parameters.put(PROCESS_DETAILS_ID, "3")
        request.params() >> parameters
        request.body() >> "{}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.updateBatchProcessDetails(request, response) as GenericResponse<IdPojo>

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ServerException : Account identifier is invalid"
    }

    def "UpdateBatchProcessDetails process id invalid"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(BATCH_JOB_NAME, "batch-job-name")
        parameters.put(PROCESS_DETAILS_ID, "999")
        request.params() >> parameters
        request.body() >> "{}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.updateBatchProcessDetails(request, response) as GenericResponse<IdPojo>

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ServerException : Process with id [999] unavailable"
    }

    def "UpdateBatchProcessDetails process name invalid"() {

        given:

        IdPojo process = addProcessIT()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(BATCH_JOB_NAME,"batch_job_test")
        parameters.put(PROCESS_DETAILS_ID,process.getId() as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"processName\": \"process-test-upd...........................................................................................................................................................................................................................................................................................................................................\",\n" +
                "  \"hostDetails\": [\n" +
                "    {\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"id\": 7,\n" +
                "          \"name\": \"name_1-updated\",\n" +
                "          \"value\": \"value_1-updated\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"id\": 8,\n" +
                "          \"name\": \"name_2-upd\",\n" +
                "          \"value\": \"value_2\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse<String> genericResponse = service.updateBatchProcessDetails(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Invalid request. Kindly check the logs."

        cleanup:
        deleteProcessIT(process.getId())
    }

    def "UpdateBatchProcessDetails arg name invalid"() {

        given:

        IdPojo process = addProcessIT()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(BATCH_JOB_NAME,"batch_job_test")
        parameters.put(PROCESS_DETAILS_ID,process.getId() as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"processName\": \"process-test-upd\",\n" +
                "  \"hostDetails\": [\n" +
                "    {\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"id\": 7,\n" +
                "          \"name\": \"name_1-updated...........................................................................................................................................................................................................................................................................................................................................\",\n" +
                "          \"value\": \"value_1-updated\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"id\": 8,\n" +
                "          \"name\": \"name_2-upd\",\n" +
                "          \"value\": \"value_2\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse<String> genericResponse = service.updateBatchProcessDetails(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Invalid request. Kindly check the logs."

        cleanup:
        deleteProcessIT(process.getId())
    }

    def "UpdateBatchProcessDetails arg value invalid"() {

        given:

        IdPojo process = addProcessIT()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(BATCH_JOB_NAME,"batch_job_test")
        parameters.put(PROCESS_DETAILS_ID,process.getId() as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"processName\": \"process-test-upd\",\n" +
                "  \"hostDetails\": [\n" +
                "    {\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"id\": 7,\n" +
                "          \"name\": \"name_1-updated\",\n" +
                "          \"value\": \"value_1-updated\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"id\": 8,\n" +
                "          \"name\": \"name_2-upd\",\n" +
                "          \"value\": \"value_2...........................................................................................................................................................................................................................................................................................................................................\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse<String> genericResponse = service.updateBatchProcessDetails(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Invalid request. Kindly check the logs."

        cleanup:
        deleteProcessIT(process.getId())
    }

}
