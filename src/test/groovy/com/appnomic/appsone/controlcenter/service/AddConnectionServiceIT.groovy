package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import org.apache.commons.lang.RandomStringUtils
import spark.Request
import spark.Response
import spock.lang.Specification

class AddConnectionServiceIT extends Specification{
    class DummyResponse extends Response {
        int status

        @Override
        void status(int i) {
            status = i
        }

        @Override
        int status() {
            return this.status
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def "Server validation failure - invalid accountIdentifier"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> new HashMap<String, String[]>()
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "dummy-account-identifier")
        request.params() >> parameters
        request.body() >>  "[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": \"NB-DB-Service\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-User\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-User\",\n" +
                "        \"destinationName\": \"LOS-User\",\n" +
                "        \"destinationServiceIdentifier\": \"LOS-User\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new AddConnectionService().addConnection(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Account identifier is invalid"
    }

    def "Server validation failure - invalid userId"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> "Dummy-auth-token"
        request.queryMap() >> new HashMap<String, String[]>()
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>  "[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": \"NB-DB-Service\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-User\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-User\",\n" +
                "        \"destinationName\": \"LOS-User\",\n" +
                "        \"destinationServiceIdentifier\": \"LOS-User\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new AddConnectionService().addConnection(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Error while extracting user details from authorization token"
    }

    def "Server validation failure - invalid 'sourceServiceIdentifier'"() {
        given:
        String identifier = RandomStringUtils.random(16, true, true)
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> new HashMap<String, String[]>()
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>  "[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-User\",\n" +
                "        \"sourceServiceIdentifier\": \""+ identifier +"\",\n" +
                "        \"destinationName\": \"LOS-User\",\n" +
                "        \"destinationServiceIdentifier\": \"LOS-User\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new AddConnectionService().addConnection(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Source Service Identifier '[" + identifier + "]' does not exist."
    }

    def "Server validation failure - invalid 'destinationServiceIdentifier'"() {
        given:
        String identifier = RandomStringUtils.random(16, true, true)
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> new HashMap<String, String[]>()
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>  "[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": \""+ identifier +"\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new AddConnectionService().addConnection(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Destination Service Identifier '[" + identifier + "]' does not exist."
    }

    def "Server validation failure - Connection between 'sourceServiceIdentifier' & 'destinationServiceIdentifier' already exists"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> new HashMap<String, String[]>()
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "        \"sourceServiceIdentifier\": \"NB-User\",\n" +
                "        \"destinationServiceIdentifier\": \"LOS-User\"\n" +
                "    }\n" +
                "]"

        new AddConnectionService().addConnection(request, response)


        Request request2 = Spy(Request.class)
        DummyResponse response2 = Spy(DummyResponse.class)
        request2.headers() >> header
        request2.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request2.params() >> parameters
        String[] t = ["NB-User|LOS-User"]
        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("data", t)
        request2.queryMap() >> queryMapData
        request2.body() >> ""

        when:
        GenericResponse res = new AddConnectionService().addConnection(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Connection between Source Service Identifier '[NB-User]' and Destination Service Identifier '[LOS-User]' already exists."

        cleanup:
        new DeleteConnectionService().remConnection(request2, response2)
    }

    def "Success Case"() {
        setup:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")

        Request request1 = Spy(Request.class)
        DummyResponse response1 = Spy(DummyResponse.class)
        request1.headers() >> header
        request1.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request1.params() >> parameters
        request1.queryMap() >> new HashMap<String, String[]>()
        request1.body() >> "[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": \"NB-DB-Service\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-User\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-User\",\n" +
                "        \"destinationName\": \"LOS-User\",\n" +
                "        \"destinationServiceIdentifier\": \"LOS-User\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]"


        Request request2 = Spy(Request.class)
        DummyResponse response2 = Spy(DummyResponse.class)
        request2.headers() >> header
        request2.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request2.params() >> parameters
        String[] t = ["NB-Web-Service|NB-DB-Service,NB-User|LOS-User"]
        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("data", t)
        request2.queryMap() >> queryMapData
        request2.body() >> ""

        when:
        GenericResponse res1 = new AddConnectionService().addConnection(request1, response1)

        then:
        response1.getStatus() == 200
        res1.getResponseStatus() == "SUCCESS"
        res1.getMessage() == UIMessages.CONNECTION_ADD_SUCCESS
        res1.getData().size() == 2

        cleanup:
        new DeleteConnectionService().remConnection(request2, response2)
    }

}