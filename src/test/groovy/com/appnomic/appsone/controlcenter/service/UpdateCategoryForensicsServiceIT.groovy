package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.dao.mysql.ITCleanUpDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.util.StringUtils
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

import static com.appnomic.appsone.controlcenter.common.Constants.*

class UpdateCategoryForensicsServiceIT extends Specification {


    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            String authToken = KeycloakConnectionManager.getAccessToken()
            if (StringUtils.isEmpty(authToken))
                return "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCS" +
                        "jhzMnVhazFUUWd3In0.eyJleHAiOjE2MjU4MjM1MjEsImlhdCI6MTYyNTgyMDgyMSwianRpIjoiNmFlNGVjMDEtZDI5N" +
                        "i00ZTQ4LWI2NTQtNzY4Y2YxY2M0NGQxIiwiaXNzIjoiaHR0cHM6Ly8xOTIuMTY4LjEzLjQ0Ojg0NDMvYXV0aC9yZWFsb" +
                        "XMvbWFzdGVyIiwic3ViIjoiNzY0MDEyM2EtZmJkZS00ZmU1LTk4MTItNTgxY2QxZTNhOWMxIiwidHlwIjoiQmVhcmVyI" +
                        "iwiYXpwIjoiYWRtaW4tY2xpIiwic2Vzc2lvbl9zdGF0ZSI6IjkxNjBiOWQyLWI5NjctNGZlOS1hZWFmLTI5N2Q5YTBjO" +
                        "GRlNyIsImFjciI6IjEiLCJhbGxvd2VkLW9yaWdpbnMiOlsiKiJdLCJzY29wZSI6InByb2ZpbGUgZW1haWwiLCJlbWFpb" +
                        "F92ZXJpZmllZCI6ZmFsc2UsInByZWZlcnJlZF91c2VybmFtZSI6ImFwcHNvbmVhZG1pbiIsImVtYWlsIjoiYXBwc29uZW" +
                        "FkbWluLmtAYXBwbm9taWMuY29tIn0.FmNEY7oOraBuukI4ORBzK4TOkdRI9LBKQMiSzJhjhFQ7LIOih5SyoioHKfOI_Kh" +
                        "PaklnCTQSTlBSO7teYTo1M48I0AEOjCf7MMGzIxwm78UrPcJlg_VuNcoHIM2XaPiZxUcKZHVIsC33OuX0uPBf2TKt8GyC" +
                        "nQQoC9CiKHT3HKVG1koDbL-wRCL4hG2_LHQq5z3LqO9u_Cq5QUbJvFE2N2fTt6j8bkKIWK0FtC4CsNCKTtB5fX8m7YhfD" +
                        "2pXy0I-7i-B1fZFnM7DE0uFRScvp4CoPQyHZ11UWAdhPdOewffO9FDpEXfqhYyX195NJSDVfqkrR1Kx1XU-VVirxegD8Q"
            return authToken
        }
    }

    class InvalidTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    class NullTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return null
        }
    }

    class EmptyTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return ""
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    Set<String> header = new HashSet<>()
    Map<String, String> parameters = new HashMap<>()
    Map<String, String[]> queryMap = new HashMap<>()
    String identifier = "d681ef13-d690-4917-jkhg-6c79b-1"
    UpdateCategoryForensicsService service = new UpdateCategoryForensicsService()

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }


    def "UpdateCategoryForensics"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    31,\n" +
                "    30\n" +
                "  ],\n" +
                "  \"categoryForensicDetails\": [\n" +
                "    {\n" +
                "      \"categoryId\": 1,\n" +
                "      \"forensicId\": 1,\n" +
                "      \"suppressionInterval\": 8,\n" +
                "      \"triggerForensicStatus\": 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"categoryId\": 6,\n" +
                "      \"forensicId\": 8,\n" +
                "      \"suppressionInterval\": 7,\n" +
                "      \"triggerForensicStatus\": 0\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        service.updateCategoryForensics(request, response)

        then:
        response.status == SUCCESS_STATUS_CODE

        cleanup:
        ITCleanUpDataService.updateInstanceForensicITCleanUp(30)
    }

    def "UpdateCategoryForensics : Auth token NULL"() {
        setup:
        NullTokenDummyRequest nullDummyRequest = Spy(NullTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        nullDummyRequest.headers() >> header.toSet()
        nullDummyRequest.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        nullDummyRequest.params() >> parameters
        nullDummyRequest.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    30,\n" +
                "    31\n" +
                "  ],\n" +
                "  \"categoryForensicDetails\": [\n" +
                "    {\n" +
                "      \"categoryId\": 2,\n" +
                "      \"forensicId\": 4,\n" +
                "      \"suppressionInterval\": 5,\n" +
                "      \"triggerForensicStatus\": 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateCategoryForensics(nullDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Invalid Authorization Token.")
    }

    def "UpdateCategoryForensics : Request body NULL"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> null


        when:
        def res = service.updateCategoryForensics(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.INVALID_REQUEST_BODY)
    }

    def "UpdateCategoryForensics : Request body empty"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> " "


        when:
        def res = service.updateCategoryForensics(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.INVALID_REQUEST_BODY)
    }

    def "UpdateCategoryForensics : Auth token Empty"() {
        setup:
        EmptyTokenDummyRequest emptyDummyRequest = Spy(EmptyTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        emptyDummyRequest.headers() >> header.toSet()
        emptyDummyRequest.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        emptyDummyRequest.params() >> parameters
        emptyDummyRequest.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    30,\n" +
                "    31\n" +
                "  ],\n" +
                "  \"categoryForensicDetails\": [\n" +
                "    {\n" +
                "      \"categoryId\": 2,\n" +
                "      \"forensicId\": 4,\n" +
                "      \"suppressionInterval\": 5,\n" +
                "      \"triggerForensicStatus\": 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateCategoryForensics(emptyDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Invalid Authorization Token.")
    }

    def "UpdateCategoryForensics : Account Identifier NULL"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, null)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    30,\n" +
                "    31\n" +
                "  ],\n" +
                "  \"categoryForensicDetails\": [\n" +
                "    {\n" +
                "      \"categoryId\": 2,\n" +
                "      \"forensicId\": 4,\n" +
                "      \"suppressionInterval\": 5,\n" +
                "      \"triggerForensicStatus\": 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateCategoryForensics(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Account identifier is null or empty.")
    }

    def "UpdateCategoryForensics : Account Identifier Empty"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, "")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    30,\n" +
                "    31\n" +
                "  ],\n" +
                "  \"categoryForensicDetails\": [\n" +
                "    {\n" +
                "      \"categoryId\": 2,\n" +
                "      \"forensicId\": 4,\n" +
                "      \"suppressionInterval\": 5,\n" +
                "      \"triggerForensicStatus\": 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateCategoryForensics(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Account identifier is null or empty.")
    }

    def "UpdateCategoryForensics : Instance Ids Empty"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        queryMap.put("instanceIds", new String[0])
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [],\n" +
                "  \"categoryForensicDetails\": [\n" +
                "    {\n" +
                "      \"categoryId\": 2,\n" +
                "      \"forensicId\": 4,\n" +
                "      \"suppressionInterval\": 5,\n" +
                "      \"triggerForensicStatus\": 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateCategoryForensics(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("ClientException : Invalid request. Kindly check the logs.")
    }

    def "UpdateCategoryForensics : Invalid JSON"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    abc,\n" +
                "    31\n" +
                "  ],\n" +
                "  \"categoryForensicDetails\": [\n" +
                "    {\n" +
                "      \"categoryId\": 2,\n" +
                "      \"forensicId\": 4,\n" +
                "      \"suppressionInterval\": 5,\n" +
                "      \"triggerForensicStatus\": 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateCategoryForensics(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("ClientException : Invalid JSON.")
    }

    def "UpdateCategoryForensics : Instance Ids negative"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    -30,\n" +
                "    -31\n" +
                "  ],\n" +
                "  \"categoryForensicDetails\": [\n" +
                "    {\n" +
                "      \"categoryId\": 2,\n" +
                "      \"forensicId\": 4,\n" +
                "      \"suppressionInterval\": 5,\n" +
                "      \"triggerForensicStatus\": 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateCategoryForensics(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("ClientException : Invalid request. Kindly check the logs.")
    }

    def "UpdateCategoryForensics : Auth token Invalid"() {
        setup:
        InvalidTokenDummyRequest invalidDummyRequest = Spy(InvalidTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        invalidDummyRequest.headers() >> header.toSet()
        invalidDummyRequest.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        invalidDummyRequest.params() >> parameters
        invalidDummyRequest.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    30,\n" +
                "    31\n" +
                "  ],\n" +
                "  \"categoryForensicDetails\": [\n" +
                "    {\n" +
                "      \"categoryId\": 2,\n" +
                "      \"forensicId\": 4,\n" +
                "      \"suppressionInterval\": 5,\n" +
                "      \"triggerForensicStatus\": 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateCategoryForensics(invalidDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.AUTH_KEY_INVALID)
    }

    def "UpdateCategoryForensics : Account Identifier Invalid"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, "invalid-account-identifier")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    30,\n" +
                "    31\n" +
                "  ],\n" +
                "  \"categoryForensicDetails\": [\n" +
                "    {\n" +
                "      \"categoryId\": 2,\n" +
                "      \"forensicId\": 4,\n" +
                "      \"suppressionInterval\": 5,\n" +
                "      \"triggerForensicStatus\": 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateCategoryForensics(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.ACCOUNT_IDENTIFIER_INVALID)
    }

    def "UpdateCategoryForensics : Instance Ids invalid"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    60\n" +
                "  ],\n" +
                "  \"categoryForensicDetails\": [\n" +
                "    {\n" +
                "      \"categoryId\": 2,\n" +
                "      \"forensicId\": 4,\n" +
                "      \"suppressionInterval\": 5,\n" +
                "      \"triggerForensicStatus\": 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateCategoryForensics(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Invalid instance Id")
    }

    def "UpdateCategoryForensics : Instance Ids different component"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    10,\n" +
                "    1\n" +
                "  ],\n" +
                "  \"categoryForensicDetails\": [\n" +
                "    {\n" +
                "      \"categoryId\": 2,\n" +
                "      \"forensicId\": 4,\n" +
                "      \"suppressionInterval\": 5,\n" +
                "      \"triggerForensicStatus\": 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateCategoryForensics(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("InstanceIds specified are mapped to different components.")
    }

}
