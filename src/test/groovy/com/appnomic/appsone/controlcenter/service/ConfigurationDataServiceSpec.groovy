package com.appnomic.appsone.controlcenter.service


import com.appnomic.appsone.controlcenter.dao.mysql.ConfigurationDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.NotificationChoice
import spock.lang.Specification

class ConfigurationDataServiceSpec extends Specification{
    //TODO: uncomment the test cases in next release.

    /*def setupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)

        String H2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/create.sql'\\;RUNSCRIPT FROM './src/test/resources/views.sql'\\;RUNSCRIPT FROM './src/test/resources/populate.sql'"

        MySQLConnectionManager.getInstance().setH2URL(H2URL)

        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        MySQLConnectionManager.getInstance().getHandle()

        try {
            Thread.sleep(20000)
        } catch (InterruptedException e) {
            println "Failed in sleeping"
        }

        MySQLConnectionManager.getInstance().setTestIdlePoolSize(2)
        MySQLConnectionManager.getInstance().unlockTestIdlePoolSizeSetter()
    }

    def "Default category : ALL"(){
        setup:
        String applicableUserId = "7640123a-fbde-4fe5-9812-581cd1e3a9c1"

        when:
        NotificationChoice notificationChoice = ConfigurationDataService.getNotificationChoice(308, applicableUserId)

        then:
        noExceptionThrown()

        then:
        notificationChoice.getType() == "All"
    }

    def "Default category : Component"(){
        setup:
        String applicableUserId = "c3e7c261-0ed1-428a-8d1d-5bc590bf826d"

        when:
        NotificationChoice notificationChoice = ConfigurationDataService.getNotificationChoice(309, applicableUserId)

        then:
        noExceptionThrown()

        then:
        notificationChoice.getType() == "Component"

        then:
        notificationChoice.getComponent() == "Host"
    }*/
}
