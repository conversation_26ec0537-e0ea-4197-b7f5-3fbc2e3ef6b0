package com.appnomic.appsone.controlcenter.service


import spock.lang.Specification

class InstanceHealthServiceIT extends Specification {

/*
    class DummyResponse extends Response {
        int status;

        void status(int i) {
            status = i;
        }
    }
    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def  "GetServiceBreakage"() {

        setup:

        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3TWFqem5objVlcXM4ZThfMW80djZ5cXVDZFdUQUdKWVNpRE1hejk5dTFvIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hIYgmCtAbXr9TTUx1JLbvfTzzISWiOTJWMBeN-b5OjAjAbl4ouhrfAd173KB_9fN1oNjB3snKeTnUShhzAj_ZvHhW-d491UZ22uZT9LpKtnY5X8fMVfKiNnOZfDkYu-XDxzxXz4Yd1UWv4ix_UNvmF86ar7usPJj_M-f-liCJ-0p81bQAlRLyrEiQHQdf2fuZ0zGCKnKsoxPDs2f3sifGqAavN5BVLB_gDuj7-o8irkG7kyBODRwrtYmhQiT-7MgWb5n4pu4HbbvOJfDthJIK9NgPRIwnZ--dTEYjQ_oKIAN4oW7NH7VoNhzlHX42cxSyphyKAV2qUW1CU9EqFHZMA"
        response.status(200)

        when:
        GenericResponse genericResponse = InstanceHealthService.getInstanceHealthBreakage(request, response)


        then:
        response.status == 200

        then:
        genericResponse != null && genericResponse.data != null

        then:
        List<InstanceHealthDetails> list = new ArrayList<>(genericResponse.data as List<InstanceHealthDetails>)
        list.size() > 0
    }

    def "invalid GetServiceBreakage"() {

        setup:

        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-3"
        request.headers("Authorization") >> "eyJhbGciOiJSUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3TWFqem5objVlcXM4ZThfMW80djZ5cXVDZFdUQUdKWVNpRE1hejk5dTFvIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hIYgmCtAbXr9TTUx1JLbvfTzzISWiOTJWMBeN-b5OjAjAbl4ouhrfAd173KB_9fN1oNjB3snKeTnUShhzAj_ZvHhW-d491UZ22uZT9LpKtnY5X8fMVfKiNnOZfDkYu-XDxzxXz4Yd1UWv4ix_UNvmF86ar7usPJj_M-f-liCJ-0p81bQAlRLyrEiQHQdf2fuZ0zGCKnKsoxPDs2f3sifGqAavN5BVLB_gDuj7-o8irkG7kyBODRwrtYmhQiT-7MgWb5n4pu4HbbvOJfDthJIK9NgPRIwnZ--dTEYjQ_oKIAN4oW7NH7VoNhzlHX42cxSyphyKAV2qUW1CU9EqFHZMA"
        response.status(400)

        when:
        GenericResponse genericResponse = InstanceHealthService.getInstanceHealthBreakage(request, response)

        then:
        response.status == 400

        then:
        genericResponse.data == []

    }

    def "invalid author user"() {

        setup:

        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-3"
        request.headers("Authorization") >> "xyaqleyJhbGciOiJSUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3TWFqem5objVlcXM4ZThfMW80djZ5cXVDZFdUQUdKWVNpRE1hejk5dTFvIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hIYgmCtAbXr9TTUx1JLbvfTzzISWiOTJWMBeN-b5OjAjAbl4ouhrfAd173KB_9fN1oNjB3snKeTnUShhzAj_ZvHhW-d491UZ22uZT9LpKtnY5X8fMVfKiNnOZfDkYu-XDxzxXz4Yd1UWv4ix_UNvmF86ar7usPJj_M-f-liCJ-0p81bQAlRLyrEiQHQdf2fuZ0zGCKnKsoxPDs2f3sifGqAavN5BVLB_gDuj7-o8irkG7kyBODRwrtYmhQiT-7MgWb5n4pu4HbbvOJfDthJIK9NgPRIwnZ--dTEYjQ_oKIAN4oW7NH7VoNhzlHX42cxSyphyKAV2qUW1CU9EqFHZMA"
        response.status(400)

        when:
        GenericResponse genericResponse = InstanceHealthService.getInstanceHealthBreakage(request, response)

        then:
        response.status == 400

        then:
        genericResponse.data == []

    }
*/

}
