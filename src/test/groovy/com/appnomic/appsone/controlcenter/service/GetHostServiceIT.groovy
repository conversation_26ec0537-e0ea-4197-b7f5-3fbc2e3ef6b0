package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

class GetHostServiceIT extends Specification{

    class DummyResponse extends Response {
        int status

        @Override
        void status(int i) {
            status = i
        }

        @Override
        int status() {
            return this.status
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def "Server validation failure - invalid accountIdentifier"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> new HashMap<>()
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "dummy-account-identifier")
        request.params() >> parameters
        request.body() >> ""
        when:
        GenericResponse res = new GetHostService().getHost(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Account identifier is invalid"
    }

    def "Server validation failure - invalid userId"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> "Dummy-auth-token"
        request.queryMap() >> new HashMap<>()
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> ""
        when:
        GenericResponse res = new GetHostService().getHost(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Error while extracting user details from authorization token"
    }

    def "Success Case"() {
        setup:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params() >> parameters
        request.queryMap() >> new HashMap<String, String[]>()
        request.body() >> ""

        when:
        GenericResponse res = new GetHostService().getHost(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
        res.getMessage() == UIMessages.HOST_FETCH_SUCCESS
        res.getData().size() >= 0
    }
}
