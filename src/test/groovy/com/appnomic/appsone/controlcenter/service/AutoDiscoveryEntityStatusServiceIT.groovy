package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.businesslogic.AutoDiscoveryEntityStatusBL
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.exceptions.ServerException
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.AutoDiscoveryEntityStatusPojo
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

class AutoDiscoveryEntityStatusServiceIT extends Specification{

    class DummyResponse extends Response {
        int status

        @Override
        void status(int i) {
            status = i
        }

        @Override
        int status() {
            return this.status
        }
    }

    Request request = Spy(Request.class)
    Response response = Spy(DummyResponse)

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def "Server Validations failure- User ID is NULL"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "        \"isIgnored\": 1,\n" +
                "        \"identifiers\": [\"test-host-identifier-1\", \"test-host-identifier-2\"], \n" +
                "        \"entityType\": \"Host\" \n" +
                "    }\n")
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("Authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        AutoDiscoveryEntityStatusBL autoDiscoveryIgnoreHostsBL = new AutoDiscoveryEntityStatusBL()
        UtilityBean<AutoDiscoveryEntityStatusPojo> data = autoDiscoveryIgnoreHostsBL.clientValidation(requestObject)

        when:
        autoDiscoveryIgnoreHostsBL.serverValidation(data)

        then:
        final e = thrown(ServerException)
        e.getMessage() == "ServerException : " + UIMessages.AUTH_KEY_INVALID;
    }

    def "Server Validations failure- Invalid account ID"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "        \"isIgnored\": 1,\n" +
                "        \"identifiers\": [\"test-host-identifier-1\", \"test-host-identifier-2\"], \n" +
                "        \"entityType\": \"Host\" \n" +
                "    }\n")
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier", "4qa-d681ef13-d690-24917-jkhg-6c79b-10")
        requestObject.setParams(params)
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("Authorization", "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.qOjgvpTBkqZQh0cUGHcxrsQ-GXQdZ2pje2DK26Qbjb3MjFk61Wpi98AvWoZVUusl4H-0YtZj-6ksiMSyZsGIMtQkwQLV_NW-b9773ltBwli3BhCoP0fg4_eXIcVtjaJVjid3F6tQBJ5RqrYWbapfsqr0Or5a6f9hsiAN8DZZfWXPtzavkSxGrX8uHn0zKLYLri7ImCBfJYX-Q1wlFKTtAoYvlHjhFA3nEJFBlRDIENGHjcp9SjMJ27sBOuUCOH3nLQzkwG1dxpECaYrEBWxOzRfvLJd1nJe2SAfOOwnu3_tdeQedEqL5-6gbumDi80CYUx-4GOUnbRPpUfyB_8uPZQ")
        requestObject.setHeaders(headers)

        AutoDiscoveryEntityStatusBL autoDiscoveryIgnoreHostsBL = new AutoDiscoveryEntityStatusBL()
        UtilityBean<AutoDiscoveryEntityStatusPojo> data = autoDiscoveryIgnoreHostsBL.clientValidation(requestObject)

        when:
        autoDiscoveryIgnoreHostsBL.serverValidation(data)

        then:
        final e = thrown(ServerException)
        e.getMessage() == "ServerException : " + UIMessages.INVALID_ACCOUNT_MESSAGE;
    }

    def "Process failure"(){
        setup:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> new HashMap<>()
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> ("{\n" +
                "        \"isIgnored\": 1,\n" +
                "        \"identifiers\": [\"test-host-identifier-1\"], \n" +
                "        \"entityType\": \"Host\" \n" +
                "    }\n")

        when:
        GenericResponse res = new AutoDiscoveryEntityStatusService().isIgnored(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "Invalid identifier(s) found."
    }

}
