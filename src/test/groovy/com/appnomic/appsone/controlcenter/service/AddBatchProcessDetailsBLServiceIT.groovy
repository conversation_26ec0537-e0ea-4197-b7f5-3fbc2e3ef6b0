package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.dao.mysql.BatchProcessDataService
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ProcessDetailsBean
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.IdPojo
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

import static com.appnomic.appsone.controlcenter.common.Constants.*

class AddBatchProcessDetailsBLServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    class EmptyUserDummyRequest extends Request {

        @Override
        String headers(String header) {
            return ""
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    @Shared
    Set<String> header = new HashSet<>()
    Map<String, String> parameters = new HashMap<>()
    String identifier = "d681ef13-d690-4917-jkhg-6c79b-1"
    def batchProcessDataService = new BatchProcessDataService()
    def service = new AddBatchProcessService()

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def addProcessIT() {
        DummyRequest request = Spy(DummyRequest.class)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(BATCH_JOB_NAME,"batch_job_test")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"processName\": \"process-test\",\n" +
                "  \"processIdentifier\": \"process-test-identifier\",\n" +
                "  \"hostDetails\": [\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1\",\n" +
                "          \"value\": \"value_1\",\n" +
                "          \"defaultValue\": \"default_value_1\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2\",\n" +
                "          \"value\": \"value_2\",\n" +
                "          \"defaultValue\": \"default_value_2\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1\",\n" +
                "          \"value\": \"value_1\",\n" +
                "          \"defaultValue\": \"default_value_1\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2\",\n" +
                "          \"value\": \"value_2\",\n" +
                "          \"defaultValue\": \"default_value_2\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        return service.addBatchProcess(request,response).getData()
    }

    def deleteProcessIT(int id) {
        batchProcessDataService.deleteProcessIT(id,null)
    }

    def "AddBatchProcess"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(BATCH_JOB_NAME,"batch_job_test1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"processName\": \"process-test-1\",\n" +
                "  \"processIdentifier\": \"process-test-identifier\",\n" +
                "  \"batchJobs\": [\"batchJob1\",\"batchJob1\"],\n" +
                "  \"hostDetails\": [\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1-test\",\n" +
                "          \"value\": \"value_1-test\",\n" +
                "          \"defaultValue\": \"default_value_1-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2-test\",\n" +
                "          \"value\": \"value_2-test\",\n" +
                "          \"defaultValue\": \"default_value_2-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1-test\",\n" +
                "          \"value\": \"value_1-test\",\n" +
                "          \"defaultValue\": \"default_value_1-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2-test\",\n" +
                "          \"value\": \"value_2-test\",\n" +
                "          \"defaultValue\": \"default_value_2-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.addBatchProcess(request,response)

        then:
        response.status == SUCCESS_STATUS_CODE
        IdPojo process = genericResponse.getData()
        ProcessDetailsBean processDetailsBean = batchProcessDataService.getBatchProcessDetailsByNameAndAccount(2, "process-test-1")
        processDetailsBean != null
        processDetailsBean.getName() == process.getName()
        processDetailsBean.getIdentifier() == process.getIdentifier()
        List<Integer> ids = new ArrayList<>()
        ids.add(process.getId())
        new BindInDataService().getHostDetailsForProcesses(ids).size() == 2

        cleanup:
        deleteProcessIT(process.getId())
    }

    def "AddBatchProcess empty user"() {

        given:
        EmptyUserDummyRequest request = Spy(EmptyUserDummyRequest.class)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(BATCH_JOB_NAME,"batch_job_test1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"processName\": \"process-test-1\",\n" +
                "  \"processIdentifier\": \"process-test-identifier\",\n" +
                "  \"hostDetails\": [\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1-test\",\n" +
                "          \"value\": \"value_1-test\",\n" +
                "          \"defaultValue\": \"default_value_1-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2-test\",\n" +
                "          \"value\": \"value_2-test\",\n" +
                "          \"defaultValue\": \"default_value_2-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1-test\",\n" +
                "          \"value\": \"value_1-test\",\n" +
                "          \"defaultValue\": \"default_value_1-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2-test\",\n" +
                "          \"value\": \"value_2-test\",\n" +
                "          \"defaultValue\": \"default_value_2-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.addBatchProcess(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Invalid Authorization Token."
    }

    def "AddBatchProcess request null"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(BATCH_JOB_NAME,"batch_job_test1")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.addBatchProcess(null,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Invalid request body. Reason: Request body is either NULL or empty."

    }

    def "AddBatchProcess request body empty"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(BATCH_JOB_NAME,"batch_job_test1")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.addBatchProcess(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Invalid request body. Reason: Request body is either NULL or empty."

    }

    def "AddBatchProcess identifier empty"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,"")
        parameters.put(BATCH_JOB_NAME,"batch_job_test1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"processName\": \"process-test-1\",\n" +
                "  \"processIdentifier\": \"process-test-identifier\",\n" +
                "  \"hostDetails\": [\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1-test\",\n" +
                "          \"value\": \"value_1-test\",\n" +
                "          \"defaultValue\": \"default_value_1-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2-test\",\n" +
                "          \"value\": \"value_2-test\",\n" +
                "          \"defaultValue\": \"default_value_2-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1-test\",\n" +
                "          \"value\": \"value_1-test\",\n" +
                "          \"defaultValue\": \"default_value_1-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2-test\",\n" +
                "          \"value\": \"value_2-test\",\n" +
                "          \"defaultValue\": \"default_value_2-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.addBatchProcess(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Account identifier is null or empty."
    }

    def "AddBatchProcess JSON invalid"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(BATCH_JOB_NAME,"batch_job_test1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"processName\": \"process-test-1\",\n" +
                "  \"processIdentifier\": \"process-test-identifier\"\n" +
                "  \"hostDetails\": [\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1-test\",\n" +
                "          \"value\": \"value_1-test\",\n" +
                "          \"defaultValue\": \"default_value_1-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2-test\",\n" +
                "          \"value\": \"value_2-test\",\n" +
                "          \"defaultValue\": \"default_value_2-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1-test\",\n" +
                "          \"value\": \"value_1-test\",\n" +
                "          \"defaultValue\": \"default_value_1-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2-test\",\n" +
                "          \"value\": \"value_2-test\",\n" +
                "          \"defaultValue\": \"default_value_2-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.addBatchProcess(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Invalid JSON."
    }

    def "AddBatchProcess request invalid"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(BATCH_JOB_NAME,"batch_job_test1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"processName\": null,\n" +
                "  \"processIdentifier\": \"process-test-identifier\",\n" +
                "  \"hostDetails\": [\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1-test\",\n" +
                "          \"value\": \"value_1-test\",\n" +
                "          \"defaultValue\": \"default_value_1-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2-test\",\n" +
                "          \"value\": \"value_2-test\",\n" +
                "          \"defaultValue\": \"default_value_2-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1-test\",\n" +
                "          \"value\": \"value_1-test\",\n" +
                "          \"defaultValue\": \"default_value_1-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2-test\",\n" +
                "          \"value\": \"value_2-test\",\n" +
                "          \"defaultValue\": \"default_value_2-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.addBatchProcess(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Invalid request. Kindly check the logs."
    }

    def "AddBatchProcess invalid token"() {

        given:
        InvalidDummyRequest request = Spy(InvalidDummyRequest.class)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(BATCH_JOB_NAME,"batch_job_test1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"processName\": \"process-test-1\",\n" +
                "  \"processIdentifier\": \"process-test-identifier\",\n" +
                "  \"hostDetails\": [\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1-test\",\n" +
                "          \"value\": \"value_1-test\",\n" +
                "          \"defaultValue\": \"default_value_1-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2-test\",\n" +
                "          \"value\": \"value_2-test\",\n" +
                "          \"defaultValue\": \"default_value_2-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1-test\",\n" +
                "          \"value\": \"value_1-test\",\n" +
                "          \"defaultValue\": \"default_value_1-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2-test\",\n" +
                "          \"value\": \"value_2-test\",\n" +
                "          \"defaultValue\": \"default_value_2-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.addBatchProcess(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ServerException : Error while extracting user details from authorization token"
    }

    def "AddBatchProcess account identifier invalid"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,"invalid-account-identifier")
        parameters.put(BATCH_JOB_NAME,"batch_job_test1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"processName\": \"process-test-1\",\n" +
                "  \"processIdentifier\": \"process-test-identifier\",\n" +
                "  \"hostDetails\": [\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1-test\",\n" +
                "          \"value\": \"value_1-test\",\n" +
                "          \"defaultValue\": \"default_value_1-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2-test\",\n" +
                "          \"value\": \"value_2-test\",\n" +
                "          \"defaultValue\": \"default_value_2-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1-test\",\n" +
                "          \"value\": \"value_1-test\",\n" +
                "          \"defaultValue\": \"default_value_1-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2-test\",\n" +
                "          \"value\": \"value_2-test\",\n" +
                "          \"defaultValue\": \"default_value_2-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.addBatchProcess(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ServerException : Account identifier is invalid"
    }

    def "AddBatchProcess process identifier invalid"() {

        given:
        int processId = addProcessIT().getId()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(BATCH_JOB_NAME,"batch_job_test1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"processName\": \"process-test-1\",\n" +
                "  \"processIdentifier\": \"process-test-identifier\",\n" +
                "  \"hostDetails\": [\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1-test\",\n" +
                "          \"value\": \"value_1-test\",\n" +
                "          \"defaultValue\": \"default_value_1-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2-test\",\n" +
                "          \"value\": \"value_2-test\",\n" +
                "          \"defaultValue\": \"default_value_2-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1-test\",\n" +
                "          \"value\": \"value_1-test\",\n" +
                "          \"defaultValue\": \"default_value_1-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2-test\",\n" +
                "          \"value\": \"value_2-test\",\n" +
                "          \"defaultValue\": \"default_value_2-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.addBatchProcess(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ServerException : 'processIdentifier' should be unique across all accounts"

        cleanup:
        deleteProcessIT(processId)
    }

    def "AddBatchProcess process name invalid"() {

        given:
        int processId = addProcessIT().getId()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(BATCH_JOB_NAME,"batch_job_test1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"processName\": \"process-test\",\n" +
                "  \"processIdentifier\": \"process-test-identifier-1\",\n" +
                "  \"batchJobs\": [\"batchJob1\",\"batchJob1\"],\n" +
                "  \"hostDetails\": [\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1-test\",\n" +
                "          \"value\": \"value_1-test\",\n" +
                "          \"defaultValue\": \"default_value_1-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2-test\",\n" +
                "          \"value\": \"value_2-test\",\n" +
                "          \"defaultValue\": \"default_value_2-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"hostAddress\": \"**************\",\n" +
                "      \"directoryPath\": \"/root\",\n" +
                "      \"arguments\": [\n" +
                "        {\n" +
                "          \"name\": \"name_1-test\",\n" +
                "          \"value\": \"value_1-test\",\n" +
                "          \"defaultValue\": \"default_value_1-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"name_2-test\",\n" +
                "          \"value\": \"value_2-test\",\n" +
                "          \"defaultValue\": \"default_value_2-test\",\n" +
                "          \"order\": 1,\n" +
                "          \"placeHolder\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = service.addBatchProcess(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ServerException : Process with name [process-test-identifier-1] already exists for accountId [2]"

        cleanup:
        deleteProcessIT(processId)
    }
}
