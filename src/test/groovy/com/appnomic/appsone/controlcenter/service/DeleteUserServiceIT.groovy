package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.StatusResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.UserDetails
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

class DeleteUserServiceIT extends Specification {

    class DummyResponse extends Response {
        int status = 0

        void status(int i) {
            this.status = i
        }

        int status() {
            return this.status
        }
    }

    Request request = Spy(Request.class)
    Response response = Spy(DummyResponse.class)
    Response addResponse = new DummyResponse()
    DummyRequest addRequest = Spy(DummyRequest.class)

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(false)
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def cleanupSpec() {
        DBTestCache.rollback()
    }

    def "Client validation failure"() {
        given:
        request.params(":userIdentifier") >> ""
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        response.status(200)
        when:
        def result = DeleteUserService.deleteUser(request, response)
        then:
        result.getMessage() == "User Id is null or empty."
        then:
        response.getStatus() == 400
    }

    def "Client validation with no parameter"() {
        given:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        response.status(200)
        when:
        def result = DeleteUserService.deleteUser(request, response)
        then:
        result.getMessage() == "Internal server error, Kindly contact the Administrator."
        then:
        response.getStatus() == Constants.INTERNAL_SERVER_ERROR_STATUS_CODE
    }

    def "Server validation failure"() {
        given:
        request.params(":userIdentifier") >> "a4caffcd-b0d7-4181-adac-bb61b3b2d401"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        response.status(200)
        when:
        def result = DeleteUserService.deleteUser(request, response)
        then:
        result.getMessage() == "User profile doesn't not exist."
        then:
        response.getStatus() == 400
    }

    def "delete user success"() {
        given:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        Set<String> header = new HashSet<>()
        Map<String, String> parameters = new HashMap<>()
        header.add(Constants.AUTHORIZATION)
        addRequest.headers() >> header.toSet()
        addRequest.queryMap() >> new HashMap<>()
        addRequest.params() >> parameters
        addRequest.body() >> "{\n" +
                "    \"userName\": \"user_app_user\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"**********\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 2,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "              9,\n" +
                "              14\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        when:
        UserService.add(addRequest, addResponse)
        UserDetails user = UserService.getUsers(addRequest, addResponse).data.parallelStream()
                .filter({ u -> u.userName == "user_app_user" }).findAny().orElse(null)
        request.params(":userIdentifier") >> user.getUserId()

        then:
        def genericResponse = DeleteUserService.deleteUser(request, response)
        then:
        response.getStatus() == 200
        genericResponse.getMessage() == UIMessages.USER_DELETE_SUCCESS
        genericResponse.getResponseStatus() == StatusResponse.SUCCESS.name()
    }
}
