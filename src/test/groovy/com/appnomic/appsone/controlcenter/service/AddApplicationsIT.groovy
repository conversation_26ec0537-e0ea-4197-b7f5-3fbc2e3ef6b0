package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.ResponseObject
import com.appnomic.appsone.controlcenter.dao.mysql.ITCleanUpDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.util.CommonUtils
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

import java.util.stream.Collectors

import static com.appnomic.appsone.controlcenter.common.Constants.*

class AddApplicationsIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def identifier = "d681ef13-d690-4917-jkhg-6c79b-1"

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

   /* def "AddApplications"() {

        setup:
        request.headers(AUTHORIZATION) >> KeycloakConnectionManager.getAccessToken()
        request.params(ACCOUNT_IDENTIFIER) >> identifier
        request.body() >> "[\n" +
                "   {\n" +
                "      \"identifier\":\"los_1_DR-1111\",\n" +
                "      \"name\":\"LOS-DR-1\",\n" +
                "      \"txnViolationConfigs\":null,\n" +
                "      \"tags\":[\n" +
                "         {\n" +
                "            \"identifier\":\"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n" +
                "            \"subTypeName\":\"Account\",\n" +
                "            \"name\":\"Timezone\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-DB-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"db_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-Web-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"web_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-App-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"app_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-User-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"user\"\n" +
                "         }\n" +
                "      ]\n" +
                "   },\n" +
                "   {\n" +
                "      \"identifier\":\"los_2_DR-1111\",\n" +
                "      \"name\":\"LOS-DR-2\",\n" +
                "      \"txnViolationConfigs\":null,\n" +
                "      \"tags\":[\n" +
                "         {\n" +
                "            \"identifier\":\"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n" +
                "            \"subTypeName\":\"Account\",\n" +
                "            \"name\":\"Timezone\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-DB-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"db_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-Web-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"web_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-App-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"app_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-User-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"user\"\n" +
                "         }\n" +
                "      ]\n" +
                "   }\n" +
                "]"
        response.status(200)

        when:
        ResponseObject responseObject = ApplicationService.addApplications(request, response)

        then:
        response.status == 200

        then:
        responseObject != null
        List<Integer> appIds = CommonUtils.getControllersByType(APPLICATION_CONTROLLER_TYPE,2)
                .parallelStream().filter({ c -> c.getIdentifier().equalsIgnoreCase("los_2_DR-1111") ||
                c.getIdentifier().equalsIgnoreCase("los_1_DR-1111") }).map({ c -> Integer.parseInt(c.getAppId())})
                .collect(Collectors.toList())
        appIds.size() == 2

        cleanup:
        appIds.forEach({ id -> ITCleanUpDataService.addApplicationItCleanUp(id) })

    }*/

    def "AddApplications request body null"() {

        setup:
        request.headers(AUTHORIZATION) >> KeycloakConnectionManager.getAccessToken()
        request.params(ACCOUNT_IDENTIFIER) >> identifier
        request.body() >> null
        response.status(200)

        when:
        ResponseObject responseObject = ApplicationService.addApplications(request, response)

        then:
        response.status == 403
        responseObject.getMessage() == "Request body can not be null."
    }

    def "AddApplications identifier null"() {

        setup:
        request.headers(AUTHORIZATION) >> KeycloakConnectionManager.getAccessToken()
        request.params(ACCOUNT_IDENTIFIER) >> null
        request.body() >> "[\n" +
                "   {\n" +
                "      \"identifier\":\"los_1_DR\",\n" +
                "      \"name\":\"LOS-DR-1\",\n" +
                "      \"txnViolationConfigs\":null,\n" +
                "      \"tags\":[\n" +
                "         {\n" +
                "            \"identifier\":\"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n" +
                "            \"subTypeName\":\"Account\",\n" +
                "            \"name\":\"Timezone\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-DB-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"db_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-Web-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"web_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-App-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"app_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-User-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"user\"\n" +
                "         }\n" +
                "      ]\n" +
                "   },\n" +
                "   {\n" +
                "      \"identifier\":\"los_2_DR\",\n" +
                "      \"name\":\"LOS-DR-2\",\n" +
                "      \"txnViolationConfigs\":null,\n" +
                "      \"tags\":[\n" +
                "         {\n" +
                "            \"identifier\":\"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n" +
                "            \"subTypeName\":\"Account\",\n" +
                "            \"name\":\"Timezone\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-DB-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"db_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-Web-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"web_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-App-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"app_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-User-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"user\"\n" +
                "         }\n" +
                "      ]\n" +
                "   }\n" +
                "]"
        response.status(200)

        when:
        ResponseObject responseObject = ApplicationService.addApplications(request, response)

        then:
        response.status == 403
        responseObject.getMessage() == "Invalid account identifier."
    }

    def "AddApplications identifier empty"() {

        setup:
        request.headers(AUTHORIZATION) >> KeycloakConnectionManager.getAccessToken()
        request.params(ACCOUNT_IDENTIFIER) >> ""
        request.body() >> "[\n" +
                "   {\n" +
                "      \"identifier\":\"los_1_DR\",\n" +
                "      \"name\":\"LOS-DR-1\",\n" +
                "      \"txnViolationConfigs\":null,\n" +
                "      \"tags\":[\n" +
                "         {\n" +
                "            \"identifier\":\"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n" +
                "            \"subTypeName\":\"Account\",\n" +
                "            \"name\":\"Timezone\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-DB-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"db_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-Web-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"web_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-App-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"app_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-User-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"user\"\n" +
                "         }\n" +
                "      ]\n" +
                "   },\n" +
                "   {\n" +
                "      \"identifier\":\"los_2_DR\",\n" +
                "      \"name\":\"LOS-DR-2\",\n" +
                "      \"txnViolationConfigs\":null,\n" +
                "      \"tags\":[\n" +
                "         {\n" +
                "            \"identifier\":\"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n" +
                "            \"subTypeName\":\"Account\",\n" +
                "            \"name\":\"Timezone\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-DB-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"db_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-Web-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"web_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-App-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"app_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-User-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"user\"\n" +
                "         }\n" +
                "      ]\n" +
                "   }\n" +
                "]"
        response.status(200)

        when:
        ResponseObject responseObject = ApplicationService.addApplications(request, response)

        then:
        response.status == 403
        responseObject.getMessage() == "Invalid account identifier."
    }

    def "AddApplications auth token null"() {

        setup:
        request.headers(AUTHORIZATION) >> null
        request.params(ACCOUNT_IDENTIFIER) >> identifier
        request.body() >> "[\n" +
                "   {\n" +
                "      \"identifier\":\"los_1_DR\",\n" +
                "      \"name\":\"LOS-DR-1\",\n" +
                "      \"txnViolationConfigs\":null,\n" +
                "      \"tags\":[\n" +
                "         {\n" +
                "            \"identifier\":\"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n" +
                "            \"subTypeName\":\"Account\",\n" +
                "            \"name\":\"Timezone\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-DB-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"db_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-Web-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"web_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-App-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"app_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-User-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"user\"\n" +
                "         }\n" +
                "      ]\n" +
                "   },\n" +
                "   {\n" +
                "      \"identifier\":\"los_2_DR\",\n" +
                "      \"name\":\"LOS-DR-2\",\n" +
                "      \"txnViolationConfigs\":null,\n" +
                "      \"tags\":[\n" +
                "         {\n" +
                "            \"identifier\":\"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n" +
                "            \"subTypeName\":\"Account\",\n" +
                "            \"name\":\"Timezone\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-DB-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"db_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-Web-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"web_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-App-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"app_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-User-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"user\"\n" +
                "         }\n" +
                "      ]\n" +
                "   }\n" +
                "]"
        response.status(200)

        when:
        ResponseObject responseObject = ApplicationService.addApplications(request, response)

        then:
        response.status == 403
        responseObject.getMessage() == "Unable to get the username from token. Kindly look into the heal-controlcenter logs."
    }

    def "AddApplications auth token invalid"() {

        setup:
        request.headers(AUTHORIZATION) >> "invalid-token"
        request.params(ACCOUNT_IDENTIFIER) >> identifier
        request.body() >> "[\n" +
                "   {\n" +
                "      \"identifier\":\"los_1_DR\",\n" +
                "      \"name\":\"LOS-DR-1\",\n" +
                "      \"txnViolationConfigs\":null,\n" +
                "      \"tags\":[\n" +
                "         {\n" +
                "            \"identifier\":\"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n" +
                "            \"subTypeName\":\"Account\",\n" +
                "            \"name\":\"Timezone\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-DB-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"db_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-Web-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"web_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-App-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"app_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-User-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"user\"\n" +
                "         }\n" +
                "      ]\n" +
                "   },\n" +
                "   {\n" +
                "      \"identifier\":\"los_2_DR\",\n" +
                "      \"name\":\"LOS-DR-2\",\n" +
                "      \"txnViolationConfigs\":null,\n" +
                "      \"tags\":[\n" +
                "         {\n" +
                "            \"identifier\":\"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n" +
                "            \"subTypeName\":\"Account\",\n" +
                "            \"name\":\"Timezone\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-DB-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"db_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-Web-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"web_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-App-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"app_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-User-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"user\"\n" +
                "         }\n" +
                "      ]\n" +
                "   }\n" +
                "]"
        response.status(200)

        when:
        ResponseObject responseObject = ApplicationService.addApplications(request, response)

        then:
        response.status == 403
        responseObject.getMessage() == "Unable to get the username from token. Kindly look into the heal-controlcenter logs."
    }

    def "AddApplications JSON parse error"() {

        setup:
        request.headers(AUTHORIZATION) >> KeycloakConnectionManager.getAccessToken()
        request.params(ACCOUNT_IDENTIFIER) >> identifier
        request.body() >> "[\n" +
                "      \"identifier\":\"los_1_DR\",\n" +
                "      \"name\":\"LOS-DR-1\",\n" +
                "      \"txnViolationConfigs\":null,\n" +
                "      \"tags\":[\n" +
                "         {\n" +
                "            \"identifier\":\"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n" +
                "            \"subTypeName\":\"Account\",\n" +
                "            \"name\":\"Timezone\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-DB-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"db_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-Web-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"web_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-App-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"app_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-User-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"user\"\n" +
                "         }\n" +
                "      ]\n" +
                "   },\n" +
                "   {\n" +
                "      \"identifier\":\"los_2_DR\",\n" +
                "      \"name\":\"LOS-DR-2\",\n" +
                "      \"txnViolationConfigs\":null,\n" +
                "      \"tags\":[\n" +
                "         {\n" +
                "            \"identifier\":\"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n" +
                "            \"subTypeName\":\"Account\",\n" +
                "            \"name\":\"Timezone\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-DB-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"db_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-Web-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"web_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-App-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"app_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-User-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"user\"\n" +
                "         }\n" +
                "      ]\n" +
                "   }\n" +
                "]"
        response.status(200)

        when:
        ResponseObject responseObject = ApplicationService.addApplications(request, response)

        then:
        response.status == 403
        responseObject.getMessage() == "Error in parsing JSON request."
    }

    def "AddApplications app list empty"() {

        setup:
        request.headers(AUTHORIZATION) >> KeycloakConnectionManager.getAccessToken()
        request.params(ACCOUNT_IDENTIFIER) >> identifier
        request.body() >> "[]"
        response.status(200)

        when:
        ResponseObject responseObject = ApplicationService.addApplications(request, response)

        then:
        response.status == 403
        responseObject.getMessage() == "Received empty list of application objects."
    }

    def "AddApplications duplicate identifier"() {

        setup:
        request.headers(AUTHORIZATION) >> KeycloakConnectionManager.getAccessToken()
        request.params(ACCOUNT_IDENTIFIER) >> identifier
        request.body() >> "[\n" +
                "   {\n" +
                "      \"identifier\":\"los_2_DR\",\n" +
                "      \"name\":\"LOS-DR-1\",\n" +
                "      \"txnViolationConfigs\":null,\n" +
                "      \"tags\":[\n" +
                "         {\n" +
                "            \"identifier\":\"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n" +
                "            \"subTypeName\":\"Account\",\n" +
                "            \"name\":\"Timezone\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-DB-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"db_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-Web-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"web_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-App-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"app_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-User-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"user\"\n" +
                "         }\n" +
                "      ]\n" +
                "   },\n" +
                "   {\n" +
                "      \"identifier\":\"los_2_DR\",\n" +
                "      \"name\":\"LOS-DR-2\",\n" +
                "      \"txnViolationConfigs\":null,\n" +
                "      \"tags\":[\n" +
                "         {\n" +
                "            \"identifier\":\"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n" +
                "            \"subTypeName\":\"Account\",\n" +
                "            \"name\":\"Timezone\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-DB-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"db_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-Web-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"web_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-App-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"app_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-User-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"user\"\n" +
                "         }\n" +
                "      ]\n" +
                "   }\n" +
                "]"
        response.status(200)

        when:
        ResponseObject responseObject = ApplicationService.addApplications(request, response)

        then:
        response.status == 403
        responseObject.getMessage() == "Duplicate Application identifiers are present in JSON Request."
    }

    def "AddApplications duplicate names"() {

        setup:
        request.headers(AUTHORIZATION) >> KeycloakConnectionManager.getAccessToken()
        request.params(ACCOUNT_IDENTIFIER) >> identifier
        request.body() >> "[\n" +
                "   {\n" +
                "      \"identifier\":\"los_1_DR\",\n" +
                "      \"name\":\"LOS-DR-1\",\n" +
                "      \"txnViolationConfigs\":null,\n" +
                "      \"tags\":[\n" +
                "         {\n" +
                "            \"identifier\":\"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n" +
                "            \"subTypeName\":\"Account\",\n" +
                "            \"name\":\"Timezone\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-DB-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"db_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-Web-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"web_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-App-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"app_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-User-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"user\"\n" +
                "         }\n" +
                "      ]\n" +
                "   },\n" +
                "   {\n" +
                "      \"identifier\":\"los_2_DR\",\n" +
                "      \"name\":\"LOS-DR-1\",\n" +
                "      \"txnViolationConfigs\":null,\n" +
                "      \"tags\":[\n" +
                "         {\n" +
                "            \"identifier\":\"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n" +
                "            \"subTypeName\":\"Account\",\n" +
                "            \"name\":\"Timezone\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-DB-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"db_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-Web-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"web_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-App-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"app_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-User-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"user\"\n" +
                "         }\n" +
                "      ]\n" +
                "   }\n" +
                "]"
        response.status(200)

        when:
        ResponseObject responseObject = ApplicationService.addApplications(request, response)

        then:
        response.status == 403
        responseObject.getMessage() == "Duplicate Application names are present in JSON Request."
    }

    def "AddApplications identifier exists"() {

        setup:
        request.headers(AUTHORIZATION) >> KeycloakConnectionManager.getAccessToken()
        request.params(ACCOUNT_IDENTIFIER) >> identifier
        request.body() >> "[\n" +
                "   {\n" +
                "      \"identifier\":\"los_2\",\n" +
                "      \"name\":\"LOS-DR-1\",\n" +
                "      \"txnViolationConfigs\":null,\n" +
                "      \"tags\":[\n" +
                "         {\n" +
                "            \"identifier\":\"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n" +
                "            \"subTypeName\":\"Account\",\n" +
                "            \"name\":\"Timezone\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-DB-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"db_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-Web-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"web_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-App-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"app_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-User-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"user\"\n" +
                "         }\n" +
                "      ]\n" +
                "   },\n" +
                "   {\n" +
                "      \"identifier\":\"los_2_DR\",\n" +
                "      \"name\":\"LOS-DR-2\",\n" +
                "      \"txnViolationConfigs\":null,\n" +
                "      \"tags\":[\n" +
                "         {\n" +
                "            \"identifier\":\"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n" +
                "            \"subTypeName\":\"Account\",\n" +
                "            \"name\":\"Timezone\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-DB-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"db_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-Web-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"web_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-App-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"app_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-User-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"user\"\n" +
                "         }\n" +
                "      ]\n" +
                "   }\n" +
                "]"
        response.status(200)

        when:
        ResponseObject responseObject = ApplicationService.addApplications(request, response)

        then:
        response.status == 403
    }

    def "AddApplications names exists"() {

        setup:
        request.headers(AUTHORIZATION) >> KeycloakConnectionManager.getAccessToken()
        request.params(ACCOUNT_IDENTIFIER) >> identifier
        request.body() >> "[\n" +
                "   {\n" +
                "      \"identifier\":\"los_1_DR\",\n" +
                "      \"name\":\"LOS\",\n" +
                "      \"txnViolationConfigs\":null,\n" +
                "      \"tags\":[\n" +
                "         {\n" +
                "            \"identifier\":\"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n" +
                "            \"subTypeName\":\"Account\",\n" +
                "            \"name\":\"Timezone\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-DB-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"db_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-Web-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"web_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-App-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"app_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-User-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"user\"\n" +
                "         }\n" +
                "      ]\n" +
                "   },\n" +
                "   {\n" +
                "      \"identifier\":\"los_2_DR\",\n" +
                "      \"name\":\"LOS-DR-2\",\n" +
                "      \"txnViolationConfigs\":null,\n" +
                "      \"tags\":[\n" +
                "         {\n" +
                "            \"identifier\":\"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n" +
                "            \"subTypeName\":\"Account\",\n" +
                "            \"name\":\"Timezone\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-DB-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"db_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-Web-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"web_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-App-Service-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"app_service\"\n" +
                "         },\n" +
                "         {\n" +
                "            \"identifier\":\"LOS-User-DR\",\n" +
                "            \"subTypeName\":\"Services\",\n" +
                "            \"name\":\"Controller\",\n" +
                "            \"layer\":\"user\"\n" +
                "         }\n" +
                "      ]\n" +
                "   }\n" +
                "]"
        response.status(200)

        when:
        ResponseObject responseObject = ApplicationService.addApplications(request, response)

        then:
        response.status == 403
    }

}
