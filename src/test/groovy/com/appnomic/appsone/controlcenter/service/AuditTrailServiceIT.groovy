package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class AuditTrailServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }
    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }
    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    @Shared
    Map<String, String> parameters = new HashMap<>()
    @Shared
    Map<String, String[]> queryMapData = new HashMap<>()
    @Shared
    Set<String> header = new HashSet<>()

    String[] t1=["1597240800000"]
    String[] t2=["1597242600000"]
    String[] t3=["159898089889ts7240800000"]
    String[] t4=[""]

    def "get Audit Trail api fail"() {
        given:
        Request req = Spy(InvalidDummyRequest.class)
        Set<String> head = new HashSet<>()
        head.add(Constants.AUTHORIZATION)
        req.headers() >> head.toSet()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        req.params() >> parameters
        req.queryMap() >> queryMapData
        req.body() >> ""

        when:
        AuditTrailService auditTrailService= new AuditTrailService()
        GenericResponse res = auditTrailService.getAuditAuditData(req, response)

        then:
        response.getStatus() == 400
        res.getMessage() == "ServerException : Invalid Authorization Token."
    }

    def "get Audit Trail fail null request"() {
        when:
        AuditTrailService auditTrailService= new AuditTrailService()
        GenericResponse res = auditTrailService.getAuditAuditData(null, response)

        then:
        response.getStatus() == 400
        res.getMessage() == "ClientException : Authorization Token is empty."
    }

    def "get Audit Trail api success"() {
        given:
        Request req = Spy(DummyRequest.class)
        Set<String> headers = new HashSet<>()
        headers.add(Constants.AUTHORIZATION)
        req.headers() >> headers.toSet()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        req.params() >> parameters
        req.queryMap() >> queryMapData
        req.body() >> " "
        when:
        AuditTrailService auditTrailService= new AuditTrailService()
        GenericResponse res = auditTrailService.getAuditAuditData(req, response)

        then:
        response.getStatus() == 200
        res.getData() != null
    }
    def "get Audit Trail api fail empty account identifier"() {
        given:
        Request req = Spy(DummyRequest.class)
        Set<String> headers = new HashSet<>()
        headers.add(Constants.AUTHORIZATION)
        req.headers() >> headers.toSet()
        parameters.put(":identifier", "")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        req.params() >> parameters
        req.queryMap() >> queryMapData
        req.body() >> " "
        when:
        AuditTrailService auditTrailService= new AuditTrailService()
        GenericResponse res = auditTrailService.getAuditAuditData(req, response)

        then:
        response.getStatus() == 400
        res.getMessage() == "ClientException : Account Identifier should not be empty."
    }
    def "get Audit Trail api fail invalid account identifier"() {
        given:
        Request req = Spy(DummyRequest.class)
        Set<String> headers = new HashSet<>()
        headers.add(Constants.AUTHORIZATION)
        req.headers() >> headers.toSet()
        parameters.put(":identifier", "26373")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t2)
        req.params() >> parameters
        req.queryMap() >> queryMapData
        req.body() >> " "
        when:
        AuditTrailService auditTrailService= new AuditTrailService()
        GenericResponse res = auditTrailService.getAuditAuditData(req, response)

        then:
        response.getStatus() == 400
        res.getMessage() == "ServerException : Invalid Account Identifier."
    }
    def "get Audit Trail api fail empty start time"() {
        given:
        Request req = Spy(DummyRequest.class)
        Set<String> headers = new HashSet<>()
        headers.add(Constants.AUTHORIZATION)
        req.headers() >> headers.toSet()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        queryMapData.put("fromTime", t4)
        queryMapData.put("toTime", t2)
        req.params() >> parameters
        req.queryMap() >> queryMapData
        req.body() >> " "
        when:
        AuditTrailService auditTrailService= new AuditTrailService()
        GenericResponse res = auditTrailService.getAuditAuditData(req, response)

        then:
        response.getStatus() == 400
        res.getMessage() == "ClientException : invalid fromTime provided"
    }
    def "get Audit Trail api fail invalid start time"() {
        given:
        Request req = Spy(DummyRequest.class)
        Set<String> headers = new HashSet<>()
        headers.add(Constants.AUTHORIZATION)
        req.headers() >> headers.toSet()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        queryMapData.put("fromTime", t3)
        queryMapData.put("toTime", t2)
        req.params() >> parameters
        req.queryMap() >> queryMapData
        req.body() >> " "
        when:
        AuditTrailService auditTrailService= new AuditTrailService()
        GenericResponse res = auditTrailService.getAuditAuditData(req, response)

        then:
        response.getStatus() == 400
        res.getMessage() == "ClientException : invalid fromTime provided"
    }
    def "get Audit Trail api fail empty end time"() {
        given:
        Request req = Spy(DummyRequest.class)
        Set<String> headers = new HashSet<>()
        headers.add(Constants.AUTHORIZATION)
        req.headers() >> headers.toSet()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t4)
        req.params() >> parameters
        req.queryMap() >> queryMapData
        req.body() >> " "
        when:
        AuditTrailService auditTrailService= new AuditTrailService()
        GenericResponse res = auditTrailService.getAuditAuditData(req, response)

        then:
        response.getStatus() == 400
        res.getMessage() == "ClientException : invalid toTime provided"
    }
    def "get Audit Trail api fail invalid end time"() {
        given:
        Request req = Spy(DummyRequest.class)
        Set<String> headers = new HashSet<>()
        headers.add(Constants.AUTHORIZATION)
        req.headers() >> headers.toSet()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        queryMapData.put("fromTime", t1)
        queryMapData.put("toTime", t3)
        req.params() >> parameters
        req.queryMap() >> queryMapData
        req.body() >> " "
        when:
        AuditTrailService auditTrailService= new AuditTrailService()
        GenericResponse res = auditTrailService.getAuditAuditData(req, response)

        then:
        response.getStatus() == 400
        res.getMessage() == "ClientException : invalid toTime provided"
    }

}
