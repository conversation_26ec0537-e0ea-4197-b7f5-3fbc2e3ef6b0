package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.GetCategory
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class GetAvailabilityCategoriesServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    @Shared
    Set<String> header = new HashSet<>()
    @Shared
    Map<String, String> parameters = new HashMap<>()

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    GetAvailabilityCategoriesService getCategoriesService = new GetAvailabilityCategoriesService()

    def "GetCategories"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER,"d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = getCategoriesService.getAvailabilityCategories(request, response)

        then:
        response.status == 200
        genericResponse.message == UIMessages.SUCCESS_CATEGORIES
        List<GetCategory> categories = genericResponse.data
        categories.size() > 0
        !categories.contains(GetCategory.builder().id(1).name("CPU").workLoad(0).build())
        !categories.contains(GetCategory.builder().id(6).name("Process").workLoad(0).build())
        !categories.contains(GetCategory.builder().id(34).name("DBCache").workLoad(0).build())
    }

    def "GetCategories user invalid"() {

        given:
        InvalidDummyRequest request = Spy(InvalidDummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER,"d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = getCategoriesService.getAvailabilityCategories(request, response)

        then:
        response.status == 400
        genericResponse.message == "ServerException : Invalid Authorization Token."
    }

    def "GetCategories account empty"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER,"")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = getCategoriesService.getAvailabilityCategories(request, response)

        then:
        response.status == 400
        genericResponse.message == "ClientException : Account Identifier should not be empty."
    }

    def "GetCategories identifier invalid"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER,"d681ef13-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = getCategoriesService.getAvailabilityCategories(request, response)

        then:
        response.status == 400
        genericResponse.message == "ServerException : Invalid account id provided."
    }
}
