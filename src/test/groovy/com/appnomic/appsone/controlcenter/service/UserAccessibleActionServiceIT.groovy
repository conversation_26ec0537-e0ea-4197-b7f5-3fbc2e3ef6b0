package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.UserAccessibleActions
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

class UserAccessibleActionServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }


    def "getUserAccessibilityDetails fail"() {
        when:
        UserAccessibleActionService userService=new UserAccessibleActionService()
        GenericResponse res=userService.getUserAccessibilityDetails(null, response)

        then:
        response.status == 400
        res.getMessage()=="Request validation failure. Refer to application logs for more details."
    }

    def  "getUserAccessibilityDetails success"() {
        given:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        response.status(200)

        when:
        UserAccessibleActionService userService=new UserAccessibleActionService()
        GenericResponse<UserAccessibleActions> genericResponse = userService.getUserAccessibilityDetails(request, response)

        then:
        response.status == 200
        genericResponse.data != null
    }

    def "getUserAccessibilityDetails invalid request"() {
        given:
        request.headers("Authorization") >> ""
        response.status(200)

        when:
        UserAccessibleActionService userService=new UserAccessibleActionService()
        GenericResponse res=userService.getUserAccessibilityDetails(request, response)

        then:
        response.status == 400
        res.message=="Request validation failure. Refer to application logs for more details."
    }

    def "getUserAccessibilityDetails invalid user"() {

        given:
        request.headers("Authorization") >> "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        response.status(200)

        when:
        UserAccessibleActionService userService=new UserAccessibleActionService()
        GenericResponse res=userService.getUserAccessibilityDetails(request, response)

        then:
        response.status == 400
        res.message=="User details unavailable."
    }
}
