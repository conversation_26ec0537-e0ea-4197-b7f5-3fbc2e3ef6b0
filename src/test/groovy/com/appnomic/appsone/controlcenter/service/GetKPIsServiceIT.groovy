package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.KpiDetailsPojo
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

import static com.appnomic.appsone.controlcenter.common.Constants.*

class GetKPIsServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    class InvalidTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    class NullTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return null
        }
    }

    class EmptyTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return ""
        }
    }

    Set<String> header = new HashSet<>()
    Map<String, String> parameters = new HashMap<>()
    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    GetKPIsService service = new GetKPIsService()
    String identifier = "d681ef13-d690-4917-jkhg-6c79b-1"

    def "GetKPIs"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getKPIs(request, response)

        then:
        noExceptionThrown()
        response.status == SUCCESS_STATUS_CODE
        res.getMessage() == "KPI details fetched successfully for the account."
        res.getData() != null

        then:
        List<KpiDetailsPojo> data = res.getData()
        !data.isEmpty()
        data.parallelStream().noneMatch({ kpi -> kpi.getComponent() == null })
    }

    def "GetKPIs : Auth token NULL"() {
        setup:
        NullTokenDummyRequest nullDummyRequest = Spy(NullTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        nullDummyRequest.headers() >> header.toSet()
        nullDummyRequest.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        nullDummyRequest.params() >> parameters
        nullDummyRequest.body() >> ""


        when:
        def res = service.getKPIs(nullDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.AUTH_KEY_EMPTY)
    }

    def "GetKPIs : Auth token Empty"() {
        setup:
        EmptyTokenDummyRequest emptyDummyRequest = Spy(EmptyTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        emptyDummyRequest.headers() >> header.toSet()
        emptyDummyRequest.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        emptyDummyRequest.params() >> parameters
        emptyDummyRequest.body() >> ""


        when:
        def res = service.getKPIs(emptyDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.AUTH_KEY_EMPTY)
    }

    def "GetKPIs : Account Identifier NULL"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, null)
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getKPIs(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.ACCOUNT_EMPTY)
    }

    def "GetKPIs : Account Identifier Empty"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, "")
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getKPIs(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.ACCOUNT_EMPTY)
    }

    def "GetKPIs : Auth token Invalid"() {
        setup:
        InvalidTokenDummyRequest invalidDummyRequest = Spy(InvalidTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        invalidDummyRequest.headers() >> header.toSet()
        invalidDummyRequest.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        invalidDummyRequest.params() >> parameters
        invalidDummyRequest.body() >> ""


        when:
        def res = service.getKPIs(invalidDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.AUTH_KEY_INVALID)
    }

    def "GetKPIs : Account Identifier Invalid"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, "invalid-account-identifier")
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getKPIs(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.ACCOUNT_IDENTIFIER_INVALID)
    }

}
