package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.beans.ServiceListPage
import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.StatusResponse
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

class serviceListIT extends Specification {


    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }
    def setupSpec(){
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(false)
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def cleanupSpec(){
        DBTestCache.rollback()
    }
    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def  "valid serviceDetails"() {

        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.queryParams("agentDetailRequired") >> "false"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        

        when:
        GenericResponse genericResponse = ServiceDetails.getServiceListPage(request, response)


        then:
        response.status == 200

        then:
        genericResponse != null && genericResponse.data != null

        then:
        List<ServiceListPage> list = new ArrayList<>(genericResponse.data as List<ServiceListPage>)
        list.size() > 0
    }

    def  "valid serviceDetails with agent details"() {

        setup:

        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.queryParams("agentDetailRequired") >> "true"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        

        when:
        GenericResponse genericResponse = ServiceDetails.getServiceListPage(request, response)


        then:
        response.status == 200

        then:
        genericResponse != null && genericResponse.data != null

        then:
        List<ServiceListPage> list = new ArrayList<>(genericResponse.data as List<ServiceListPage>)
        list.size() > 0
    }

    def "Account identifier not mapped in system: serviceDetails with agent details"() {

        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-12"
        request.queryParams("agentDetailRequired") >> "true"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        

        when:
        GenericResponse genericResponse = ServiceDetails.getServiceListPage(request, response)

        then:
        response.status == 400

        then:
        genericResponse !=null && genericResponse.getData() == null && genericResponse.getMessage() == "Invalid account identifier d681ef13-d690-4917-jkhg-6c79b-12" &&
                genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()

    }
    def "Account identifier null: serviceDetails with agent details"() {

        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-12"
        request.queryParams("agentDetailRequired") >> "true"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        

        when:
        GenericResponse genericResponse = ServiceDetails.getServiceListPage(request, response)

        then:
        response.status == 400

        then:
        genericResponse !=null && genericResponse.getData() == null && genericResponse.getMessage() == "Invalid account identifier d681ef13-d690-4917-jkhg-6c79b-12" &&
                genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()

    }

    def "invalid author user with agent details"() {

        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-3"
        request.queryParams("agentDetailRequired") >> "true"
        request.headers("Authorization") >> "1234567-1234567"

        when:
        GenericResponse genericResponse = ServiceDetails.getServiceListPage(request, response)

        then:
        response.status == 400

        then:
        genericResponse !=null && genericResponse.getData() == null && genericResponse.getMessage() == "Unable to get the username from token. Kindly look into the heal-controlcenter logs." &&
                genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()

    }
    def "agentDetailRequired is false in serviceDetails"() {

        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-3"
        request.queryParams("agentDetailRequired") >> "false"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()

        when:
        GenericResponse genericResponse = ServiceDetails.getServiceListPage(request, response)

        then:
        response.status == 200

        then:
        genericResponse !=null && genericResponse.getMessage() == "" && genericResponse.getResponseStatus() == StatusResponse.SUCCESS.name()

        then:
        def data = (List<ServiceListPage>) genericResponse.getData()
         data != null && data.get(0).getAgentStatus() == null

    }

}
