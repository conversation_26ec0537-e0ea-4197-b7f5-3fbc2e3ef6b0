package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.beans.KeycloakUserBean
import com.appnomic.appsone.controlcenter.beans.UserBean
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.dao.mysql.UserAccessDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.UserDetails
import com.appnomic.appsone.controlcenter.util.CommonUtils
import com.appnomic.appsone.controlcenter.util.StringUtils
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.gson.reflect.TypeToken
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

import static com.appnomic.appsone.controlcenter.common.Constants.*

class UpdateUsersServiceIT extends Specification {

    private static final ObjectMapper OBJECT_MAPPER = CommonUtils.getObjectMapperWithHtmlEncoder()

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            String authToken = KeycloakConnectionManager.getAccessToken()
            if (StringUtils.isEmpty(authToken))
                return "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCS" +
                        "jhzMnVhazFUUWd3In0.eyJleHAiOjE2MjU4MjM1MjEsImlhdCI6MTYyNTgyMDgyMSwianRpIjoiNmFlNGVjMDEtZDI5N" +
                        "i00ZTQ4LWI2NTQtNzY4Y2YxY2M0NGQxIiwiaXNzIjoiaHR0cHM6Ly8xOTIuMTY4LjEzLjQ0Ojg0NDMvYXV0aC9yZWFsb" +
                        "XMvbWFzdGVyIiwic3ViIjoiNzY0MDEyM2EtZmJkZS00ZmU1LTk4MTItNTgxY2QxZTNhOWMxIiwidHlwIjoiQmVhcmVyI" +
                        "iwiYXpwIjoiYWRtaW4tY2xpIiwic2Vzc2lvbl9zdGF0ZSI6IjkxNjBiOWQyLWI5NjctNGZlOS1hZWFmLTI5N2Q5YTBjO" +
                        "GRlNyIsImFjciI6IjEiLCJhbGxvd2VkLW9yaWdpbnMiOlsiKiJdLCJzY29wZSI6InByb2ZpbGUgZW1haWwiLCJlbWFpb" +
                        "F92ZXJpZmllZCI6ZmFsc2UsInByZWZlcnJlZF91c2VybmFtZSI6ImFwcHNvbmVhZG1pbiIsImVtYWlsIjoiYXBwc29uZW" +
                        "FkbWluLmtAYXBwbm9taWMuY29tIn0.FmNEY7oOraBuukI4ORBzK4TOkdRI9LBKQMiSzJhjhFQ7LIOih5SyoioHKfOI_Kh" +
                        "PaklnCTQSTlBSO7teYTo1M48I0AEOjCf7MMGzIxwm78UrPcJlg_VuNcoHIM2XaPiZxUcKZHVIsC33OuX0uPBf2TKt8GyC" +
                        "nQQoC9CiKHT3HKVG1koDbL-wRCL4hG2_LHQq5z3LqO9u_Cq5QUbJvFE2N2fTt6j8bkKIWK0FtC4CsNCKTtB5fX8m7YhfD" +
                        "2pXy0I-7i-B1fZFnM7DE0uFRScvp4CoPQyHZ11UWAdhPdOewffO9FDpEXfqhYyX195NJSDVfqkrR1Kx1XU-VVirxegD8Q"
            return authToken
        }
    }

    class InvalidTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    class NullTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return null
        }
    }

    class EmptyTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return ""
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    @Shared
    Set<String> header = new HashSet<>()
    @Shared
    Map<String, String> parameters = new HashMap<>()

    def service = new UpdateUsersService()

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def checkUserKeycloak(String name) {
        Optional<UserBean> user = UserAccessDataService.getUserDetailsFromKeycloak().parallelStream()
                .filter({ u -> u.getUsername() == name }).findAny()
        if (user.ifPresent()) {
            deleteUserKeycloakIT(user.get().getId())
        }
    }

    def addUserIT(String userName) {
        //checkUserKeycloak(userName)
        DummyRequest request = Spy(DummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"" + userName + "\",\n" +
                "    \"firstName\": \"" + userName + "_user123\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"" + userName + "@test.com\",\n" +
                "    \"contactNumber\": \"************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 2,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "              9,\n" +
                "              14\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)
        UserService.add(request, response)
        UserDetails user = UserService.getUsers(request, response).data.parallelStream()
                .filter({ u -> u.getUserName() == userName }).findAny().orElse(null)

        return user.userId
    }

    def deleteUserIT(String userId) {
        request.params(":userIdentifier") >> userId
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        response.status(200)
        DeleteUserService.deleteUser(request, response)
    }

    def "delete user"() {
        when:
        deleteUserIT("3de51227-0e0f-4001-84a3-41c9eadda494")
        then:
        true
    }

    def addUserKeycloakIT() {
        String user = UUID.randomUUID().toString()
        checkUserKeycloak(user)
        KeycloakConnectionManager.addUser(OBJECT_MAPPER.writeValueAsString(KeycloakUserBean.builder()
                .username(user)
                .firstName("ad").lastName("user")
                .enabled("true").email(user + "@test.com")
                .build()))

        List<UserBean> users = CommonUtils.jsonToObject(KeycloakConnectionManager.getUsers(),
                new TypeToken<List<UserBean>>() {
                }.getType())

        return users.parallelStream().filter({ u -> u.getUsername() == user })
                .findAny().get()
    }

    def deleteUserKeycloakIT(String userId) {
        KeycloakConnectionManager.deleteKeycloakUser(userId)
    }

    def "UpdateUsers update status"() {

        setup:
        String userId1 = addUserIT("user1")
        String userId2 = addUserIT("user2")
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"deleteUsers\": [],\n" +
                "  \"status\": 0,\n" +
                "  \"modifyStatusForUsers\": [\n" +
                "    \"" + userId1 + "\",\n" +
                "    \"" + userId2 + "\"\n" +
                "  ]\n" +
                "}"
        response.status(SUCCESS_STATUS_CODE)

        when:
        def res = service.updateUsers(request, response)

        then:
        response.status == SUCCESS_STATUS_CODE
        res.getData().contains("Status of the users updated successfully.")

        cleanup:
        deleteUserIT(userId1)
        deleteUserIT(userId2)

    }

    def "UpdateUsers delete users"() {

        setup:
        String userId1 = addUserIT("user1")
        String userId2 = addUserIT("user2")
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"deleteUsers\": [\n" +
                "    \"" + userId1 + "\",\n" +
                "    \"" + userId2 + "\"\n" +
                "  ]\n" +
                "}"
        response.status(SUCCESS_STATUS_CODE)

        when:
        def res = service.updateUsers(request, response)

        then:
        response.status == SUCCESS_STATUS_CODE
        res.getData().contains("Users deleted successfully.")

    }

    def "UpdateUsers : Auth token NULL"() {
        setup:
        NullTokenDummyRequest nullDummyRequest = Spy(NullTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        nullDummyRequest.headers() >> header.toSet()
        nullDummyRequest.queryMap() >> new HashMap<>()
        parameters.put(GROUP_ID, 20 as String)
        nullDummyRequest.params() >> parameters
        nullDummyRequest.body() >> "{\n" +
                "  \"deleteUsers\": [\n" +
                "    \"userIdentifier-1\",\n" +
                "    \"userIdentifier-2\",\n" +
                "    \"userIdentifier-3\"\n" +
                "  ],\n" +
                "  \"status\": 1,\n" +
                "  \"modifyStatusForUsers\": [\n" +
                "    \"userIdentifier-4\",\n" +
                "    \"userIdentifier-5\"\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateUsers(nullDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Invalid Authorization Token.")
    }

    def "UpdateUsers : Auth token Empty"() {
        setup:
        EmptyTokenDummyRequest emptyDummyRequest = Spy(EmptyTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        emptyDummyRequest.headers() >> header.toSet()
        emptyDummyRequest.queryMap() >> new HashMap<>()
        emptyDummyRequest.params() >> parameters
        emptyDummyRequest.body() >> "{\n" +
                "  \"deleteUsers\": [\n" +
                "    \"userIdentifier-1\",\n" +
                "    \"userIdentifier-2\",\n" +
                "    \"userIdentifier-3\"\n" +
                "  ],\n" +
                "  \"status\": 1,\n" +
                "  \"modifyStatusForUsers\": [\n" +
                "    \"userIdentifier-4\",\n" +
                "    \"userIdentifier-5\"\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateUsers(emptyDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Invalid Authorization Token.")
    }

    def "UpdateUsers : Auth token Invalid"() {
        setup:
        InvalidTokenDummyRequest invalidDummyRequest = Spy(InvalidTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        invalidDummyRequest.headers() >> header.toSet()
        invalidDummyRequest.queryMap() >> new HashMap<>()
        invalidDummyRequest.params() >> parameters
        invalidDummyRequest.body() >> "{\n" +
                "  \"deleteUsers\": [\n" +
                "    \"userIdentifier-1\",\n" +
                "    \"userIdentifier-2\",\n" +
                "    \"userIdentifier-3\"\n" +
                "  ],\n" +
                "  \"status\": 1,\n" +
                "  \"modifyStatusForUsers\": [\n" +
                "    \"userIdentifier-4\",\n" +
                "    \"userIdentifier-5\"\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateUsers(invalidDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.AUTH_KEY_INVALID)
    }

    def "UpdateUsers : Request body empty"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.updateUsers(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Request body is NULL or empty.")
    }

    def "UpdateUsers : Request body null"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> null


        when:
        def res = service.updateUsers(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Request body is NULL or empty.")
    }

    def "UpdateUsers : Invalid JSON"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"deleteUsers\": [\n" +
                "    \"userIdentifier-1\",\n" +
                "    \"userIdentifier-2\",\n" +
                "    \"userIdentifier-3\"\n" +
                "  ]\n" +
                "  \"status\": 1,\n" +
                "  \"modifyStatusForUsers\": [\n" +
                "    \"userIdentifier-4\",\n" +
                "    \"userIdentifier-5\"\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateUsers(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Invalid JSON.")
    }

    def "UpdateUsers : Invalid request"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"deleteUsers\": [],\n" +
                "  \"status\": 1,\n" +
                "  \"modifyStatusForUsers\": []\n" +
                "}"


        when:
        def res = service.updateUsers(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Request validation failure.")
    }

    def "UpdateUsers : Invalid status"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"deleteUsers\": [\n" +
                "    \"userIdentifier-1\",\n" +
                "    \"userIdentifier-2\",\n" +
                "    \"userIdentifier-3\"\n" +
                "  ],\n" +
                "  \"status\": 4,\n" +
                "  \"modifyStatusForUsers\": [\n" +
                "    \"userIdentifier-4\",\n" +
                "    \"userIdentifier-5\"\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateUsers(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Request validation failure.")
    }

    def "UpdateUsers : Identifier empty in 'deleteUsers'"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"deleteUsers\": [\n" +
                "    \"userIdentifier-1\",\n" +
                "    \" \",\n" +
                "    \"userIdentifier-3\"\n" +
                "  ],\n" +
                "  \"status\": 1,\n" +
                "  \"modifyStatusForUsers\": [\n" +
                "    \"userIdentifier-4\",\n" +
                "    \"userIdentifier-5\"\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateUsers(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Request validation failure.")
    }

    def "UpdateUsers : Identifier NULL in 'modifyUserStatus'"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"deleteUsers\": [\n" +
                "    \"userIdentifier-1\",\n" +
                "    \"userIdentifier-2\",\n" +
                "    \"userIdentifier-3\"\n" +
                "  ],\n" +
                "  \"status\": 0,\n" +
                "  \"modifyStatusForUsers\": [\n" +
                "    \"userIdentifier-4\",\n" +
                "    null\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateUsers(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Request validation failure.")
    }

    def "UpdateUsers : Invalid User Identifier"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"status\": 0,\n" +
                "  \"modifyStatusForUsers\": [\n" +
                "    \"userIdentifier-5\"\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateUsers(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("User profile doesn't not exist.")
    }

}
