package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.beans.KeycloakUserBean
import com.appnomic.appsone.controlcenter.beans.UserBean
import com.appnomic.appsone.controlcenter.businesslogic.UsersBL
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.StatusResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.dao.mysql.UserAccessDataService
import com.appnomic.appsone.controlcenter.dao.mysql.UserDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.UserDetails
import com.appnomic.appsone.controlcenter.pojo.UserInfo
import com.appnomic.appsone.controlcenter.util.CommonUtils
import com.appnomic.appsone.controlcenter.util.StringUtils
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.gson.reflect.TypeToken
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class UserServiceIT extends Specification {

    private static final ObjectMapper OBJECT_MAPPER = CommonUtils.getObjectMapperWithHtmlEncoder()

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            String authToken = KeycloakConnectionManager.getAccessToken()
            if (StringUtils.isEmpty(authToken))
                return "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCS" +
                        "jhzMnVhazFUUWd3In0.eyJleHAiOjE2MjU4MjM1MjEsImlhdCI6MTYyNTgyMDgyMSwianRpIjoiNmFlNGVjMDEtZDI5N" +
                        "i00ZTQ4LWI2NTQtNzY4Y2YxY2M0NGQxIiwiaXNzIjoiaHR0cHM6Ly8xOTIuMTY4LjEzLjQ0Ojg0NDMvYXV0aC9yZWFsb" +
                        "XMvbWFzdGVyIiwic3ViIjoiNzY0MDEyM2EtZmJkZS00ZmU1LTk4MTItNTgxY2QxZTNhOWMxIiwidHlwIjoiQmVhcmVyI" +
                        "iwiYXpwIjoiYWRtaW4tY2xpIiwic2Vzc2lvbl9zdGF0ZSI6IjkxNjBiOWQyLWI5NjctNGZlOS1hZWFmLTI5N2Q5YTBjO" +
                        "GRlNyIsImFjciI6IjEiLCJhbGxvd2VkLW9yaWdpbnMiOlsiKiJdLCJzY29wZSI6InByb2ZpbGUgZW1haWwiLCJlbWFpb" +
                        "F92ZXJpZmllZCI6ZmFsc2UsInByZWZlcnJlZF91c2VybmFtZSI6ImFwcHNvbmVhZG1pbiIsImVtYWlsIjoiYXBwc29uZW" +
                        "FkbWluLmtAYXBwbm9taWMuY29tIn0.FmNEY7oOraBuukI4ORBzK4TOkdRI9LBKQMiSzJhjhFQ7LIOih5SyoioHKfOI_Kh" +
                        "PaklnCTQSTlBSO7teYTo1M48I0AEOjCf7MMGzIxwm78UrPcJlg_VuNcoHIM2XaPiZxUcKZHVIsC33OuX0uPBf2TKt8GyC" +
                        "nQQoC9CiKHT3HKVG1koDbL-wRCL4hG2_LHQq5z3LqO9u_Cq5QUbJvFE2N2fTt6j8bkKIWK0FtC4CsNCKTtB5fX8m7YhfD" +
                        "2pXy0I-7i-B1fZFnM7DE0uFRScvp4CoPQyHZ11UWAdhPdOewffO9FDpEXfqhYyX195NJSDVfqkrR1Kx1XU-VVirxegD8Q"
            return authToken
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    @Shared
    Set<String> header = new HashSet<>()
    @Shared
    Map<String, String> parameters = new HashMap<>()

    def userDataService = new UserDataService()

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def checkUserKeycloak(String name) {
        Optional<UserBean> user = UserAccessDataService.getUserDetailsFromKeycloak().parallelStream()
                .filter({ u -> u.getUsername() == name }).findAny()
        if (user.ifPresent())
        {
            deleteUserKeycloakIT(user.get().getId())
        }
    }

    def addUserIT() {
        String userName = "dummy_user"
        checkUserKeycloak(userName)
        DummyRequest request = Spy(DummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \""+userName+"\",\n" +
                "    \"firstName\": \"add_user123\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 2,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "              9,\n" +
                "              14\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)
        UserService.add(request, response)
        UserDetails user = UserService.getUsers(request, response).data.parallelStream()
                .filter({ u -> u.getUserName() == userName }).findAny().orElse(null)

        return user.userId
    }

    def deleteUserIT(String userId) {
        request.params(":userIdentifier") >> userId
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        response.status(200)
        DeleteUserService.deleteUser(request, response)
    }

    def addUserKeycloakIT() {
        String user = UUID.randomUUID().toString()
        checkUserKeycloak(user)
        KeycloakConnectionManager.addUser(OBJECT_MAPPER.writeValueAsString(KeycloakUserBean.builder()
                .username(user)
                .firstName("ad").lastName("user")
                .enabled("true").email(user+"@test.com")
                .build()))

        List<UserBean> users = CommonUtils.jsonToObject(KeycloakConnectionManager.getUsers(),
                new TypeToken<List<UserBean>>() {
                }.getType())

        return users.parallelStream().filter({ u -> u.getUsername() == user })
                .findAny().get()
    }

    def deleteUserKeycloakIT(String userId) {
        KeycloakConnectionManager.deleteKeycloakUser(userId)
    }

    def "GetUsers"() {

        given:
        Request request = Spy(DummyRequest.class)
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        response.status(200)

        when:
        GenericResponse genericResponse = UserService.getUsers(request, response)

        then:
        response.status == 200
        genericResponse.message == UIMessages.FETCH_USERS_SUCCESS

    }

    def "GetUsers user invalid"() {

        given:
        request.headers("Authorization") >> "abc"
        response.status(200)

        when:
        GenericResponse res = UserService.getUsers(request, response)

        then:
        response.status == 400
        res.message == "Invalid user identifier."
    }

    def "GetRoles"() {

        given:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        response.status(200)

        when:
        GenericResponse genericResponse = UserService.getRoles(request, response)

        then:
        response.status == 200
        genericResponse.message == UIMessages.FETCH_ROLES_SUCCESS
        genericResponse.data.size() == 3
        genericResponse.data.get(0).name == "Admin"
        genericResponse.data.get(1).name == "User Manager"
        genericResponse.data.get(2).name == "User"


    }

    def "GetRoles invalid"() {

        given:
        request.headers("Authorization") >> "jkhuisacgbk"
        response.status(200)

        when:
        GenericResponse res = UserService.getRoles(request, response)

        then:
        response.status == 400
        res.message == "Invalid user identifier."

    }

    def "GetProfilesForRoleId valid"() {

        given:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params(":roleId") >> 2
        response.status(200)

        when:
        GenericResponse genericResponse = UserService.getProfilesForRoleId(request, response)

        then:
        response.status == 200
        genericResponse.message == UIMessages.FETCH_PROFILES_SUCCESS
        genericResponse.data.size() == 2
        genericResponse.data.get(0).id == 2
        genericResponse.data.get(1).name == "Application Owner"

    }

    def "GetProfilesForRoleId roleId inValid 1"() {

        given:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params(":roleId") >> "99"
        response.status(200)

        when:
        GenericResponse res = UserService.getProfilesForRoleId(request, response)

        then:
        response.status == 400
        res.message == "RequestException :: Invalid input provided for 'roleId'."
    }

    def "GetProfilesForRoleId roleId inValid 2"() {

        given:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params(":roleId") >> "abc"
        response.status(200)

        when:
        GenericResponse res = UserService.getProfilesForRoleId(request, response)

        then:
        response.status == 400
        res.message == "RequestException :: Error while converting roleId to integer."
    }

    def "GetProfilesForRoleId roleId null"() {

        given:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params(":roleId") >> null

        when:
        GenericResponse res = UserService.getProfilesForRoleId(request, response)

        then:
        response.status == 400
        res.message == "RequestException :: roleId is null or empty."
    }

    def "GetProfilesForRoleId roleId empty"() {

        given:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params(":roleId") >> ""
        response.status(200)

        when:
        GenericResponse res = UserService.getProfilesForRoleId(request, response)

        then:
        response.status == 400
        res.message == "RequestException :: roleId is null or empty."
    }

    def "GetProfilesForRoleId user inValid"() {

        given:
        request.headers("Authorization") >> "xyz"
        request.params(":roleId") >> "2"
        response.status(200)

        when:
        GenericResponse res = UserService.getProfilesForRoleId(request, response)

        then:
        response.status == 400
        res.message == "Invalid user identifier."
    }

    def "ValidateUsername available"() {

        given:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryParams("name") >> "user_test_hghgjkhjvj"
        response.status(200)

        when:
        GenericResponse genericResponse = UserService.validateUserName(request, response)

        then:
        response.status == 200
        genericResponse.responseStatus == StatusResponse.SUCCESS.name()
        genericResponse.message == "AVAILABLE"

    }

    def "ValidateUsername user inValid"() {

        given:
        request.headers("Authorization") >> "xyz"
        response.status(200)

        when:
        GenericResponse res = UserService.validateUserName(request, response)

        then:
        response.status == 400
        res.message == "Invalid user identifier."

    }

    def "ValidateUsername not available"() {

        given:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryParams("name") >> "appsoneadmin"
        response.status(200)

        when:
        GenericResponse genericResponse = UserService.validateUserName(request, response)

        then:
        response.status == 200
        genericResponse.responseStatus == StatusResponse.FAILURE.name()
        genericResponse.message == "NOT AVAILABLE"

    }

    def "ValidateUsername invalid"() {

        given:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryParams("name") >> " "
        response.status(200)

        when:
        GenericResponse res = UserService.validateUserName(request, response)

        then:
        response.status == 400
        res.message == "RequestException :: name should not be null or empty."

    }

    def "ValidateUsername null"() {

        given:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryParams("name") >> null

        when:
        GenericResponse res = UserService.validateUserName(request, response)

        then:
        response.status == 400
        res.message == "RequestException :: name should not be null or empty."

    }

    def "ValidateUsername ad available"() {

        given:
        UserBean user = addUserKeycloakIT()
        userDataService.updateSetup("AD Integrated")
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryParams("name") >> user.getUsername()
        response.status(200)

        when:
        GenericResponse genericResponse = UserService.validateUserName(request, response)

        then:
        response.status == 200
        genericResponse.responseStatus == StatusResponse.SUCCESS.name()
        genericResponse.message == "AVAILABLE"

        cleanup:
        deleteUserKeycloakIT(user.getId())
        userDataService.updateSetup("Keycloak")

    }

    def "ValidateUsername ad not available"() {

        given:
        userDataService.updateSetup("AD Integrated")
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryParams("name") >> "adtestuser_123"
        response.status(200)

        when:
        GenericResponse genericResponse = UserService.validateUserName(request, response)

        then:
        response.status == 200
        genericResponse.responseStatus == StatusResponse.FAILURE.name()
        genericResponse.message == "NOT AVAILABLE : USER IS NOT PRESENT FOR MAPPING."

        cleanup:
        userDataService.updateSetup("Keycloak")

    }

    def "GetUserDetails valid"() {

        setup:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        String userId = addUserIT()
        request.params(":userIdentifier") >> userId
        response.status(200)

        when:
        GenericResponse genericResponse = UserService.getUserDetails(request, response)

        then:
        response.status == 200
        genericResponse.data.userName == "dummy_user"
        genericResponse.data.emailId == "<EMAIL>"
        genericResponse.data.status == 1
        genericResponse.data.roleId == 4
        genericResponse.data.profileId == 5
        genericResponse.data.accessDetails.get(0).accountId == 2
        genericResponse.data.accessDetails.get(0).applications.get(0).ids.size() == 2
        genericResponse.data.accessDetails.get(1).accountId == 3
        genericResponse.data.accessDetails.get(1).applications.get(0).ids.size() == 1

        cleanup:
        deleteUserIT(userId)

    }

    def "GetUserDetails user invalid"() {

        setup:
        request.headers("Authorization") >> "xyz"
        request.params(":userIdentifier") >> "7640123a-fbde-4fe5-9812-581cd1e3a9c1"
        response.status(200)

        when:
        GenericResponse res = UserService.getUserDetails(request, response)

        then:
        response.status == 400
        res.message == "Invalid user identifier."
    }

    def "GetUserDetails userIdentifier invalid"() {

        setup:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params(":userIdentifier") >> "fbde-4fe5-9812-581cd1e3a9c1"
        response.status(200)

        when:
        GenericResponse res = UserService.getUserDetails(request, response)

        then:
        response.status == 400
        res.message == "RequestException :: User is not available."
    }

    def "GetUserDetails userIdentifier null"() {

        setup:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params(":userIdentifier") >> null
        response.status(200)

        when:
        GenericResponse res = UserService.getUserDetails(request, response)

        then:
        response.status == 400
        res.message == "RequestException :: User is not available."
    }

    def "GetUserDetails userIdentifier empty"() {

        setup:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params(":userIdentifier") >> " "
        response.status(200)

        when:
        GenericResponse res = UserService.getUserDetails(request, response)

        then:
        response.status == 400
        res.message == "RequestException :: User is not available."
    }

    def "GetUserDetails super admin"() {

        setup:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params(":userIdentifier") >> "7640123a-fbde-4fe5-9812-581cd1e3a9c1"
        response.status(200)

        when:
        GenericResponse genericResponse = UserService.getUserDetails(request, response)

        then:
        response.status == 200
        genericResponse.data.userName == "appsoneadmin"
        genericResponse.data.emailId == null
        genericResponse.data.status == 1
        genericResponse.data.roleId == 1
        genericResponse.data.profileId == 1
        genericResponse.data.accessDetails.get(0).accountId == "*"
        genericResponse.data.accessDetails.get(0).applications == null

    }

    def "AddUser app user"() {

        given:
        checkUserKeycloak("test_app_user")
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"test_app_user\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 2,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "              9,\n" +
                "              14\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        UserService.add(request, response)

        then:
        response.status == 200
        UserDetails user = UserService.getUsers(request, response).data.parallelStream()
                .filter({ u -> u.userName == "test_app_user" }).findAny().orElse(null)
        user != null

        cleanup:
        deleteUserIT(user.userId)
    }

    def "AddUser user inValid"() {

        given:

        InvalidDummyRequest request = Spy(InvalidDummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"test_app_user\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 2,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "              9,\n" +
                "              14\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        GenericResponse res = UserService.add(request, response)

        then:
        response.status == 400
        res.message == "ServerException : Error while extracting user details from authorization token"

    }

    def "AddUser request null"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> null
        response.status(200)

        when:
        GenericResponse res = UserService.add(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Invalid request body. Reason: Request body is either NULL or empty."
    }

    def "AddUser request empty"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> " "
        response.status(200)

        when:
        GenericResponse res = UserService.add(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Invalid request body. Reason: Request body is either NULL or empty."
    }

    def "AddUser JSON parse exception"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"test_app_user\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 2\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "              9,\n" +
                "              14\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        GenericResponse res = UserService.add(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Exception encountered while parsing the JSON request body."
    }

    def "AddUser invalid userName"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"appsoneadmin\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 2,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "              9,\n" +
                "              14\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        GenericResponse res = UserService.add(request, response)

        then:
        response.status == 400
        res.message == "ServerException : Unable to validate user name"
    }

    def "AddUser access details null"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"test_app_user\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": null\n" +
                "  }\n"
        response.status(200)

        when:
        GenericResponse res = UserService.add(request, response)

        then:
        response.status == 400
        res.message == "ServerException : User Access Details are not specified."
    }

    def "AddUser access details empty"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"test_app_user\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": []\n" +
                "  }\n"
        response.status(200)

        when:
        GenericResponse res = UserService.add(request, response)

        then:
        response.status == 400
        res.message == "ServerException : User Access Details are not specified."
    }

    def "AddUser invalid access details 1"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"user_app_user\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"**************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 2,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "              9,\n" +
                "              14\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": \"*\",\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        GenericResponse res = UserService.add(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Invalid request. Kindly check the logs."

    }

    def "AddUser invalid access details 2"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"user_app1\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"**************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 2,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"edit\",\n" +
                "            \"ids\": [\n" +
                "              \"*\",\n" +
                "              14\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        GenericResponse res = UserService.add(request, response)

        then:
        response.status == 400
        res.message == "ServerException : Only 'add' action is valid."

    }

    def "AddUser invalid status"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"user_app_user\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"**************\",\n" +
                "    \"status\": 10,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 2,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "              9,\n" +
                "              14\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        GenericResponse res = UserService.add(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Invalid request. Kindly check the logs."

    }

    def "AddUser invalid app"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"user_app2\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"**************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 2,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "              9,\n" +
                "              144\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        GenericResponse res = UserService.add(request, response)

        then:
        response.status == 400
        res.message == "ServerException : Application Id specified is not available for the account : 144"

    }

    def "AddUser invalid role"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"user_app2\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"**************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 9,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 2,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "              9,\n" +
                "              14\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        GenericResponse res = UserService.add(request, response)

        then:
        response.status == 400
        res.message == "ServerException : Role Id requested is not present."

    }

    def "AddUser invalid profile"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"user_app2\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"**************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 2,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 2,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "              9,\n" +
                "              14\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        GenericResponse res = UserService.add(request, response)

        then:
        response.status == 400
        res.message == "ServerException : Profile Id requested is not present for the role specified."

    }

    def "AddUser invalid account"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"user_app2\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"**************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 200,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "              9,\n" +
                "              14\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        GenericResponse res = UserService.add(request, response)

        then:
        response.status == 400
        res.message == "ServerException : Account Id is not available : 200"

    }

    def "AddUser no apps for an account"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"user_app2\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"**************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 2,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": []\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        GenericResponse res = UserService.add(request, response)

        then:
        response.status == 400
        res.message == "ServerException : No applications are present in accessDetails for the account. " +
                "IdPojo(id=2, name=null, identifier=d681ef13-d690-4917-jkhg-6c79b-1)"

    }

    def "AddUser accessDetails empty"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"user_app2\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"**************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": \" \"" +
                "  }\n"
        response.status(200)

        when:
        GenericResponse res = UserService.add(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Exception encountered while parsing the JSON request body."

    }

    def "AddUser user manager"() {

        given:
        checkUserKeycloak("add_user_manager_test1")
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"add_user_manager_test1\",\n" +
                "    \"firstName\": \"add_userManager\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"**************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 3,\n" +
                "    \"profileId\": 4,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": \"*\"\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        UserService.add(request, response)

        then:
        response.status == 200
        UserDetails user = UserService.getUsers(request, response).data.parallelStream()
                .filter({ u -> u.userName == "add_user_manager_test1" }).findAny().orElse(null)
        user != null

        cleanup:
        deleteUserIT(user.userId)

    }

    def "AddUser no contact number"() {

        given:
        checkUserKeycloak("user1231")
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"user1231\",\n" +
                "    \"firstName\": \"add_user\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 3,\n" +
                "    \"profileId\": 4,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": \"*\"\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        UserService.add(request, response)

        then:
        response.status == 200
        UserDetails user = UserService.getUsers(request, response).data.parallelStream()
                .filter({ u -> u.userName == "user1231" }).findAny().orElse(null)
        user != null

        cleanup:
        deleteUserIT(user.userId)

    }

    def "AddUser heal admin"() {

        given:
        checkUserKeycloak("heal_admin_test1")
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"userName\": \"heal_admin_test1\",\n" +
                "  \"firstName\": \"heal_admin_test\",\n" +
                "  \"lastName\": \"test123\",\n" +
                "  \"emailId\": \"<EMAIL>\",\n" +
                "  \"contactNumber\": \"**************\",\n" +
                "  \"status\": 1,\n" +
                "  \"roleId\": 2,\n" +
                "  \"profileId\": 3,\n" +
                "  \"accessDetails\": [\n" +
                "    {\n" +
                "      \"action\": \"add\",\n" +
                "      \"accountId\": \"2\",\n" +
                "      \"applications\": [\n" +
                "        {\n" +
                "          \"action\": \"add\",\n" +
                "          \"ids\": [\n" +
                "            \"*\"\n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\": \"add\",\n" +
                "      \"accountId\": \"3\",\n" +
                "      \"applications\": [\n" +
                "        {\n" +
                "          \"action\": \"add\",\n" +
                "          \"ids\": [\n" +
                "            \"*\"\n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\": \"add\",\n" +
                "      \"accountId\": \"4\",\n" +
                "      \"applications\": [\n" +
                "        {\n" +
                "          \"action\": \"add\",\n" +
                "          \"ids\": [\n" +
                "            \"*\"\n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        UserService.add(request, response)

        then:
        response.status == 200
        UserDetails user = UserService.getUsers(request, response).data.parallelStream()
                .filter({ u -> u.userName == "heal_admin_test1" }).findAny().orElse(null)
        user != null

        cleanup:
        deleteUserIT(user.userId)

    }

    def "AddUser invalid email"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        String userId = addUserIT()
        request.body() >> "{\n" +
                "    \"userName\": \"user-test-invalid-email\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"*************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 2,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "              9,\n" +
                "              14\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        GenericResponse res = UserService.add(request, response)

        then:
        response.status == 400
        res.message == "ServerException : Email Address already exists"

        cleanup:
        deleteUserIT(userId)

    }

    def "AddUser access details account invalid"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"user_app_usertest\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"*************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 27,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "              9,\n" +
                "              14\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        GenericResponse res = UserService.add(request, response)

        then:
        response.status == 400
        res.message == "ServerException : Account Id is not available : 27"
    }

    def "AddUser invalid no apps"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"user_app_usertest\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"*************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 2,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        GenericResponse res = UserService.add(request, response)

        then:
        response.status == 400
        res.message == "ServerException : No applications are present in accessDetails for the account. " +
                "IdPojo(id=2, name=null, identifier=d681ef13-d690-4917-jkhg-6c79b-1)"

    }

    def "AddUser access details app invalid"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"user_app_usertest\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"*************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 2,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "              9564,\n" +
                "              14\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        GenericResponse res = UserService.add(request, response)

        then:
        response.status == 400
        res.message == "ServerException : Application Id specified is not available for the account : 9564"
    }

    def "EditUser user invalid"() {

        given:
        InvalidDummyRequest request = Spy(InvalidDummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":userIdentifier","8e38821d-e0de-4bc6-8128-f7d86d87270e")
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"add_user_test\",\n" +
                "    \"firstName\": \"User1EditUserTest\",\n" +
                "    \"lastName\": \"\",\n" +
                "    \"contactNumber\": \"\",\n" +
                "    \"status\": 0,\n" +
                "    \"roleId\": 44,\n" +
                "    \"profileId\": 5,\n" +
                "    \"profileChange\": 0,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"edit\",\n" +
                "        \"accountId\": 2,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"delete\",\n" +
                "            \"ids\": [\n" +
                "              9\n" +
                "            ]\n" +
                "          },\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "              14\n" +
                "            ]\n" +
                "          }\n" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"delete\",\n" +
                "        \"accountId\": 4\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        GenericResponse res = UserService.update(request, response)

        then:
        response.status == 400
        res.message == "ServerException : Error while extracting user details from authorization token"

    }

    def "EditUser request null"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":userIdentifier","8e38821d-e0de-4bc6-8128-f7d86d87270e")
        request.params() >> parameters
        request.body() >> null
        response.status(200)

        when:
        GenericResponse res = UserService.update(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Invalid request body. Reason: Request body is either NULL or empty."

    }

    def "EditUser userIdentifier invalid"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":userIdentifier","user-invalid-identifier")
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"add_user_test\",\n" +
                "    \"firstName\": \"User1EditUserTest\",\n" +
                "    \"lastName\": \"\",\n" +
                "    \"contactNumber\": \"************\",\n" +
                "    \"status\": 0,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"profileChange\": 0,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"edit\",\n" +
                "        \"accountId\": 2,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"delete\",\n" +
                "            \"ids\": [\n" +
                "              9\n" +
                "            ]\n" +
                "          },\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "              14\n" +
                "            ]\n" +
                "          }\n" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"delete\",\n" +
                "        \"accountId\": 4\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        GenericResponse res = UserService.update(request, response)

        then:
        response.status == 400
        res.message == "ServerException : Invalid userRequest identifier provided."

    }

    def "EditUser status"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        String userId = addUserIT()
        parameters.put(":userIdentifier",userId)
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"dummy_user\",\n" +
                "    \"status\": 0\n" +
                "  }\n"
        response.status(200)

        when:
        UserService.update(request,response)

        then:
        response.status == 200
        UserDetails user = UserService.getUsers(request, response).data.parallelStream()
                .filter({ u -> u.getUserName() == "dummy_user" }).findAny().orElse(null)
        user != null
        user.status == 0

        cleanup:
        deleteUserIT(userId)
    }

    def "EditUser valid"() {
        UsersBL usersBL = new UsersBL();

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        String userId = addUserIT()
        parameters.put(":userIdentifier",userId)
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"dummy_user\",\n" +
                "    \"firstName\": \"edit_user\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"*************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"edit\",\n" +
                "        \"accountId\": 2,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"delete\",\n" +
                "            \"ids\": [\n" +
                "              14\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"delete\",\n" +
                "        \"accountId\": 3\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 4,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "              28\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        UserService.update(request,response)

        then:
        response.status == 200
        UserInfo details = usersBL.getUserDetails(userId)
        details.userName == "dummy_user"
        details.firstName == "edit_user"
        details.emailId == "<EMAIL>"
        details.status == 1
        details.roleId == 4
        details.profileId == 5
        details.accessDetails.size() == 2
        details.accessDetails.get(0).accountId == 2
        details.accessDetails.get(0).applications.get(0).ids.size() == 1
        details.accessDetails.get(1).accountId == 4
        details.accessDetails.get(1).applications.get(0).ids.size() == 1


        cleanup:
        deleteUserIT(userId)
    }

    def "EditUser profile"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        String userId = addUserIT()
        parameters.put(":userIdentifier",userId)
        request.params() >> parameters
        request.body() >> "{\n" +
                "    \"userName\": \"dummy_user\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 3,\n" +
                "    \"profileId\": 4,\n" +
                "    \"profileChange\": 1,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": \"*\"\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        UserService.update(request,response)

        then:
        response.status == 200
        UserDetails user = UserService.getUsers(request, response).data.parallelStream()
                .filter({ u -> u.getUserName() == "dummy_user" }).findAny().orElse(null)
        user != null
        user.role == 'User Manager'
        user.userProfile == 'User Manager'

        cleanup:
        deleteUserIT(userId)
    }

    def "AddUser ad user manager"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        userDataService.updateSetup("AD Integrated")
        UserBean user = addUserKeycloakIT()
        request.body() >> "{\n" +
                "    \"userName\": \""+user.getUsername()+"\",\n" +
                "    \"firstName\": \"ad_userManager\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"**************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 3,\n" +
                "    \"profileId\": 4,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": \"*\"\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        UserService.add(request, response)

        then:
        response.status == 200
        UserDetails userAdded = UserService.getUsers(request, response).data.parallelStream()
                .filter({ u -> u.userName == user.getUsername() }).findAny().orElse(null)
        userAdded != null

        cleanup:
        userDataService.updateSetup("Keycloak")
        deleteUserIT(userAdded.getUserId())

    }

    def "AddUser ad app user"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        userDataService.updateSetup("AD Integrated")
        UserBean user = addUserKeycloakIT()
        request.body() >> "{\n" +
                "    \"userName\": \""+user.getUsername()+"\",\n" +
                "    \"firstName\": \"add_user7\",\n" +
                "    \"lastName\": \"test123\",\n" +
                "    \"emailId\": \"<EMAIL>\",\n" +
                "    \"contactNumber\": \"************\",\n" +
                "    \"status\": 1,\n" +
                "    \"roleId\": 4,\n" +
                "    \"profileId\": 5,\n" +
                "    \"accessDetails\": [\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 2,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\n" +
                "              9,\n" +
                "              14\n" +
                "            ]\n" +
                "          }" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"action\": \"add\",\n" +
                "        \"accountId\": 3,\n" +
                "        \"applications\": [\n" +
                "          {\n" +
                "            \"action\": \"add\",\n" +
                "            \"ids\": [\"*\"]\n" +
                "          }\n" +
                "        ]\n" +
                "      }" +
                "    ]\n" +
                "  }\n"
        response.status(200)

        when:
        UserService.add(request, response)

        then:
        response.status == 200
        UserDetails userAdded = UserService.getUsers(request, response).data.parallelStream()
                .filter({ u -> u.userName == user.getUsername() }).findAny().orElse(null)
        userAdded != null

        cleanup:
        userDataService.updateSetup("Keycloak")
        deleteUserIT(userAdded.getUserId())
    }

}