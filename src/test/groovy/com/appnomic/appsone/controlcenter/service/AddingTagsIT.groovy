package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.StatusResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

class AddingTagsIT extends Specification {
    class DummyResponse extends Response {
        int status

        @Override
        void status(int i) {
            status = i
        }

        @Override
        int status() {
            return this.status
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(false)
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }
    def cleanupSpec(){
        DBTestCache.rollback()
    }

    def "adding tags with tag value 1"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.queryParams("tagName") >> "entrypoint"

        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.requestMethod() >> "POST"
        request.body() >> "[{\n" +
                "\"whomToTag\": \"service\"\n," +
                "\"referenceId\": \"NB-Web-Service\"\n," +
                "\"tagValue\": \"1\"\n" +
                "}]"
        
        when:
        GenericResponse genericResponse = new TaggingService().addTagging(request, response)

        then:
        genericResponse.responseStatus == StatusResponse.SUCCESS.name()
        genericResponse != null

        cleanup:
        Request req = Spy(Request.class)
        req.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        req.queryParams("tagName") >> "entrypoint"
        req.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        req.requestMethod() >> "DELETE"
        req.body() >> "[{\n" +
                "\"whomToTag\": \"service\"\n," +
                "\"referenceId\": \"NB-Web-Service\"\n," +
                "\"tagValue\": \"0\"\n" +
                "}]"

        new TaggingService().deleteTagging(req, response)
    }

    def "adding tags with tag value 0"() {
        setup:
        Request req = Spy(Request.class)
        req.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        req.queryParams("tagName") >> "entrypoint"
        req.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        req.requestMethod() >> "POST"
        req.body() >> "[{\n" +
                "\"whomToTag\": \"service\"\n," +
                "\"referenceId\": \"NB-Web-Service\"\n," +
                "\"tagValue\": \"1\"\n" +
                "}]"
        new TaggingService().addTagging(req, response)

        when:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.queryParams("tagName") >> "entrypoint"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.requestMethod() >> "DELETE"
        request.body() >> "[{\n" +
                "\"whomToTag\": \"service\"\n," +
                "\"referenceId\": \"NB-Web-Service\"\n," +
                "\"tagValue\": \"0\"\n" +
                "}]"
        

        GenericResponse genericResponse = new TaggingService().deleteTagging(request, response)

        then:
        genericResponse.responseStatus == StatusResponse.SUCCESS.name()
        genericResponse != null
    }

    def "adding tags invalid tagName"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.queryParams("tagName") >> "entrypoint1"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "[{\n" +
                "\"referenceId\": \"NB-User\"\n," +
                "\"tagValue\": \"1\"\n" +
                "}]"

        when:
        GenericResponse genericResponse = new TaggingService().addTagging(request, response)

        then:
        response.status == 400

        then:
        genericResponse != null && genericResponse.responseStatus.equalsIgnoreCase("Failure")
    }
    def "adding tags with null request"() {
        when:
        GenericResponse genericResponse = new TaggingService().addTagging(null, response)

        then:
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "Request or request body cannot be NULL or empty."
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
    }
    def "adding tags with invalid account request"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d60-4917-jkhg-6c79b-1"
        request.queryParams("tagName") >> "entrypoint1"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "[{\n" +
                "\"referenceId\": \"NB-User\"\n," +
                "\"tagValue\": \"1\"\n" +
                "}]"
        when:
        GenericResponse genericResponse = new TaggingService().addTagging(request, response)

        then:
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == UIMessages.INVALID_ACCOUNT_MESSAGE
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
    }
    def "adding tags with invalid user token request"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.queryParams("tagName") >> "entrypoint1"
        request.headers("Authorization") >> "*********-**********"
        request.body() >> "[{\n" +
                "\"referenceId\": \"NB-User\"\n," +
                "\"tagValue\": \"1\"\n" +
                "}]"
        when:
        GenericResponse genericResponse = new TaggingService().addTagging(request, response)

        then:
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "Invalid user identifier."
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
    }

    def "adding tags with invalid tag fields"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.queryParams("tagName") >> "entrypoint"

        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.requestMethod() >> "POST"
        request.body() >> "[{\n" +
                "\"whomToTags\": \"service\"\n," +
                "\"referenceId\": \"NB-Web-Service\"\n," +
                "\"tagValue\": \"1\"\n" +
                "}]"
        
        when:
        GenericResponse genericResponse = new TaggingService().addTagging(request, response)

        then:
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "Error while converting request object into tag object."
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()

    }
    def "adding tags with invalid tag value when request method is POST"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.queryParams("tagName") >> "entrypoint"

        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.requestMethod() >> "POST"
        request.body() >> "[{\n" +
                "\"whomToTag\": \"service\"\n," +
                "\"referenceId\": \"NB-Web-Service\"\n," +
                "\"tagValue\": \"0\"\n" +
                "}]"
        
        when:
        GenericResponse genericResponse = new TaggingService().addTagging(request, response)

        then:
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "Trying to delete an entry point in POST request"
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()

    }
    def "adding tags with invalid tag value when request method is DELETE"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.queryParams("tagName") >> "entrypoint"

        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.requestMethod() >> "DELETE"
        request.body() >> "[{\n" +
                "\"whomToTag\": \"service\"\n," +
                "\"referenceId\": \"NB-Web-Service\"\n," +
                "\"tagValue\": \"1\"\n" +
                "}]"

        when:
        GenericResponse genericResponse = new TaggingService().addTagging(request, response)

        then:
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "Trying to add an entry point in DELETE request"
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()

    }
    def "deleting tags with null request"() {
        when:
        GenericResponse genericResponse = new TaggingService().deleteTagging(null, response)

        then:
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "Request or request body cannot be NULL or empty."
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
    }
    def "deleting tags with invalid account request"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d60-4917-jkhg-6c79b-1"
        request.queryParams("tagName") >> "entrypoint1"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "[{\n" +
                "\"referenceId\": \"NB-User\"\n," +
                "\"tagValue\": \"1\"\n" +
                "}]"
        when:
        GenericResponse genericResponse = new TaggingService().deleteTagging(request, response)

        then:
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == UIMessages.INVALID_ACCOUNT_MESSAGE
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
    }
    def "deleting tags with invalid user token request"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.queryParams("tagName") >> "entrypoint1"
        request.headers("Authorization") >> "*********-**********"
        request.body() >> "[{\n" +
                "\"referenceId\": \"NB-User\"\n," +
                "\"tagValue\": \"1\"\n" +
                "}]"
        when:
        GenericResponse genericResponse = new TaggingService().deleteTagging(request, response)

        then:
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "Invalid user identifier."
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
    }

    def "deleting tags with invalid tag fields"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.queryParams("tagName") >> "entrypoint"

        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.requestMethod() >> "POST"
        request.body() >> "[{\n" +
                "\"whomToTags\": \"service\"\n," +
                "\"referenceId\": \"NB-Web-Service\"\n," +
                "\"tagValue\": \"1\"\n" +
                "}]"

        when:
        GenericResponse genericResponse = new TaggingService().deleteTagging(request, response)

        then:
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "Error while converting request object into tag object."
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()

    }
}
