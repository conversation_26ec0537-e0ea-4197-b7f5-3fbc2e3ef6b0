package com.appnomic.appsone.controlcenter.service


import spock.lang.Specification

class AddInstanceLevelKpiThresholdServiceIT extends Specification {

/*    abstract class DummyHttpServletRequest implements HttpServletRequest {
        @Override
        Map<String, String[]> getParameterMap() {
            return new HashMap<String, String[]>()
        }

        @Override
        Enumeration<String> getHeaderNames() {
            return new Hashtable<String, String>().keys()
        }

    }

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def "Server validation failure - invalid userId"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> "Dummy"
        request.body() >> "{\"instances\" : [\"26\", \"27\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"sshd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]}"
        when:
        GenericResponse res = new AddInstanceLevelKpiThresholdService().addThresholds(request, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Error while extracting user details from authorization token"
    }

    def "Server validation failure - invalid accountIdentifier"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.ACCOUNT_IDENTIFIER, "dummy")
        params.put(Constants.INSTANCE_ID, "26")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\"instances\" : [\"26\", \"27\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"sshd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]}"
        when:
        GenericResponse res = new AddInstanceLevelKpiThresholdService().addThresholds(request, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Account identifier is invalid"
    }

    def "Server validation failure - invalid instanceId"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(Constants.INSTANCE_ID, "26662")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\"instances\" : [\"26662\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"sshd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]}"
        when:
        GenericResponse res = new AddInstanceLevelKpiThresholdService().addThresholds(request, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Invalid instanceId provided"
    }

    def "Server validation failure - invalid kpiId"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 3223,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"sshd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]}"
        when:
        GenericResponse res = new AddInstanceLevelKpiThresholdService().addThresholds(request, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Kpi with ID [3223] is unavailable"
    }

    def "Server validation failure - invalid groupKpiId"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4004,\n" +
                "    \"attributeValue\": \"sshd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]}"
        when:
        GenericResponse res = new AddInstanceLevelKpiThresholdService().addThresholds(request, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Group Kpi with ID [4004] is not mapped to Kpi with ID [32]"
    }

    def "Server validation failure - invalid groupKpi-compInstance mapping"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 5,\n" +
                "    \"attributeValue\": \"sshd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]}"
        when:
        GenericResponse res = new AddInstanceLevelKpiThresholdService().addThresholds(request, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Group Kpi with ID [5] is not mapped to Kpi with ID [32]"
    }

    def "Server validation failure - invalid kpi-compInstance mapping"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 10,\n" +
                "    \"groupKpiId\": 0,\n" +
                "    \"attributeValue\": \"ALL\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]}"
        when:
        GenericResponse res = new AddInstanceLevelKpiThresholdService().addThresholds(request, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Non-group KPI [10] provided are not mapped to compInstanceId [26], accountId [2]"
    }

    def "Server validation failure - invalid attribute - compInstance mapping"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"ALL\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]}"
        when:
        GenericResponse res = new AddInstanceLevelKpiThresholdService().addThresholds(request, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Group KPI [4] with attribute [ALL] is not mapped to compInstanceId [26], accountId [2]"
    }

    def "Success case"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"sshd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]}"
        when:
        GenericResponse res = new AddInstanceLevelKpiThresholdService().addThresholds(request, response)
        then:
        res.getResponseStatus() == "SUCCESS"
        res.getData() == "Attribute thresholds added successfully"
        List<InstanceKpiAttributeThresholdBean> existingThresholdBeans = new KPIDataService().fetchCompInstanceKpiAttrThresholds(26, null)
        existingThresholdBeans.size() == 1
        existingThresholdBeans.get(0).attributeValue == "sshd"
        existingThresholdBeans.get(0).minThreshold == 2
        existingThresholdBeans.get(0).maxThreshold == 3
        existingThresholdBeans.get(0).operationId == 150
        existingThresholdBeans.get(0).kpiId == 32
        existingThresholdBeans.get(0).kpiGroupId == 4

        cleanup:
        new KPIDataService().deleteCompInstanceThresholdDetails(26)
    }

    def "Success case - Multiple thresholds with 0 as max threshold"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\"instances\" : [\"26\", \"25\"],\"thresholds\" : [\n" +
                "   {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"httpd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 3,\n" +
                "   \"maxThreshold\" : 0,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]}"
        when:
        GenericResponse res = new AddInstanceLevelKpiThresholdService().addThresholds(request, response)
        then:
        res.getResponseStatus() == "SUCCESS"
        res.getData() == "Attribute thresholds added successfully"
        List<InstanceKpiAttributeThresholdBean> existingThresholdBeans = new KPIDataService().fetchCompInstanceKpiAttrThresholds(26, null)
        existingThresholdBeans.size() == 1
        existingThresholdBeans.get(0).attributeValue == "httpd"
        existingThresholdBeans.get(0).minThreshold == 3
        existingThresholdBeans.get(0).maxThreshold == 0
        existingThresholdBeans.get(0).operationId == 150
        existingThresholdBeans.get(0).kpiId == 32
        existingThresholdBeans.get(0).kpiGroupId == 4

        cleanup:
        new KPIDataService().deleteCompInstanceThresholdDetails(25)
        new KPIDataService().deleteCompInstanceThresholdDetails(26)
    }

    def "Success case - Multiple thresholds"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"sshd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  },\n" +
                "   {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"httpd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 3,\n" +
                "   \"maxThreshold\" : 4,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  },\n" +
                "   {\n" +
                "    \"kpiId\": 1,\n" +
                "    \"groupKpiId\": 0,\n" +
                "    \"attributeValue\": \"ALL\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 3,\n" +
                "   \"maxThreshold\" : 0,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]}"
        when:
        GenericResponse res = new AddInstanceLevelKpiThresholdService().addThresholds(request, response)
        then:
        res.getResponseStatus() == "SUCCESS"
        res.getData() == "Attribute thresholds added successfully"
        List<InstanceKpiAttributeThresholdBean> existingThresholdBeans = new KPIDataService().fetchCompInstanceKpiAttrThresholds(26, null)
        existingThresholdBeans.size() == 3
        existingThresholdBeans.get(0).attributeValue == "sshd"
        existingThresholdBeans.get(0).minThreshold == 2
        existingThresholdBeans.get(0).maxThreshold == 3
        existingThresholdBeans.get(0).operationId == 150
        existingThresholdBeans.get(0).kpiId == 32
        existingThresholdBeans.get(0).kpiGroupId == 4
        existingThresholdBeans.get(1).attributeValue == "httpd"
        existingThresholdBeans.get(1).minThreshold == 3
        existingThresholdBeans.get(1).maxThreshold == 4
        existingThresholdBeans.get(1).operationId == 150
        existingThresholdBeans.get(1).kpiId == 32
        existingThresholdBeans.get(1).kpiGroupId == 4
        existingThresholdBeans.get(2).attributeValue == "ALL"
        existingThresholdBeans.get(2).minThreshold == 3
        existingThresholdBeans.get(2).maxThreshold == 0
        existingThresholdBeans.get(2).operationId == 150
        existingThresholdBeans.get(2).kpiId == 1
        existingThresholdBeans.get(2).kpiGroupId == 0

        cleanup:
        new KPIDataService().deleteCompInstanceThresholdDetails(26)
    }

    def "Server validation failure - Attribute thresholds already exist"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"sshd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]}"
        new AddInstanceLevelKpiThresholdService().addThresholds(request, response)

        when:
        GenericResponse res = new AddInstanceLevelKpiThresholdService().addThresholds(request, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Thresholds for KPI [32] and attribute [sshd] already exist for instanceId [26]"

        cleanup:
        new KPIDataService().deleteCompInstanceThresholdDetails(26)
    }*/
}
