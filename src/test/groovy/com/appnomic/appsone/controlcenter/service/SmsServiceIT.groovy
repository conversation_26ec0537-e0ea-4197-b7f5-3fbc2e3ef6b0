package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.businesslogic.CreateSmsBL
import com.appnomic.appsone.controlcenter.businesslogic.GetSmsBL
import com.appnomic.appsone.controlcenter.businesslogic.UpdateSmsBL
import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.StatusResponse
import com.appnomic.appsone.controlcenter.dao.mysql.NotificationDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.SmsDetails
import com.appnomic.appsone.controlcenter.pojo.SmsParameter
import com.appnomic.appsone.controlcenter.util.ValidationUtils
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class SmsServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    @Shared
    Map<String, String> parameters = new HashMap<>()
    @Shared
    Set<String> header = new HashSet<>()
    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(false)
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }
    def cleanup(){
        DBTestCache.rollback()
    }

    def "add SMS api success for HTTP protocol type"() {
        setup:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "    ,{\n" +
                "      \"parameterValue\": \"{Message}\",\n" +
                "      \"parameterName\": \"smsContent\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n"+
                "  ]\n" +
                "}"
        when:
        def genericResponse = new SmsService().addSMSConfiguration(request, response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.SUCCESS.name()
        response.getStatus() == 200
        cleanup:
        new NotificationDataService().deleteSmsDetailsAndParameters(2)
    }

    def "add SMS api failure: Invalid placeholder name in parameter value"() {
        setup:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MbileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        def genericResponse = new SmsService().addSMSConfiguration(request, response)
        then:
        response.getStatus() == 400
        genericResponse.getMessage() == "Error while adding SMS configuration"
    }
    def " Create SMS Server validation failure due to invalid account identifier"() {
        setup:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        def genericResponse = new SmsService().addSMSConfiguration(request, response)
        then:
        response.getStatus() == 400
        genericResponse.getMessage() == "Error while adding SMS configuration"
    }
    def " Create SMS Server validation failure due to invalid user token"() {
        setup:
        InvalidDummyRequest invalidRequest = Spy(InvalidDummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        invalidRequest.headers() >> header.toSet()
        invalidRequest.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        invalidRequest.params() >> parameters
        invalidRequest.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        def genericResponse = new SmsService().addSMSConfiguration(invalidRequest, response)
        then:
        response.getStatus() == 400
        genericResponse.getMessage() == "Error while adding SMS configuration"
    }

    def "add SMS api failure due to account already exists"() {
        setup:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "  ]\n" +
                "}"
        new SmsService().addSMSConfiguration(request, response)
        when:
        def genericResponse = new SmsService().addSMSConfiguration(request, response)
        then:
        response.getStatus() == 400
        genericResponse.getMessage() == "Error while adding SMS configuration"
        cleanup:
        new NotificationDataService().deleteSmsDetailsAndParameters(2)
    }
    def "add SMS api failure due to invalid protocol name "() {
        setup:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-2")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTPSS\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        def genericResponse = new SmsService().addSMSConfiguration(request, response)
        then:
        response.getStatus() == 400
        genericResponse.getMessage() == "Error while adding SMS configuration"
    }
    def "add SMS api failure due to invalid http method "() {
        setup:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-2")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GETS\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        def genericResponse = new SmsService().addSMSConfiguration(request, response)
        then:
        response.getStatus() == 400
        genericResponse.getMessage() == "Error while adding SMS configuration"
    }
    def "add SMS api failure due to invalid sms parameter type "() {
        setup:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-2")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParamter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        def genericResponse = new SmsService().addSMSConfiguration(request, response)
        then:
        response.getStatus() == 400
        genericResponse.getMessage() == "Error while adding SMS configuration"
    }

    /* Update Sms test cases */
    def "Update SMS api success"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add(Constants.AUTHORIZATION)

        Request req = Spy(DummyRequest.class)
        req.headers() >> headers.toSet()
        req.queryMap() >> new HashMap<>()

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        req.params() >> params
        req.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "    ,{\n" +
                "      \"parameterValue\": \"{Message}\",\n" +
                "      \"parameterName\": \"smsContent\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n"+
                "  ]\n" +
                "}"
        new SmsService().addSMSConfiguration(req, response)

        def smsDetails = new GetSmsBL().process(2)
        def smsParameterIdForUpdate = smsDetails.getParameters().get(0).getParameterId()
        def smsParameterIdForDelete = smsDetails.getParameters().get(1).getParameterId()
        /*def parameterIdString = */
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterId\":"+ smsParameterIdForUpdate+",\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"8840669978\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"edit\" \n" +
                "    },\n" +
                "    {\n" +
                "      \"parameterId\":"+ smsParameterIdForDelete+",\n" +
                "      \"parameterValue\": \"{Message}\",\n" +
                "      \"parameterName\": \"smsContent\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"delete\" \n" +
                "    },\n" +
                "    {\n" +
                "      \"parameterValue\": \"{Message}\",\n" +
                "      \"parameterName\": \"smsContentName\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": false,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n"+
                "  ]\n" +
                "}"
        when:
        def genericResponse = new SmsService().updateSmsConfigurations(request, response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.SUCCESS.name()
        response.getStatus() == 200
        cleanup:
        new NotificationDataService().deleteSmsDetailsAndParameters(2)
    }

    def "Update SMS api failure: Invalid placeholder name in parameter value"() {
        setup:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-2")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MbileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        def genericResponse = new SmsService().updateSmsConfigurations(request, response)
        then:
        response.getStatus() == 400
        genericResponse.getMessage() == "Error while updating SMS configuration"
    }
    def " Update SMS Server validation failure due to invalid account identifier"() {
        setup:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        def genericResponse = new SmsService().updateSmsConfigurations(request, response)
        then:
        response.getStatus() == 400
        genericResponse.getMessage() == "Error while updating SMS configuration"
    }
    def " Update SMS Server validation failure due to invalid user token"() {
        setup:
        InvalidDummyRequest invalidRequest = Spy(InvalidDummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        invalidRequest.headers() >> header.toSet()
        invalidRequest.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        invalidRequest.params() >> parameters
        invalidRequest.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        def genericResponse = new SmsService().updateSmsConfigurations(invalidRequest, response)
        then:
        response.getStatus() == 400
        genericResponse.getMessage() == "Error while updating SMS configuration"
    }
    def "Update SMS api failure due to account not exists"() {
        setup:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-2")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        def genericResponse = new SmsService().updateSmsConfigurations(request, response)
        then:
        response.getStatus() == 400
        genericResponse.getMessage() == "Error while updating SMS configuration"
    }
    def "Update SMS api failure due to invalid protocol name "() {
        setup:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTPSS\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        def genericResponse = new SmsService().updateSmsConfigurations(request, response)
        then:
        response.getStatus() == 400
        genericResponse.getMessage() == "Error while updating SMS configuration"
    }
    def "Update SMS api failure due to invalid http method "() {
        setup:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GETS\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        def genericResponse = new SmsService().updateSmsConfigurations(request, response)
        then:
        response.getStatus() == 400
        genericResponse.getMessage() == "Error while updating SMS configuration"
    }
    def "Update SMS api failure due to invalid sms parameter type "() {
        setup:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParamter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        def genericResponse = new SmsService().updateSmsConfigurations(request, response)
        then:
        response.getStatus() == 400
        genericResponse.getMessage() == "Error while updating SMS configuration"
    }
    def "Create SMS process server validation success for TCP "(){
        given:
        def list = new ArrayList<SmsParameter>()
        def smsParameter = new SmsParameter()
        smsParameter.setAction("add")
        smsParameter.setParameterName("testParam")
        smsParameter.setParameterValue("mobile")
        smsParameter.setIsPlaceholder(false)
        smsParameter.setParameterType("QueryParameter")
        list.add(smsParameter)
        def smsDetails = new SmsDetails()
        smsDetails.setAddress("appnomic.com")
        smsDetails.setCountryCode("IND")
        smsDetails.setProtocolName("TCP")
        smsDetails.setHttpMethod("")
        smsDetails.setHttpRelativeUrl("")
        smsDetails.setIsMultiRequest(0)
        smsDetails.setPostData("")
        smsDetails.setParameters(list)
        def account = ValidationUtils.validAndGetAccount("d681ef13-d690-4917-jkhg-6c79b-2")
        UtilityBean<SmsDetails> bean = UtilityBean.<SmsDetails>builder().pojoObject(smsDetails).accountIdentifier("d681ef13-d690-4917-jkhg-6c79b-2").authToken(KeycloakConnectionManager.getAccessToken()).account(account).build()
        when:
        new CreateSmsBL().serverValidation(bean)
        then:
        noExceptionThrown()
    }
    def "Update SMS process server validation success for TCP "(){
        given:
        Set<String> headers = new HashSet<>()
        headers.add(Constants.AUTHORIZATION)

        Request req = Spy(DummyRequest.class)
        req.headers() >> headers.toSet()
        req.queryMap() >> new HashMap<>()

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        req.params() >> params
        req.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "    ,{\n" +
                "      \"parameterValue\": \"{Message}\",\n" +
                "      \"parameterName\": \"smsContent\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n"+
                "  ]\n" +
                "}"
        new SmsService().addSMSConfiguration(req, response)

        def list = new ArrayList<SmsParameter>()
        def smsParameter = new SmsParameter()
        smsParameter.setAction("add")
        smsParameter.setParameterName("testParam")
        smsParameter.setParameterValue("mobile")
        smsParameter.setIsPlaceholder(false)
        smsParameter.setParameterType("QueryParameter")
        list.add(smsParameter)
        def smsDetails = new SmsDetails()
        smsDetails.setAddress("appnomic.com")
        smsDetails.setCountryCode("IND")
        smsDetails.setProtocolName("TCP")
        smsDetails.setHttpMethod("")
        smsDetails.setHttpRelativeUrl("")
        smsDetails.setIsMultiRequest(0)
        smsDetails.setPostData("")
        smsDetails.setParameters(list)
        def account = ValidationUtils.validAndGetAccount("d681ef13-d690-4917-jkhg-6c79b-1")
        UtilityBean<SmsDetails> bean = UtilityBean.<SmsDetails>builder().pojoObject(smsDetails).accountIdentifier("d681ef13-d690-4917-jkhg-6c79b-1").authToken(KeycloakConnectionManager.getAccessToken()).account(account).build()
        when:
        new UpdateSmsBL().serverValidation(bean)
        then:
        noExceptionThrown()
        cleanup:
        new NotificationDataService().deleteSmsDetailsAndParameters(2)
    }
    def "Get SMS api Failure: Account identifier invalid"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-10")
        request.params() >> parameters
        request.body() >> ""
        when:
        def res = new SmsService().getSmsConfiguration(request, response)
        then:
        response.getStatus() == 400
        res.getMessage() == "Error while fetching SMS configuration"
    }
    def "Get SMS api Failure: User token identifier invalid"() {
        given:
        InvalidDummyRequest invalidRequest = Spy(InvalidDummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        invalidRequest.headers() >> header.toSet()
        invalidRequest.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        invalidRequest.params() >> parameters
        invalidRequest.body() >> ""
        when:
        def res = new SmsService().getSmsConfiguration(invalidRequest, response)
        then:
        response.getStatus() == 400
        res.getMessage() == "Error while fetching SMS configuration"
    }
    def "Get SMS api Success"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> ""
        when:
        def res = new SmsService().getSmsConfiguration(request, response)
        then:
        response.getStatus() == 200
        res.getMessage() == "SMS configuration fetched successfully"
    }
/*    def "Reverting the sms parametrs"(){
        given:
        def smsDetails = new GetSmsBL().process(2)
        int[] smsParameterIds = smsDetails.getParameters().parallelStream().map({ c -> c.getParameterId() }).collect(Collectors.toList()) as int[]
        when:
        DBTestCache.addToCache("sms_parameters", smsParameterIds)
        then:
        noExceptionThrown()
    }
    def "Reverting the sms details"(){
        given:
        def smsDetails = new GetSmsBL().process(2)
        when:
        DBTestCache.addToCache("sms_details", smsDetails.getId())
        then:
        noExceptionThrown()
    }*/
}