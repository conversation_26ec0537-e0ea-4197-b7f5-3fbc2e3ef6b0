package com.appnomic.appsone.controlcenter.service


import spock.lang.Specification

class ImportCsvProcessIT extends Specification {

/*
    Logger logger = Logger.getLogger("ImportCsvProcessIT")
    Request request = Spy(Request.class)
    ImportFileService service = Spy(ImportFileService.class)
    Response response = new DummyResponse()



    def "validate Import Csv File"() {
        setup:
        String fileName = "7_host_comp_service_app_details.csv"
        File f1=new File(fileName)
        String file=f1.absolutePath
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        FileProcessDao fileProcessDao = MySQLConnectionManager.getInstance().open(FileProcessDao.class)
        String HostInstanceName="NB_Web_Host_154_Inst_1"
        String HostAddress="**************"
        String HostOS="CentOS"
        String HostOSVersion="2"
        String ComponentInstanceName="NB_Web_154_Inst_1"
        String ComponentName="IHS - IBM Http Server"
        String ComponentVersion="9"
        String ServiceName="NB-Web-service"
        int accountId=2
        String applicationName ="NetBanking"

        int serviceType=192
        int appType=191

        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        service.getUserId(request) >> "test-user1"

        request.raw() >> MultiPartUploadHelper.getHttpRequest(file)
        when:
        GenericResponse genericResponse = service.importUploadFile(request, response)

        then:
        response.status() == 200

        then:
        genericResponse != null && genericResponse.data != null

        when:
        int serviceId= fileProcessDao.getController(ServiceName,serviceType,accountId)
        then:
        List<Integer> appIds=fileProcessDao.getTagId(serviceId, Constants.CONTROLLER,accountId)
        List<String> appName=fileProcessDao.getAppName(appIds,appType,accountId)
        appName.get(0)==(applicationName)

        List<Integer> hostIds=fileProcessDao.getTagId(serviceId, Constants.COMP_INSTANCE_TABLE,accountId)
        ComponentInstanceBean hostName=fileProcessDao.getHostList(hostIds,accountId)
        hostName.getName()==(HostInstanceName)
        hostName.getHostAddress()==(HostAddress)
        hostName.getMstCommonVersionId()== Integer.valueOf(HostOSVersion)

        ComponentInstanceBean compName=fileProcessDao.getCompList(hostIds,accountId)
        compName.getName() == ComponentInstanceName
        compName.getHostAddress() == HostAddress
        compName.getMstCommonVersionId()== Integer.valueOf(ComponentVersion)

        ComponentInstanceBean hostCluster=fileProcessDao.getHostCluster(hostIds,accountId)
        hostCluster.getName() == HostOS + "_" + ServiceName

        ComponentInstanceBean compCluster=fileProcessDao.getCompCluster(hostIds,accountId)
        compCluster.getName() == ComponentName + "_" + ServiceName

        int hostClusterMappingId=fileProcessDao.getCompClusterMapping(hostName.getId(),hostCluster.getId(),accountId)
        hostClusterMappingId>0

        int compClusterMappingId=fileProcessDao.getCompClusterMapping(compName.getId(),compCluster.getId(),accountId)

        compClusterMappingId>0


        cleanup:

        fileProcessDao.deleteTagMapping(serviceId, Constants.CONTROLLER)
        fileProcessDao.deleteTagMapping(appIds.get(0), Constants.COMP_INSTANCE_TABLE)
        fileProcessDao.deleteTagMapping(serviceId, Constants.COMP_INSTANCE_TABLE)
        fileProcessDao.deleteClusterMapping(hostCluster.getId())
        fileProcessDao.deleteClusterMapping(compCluster.getId())
        fileProcessDao.deleteInstances(hostCluster.getId())
        fileProcessDao.deleteInstances(compCluster.getId())
        fileProcessDao.deleteInstances(hostName.getId())
        fileProcessDao.deleteInstances(compName.getId())
        fileProcessDao.deleteController(serviceId)
        fileProcessDao.deleteController(appIds.get(0))

    }
    class DummyResponse extends Response {
        int status;

        void status(int i) {
            status = i;
        }

        public int status() {
            return status;
        }
    }
*/
}
