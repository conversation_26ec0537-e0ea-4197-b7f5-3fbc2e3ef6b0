package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.TimezoneResponse
import spock.lang.Specification

class TimezoneServiceIT extends Specification {

    def "GetAllTimezones"() {

        setup:
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)

        when:
        TimezoneResponse res = new TimezoneService().getAllTimezones()

        then:
        res != null
        res.responseStatus == 'SUCCESS'
        res.responseMessage == 'SUCCESS'
        res.data.size() == 76
        res.data.parallelStream().noneMatch({ t -> t.status != 1 || t.accountId != 1 })
        res.data.parallelStream().anyMatch({ t ->
            t.timeZoneId == '(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi' &&
                    t.offset == ********})
    }
}
