package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

class UpdateInstanceServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        @Override
        void status(int i) {
            status = i
        }

        @Override
        int status() {
            return this.status
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def "Server validation failure - invalid accountIdentifier"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> new HashMap<String, String[]>()
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "dummy-account-identifier")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "        \"instanceId\": 37,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"name\": \"WINDOWS_53_Host_New\"\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new UpdateInstanceService().updateInstance(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Account identifier is invalid"
    }

    def "Server validation failure - invalid userId"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> "Dummy-auth-token"
        request.queryMap() >> new HashMap<String, String[]>()
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "        \"instanceId\": 37,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"name\": \"WINDOWS_53_Host_New\"\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new UpdateInstanceService().updateInstance(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Error while extracting user details from authorization token"
    }

    def "Server validation failure - invalid instance id and identifier"() {
        setup:
        int instanceId = 9999999
        String instanceIdentifier = "RandomIdentifier"
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params() >> parameters
        request.queryMap() >> new HashMap<String, String[]>()
        request.body() >> "[\n" +
                "    {\n" +
                "        \"instanceId\": " + instanceId + ",\n" +
                "        \"instanceIdentifier\": \"" + instanceIdentifier + "\",\n" +
                "        \"name\": \"WINDOWS_53_Host_New\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 19,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \"MSSQL-DB-Service\",\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ],\n" +
                "        \"environment\": \"STAND BY\"\n" +
                "    }\n" +
                "]"

        when:
        GenericResponse res = new UpdateInstanceService().updateInstance(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Instance with id [" + instanceId + "] and identifier [" + instanceIdentifier + "]" +
                " is unavailable for this account."
    }

    def "Server validation failure - invalid Application id and identifier"() {
        setup:
        int instanceId = 37
        String instanceIdentifier = "WINDOWS_53_Host"
        int appId = 9999999
        String appIdentifier = "RandomIdentifier"
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params() >> parameters
        request.queryMap() >> new HashMap<String, String[]>()
        request.body() >> "[\n" +
                "    {\n" +
                "        \"instanceId\": " + instanceId + ",\n" +
                "        \"instanceIdentifier\": \"" + instanceIdentifier + "\",\n" +
                "        \"name\": \"WINDOWS_53_Host_New\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": " + appId + ",\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"" + appIdentifier + "\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \"MSSQL-DB-Service\",\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ],\n" +
                "        \"environment\": \"STAND BY\"\n" +
                "    }\n" +
                "]"

        when:
        GenericResponse res = new UpdateInstanceService().updateInstance(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Application with id [" + appId + "] and identifier [" + appIdentifier + "] " +
                "is unavailable for this account."
    }

    def "Server validation failure - invalid Service id and identifier"() {
        setup:
        int instanceId = 37
        String instanceIdentifier = "WINDOWS_53_Host"
        int appId = 19
        String appIdentifier = "microbanking_1"
        int serviceId = 9999999
        String serviceIdentifier = "RandomIdentifier"
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params() >> parameters
        request.queryMap() >> new HashMap<String, String[]>()
        request.body() >> "[\n" +
                "    {\n" +
                "        \"instanceId\": " + instanceId + ",\n" +
                "        \"instanceIdentifier\": \"" + instanceIdentifier + "\",\n" +
                "        \"name\": \"WINDOWS_53_Host_New\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": " + appId + ",\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"" + appIdentifier + "\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": " + serviceId + ",\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \"" + serviceIdentifier + "\",\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ],\n" +
                "        \"environment\": \"STAND BY\"\n" +
                "    }\n" +
                "]"

        when:
        GenericResponse res = new UpdateInstanceService().updateInstance(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Service with the id [" + serviceId + "] and identifier [" + serviceIdentifier + "] " +
                "is unavailable for this account."
    }

    def "Server validation failure - invalid application service mapping"() {
        setup:
        int instanceId = 37
        String instanceIdentifier = "WINDOWS_53_Host"
        int appId = 19
        String appIdentifier = "microbanking_1"
        int serviceId = 16
        String serviceIdentifier = "ENET-App-Service"
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params() >> parameters
        request.queryMap() >> new HashMap<String, String[]>()
        request.body() >> "[\n" +
                "    {\n" +
                "        \"instanceId\": " + instanceId + ",\n" +
                "        \"instanceIdentifier\": \"" + instanceIdentifier + "\",\n" +
                "        \"name\": \"WINDOWS_53_Host_New\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": " + appId + ",\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"" + appIdentifier + "\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": " + serviceId + ",\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \"" + serviceIdentifier + "\",\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ],\n" +
                "        \"environment\": \"STAND BY\"\n" +
                "    }\n" +
                "]"

        when:
        GenericResponse res = new UpdateInstanceService().updateInstance(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Service with id [" + serviceId + "] and identifier [" + serviceIdentifier + "] " +
                "is not mapped to Application with id [" + appId + "] and identifier [" + appIdentifier + "]."
    }

}
