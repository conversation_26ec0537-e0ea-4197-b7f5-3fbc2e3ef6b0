package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

class UserProfileServiceIT extends Specification {


    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setup() {
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
    }

    def  "getUserProfile"() {
        when:
        GenericResponse genericResponse = UserProfileService.getUserProfiles(request, response)

        then:
        genericResponse != null && genericResponse.data != null
        response.status== 200
    }

    def "getUserProfile fail"() {
        when:
        GenericResponse res=UserProfileService.getUserProfiles(null, response)

        then:
        response.status == 400
        res.message== "Request or request body cannot be NULL or empty."
    }
}
