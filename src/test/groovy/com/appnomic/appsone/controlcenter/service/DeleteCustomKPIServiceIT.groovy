package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.beans.keys.AccountKPIKey
import com.appnomic.appsone.controlcenter.businesslogic.DeleteKPI
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.dao.mysql.ITCleanUpDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.IdPojo
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

import static com.appnomic.appsone.controlcenter.common.Constants.*

class DeleteCustomKPIServiceIT extends Specification {


    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {

            String token = KeycloakConnectionManager.getAccessToken()
            if (token == null || token.isEmpty()) {
                return "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCS" +
                        "jhzMnVhazFUUWd3In0.eyJleHAiOjE2MjE5NDkxNjEsImlhdCI6MTYyMTk0NjQ2MSwianRpIjoiZjJjNTI3ZDktM2E5Y" +
                        "S00ZTFlLThhNWYtYWY1MjViYzY2OThjIiwiaXNzIjoiaHR0cHM6Ly8xOTIuMTY4LjEzLjQ0Ojg0NDMvYXV0aC9yZWFsb" +
                        "XMvbWFzdGVyIiwic3ViIjoiNzY0MDEyM2EtZmJkZS00ZmU1LTk4MTItNTgxY2QxZTNhOWMxIiwidHlwIjoiQmVhcmVyI" +
                        "iwiYXpwIjoiYWRtaW4tY2xpIiwic2Vzc2lvbl9zdGF0ZSI6ImExZGI4YWVlLTcyNjQtNDUxNi04Y2E5LTFhNTg1MzUxO" +
                        "DVlMyIsImFjciI6IjEiLCJhbGxvd2VkLW9yaWdpbnMiOlsiKiJdLCJzY29wZSI6InByb2ZpbGUgZW1haWwiLCJlbWFpb" +
                        "F92ZXJpZmllZCI6ZmFsc2UsInByZWZlcnJlZF91c2VybmFtZSI6ImFwcHNvbmVhZG1pbiIsImVtYWlsIjoiYXBwc29uZ" +
                        "WFkbWluLmtAYXBwbm9taWMuY29tIn0.V6dPBnjwGQbj99NGlXFg7rqvcqLxPpMr96qc7N5poqnITNXWL8XJA8VZ0KF0" +
                        "igqpMYQdL1b3SWJ12YXGY6d85AYH3VN4bmOFbtjxfysej2GghMuy_FWqZYDTD7UEhNgG0EoGN-Bnu_FA7zuNuItqVlzc" +
                        "DuYX5OS3Ygf7-6m2-iFMU5JWL0tDaEdAtu5hN4zq8mqQEODGuKT15Q0Y9KSKaCyCjSuGPlFad_AAZEZ6AcDYGxRVNOt0" +
                        "Hfqh_kqLxLVCEouZHsocaLfo0pziKvb8uZ9cQn0jR1ovCfg2CQFuLlt1pVEC_3cg6VXyj8jM6v4kckPw3o3FvM4h0vA" +
                        "qnrY5Gw"
            }
            return token

        }
    }

    class InvalidTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    class NullTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return null
        }
    }

    class EmptyTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return ""
        }
    }

    Set<String> header = new HashSet<>()
    Map<String, String> parameters = new HashMap<>()
    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    DeleteCustomKPIService service = new DeleteCustomKPIService()
    String identifier = "d681ef13-d690-4917-jkhg-6c79b-1"

    def addGroupKPI() {
        DummyRequest request = Spy(DummyRequest.class)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"id\": -1,\n" +
                "  \"componentTypeId\": 1,\n" +
                "  \"componentId\": 6,\n" +
                "  \"componentCommonVersionId\": 6,\n" +
                "  \"name\": \"test-custom-group-KPI\",\n" +
                "  \"groupKpiDetails\": {\n" +
                "    \"description\": \"KPI Group to collect the Procuess utilization details\",\n" +
                "    \"discovery\": 0,\n" +
                "    \"kpiType\": \"CORE\",\n" +
                "    \"id\": 4,\n" +
                "    \"groupKpiName\": \"Process Utilization\"\n" +
                "  },\n" +
                "  \"kpiCategoryDetails\": {\n" +
                "    \"id\": 5,\n" +
                "    \"name\": \"Network Utilization\",\n" +
                "    \"workLoad\": 0\n" +
                "  },\n" +
                "  \"kpiType\": \"CORE\",\n" +
                "  \"description\": \"test custom KPI\",\n" +
                "  \"computedKpiDetails\": null,\n" +
                "  \"status\": 1,\n" +
                "  \"kpiUnit\": \"Percentage\",\n" +
                "  \"clusterOperation\": \"Average\",\n" +
                "  \"rollupOperation\": \"Max\",\n" +
                "  \"collectionInterval\": 120,\n" +
                "  \"availableForAnalytics\": 1,\n" +
                "  \"dataType\": \"Integer\",\n" +
                "  \"valueType\": \"SNAPSHOT\",\n" +
                "  \"clusterAggregation\": \"SingleValue\",\n" +
                "  \"instanceAggregation\": \"SingleValue\",\n" +
                "  \"identifier\": \"test-custom-group-KPI\"\n" +
                "}"

        return new AddCustomKPIService().addCustomKPI(request, response).getData() as IdPojo
    }

    def addNonGroupKPI() {
        DummyRequest request = Spy(DummyRequest.class)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"id\": -1,\n" +
                "  \"componentTypeId\": 1,\n" +
                "  \"componentId\": 6,\n" +
                "  \"componentCommonVersionId\": 6,\n" +
                "  \"name\": \"test-custom-non-group-KPI\",\n" +
                "  \"groupKpiDetails\": null,\n" +
                "  \"kpiCategoryDetails\": {\n" +
                "    \"id\": 5,\n" +
                "    \"name\": \"Network Utilization\",\n" +
                "    \"workLoad\": 0\n" +
                "  },\n" +
                "  \"kpiType\": \"CORE\",\n" +
                "  \"description\": \"test custom KPI\",\n" +
                "  \"computedKpiDetails\": null,\n" +
                "  \"status\": 1,\n" +
                "  \"kpiUnit\": \"Percentage\",\n" +
                "  \"clusterOperation\": \"Average\",\n" +
                "  \"rollupOperation\": \"Max\",\n" +
                "  \"collectionInterval\": 120,\n" +
                "  \"availableForAnalytics\": 1,\n" +
                "  \"dataType\": \"Integer\",\n" +
                "  \"valueType\": \"SNAPSHOT\",\n" +
                "  \"clusterAggregation\": \"SingleValue\",\n" +
                "  \"instanceAggregation\": \"SingleValue\",\n" +
                "  \"identifier\": \"test-custom-non-group-KPI\"\n" +
                "}"

        return new AddCustomKPIService().addCustomKPI(request, response).getData() as IdPojo
    }

    def mapComponentToKpi(IdPojo kpi) {
        DummyRequest request = Spy(DummyRequest.class)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(KPI_IDENTIFIER_PATH_PARAMETER, kpi.getIdentifier())
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "      \"id\": " + kpi.getId() + ",\n" +
                "      \"status\": 1,\n" +
                "      \"componentTypeId\": 1,\n" +
                "      \"componentId\": 22,\n" +
                "      \"componentCommonVersionId\": 24\n" +
                "    }\n" +
                "  ]"

        return new UpdateCustomKpiService().updateCustomKPI(request, response).getData() as IdPojo
    }

    def "DeleteCustomKPI : Auth token NULL"() {
        setup:
        NullTokenDummyRequest nullDummyRequest = Spy(NullTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        nullDummyRequest.headers() >> header.toSet()
        nullDummyRequest.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(KPI_IDENTIFIER_PATH_PARAMETER, "kpi-identifier")
        nullDummyRequest.params() >> parameters
        nullDummyRequest.body() >> ""


        when:
        def res = service.deleteCustomKPI(nullDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.AUTH_KEY_EMPTY)
    }

    def "DeleteCustomKPI : Auth token Empty"() {
        setup:
        EmptyTokenDummyRequest emptyDummyRequest = Spy(EmptyTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        emptyDummyRequest.headers() >> header.toSet()
        emptyDummyRequest.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(KPI_IDENTIFIER_PATH_PARAMETER, "kpi-identifier")
        emptyDummyRequest.params() >> parameters
        emptyDummyRequest.body() >> ""


        when:
        def res = service.deleteCustomKPI(emptyDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.AUTH_KEY_EMPTY)
    }

    def "DeleteCustomKPI : Account Identifier NULL"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, null)
        parameters.put(KPI_IDENTIFIER_PATH_PARAMETER, "kpi-identifier")
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.deleteCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.ACCOUNT_NULL_OR_EMPTY)
    }

    def "DeleteCustomKPI : Account Identifier Empty"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, "")
        parameters.put(KPI_IDENTIFIER_PATH_PARAMETER, "kpi-identifier")
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.deleteCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.ACCOUNT_NULL_OR_EMPTY)
    }

    def "DeleteCustomKPI : KPI Identifier NULL"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(KPI_IDENTIFIER_PATH_PARAMETER, null)
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.deleteCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("KPI identifier is null or empty.")
    }

    def "DeleteCustomKPI : KPI Identifier Empty"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(KPI_IDENTIFIER_PATH_PARAMETER, "")
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.deleteCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("KPI identifier is null or empty.")
    }

    def "DeleteCustomKPI : Auth token Invalid"() {
        setup:
        InvalidTokenDummyRequest invalidDummyRequest = Spy(InvalidTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        invalidDummyRequest.headers() >> header.toSet()
        invalidDummyRequest.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(KPI_IDENTIFIER_PATH_PARAMETER, "kpi-identifier")
        invalidDummyRequest.params() >> parameters
        invalidDummyRequest.body() >> ""


        when:
        def res = service.deleteCustomKPI(invalidDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Error while extracting user details from authorization token")
    }

    def "DeleteCustomKPI : Account Identifier Invalid"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, "invalid-account-identifier")
        parameters.put(KPI_IDENTIFIER_PATH_PARAMETER, "kpi-identifier")
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.deleteCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.ACCOUNT_IDENTIFIER_INVALID)
    }

    def "DeleteCustomKPI : KPI Identifier Invalid"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(KPI_IDENTIFIER_PATH_PARAMETER, "invalid-KPI-identifier")
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.deleteCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Invalid KPI Identifier")
    }

    def "DeleteCustomKPI : Standard KPI"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(KPI_IDENTIFIER_PATH_PARAMETER, "CPU_UTIL")
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.deleteCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Only custom KPIs can be deleted.")
    }

    /*def "DeleteCustomKPI : Computed KPI"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(KPI_IDENTIFIER_PATH_PARAMETER, "test-computed-KPI")
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.deleteCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Only custom KPIs can be deleted.")
    }*/

    def "DeleteCustomKPI : Custom KPI mapped to component with active instances"() {
        setup:
        IdPojo kpi = addNonGroupKPI()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(KPI_IDENTIFIER_PATH_PARAMETER, kpi.getIdentifier())
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.deleteCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("The custom KPI can't be deleted as it is mapped to the component which has active instances.")

        cleanup:
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(kpi.getId()).groupKpi(false).build())
    }

    def "DeleteCustomKPI : Custom KPI mapped to more than one component"() {
        setup:
        IdPojo kpi = addGroupKPI()
        mapComponentToKpi(kpi)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(KPI_IDENTIFIER_PATH_PARAMETER, kpi.getIdentifier())
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.deleteCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("The custom KPI can't be deleted as it has more than one component mapped to it.")

        cleanup:
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(kpi.getId()).groupKpi(true).build())
    }

    /*def "DeleteCustomKPI : Custom KPI mapped to computed KPI"() {
        setup:
        IdPojo kpi = addNonGroupKPI()
        ITCleanUpDataService.updateComputedKPIMapping(kpi.getId())
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(KPI_IDENTIFIER_PATH_PARAMETER, kpi.getIdentifier())
        request.params() >> parameters
        request.body() >> ""

        when:
        def res = service.deleteCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("The custom KPI can't be deleted as it mapped to computed KPI.")

        cleanup:
        ITCleanUpDataService.updateComputedKPIMapping(1211)
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(kpi.getId()).groupKpi(false).build())
    }*/

    def "DeleteCustomKPI SUCCESS : non group KPI"() {
        setup:
        IdPojo kpi = addNonGroupKPI()
        ITCleanUpDataService.updateCompInstanceStatusByComponentId(6,0)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(KPI_IDENTIFIER_PATH_PARAMETER, kpi.getIdentifier())
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.deleteCustomKPI(request, response)

        then:
        noExceptionThrown()
        response.status == SUCCESS_STATUS_CODE
        res.getMessage().contains("deleted successfully")

        cleanup:
        ITCleanUpDataService.updateCompInstanceStatusByComponentId(6,1)
    }

    def "DeleteCustomKPI SUCCESS : group KPI"() {
        setup:
        IdPojo kpi = addGroupKPI()
        ITCleanUpDataService.updateCompInstanceStatusByComponentId(6,0)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(KPI_IDENTIFIER_PATH_PARAMETER, kpi.getIdentifier())
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.deleteCustomKPI(request, response)

        then:
        noExceptionThrown()
        response.status == SUCCESS_STATUS_CODE
        res.getMessage().contains("deleted successfully")

        cleanup:
        ITCleanUpDataService.updateCompInstanceStatusByComponentId(6,1)
    }

    /*def "delete"() {
        when:
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(1767).groupKpi(false).build())
        then:
        true
    }*/
}
