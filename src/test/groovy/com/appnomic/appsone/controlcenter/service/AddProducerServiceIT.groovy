package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.dao.mysql.ProducerDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import org.apache.commons.lang.RandomStringUtils
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class AddProducerServiceIT extends Specification{
    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def deleteProducer(int producerId,String producerType){
        ProducerDataService.removeProducerData(producerId,producerType,null)
    }

    @Shared
    Map<String, String[]> queryMapData = new HashMap<>()

    def "Server validation failure - invalid accountIdentifier"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "dummy-account-identifier")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}"
        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Account identifier is invalid"
    }

    def "Server validation failure - invalid userId"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> "Dummy-auth-token"
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}"
        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Error while extracting user details from authorization token"
    }

    def "Server validation failure - duplicate producer name"() {
        given:
        Request request1 = Spy(Request.class)
        DummyResponse response1 = Spy(DummyResponse.class)
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request1.headers() >> header
        request1.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request1.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request1.params() >> parameters
        request1.body() >>"{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}"

        Request request2 = Spy(Request.class)
        DummyResponse response2 = Spy(DummyResponse.class)
        request2.headers() >> header
        request2.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request2.queryMap() >> queryMapData
        request2.params() >> parameters
        request2.body() >>"{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}"
        when:
        GenericResponse res1 = new AddProducerService().addProducer(request1, response1)
        GenericResponse res2 = new AddProducerService().addProducer(request2, response2)

        then:
        response1.getStatus() == 200
        res1.getResponseStatus() == "SUCCESS"
        res1.message == "SUCCESS"

        response2.getStatus() == 400
        res2.getResponseStatus() == "FAILURE"
        res2.message == "ServerException : Invalid ProducerDetails. Check CC Logs."

        cleanup:
        for(Integer key: res1.getData().keySet()){
            deleteProducer(key,"SCRIPT")
        }
    }

    def "Server validation failure - invalid kpi identifier in kpiMapping"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \""+ RandomStringUtils.random(10,true,false)+"\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}"
        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Invalid ProducerDetails. Check CC Logs."
    }

    def "Server validation failure - invalid kpi type"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \""+ RandomStringUtils.random(10,true,false)+"\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}"
        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Invalid ProducerDetails. Check CC Logs."
    }

    def "Server validation failure - different groupStatus"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 1,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}"
        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Invalid ProducerDetails. Check CC Logs."
    }

    def "Server validation failure - invalid component data in kpiMapping"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \""+ RandomStringUtils.random(10,true,false)+"\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}"
        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Invalid ProducerDetails. Check CC Logs."
    }

    def "Server validation failure - different kpi and component pair groupStatus"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"5.1\",\n" +
                "            \"componentName\": \"IIS\",\n" +
                "            \"componentTypeName\": \"Web Server\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}"
        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Invalid ProducerDetails. Check CC Logs."
    }

    def "Server validation failure - invalid producerType"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \""+RandomStringUtils.random(10,true,false)+"\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}"
        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Invalid ProducerDetails. Check CC Logs."
    }

    def "Server validation failure - missing mandatory producer attributes"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}"
        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Invalid ProducerDetails. Check CC Logs."
    }

    def "Server validation failure - extra producer attributes"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\",\n" +
                "        \"extra_parameter\": \"extra_parameter_value\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}"
        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Invalid ProducerDetails. Check CC Logs."
    }

    def "Server validation failure - invalid producer parameter"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \""+RandomStringUtils.random(10,true,false)+"\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}"
        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Invalid ProducerDetails. Check CC Logs."
    }

    def "Server validation failure - random producerType"() {
        given:
        Set<String> header = new HashSet<>()
        String producerType = RandomStringUtils.random(10,true,false)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \""+producerType+"\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}"
        int producerTypeId = ProducerDataService.insertDummyProducerType(producerType,16,null)
        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 500
        res.getResponseStatus() == "FAILURE"
        res.message == "Internal server error, Kindly contact the Administrator."

        cleanup:
        ProducerDataService.removeProducerType(producerTypeId,null)
    }

    def "Server validation failure - random producerType attributes"() {
        given:
        Set<String> header = new HashSet<>()
        String producerType = RandomStringUtils.random(10,true,false)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \""+producerType+"\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"module\": \"test.sh\",\n" +
                "        \"url\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}"

        int producerTypeId = ProducerDataService.insertDummyProducerType(producerType,16,null)
        ProducerDataService.insertDummyProducerAttributes(producerTypeId,"module",0,null)
        ProducerDataService.insertDummyProducerAttributes(producerTypeId,"url",1,null)
        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 500
        res.getResponseStatus() == "FAILURE"
        res.message == "Internal server error, Kindly contact the Administrator."

        cleanup:
        ProducerDataService.removeProducerAttribute(producerTypeId,null)
        ProducerDataService.removeProducerType(producerTypeId,null)

    }

    def "Server validation failure - random producerType attributes nonMandatory field null"() {
        given:
        Set<String> header = new HashSet<>()
        String producerType = RandomStringUtils.random(10,true,false)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \""+producerType+"\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"module\": null,\n" +
                "        \"url\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}"

        int producerTypeId = ProducerDataService.insertDummyProducerType(producerType,16,null)
        ProducerDataService.insertDummyProducerAttributes(producerTypeId,"module",0,null)
        ProducerDataService.insertDummyProducerAttributes(producerTypeId,"url",1,null)
        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Invalid ProducerDetails. Check CC Logs."

        cleanup:
        ProducerDataService.removeProducerAttribute(producerTypeId,null)
        ProducerDataService.removeProducerType(producerTypeId,null)

    }

    def "Success Case Producer-Type SCRIPT"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}"

        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
        res.message == "SUCCESS"
        res.getData().containsValue("TP-DefaultKPI")

        cleanup:
        for(Integer key: res.getData().keySet()){
            deleteProducer(key,"SCRIPT")
        }
    }

    def "Success Case Producer-Type WMI"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-WMI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"WMI\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"2008 R1 SP1\",\n" +
                "            \"componentName\": \"WINDOWS\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"MEM_UTIL\",\n" +
                "            \"componentVersionId\": \"2008 R1 SP1\",\n" +
                "            \"componentName\": \"WINDOWS\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"DISK_IOREAD\",\n" +
                "            \"componentVersionId\": \"2008 R1 SP1\",\n" +
                "            \"componentName\": \"WINDOWS\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"user\",\n" +
                "            \"parameterValue\": \"{Username}\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"parameterType\": \"ENV_PARAM\",\n" +
                "            \"parameterName\": \"domain\",\n" +
                "            \"parameterValue\": \"{Domain}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}"

        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
        res.message == "SUCCESS"
        res.getData().containsValue("TP-WMI")

        cleanup:
        for(Integer key: res.getData().keySet()){
            deleteProducer(key,"WMI")
        }
    }

    def "Success Case Producer-Type HTTP"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-HTTP\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"HTTP\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"status_url\": \"/test/url/index.html\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPULoad\",\n" +
                "            \"componentVersionId\": \"2.2\",\n" +
                "            \"componentName\": \"Apache httpd - Apache\",\n" +
                "            \"componentTypeName\": \"Web Server\",\n" +
                "            \"isDefault\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"BusyWorkers\",\n" +
                "            \"componentVersionId\": \"2.2\",\n" +
                "            \"componentName\": \"Apache httpd - Apache\",\n" +
                "            \"componentTypeName\": \"Web Server\",\n" +
                "            \"isDefault\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"IdleWorkers\",\n" +
                "            \"componentVersionId\": \"2.2\",\n" +
                "            \"componentName\": \"Apache httpd - Apache\",\n" +
                "            \"componentTypeName\": \"Web Server\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": []\n" +
                "}"

        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
        res.message == "SUCCESS"
        res.getData().containsValue("TP-HTTP")

        cleanup:
        for(Integer key: res.getData().keySet()){
            deleteProducer(key,"HTTP")
        }
    }

    def "Success Case Producer-Type JMX"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-JMX\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"JMX\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"url\": \"/test/url/index.html\",\n" +
                "        \"target_object_name\": \"test object name\",\n" +
                "        \"attribute_type\": \"DirectValue\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"requestCount\",\n" +
                "            \"componentVersionId\": \"7.0.x\",\n" +
                "            \"componentName\": \"Apache Tomcat\",\n" +
                "            \"componentTypeName\": \"Application Server\",\n" +
                "            \"isDefault\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"processingTime\",\n" +
                "            \"componentVersionId\": \"7.0.x\",\n" +
                "            \"componentName\": \"Apache Tomcat\",\n" +
                "            \"componentTypeName\": \"Application Server\",\n" +
                "            \"isDefault\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"bytesReceived\",\n" +
                "            \"componentVersionId\": \"7.0.x\",\n" +
                "            \"componentName\": \"Apache Tomcat\",\n" +
                "            \"componentTypeName\": \"Application Server\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": []\n" +
                "}"

        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
        res.message == "SUCCESS"
        res.getData().containsValue("TP-JMX")

        cleanup:
        for(Integer key: res.getData().keySet()){
            deleteProducer(key,"JMX")
        }
    }

    def "Success Case Producer-Type WAS"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-WAS\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"WAS\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"module\": \"testModule\",\n" +
                "        \"target_object_name\": \"testObjectName: *\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"HeapSize\",\n" +
                "            \"componentVersionId\": \"7.0.x\",\n" +
                "            \"componentName\": \"WebSphere\",\n" +
                "            \"componentTypeName\": \"Application Server\",\n" +
                "            \"isDefault\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"UsedMemory\",\n" +
                "            \"componentVersionId\": \"7.0.x\",\n" +
                "            \"componentName\": \"WebSphere\",\n" +
                "            \"componentTypeName\": \"Application Server\",\n" +
                "            \"isDefault\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"ProcessCpuUsage\",\n" +
                "            \"componentVersionId\": \"7.0.x\",\n" +
                "            \"componentName\": \"WebSphere\",\n" +
                "            \"componentTypeName\": \"Application Server\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": []\n" +
                "}"

        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
        res.message == "SUCCESS"
        res.getData().containsValue("TP-WAS")

        cleanup:
        for(Integer key: res.getData().keySet()){
            deleteProducer(key,"WAS")
        }
    }

    def "Success Case Producer-Type JDBC"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-JDBC\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"JDBC\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"url\": \"testURL\",\n" +
                "        \"driver\": \"testDriver\",\n" +
                "        \"query\": \"testQuery\",\n" +
                "        \"query_result\": \"testQueryResult\",\n" +
                "        \"is_query_encrypted\": \"1\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"Open_tables\",\n" +
                "            \"componentVersionId\": \"5.5\",\n" +
                "            \"componentName\": \"MySQL\",\n" +
                "            \"componentTypeName\": \"Database Server\",\n" +
                "            \"isDefault\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"Threads_connected\",\n" +
                "            \"componentVersionId\": \"5.5\",\n" +
                "            \"componentName\": \"MySQL\",\n" +
                "            \"componentTypeName\": \"Database Server\",\n" +
                "            \"isDefault\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"Threads_running\",\n" +
                "            \"componentVersionId\": \"5.5\",\n" +
                "            \"componentName\": \"MySQL\",\n" +
                "            \"componentTypeName\": \"Database Server\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"KEY_VALUE\",\n" +
                "            \"parameterName\": \"testName\",\n" +
                "            \"parameterValue\": \"testValue\"\n" +
                "        }\n" +
                "    ]\n" +
                "}"

        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
        res.message == "SUCCESS"
        res.getData().containsValue("TP-JDBC")

        cleanup:
        for(Integer key: res.getData().keySet()){
            deleteProducer(key,"JDBC")
        }
    }

    def "Success Case Producer-Type JPPF"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-JPPF\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"JPPF\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"server_type\": \"Server\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"nbNodes\",\n" +
                "            \"componentVersionId\": \"4.1\",\n" +
                "            \"componentName\": \"JPPFServer\",\n" +
                "            \"componentTypeName\": \"Services\",\n" +
                "            \"isDefault\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"nbIdleNodes\",\n" +
                "            \"componentVersionId\": \"4.1\",\n" +
                "            \"componentName\": \"JPPFServer\",\n" +
                "            \"componentTypeName\": \"Services\",\n" +
                "            \"isDefault\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"NonHeapMemoryUsed\",\n" +
                "            \"componentVersionId\": \"4.1\",\n" +
                "            \"componentName\": \"JPPFServer\",\n" +
                "            \"componentTypeName\": \"Services\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": []\n" +
                "}"

        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
        res.message == "SUCCESS"
        res.getData().containsValue("TP-JPPF")

        cleanup:
        for(Integer key: res.getData().keySet()){
            deleteProducer(key,"JPPF")
        }
    }

    def "Success Case Producer-Type HTTP_JSON"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-HTTP_JSON\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"HTTP_JSON\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"json_url\": \"/test/url\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": []\n" +
                "}"

        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
        res.message == "SUCCESS"
        res.getData().containsValue("TP-HTTP_JSON")

        cleanup:
        for(Integer key: res.getData().keySet()){
            deleteProducer(key,"HTTP_JSON")
        }
    }

    def "Success Case Producer-Type EXTERNAL"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >>"{\n" +
                "    \"name\": \"TP-EXTERNAL\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"EXTERNAL\",\n" +
                "    \"producerAttributes\": {},\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"Open_tables\",\n" +
                "            \"componentVersionId\": \"5.5\",\n" +
                "            \"componentName\": \"MySQL\",\n" +
                "            \"componentTypeName\": \"Database Server\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": []\n" +
                "}"

        when:
        GenericResponse res = new AddProducerService().addProducer(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
        res.message == "SUCCESS"
        res.getData().containsValue("TP-EXTERNAL")

        cleanup:
        for(Integer key: res.getData().keySet()){
            deleteProducer(key,"EXTERNAL")
        }
    }

}
