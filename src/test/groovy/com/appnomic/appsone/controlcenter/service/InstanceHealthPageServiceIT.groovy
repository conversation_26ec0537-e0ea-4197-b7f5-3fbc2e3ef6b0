package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

class InstanceHealthPageServiceIT extends Specification {
    class DummyResponse extends Response {
        int status;

        void status(int i) {
            status = i
        }
    }


    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def cleanupSpec() {
        DBTestCache.rollback()
    }

    def "getInstanceHealthPageDetails success"() {
        setup:
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(false)
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        when:
        def result = InstanceHealthPageService.getInstanceHealthPageDetails(request, response)
        then:
        result.getData().size() == 2
        then:
        def list = result.getData()
        list.get(0).getPageName() == "ProblemPage"
    }

}
