package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class InstancesServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    DummyResponse response = Spy(DummyResponse.class)
    DummyRequest request = Spy(DummyRequest.class)

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }
    @Shared
    Map<String, String> parameters = new HashMap<>()
    @Shared
    Set<String> header = new HashSet<>()
    String token = KeycloakConnectionManager.getAccessToken()

    def "get Instances  api success"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(":serviceid", "2")
        request.params() >> parameters
        request.body() >> ""

        when:
        AgentStatusService agentStatusService = new AgentStatusService()
        GenericResponse res = agentStatusService.getInstance(request, response)

        then:
        res.getData() != null
        response.status == 200
    }

    def "get Instances  api - invalid account identifier"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", " ")
        parameters.put(":serviceid", "2")
        request.params() >> parameters
        request.body() >> ""

        when:
        AgentStatusService agentStatusService=new AgentStatusService()
        GenericResponse res = agentStatusService.getInstance(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Account Identifier should not be empty."
    }

    def "get Instances api empty request"() {
        when:
        AgentStatusService agentStatusService = new AgentStatusService()
        GenericResponse res = agentStatusService.getInstance(null, response)

        then:
        response.status == 400
        res.message == "ClientException : Account Identifier should not be empty."
    }


    def "get Instances Count api fail invalid identifier"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-11")
        parameters.put(":serviceid", "2")
        request.params() >> parameters
        request.body() >> ""
        when:
        AgentStatusService agentStatusService = new AgentStatusService()
        GenericResponse res = agentStatusService.getInstance(request, response)

        then:
        response.getStatus() == 400
        res.message == "ServerException : Invalid Account Identifier."
    }

    def "get Instances  api fail empty serviceid"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(":serviceid", "")
        request.params() >> parameters
        request.body() >> ""
        when:
        AgentStatusService agentStatusService = new AgentStatusService()
        GenericResponse res = agentStatusService.getInstance(request, response)

        then:
        response.getStatus() == 400
        res.message == "ClientException : Service Id should not be empty or null"
    }

    def "get Instances  api fail invalid serviceid"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(":serviceid", "20_12")
        request.params() >> parameters
        request.body() >> ""
        when:
        AgentStatusService agentStatusService = new AgentStatusService()
        GenericResponse res = agentStatusService.getInstance(request, response)

        then:
        response.getStatus() == 400
        res.message == "ClientException : Service Id is not an integer"
    }


}
